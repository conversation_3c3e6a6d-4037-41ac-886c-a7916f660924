package dal

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	"code.byted.org/devgpt/kiwis/mock"
)

func TestDal_CreateMemoryReflection(t *testing.T) {
	db := mock.NewMockDB(t, &MemoryReflectionPO{})
	dal := NewDAO(db)
	datasetPO, err := dal.CreateMemoryReflection(context.Background(), &CreateMemoryReflectionOption{
		OccurTime:    time.Now(),
		FromEntityID: 1,
		ToEntityID:   2,
		Description:  "test",
	})
	require.NoError(t, err)
	require.True(t, datasetPO.CreatedAt.After(time.Time{}))
}

func TestBatchCreateMemoryReflectionWithValidData(t *testing.T) {
	db := mock.NewMockDB(t, &MemoryReflectionPO{})
	dal := NewDAO(db)
	reflections := []*MemoryReflectionPO{
		{
			OccurTime:    time.Now(),
			FromEntityID: 1,
			ToEntityID:   2,
			Description:  "desc1",
			RelationType: "type1",
		},
		{
			OccurTime:    time.Now(),
			FromEntityID: 3,
			ToEntityID:   4,
			Description:  "desc2",
			RelationType: "type2",
		},
	}

	result, err := dal.BatchCreateMemoryReflection(context.Background(), reflections)
	require.NoError(t, err)
	require.Len(t, result, 2)
	require.Equal(t, reflections, result)
}

func TestGetMemoryReflectionsByIDsWithValidIDs(t *testing.T) {
	db := mock.NewMockDB(t, &MemoryReflectionPO{})
	dal := NewDAO(db)
	ids := []int64{1, 2}

	reflections := []*MemoryReflectionPO{
		{
			OccurTime:    time.Now(),
			FromEntityID: 1,
			ToEntityID:   2,
			Description:  "desc1",
			RelationType: "type1",
		},
		{
			OccurTime:    time.Now(),
			FromEntityID: 3,
			ToEntityID:   4,
			Description:  "desc2",
			RelationType: "type2",
		},
	}

	result, err := dal.BatchCreateMemoryReflection(context.Background(), reflections)
	require.NoError(t, err)

	result, err = dal.GetMemoryReflectionsByIDs(context.Background(), ids)
	require.NoError(t, err)
	require.Len(t, result, 2)
	require.Equal(t, int64(1), result[0].FromEntityID)
	require.Equal(t, int64(2), result[0].ToEntityID)
	require.Equal(t, "desc1", result[0].Description)
	require.Equal(t, "type1", result[0].RelationType)
	require.Equal(t, int64(3), result[1].FromEntityID)
	require.Equal(t, int64(4), result[1].ToEntityID)
	require.Equal(t, "desc2", result[1].Description)
	require.Equal(t, "type2", result[1].RelationType)
}

func TestGetTopKMemoryReflectionsByTypeAndFromToIDsWithValidData(t *testing.T) {
	db := mock.NewMockDB(t, &MemoryReflectionPO{})
	dal := NewDAO(db)
	topK := 2
	relationType := "type1"
	fromEntityID := int64(1)
	toEntityID := int64(2)

	reflections := []*MemoryReflectionPO{
		{
			OccurTime:    time.Now(),
			FromEntityID: 1,
			ToEntityID:   2,
			Description:  "desc1",
			RelationType: "type1",
		},
		{
			OccurTime:    time.Now(),
			FromEntityID: 3,
			ToEntityID:   4,
			Description:  "desc2",
			RelationType: "type2",
		},
	}

	result, err := dal.BatchCreateMemoryReflection(context.Background(), reflections)
	require.NoError(t, err)

	result, err = dal.GetTopKMemoryReflectionsByByTypeAndFromToIDs(context.Background(), topK, relationType, fromEntityID, toEntityID)
	require.NoError(t, err)
	require.Len(t, result, 1)
	require.Equal(t, "desc1", result[0].Description)
}
