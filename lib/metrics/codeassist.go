package metrics

import (
	"strconv"
	"strings"
	"time"

	"github.com/samber/lo"

	gmetrics "code.byted.org/gopkg/metrics/generic"
	"code.byted.org/gopkg/metrics/v4"
)

var CodeAssistMetric CodeAssistMetricSet

// InitCodeAssistMetric registers metrics set, return error if the metrics define is not valid.
func InitCodeAssistMetric() error {
	metricsClient, err := metrics.NewClient("flow.codeassist.assistant") // 固定psm前缀，否则会打到多个psm上，不好打点统计
	if err != nil {
		panic("failed to create metrics client")
	}
	return gmetrics.RegisterMetrics(&CodeAssistMetric, gmetrics.WithMetricsClient(metricsClient))
}

type CodeAssistChatTag struct {
	AppName     string `key:"app_name"`
	AppVersion  string `key:"app_version"`
	ChatSource  string `key:"chat_source"`
	IsLogin     string `key:"is_login"`
	IsError     string `key:"is_error"`
	CommandName string `key:"command_name"`
	HitVLM      bool   `key:"hit_vlm"`
	CotMode     int    `key:"cot_mode"`
}

type CodeAssistChatStep string

const (
	CodeAssistChatStepResolveContext CodeAssistChatStep = "resolve_context"
	CodeAssistChatStepAnswer         CodeAssistChatStep = "answer"
	CodeAssistChatStepSuggest        CodeAssistChatStep = "suggest"
	CodeAssistChatStepIntention      CodeAssistChatStep = "intention"
)

type CodeAssistContextStep string

const (
	/*
		file & directory & repository. 索引新构建分step耗时
			file: 不分step

			directory: 共7个step
			  step1:  context_create ()
			  step2:  context_analysis_tree
			  step3:  context_analysis_first_level
			  step4:  context_analysis_all_files
			  step5:  directory_download
			  step6:  directory_compress
			  step7:  directory_upload

			repository: 共5个step
			  step1:  sniff_repository_link
		      step2:  context_create
		      step3:  context_analysis_tree
			  step4:  context_analysis_first_level
			  step5:  context_analysis_all_files
	*/
	// context 通用的step
	ContextCreateStep                  CodeAssistContextStep = "context_create"                     // 上下文创建
	ContextDownloadTreeStep            CodeAssistContextStep = "context_download_tree"              // 文件夹&代码下载目录
	ContextAnalysisTreeStep            CodeAssistContextStep = "context_analysis_tree"              // 文件夹&代码仓库目录ckg构建
	ContextAnalysisFirstLevelFilesStep CodeAssistContextStep = "context_analysis_first_level_files" // 文件夹&代码仓库第一次目录和文件
	ContextAnalysisAllFilesStep        CodeAssistContextStep = "context_analysis_all_files"         // 文件夹&代码仓库全部文件索引完成
	// directory 压缩上传分step
	DirectoryContextDownloadStep CodeAssistContextStep = "directory_download" // 文件夹下载
	DirectoryContextCompressStep CodeAssistContextStep = "directory_compress" // 文件夹压缩
	DirectoryContextUploadStep   CodeAssistContextStep = "directory_upload"   // 文件夹压缩包上传
	// multi file 压缩上传分step
	MultiFileContextDownloadStep CodeAssistContextStep = "multi_file_download" // 多文件下载
	// Artifacts 压缩上传分step
	ArtifactsDownloadStep CodeAssistContextStep = "artifacts_download" // artifacts下载
	// repository link探测step
	RepositorySniffLinkStep CodeAssistContextStep = "sniff_repository_link" // 仓库探测阶段
)

type CodeAssistChatStepTag struct {
	AppName    string             `key:"app_name"`
	ChatSource string             `key:"chat_source"`
	Step       CodeAssistChatStep `key:"step"`
	IsLogin    string             `key:"is_login"`
	IsError    string             `key:"is_error"`
}

type CodeAssistResolverTag struct {
	ResolverID string `key:"resolver_id"`
	IsError    string `key:"is_error"`
}

type CodeAssistResolveContextTag struct {
	ContextType string `key:"context_type"`
	IsError     string `key:"is_error"`
}

type CodeAssistRecallTag struct {
	ResolverID string `key:"resolver_id"`
	DatasetID  int64  `key:"dataset_id"`
	IsEmpty    bool   `key:"is_empty"`
}

type CodeAssistChatPipelineTag struct {
	AppName    string `key:"app_name"`
	ChatSource string `key:"chat_source"`
	Handler    string `key:"handler"`
	IsError    string `key:"is_error"`
}

type ContextTaskMQLatencyTag struct {
	ResourceType string `key:"resource_type"`
	TaskType     string `key:"task_type"`
}

type CodeAssistFileKBIndexTag struct {
	ResourceType string `key:"resource_type"`
}

type CodeAssistFileStepTag struct {
	Step CodeAssistContextStep `key:"step"`
}

type CodeAssistContextIndexTag struct {
	ResourceType   string `key:"resource_type"`
	TaskType       string `key:"task_type"`
	FileNumGroup   int64  `key:"file_num_group"`   // group by 50 files
	TotalSizeGroup int64  `key:"total_size_group"` // group by 50 MB
}

type ContextIndexReadyLatencyTag struct {
	ResourceType string `key:"resource_type"`
}

type CodeAssistContextFailedTag struct {
	ResourceType string `key:"resource_type"`
	ActionType   string `key:"action_type"`
	NodeType     string `key:"node_type"`
}

type CodeAssistContextIndexStepTag struct {
	ResourceType string                `key:"resource_type"`
	Step         CodeAssistContextStep `key:"step"`
}

type DGitTag struct {
	FunctionName string `key:"function_name"`
}

type GitHubTag struct {
	FunctionName string `key:"function_name"`
}

type SniffRepositoryLinkTag struct {
	IsRepository     bool   `key:"is_repository"`
	Source           string `key:"source"`
	NotSupportReason string `key:"not_support_reason"`
}

type CodeAssistChatModelTokensTag struct {
	ModelName    string `key:"model_name"`
	InputTokens  int    `key:"input_tokens"`  // 模型输入 tokens 区间
	OutputTokens int    `key:"output_tokens"` // 模型输出 tokens 区间
	Chunks       int    `key:"chunks"`        // 模型输出的chunks数量区间
	ModelArch    string `key:"model_arch"`
}

type CodeAssistRateLimitTag struct {
	LimitKey string `key:"limit_key"`
	Result   bool   `key:"result"`
}

type CodeAssistKBFileMetaFetchTag struct {
	DatasetID int64 `key:"dataset_id"`
}

type ChatTokensTag struct {
	ModelName string `key:"model_name"`
	ModelArch string `key:"model_arch"`
	Handler   string `key:"handler"`
}

type CodeAssistArtifactsResponseTag struct {
	ArtifactsCount int  `key:"artifacts_count"`
	ParseError     bool `key:"parse_error"`
}

type CodeAssistArtifactsTag struct {
	Type      string `key:"type"`
	PartCount int    `key:"part_count"`
}

type CodeAssistArtifactsPartTag struct {
	Type     string `key:"type"`
	Language string `key:"language"`
}

type CodeAssistSandboxCodeExecutableTag struct {
	IsExecutable bool   `key:"is_executable"` // 是否可执行
	CodeType     string `key:"code_type"`     // 代码类型
}

type CodeAssistSandboxCodeFileNotExecutableTag struct {
	Language string `key:"language"`
	Category string `key:"category"`  // 不可执行原因分类
	CodeType string `key:"code_type"` // 代码类型
}

type CodeAssistSandboxCodeRunTag struct {
	FunctionType int    `key:"function_type" json:"functionType,omitempty"` // 集群
	SandboxType  int    `key:"sandbox_type" json:"sandboxType,omitempty"`   // 沙盒类型
	Language     string `key:"language" json:"language,omitempty"`          // 语言
	IsSuccess    bool   `key:"is_success" json:"isSuccess,omitempty"`       // 是否成功
	CodeType     string `key:"code_type" json:"codeType,omitempty"`         // 代码类型
	FailReason   string `key:"fail_reason" json:"failReason,omitempty"`     // 运行失败原因
}

type CodeAssistSandboxTag struct {
	FunctionID   string `key:"function_id"`
	FunctionType int    `key:"function_type"`
	SandboxType  int    `key:"sandbox_type"`
	Language     string `key:"language"`
}

type CodeAssistSandboxInstanceAllocationOverLimitTag struct {
	FunctionType  int    `key:"function_type"`
	SandboxType   int    `key:"sandbox_type"`
	Language      string `key:"language"`
	AllocationNum int    `key:"allocation_num"`
}

type CodeAssistImageReviewResultTag struct {
	IsPass bool `key:"is_pass"` // 是否通过图片安全审查
}

type CodeAssistImageReviewUnpassErrorTag struct {
	Type     string `key:"type"`
	Category string `key:"category"` // 未通过审查错误原因分类
}

type CodeAssistImageReviewHitCacheTag struct {
	IsHitCache int `key:"is_hit_cache"`
}

type CodeAssistImageReviewDownloadImageLatencyTag struct {
	IsDownloadSuccess int `key:"is_download_success"`
}

type CodeAssistImageReviewResultLatencyTag struct {
	Category int `key:"category"`
}

type CodeAssistSearchGenerationImageTag struct {
	Type string `key:"type"`
}

type CodeAssistTaskBeginTag struct {
	AgentName string `key:"agent_name"`
}

type CodeAssistTaskStatus string

const (
	CodeAssistTaskStatusSuccess  CodeAssistTaskStatus = "success"
	CodeAssistTaskStatusFailed   CodeAssistTaskStatus = "failed"
	CodeAssistTaskStatusCanceled CodeAssistTaskStatus = "canceled"
)

type CodeAssistTaskCompleteTag struct {
	AgentName   string               `key:"agent_name"`
	Status      CodeAssistTaskStatus `key:"status"`
	ErrorReason string               `key:"error_reason"`
}

type CodeAssistTaskFirstTokenTag struct {
	AgentName string `key:"agent_name"`
}

type CodeAssistTaskLastTokenTag struct {
	AgentName string               `key:"agent_name"`
	Status    CodeAssistTaskStatus `key:"status"`
}

type CodeAssistTaskStep string

const (
	CodeAssistTaskStepCreateSandbox        CodeAssistTaskStep = "create_sandbox"
	CodeAssistTaskStepParseInputAndHistory CodeAssistTaskStep = "parse_input_and_history"
	CodeAssistTaskStepFileUpload           CodeAssistTaskStep = "file_upload"
	CodeAssistTaskStepBuildAgent           CodeAssistTaskStep = "build_agent"
	CodeAssistTaskStepRecoverTask          CodeAssistTaskStep = "recover_task"
	CodeAssistTaskStepAgentRun             CodeAssistTaskStep = "agent_run"
)

type CodeAssistTaskStepStatus string

const (
	CodeAssistTaskStepStatusSuccess CodeAssistTaskStepStatus = "success"
	CodeAssistTaskStepStatusFailed  CodeAssistTaskStepStatus = "failed"
)

type CodeAssistTaskStepTag struct {
	AgentName string                   `key:"agent_name"`
	Stage     CodeAssistTaskStep       `key:"stage"`
	Status    CodeAssistTaskStepStatus `key:"status"`
}

type CodeAssistTaskQueryTag struct {
	AgentName string `key:"agent_name"`
	TableName string `key:"table_name"`
}

type AgentToolCallStatus string

const (
	AgentToolCallSuccess AgentToolCallStatus = "success"
	AgentToolCallErr     AgentToolCallStatus = "tool_call_err" // 工程方面的报错
	AgentToolRunErr      AgentToolCallStatus = "tool_run_err"  // 工具执行的报错
)

type AgentToolCallTag struct {
	AgentName string              `key:"agent_name"`
	ToolName  string              `key:"tool_name"`
	Status    AgentToolCallStatus `key:"status"`
}

type CodeAssistActorStatus string

const (
	CodeAssistActorStatusSuccess CodeAssistActorStatus = "success"
	CodeAssistActorStatusFailed  CodeAssistActorStatus = "failed"
)

type CodeAssistAgentActorTag struct {
	AgentName string                `key:"agent_name"`
	Actor     string                `key:"actor"`
	Status    CodeAssistActorStatus `key:"status"`
}

type CodeAssistAgentActorRoundsTag struct {
	AgentName string `key:"agent_name"`
	Actor     string `key:"actor"`
}

type CodeAssistAgentStepTag struct {
	AgentName string                `key:"agent_name"`
	Actor     string                `key:"actor"`
	Status    CodeAssistActorStatus `key:"status"`
}

type CodeAssistAgentLLMTag struct {
	AgentName string `key:"agent_name"`
	Model     string `key:"model"`
}

type CodeAssistAgentLLMThroughputTag struct {
	AgentName   string                `key:"agent_name"`
	Model       string                `key:"model"`
	Status      CodeAssistActorStatus `key:"status"`
	ErrorReason string                `key:"error_reason"`
}

type CodeAssistAgentLLMParseErrorTag struct {
	AgentName   string `key:"agent_name"`
	ErrorReason string `key:"error_reason"`
}

type CodeAssistAgentSandboxAllocationTag struct {
	UserId int64 `key:"user_id"`
}

type CodeAssistAgentTaskTag struct {
	AgentName string `key:"agent_name"`
}

type CodeAssistAgentSandboxReadyRetryTag struct {
	AgentName string `key:"agent_name"`
	ToolName  string `key:"tool_name"`
}

type CodeAssistAgentTrimFailedTag struct {
	AgentName string `key:"agent_name"`
	Actor     string `key:"actor"`
}

// CodeAssistMetricSet is metrics for doubao code assist.
type CodeAssistMetricSet struct {
	// Chat metrics
	// -- latency
	ChatFirstTokenLatency  gmetrics.TimerVec[CodeAssistChatTag]           `name:"chat_first_token_latency"`  // 从提问到模型吐出第一个字的延迟时间
	ChatSecondTokenLatency gmetrics.TimerVec[CodeAssistChatTag]           `name:"chat_second_token_latency"` // 从提问到模型吐出第二个字的延迟时间
	ChatLastTokenLatency   gmetrics.TimerVec[CodeAssistChatTag]           `name:"chat_last_token_latency"`   // 从模型吐出第一个字到最后一个字的延迟时间
	ChatTotalTokenLatency  gmetrics.TimerVec[CodeAssistChatTag]           `name:"chat_total_token_latency"`  // 从提问到模型吐出最后一个字的延迟时间
	ChatAvgTokenLatency    gmetrics.TimerVec[CodeAssistChatTag]           `name:"chat_avg_token_latency"`    // 模型吐出每个字（chunk）的平均延迟时间
	ChatStepLatency        gmetrics.TimerVec[CodeAssistChatStepTag]       `name:"chat_step_latency"`         // 对话分step的延迟耗时
	ResolverLatency        gmetrics.TimerVec[CodeAssistResolverTag]       `name:"resolver_latency"`          // 不同的resolver的耗时
	ChatPipelineLatency    gmetrics.TimerVec[CodeAssistChatPipelineTag]   `name:"chat_pipeline_latency"`     // 不同chat pipeline的延迟耗时
	ResolveContextLatency  gmetrics.TimerVec[CodeAssistResolveContextTag] `name:"resolve_context_latency"`   // resolveContext延迟耗时

	// -- throughput
	ChatThroughput           gmetrics.DeltaCounterVec[CodeAssistChatTag]           `name:"chat_throughput"`            // 问答的throughput
	ChatStepThroughput       gmetrics.DeltaCounterVec[CodeAssistChatStepTag]       `name:"chat_step_throughput"`       // 问答分step的throughput
	ResolverThroughput       gmetrics.DeltaCounterVec[CodeAssistResolverTag]       `name:"resolver_throughput"`        // resolver的throughput
	ResolveContextThroughput gmetrics.DeltaCounterVec[CodeAssistResolveContextTag] `name:"resolve_context_throughput"` // resolveContext的throughput
	ChatPipelineThroughput   gmetrics.DeltaCounterVec[CodeAssistChatPipelineTag]   `name:"chat_pipeline_throughput"`   // 不同chat pipeline的throughput

	RecallThroughput                gmetrics.DeltaCounterVec[CodeAssistRecallTag]            `name:"recall_throughput"`                  // resolveContext空召率throughput
	ChatModelTokens                 gmetrics.DeltaCounterVec[CodeAssistChatModelTokensTag]   `name:"chat_model_tokens"`                  // chat model tokens 统计
	RateLimitThroughput             gmetrics.DeltaCounterVec[CodeAssistRateLimitTag]         `name:"rate_limit_throughput"`              // rate limit 统计
	ChatEmptyResponseThroughput     gmetrics.DeltaCounterVec[ChatTokensTag]                  `name:"chat_empty_response_throughput"`     // chat empty response throughput
	ChatArtifactsResponseThroughput gmetrics.DeltaCounterVec[CodeAssistArtifactsResponseTag] `name:"chat_artifacts_response_throughput"` // chat artifacts response throughput
	ChatArtifactsThroughput         gmetrics.DeltaCounterVec[CodeAssistArtifactsTag]         `name:"chat_artifacts_throughput"`          // chat artifacts throughput
	ChatArtifactsPartThroughput     gmetrics.DeltaCounterVec[CodeAssistArtifactsPartTag]     `name:"chat_artifacts_part_throughput"`     // chat artifacts part throughput

	ChatInputTokens          gmetrics.TimerVec[ChatTokensTag] `name:"chat_input_tokens"`          // chat input tokens 统计
	ChatOutputTokens         gmetrics.TimerVec[ChatTokensTag] `name:"chat_output_tokens"`         // chat output tokens 统计
	ChatChunkIntervalLatency gmetrics.TimerVec[ChatTokensTag] `name:"chat_chunk_interval_tokens"` // chat chunk interval 延时

	// Context metrics
	// Context latency & throughput
	ContextIndexTotalLatency         gmetrics.TimerVec[CodeAssistContextIndexTag]        `name:"context_index_total_latency"`          // 上下文索引的整体耗时：从接口请求进来到索引解析完成总耗时
	ContextIndexThroughput           gmetrics.DeltaCounterVec[CodeAssistContextIndexTag] `name:"context_index_throughput"`             // 上下文索引的吞吐
	ContextIndexError                gmetrics.DeltaCounterVec[CodeAssistContextIndexTag] `name:"context_index_error"`                  // 上下文索引错误
	ContextTaskMQLatency             gmetrics.TimerVec[ContextTaskMQLatencyTag]          `name:"context_task_mq_latency"`              // 上下文解析任务mq延迟耗时：从推进MQ到消费出来的耗时
	ContextIndexAnalysisTotalLatency gmetrics.TimerVec[CodeAssistContextIndexTag]        `name:"context_index_analysis_total_latency"` // 上下文索引的解析总耗时：从mq出队列到索引解析完成总耗时
	ContextIndexReadyLatency         gmetrics.TimerVec[ContextIndexReadyLatencyTag]      `name:"context_index_ready_latency"`          // 上下文索引可问答延时：从接口请求进来到用户可问答总耗时

	// Context step latency & throughput
	ContextIndexStepThroughput gmetrics.DeltaCounterVec[CodeAssistContextIndexStepTag] `name:"context_step_throughput"` // 上下文索引解析阶段索引吞吐
	ContextIndexStepLatency    gmetrics.TimerVec[CodeAssistContextIndexStepTag]        `name:"context_step_latency"`    // 上下文索引解析阶段索引延迟
	ContextIndexStepError      gmetrics.DeltaCounterVec[CodeAssistContextIndexStepTag] `name:"context_step_error"`      // 上上下索引解析文阶段索引错误

	// Repo sniff result
	SniffRepositoryLink gmetrics.DeltaCounterVec[SniffRepositoryLinkTag] `name:"sniff_repository_link_throughput"` // 仓库link探测吞吐

	// File kb index latency & throughput
	FileKBIndexLatency    gmetrics.TimerVec[CodeAssistFileKBIndexTag]        `name:"file_kb_index_latency"`    // 知识库文件索引构建耗时
	FileKBIndexThroughput gmetrics.DeltaCounterVec[CodeAssistFileKBIndexTag] `name:"file_kb_index_throughput"` // 知识库文件索引吞吐

	// KB file meta fetch latency & throughput
	KBFileMetaFetchLatency    gmetrics.TimerVec[CodeAssistKBFileMetaFetchTag]        `name:"kb_file_meta_fetch_latency"`    // 知识库文件元数据获取耗时
	KBFileMetaFetchThroughput gmetrics.DeltaCounterVec[CodeAssistKBFileMetaFetchTag] `name:"kb_file_meta_fetch_throughput"` // 知识库文件元数据获取吞吐

	// DGit metrics
	DGitThroughput gmetrics.DeltaCounterVec[DGitTag] `name:"dgit_throughput"` // DGit 接口吞吐
	DGitLatency    gmetrics.TimerVec[DGitTag]        `name:"dgit_latency"`    // DGit 接口延迟
	DGitError      gmetrics.DeltaCounterVec[DGitTag] `name:"dgit_error"`      // DGit 接口错误

	// Sandbox metrics
	SandboxCodeExecutableThroughput              gmetrics.DeltaCounterVec[CodeAssistSandboxCodeExecutableTag]              `name:"sandbox_code_executable_throughput"`           // 沙盒代码可执行检测吞吐
	SandboxCodeFileNotExecutableThroughput       gmetrics.DeltaCounterVec[CodeAssistSandboxCodeFileNotExecutableTag]       `name:"sandbox_code_file_not_executable_throughput"`  // 沙盒代码代码文件不可执行结果分布
	SandboxCodeExecutableLatency                 gmetrics.TimerVec[CodeAssistSandboxCodeExecutableTag]                     `name:"sandbox_code_executable_latency"`              // 沙盒代码可执行检测时延
	SandboxCodeRunThroughput                     gmetrics.DeltaCounterVec[CodeAssistSandboxCodeRunTag]                     `name:"sandbox_code_run_throughput"`                  // 沙盒代码代码运行吞吐
	SandboxInstanceAllocationThroughput          gmetrics.StoreVec[CodeAssistSandboxTag]                                   `name:"sandbox_instance_allocation_count_throughput"` // 沙盒分配数量分布
	SandboxInstanceAllocationLatency             gmetrics.TimerVec[CodeAssistSandboxTag]                                   `name:"sandbox_instance_allocation_latency"`          // 沙盒实例分配耗时
	SandboxInstanceAllocationOverLimitThroughput gmetrics.DeltaCounterVec[CodeAssistSandboxInstanceAllocationOverLimitTag] `name:"sandbox_instance_allocation_over_limit_throughput"`
	SandboxCodeExecuteLatency                    gmetrics.TimerVec[CodeAssistSandboxTag]                                   `name:"sandbox_code_execute_latency"` // 沙盒运行/编译耗时

	// ImageReview metrics
	ImageReviewResultThroughput      gmetrics.DeltaCounterVec[CodeAssistImageReviewResultTag]        `name:"image_review_result_throughput"`
	ImageReviewUnpassErrorThroughput gmetrics.DeltaCounterVec[CodeAssistImageReviewUnpassErrorTag]   `name:"image_review_unpass_error_throughput"`
	ImageReviewHitCacheThroughput    gmetrics.DeltaCounterVec[CodeAssistImageReviewHitCacheTag]      `name:"image_review_hit_cache_throughput"`
	ImageReviewDownloadImageLatency  gmetrics.TimerVec[CodeAssistImageReviewDownloadImageLatencyTag] `name:"image_review_download_image_latency"`
	ImageReviewResultLatency         gmetrics.TimerVec[CodeAssistImageReviewResultLatencyTag]        `name:"image_review_result_latency"`

	// Search metrics
	SearchGenerationImageThroughput gmetrics.DeltaCounterVec[CodeAssistSearchGenerationImageTag] `name:"search_generation_image_throughput"`
	SearchGenerationImageLatency    gmetrics.TimerVec[CodeAssistSearchGenerationImageTag]        `name:"search_generation_image_latency"`
	SearchGenerationImageError      gmetrics.DeltaCounterVec[CodeAssistSearchGenerationImageTag] `name:"search_generation_image_error"`

	// Agent metrics
	TaskBeginThroughput              gmetrics.DeltaCounterVec[CodeAssistTaskBeginTag]          `name:"task_begin_throughput"`               // agent 任务发起吞吐
	TaskCompleteThroughput           gmetrics.DeltaCounterVec[CodeAssistTaskCompleteTag]       `name:"task_complete_throughput"`            // agent 任务结束吞吐
	TaskFirstTokenLatency            gmetrics.TimerVec[CodeAssistTaskFirstTokenTag]            `name:"task_first_token_latency"`            // 首token延迟
	TaskLastTokenLatency             gmetrics.TimerVec[CodeAssistTaskLastTokenTag]             `name:"task_last_token_latency"`             // 尾token延迟
	TaskStepLatency                  gmetrics.TimerVec[CodeAssistTaskStepTag]                  `name:"task_step_latency"`                   // 任务分步骤延迟
	TaskStepThroughput               gmetrics.DeltaCounterVec[CodeAssistTaskStepTag]           `name:"task_step_throughput"`                // 任务分步骤吞吐
	TaskQueryThroughput              gmetrics.DeltaCounterVec[CodeAssistTaskQueryTag]          `name:"task_query_throughput"`               // 任务查询数据库吞吐
	AgentToolCallLatency             gmetrics.TimerVec[AgentToolCallTag]                       `name:"agent_tool_call_latency"`             // 工具调用延迟
	AgentToolCallThroughput          gmetrics.DeltaCounterVec[AgentToolCallTag]                `name:"agent_tool_call_throughput"`          // 工具调用吞吐
	AgentActorThroughput             gmetrics.DeltaCounterVec[CodeAssistAgentActorTag]         `name:"agent_actor_throughput"`              // agent actor 吞吐
	AgentActorLatency                gmetrics.TimerVec[CodeAssistAgentActorTag]                `name:"agent_actor_latency"`                 // agent actor 执行耗时
	AgentActorRounds                 gmetrics.TimerVec[CodeAssistAgentActorRoundsTag]          `name:"agent_actor_rounds"`                  // agent actor 执行轮数
	AgentStepThroughput              gmetrics.DeltaCounterVec[CodeAssistAgentStepTag]          `name:"agent_step_throughput"`               // agent step 吞吐
	AgentStepLatency                 gmetrics.TimerVec[CodeAssistAgentStepTag]                 `name:"agent_step_latency"`                  // agent step 执行耗时
	AgentLLMFirstTokenLatency        gmetrics.TimerVec[CodeAssistAgentLLMTag]                  `name:"agent_llm_first_token_latency"`       // agent llm 首token延迟
	AgentLLMTotalTokenLatency        gmetrics.TimerVec[CodeAssistAgentLLMTag]                  `name:"agent_llm_total_token_latency"`       // agent llm 总token延迟
	AgentLLMAvgTokenLatency          gmetrics.TimerVec[CodeAssistAgentLLMTag]                  `name:"agent_llm_avg_token_latency"`         // agent llm 平均token延迟
	AgentLLMChunkIntervalLatency     gmetrics.TimerVec[CodeAssistAgentLLMTag]                  `name:"agent_llm_chunk_interval_latency"`    // agent llm chunk间隔耗时
	AgentLLMGenDeltaEventLatency     gmetrics.TimerVec[CodeAssistAgentLLMTag]                  `name:"agent_llm_gen_delta_event_latency"`   // agent llm 生成delta event延迟
	AgentLLMThroughput               gmetrics.DeltaCounterVec[CodeAssistAgentLLMThroughputTag] `name:"agent_llm_throughput"`                // agent llm 调用吞吐&错误
	AgentLLMParseErrorThroughput     gmetrics.DeltaCounterVec[CodeAssistAgentLLMParseErrorTag] `name:"agent_llm_parse_error_throughput"`    // agent llm 输出解析错误吞吐
	AgentLLMInputTokens              gmetrics.TimerVec[CodeAssistAgentLLMTag]                  `name:"agent_llm_input_tokens"`              // agent llm 输入token统计
	AgentLLMOutputTokens             gmetrics.TimerVec[CodeAssistAgentLLMTag]                  `name:"agent_llm_output_tokens"`             // agent llm 输出token统计
	CodeAssistAgentSandboxAllocation gmetrics.StoreVec[CodeAssistAgentSandboxAllocationTag]    `name:"codeassist_agent_sandbox_allocation"` // agent sandbox 分配数量分布
	AgentTaskTotalTokens             gmetrics.TimerVec[CodeAssistAgentTaskTag]                 `name:"agent_task_total_tokens"`             // agent 任务总token消耗
	AgentTaskTotalRounds             gmetrics.TimerVec[CodeAssistAgentTaskTag]                 `name:"agent_task_total_rounds"`             // agent 任务执行总轮数
	AgentSandboxReadyRetryLatency    gmetrics.TimerVec[CodeAssistAgentSandboxReadyRetryTag]    `name:"agent_sandbox_ready_retry_latency"`   // agent 沙盒准备重试延迟
	AgentTrimFailedThroughput        gmetrics.DeltaCounterVec[CodeAssistAgentTrimFailedTag]    `name:"agent_trim_failed_throughput"`        // agent 消息裁剪失败吞吐
}

func CodeAssistGroupFileNum(fileNum int64) int64 {
	return ((fileNum / 50) + 1) * 50 // group by 50
}

func CodeAssistGroupTotalSize(size int64) int64 {
	return 50 * ((size / 1024 / 1024 / 50) + 1) // group by 50MB
}

func (d *CodeAssistMetricSet) ReportResolverMetrics() func(resolverID string, isError bool) {
	start := time.Now()
	return func(resolverID string, isError bool) {
		tag := &CodeAssistResolverTag{
			ResolverID: resolverID,
			IsError:    strconv.FormatBool(isError),
		}

		// latency
		_ = d.ResolverLatency.WithTags(tag).Observe(float64(time.Since(start).Milliseconds()))

		// throughput
		_ = d.ResolverThroughput.WithTags(tag).Add(1)
	}
}

func (d *CodeAssistMetricSet) ReportChatStepMetrics() func(appName string, chatSource string, step CodeAssistChatStep, isLogin bool, isError bool) {
	start := time.Now()
	return func(appName string, chatSource string, step CodeAssistChatStep, isLogin bool, isError bool) {
		tag := &CodeAssistChatStepTag{
			AppName:    appName,
			ChatSource: chatSource,
			Step:       step,
			IsLogin:    strconv.FormatBool(isLogin),
			IsError:    strconv.FormatBool(isError),
		}
		// latency
		_ = d.ChatStepLatency.WithTags(tag).Observe(float64(time.Since(start).Milliseconds()))

		// throughput
		_ = d.ChatStepThroughput.WithTags(tag).Add(1)
	}
}

func (d *CodeAssistMetricSet) ReportResolveContextMetrics() func(contextType string, isError bool) {
	start := time.Now()
	return func(contextType string, isError bool) {
		tag := &CodeAssistResolveContextTag{
			ContextType: contextType,
			IsError:     strconv.FormatBool(isError),
		}
		// latency
		_ = d.ResolveContextLatency.WithTags(tag).Observe(float64(time.Since(start).Milliseconds()))

		// throughput
		_ = d.ResolveContextThroughput.WithTags(tag).Add(1)
	}
}

func (d *CodeAssistMetricSet) ReportContextIndexStepMetrics() func(resourceType string, step CodeAssistContextStep, isError bool) {
	start := time.Now()
	return func(resourceType string, step CodeAssistContextStep, isError bool) {
		tag := &CodeAssistContextIndexStepTag{
			ResourceType: resourceType,
			Step:         step,
		}
		// latency
		_ = d.ContextIndexStepLatency.WithTags(tag).Observe(float64(time.Since(start).Milliseconds()))

		// throughput
		_ = d.ContextIndexStepThroughput.WithTags(tag).Add(1)

		// error
		if isError {
			_ = d.ContextIndexStepError.WithTags(tag).Add(1)
		}
	}
}

func (d *CodeAssistMetricSet) ReportContextIndexReadyLatencyMetrics(resourceType string, entryTime time.Time) {
	tag := &ContextIndexReadyLatencyTag{
		ResourceType: resourceType,
	}

	_ = d.ContextIndexReadyLatency.WithTags(tag).Observe(float64(time.Since(entryTime).Milliseconds()))
}

func (d *CodeAssistMetricSet) ReportTaskStepMetrics() func(agentName string, step CodeAssistTaskStep, isError bool) {
	start := time.Now()
	return func(agentName string, step CodeAssistTaskStep, isError bool) {
		tag := &CodeAssistTaskStepTag{
			AgentName: agentName,
			Stage:     step,
			Status:    lo.Ternary[CodeAssistTaskStepStatus](isError, CodeAssistTaskStepStatusFailed, CodeAssistTaskStepStatusSuccess),
		}
		// latency
		_ = d.TaskStepLatency.WithTags(tag).Observe(float64(time.Since(start).Milliseconds()))

		// throughput
		_ = d.TaskStepThroughput.WithTags(tag).Add(1)
	}
}

// ReportAgentToolCallMetrics 上报Agent Tool Call指标
func (d *CodeAssistMetricSet) ReportAgentToolCallMetrics() func(agentName string, toolName string, status AgentToolCallStatus) {
	start := time.Now()
	return func(agentName string, toolName string, status AgentToolCallStatus) {
		tag := &AgentToolCallTag{
			AgentName: agentName,
			ToolName:  toolName,
			Status:    status,
		}
		// latency
		_ = d.AgentToolCallLatency.WithTags(tag).Observe(float64(time.Since(start).Milliseconds()))

		// throughput
		_ = d.AgentToolCallThroughput.WithTags(tag).Add(1)
	}
}

// ReportAgentActorMetrics 上报Agent Actor指标
func (d *CodeAssistMetricSet) ReportAgentActorMetrics() func(agentName string, actor string, isError bool, rounds int, actorStartTime time.Time) {
	return func(agentName string, actor string, isError bool, rounds int, actorStartTime time.Time) {
		status := lo.Ternary(isError, CodeAssistActorStatusFailed, CodeAssistActorStatusSuccess)
		// Actor latency and throughput
		actorTag := &CodeAssistAgentActorTag{
			AgentName: agentName,
			Actor:     actor,
			Status:    status,
		}
		_ = d.AgentActorLatency.WithTags(actorTag).Observe(float64(time.Since(actorStartTime).Milliseconds()))
		_ = d.AgentActorThroughput.WithTags(actorTag).Add(1)

		// Actor rounds
		roundsTag := &CodeAssistAgentActorRoundsTag{
			AgentName: agentName,
			Actor:     actor,
		}
		_ = d.AgentActorRounds.WithTags(roundsTag).Observe(float64(rounds))
	}
}

// ReportAgentStepMetrics 上报Agent Step指标
func (d *CodeAssistMetricSet) ReportAgentStepMetrics() func(agentName string, actor string, isError bool) {
	start := time.Now()
	return func(agentName string, actor string, isError bool) {
		status := lo.Ternary(isError, CodeAssistActorStatusFailed, CodeAssistActorStatusSuccess)
		tag := &CodeAssistAgentStepTag{
			AgentName: agentName,
			Actor:     actor,
			Status:    status,
		}

		// latency
		_ = d.AgentStepLatency.WithTags(tag).Observe(float64(time.Since(start).Milliseconds()))

		// throughput
		_ = d.AgentStepThroughput.WithTags(tag).Add(1)
	}
}

// ReportAgentLLMFirstTokenMetrics 上报Agent LLM首Token指标
func (d *CodeAssistMetricSet) ReportAgentLLMFirstTokenMetrics() func(agentName string, modelName string) {
	start := time.Now()
	return func(agentName string, modelName string) {
		tag := &CodeAssistAgentLLMTag{
			AgentName: agentName,
			Model:     modelName,
		}
		_ = d.AgentLLMFirstTokenLatency.WithTags(tag).Observe(float64(time.Since(start).Milliseconds()))
	}
}

// ReportAgentLLMTotalTokenMetrics 上报Agent LLM总Token指标
func (d *CodeAssistMetricSet) ReportAgentLLMTotalTokenMetrics() func(agentName string, modelName string) {
	start := time.Now()
	return func(agentName string, modelName string) {
		tag := &CodeAssistAgentLLMTag{
			AgentName: agentName,
			Model:     modelName,
		}
		_ = d.AgentLLMTotalTokenLatency.WithTags(tag).Observe(float64(time.Since(start).Milliseconds()))
	}
}

// ReportAgentLLMAvgTokenLatencyMetrics 上报Agent LLM Chunk间隔指标
func (d *CodeAssistMetricSet) ReportAgentLLMAvgTokenLatencyMetrics() func(agentName string, modelName string, chunkCount int) {
	start := time.Now()
	return func(agentName string, modelName string, chunkCount int) {
		tag := &CodeAssistAgentLLMTag{
			AgentName: agentName,
			Model:     modelName,
		}
		avgTokenLatency := time.Since(start).Milliseconds() / int64(chunkCount)
		_ = d.AgentLLMAvgTokenLatency.WithTags(tag).Observe(float64(avgTokenLatency))
	}
}

// ReportAgentLLMChunkIntervalMetrics 上报Agent LLM Chunk间隔指标
func (d *CodeAssistMetricSet) ReportAgentLLMChunkIntervalMetrics(agentName string, modelName string, intervalMs float64) {
	tag := &CodeAssistAgentLLMTag{
		AgentName: agentName,
		Model:     modelName,
	}
	_ = d.AgentLLMChunkIntervalLatency.WithTags(tag).Observe(intervalMs)
}

// ReportAgentLLMGenDeltaEventLatencyMetrics 上报生成DeltaEvent间隔指标
func (d *CodeAssistMetricSet) ReportAgentLLMGenDeltaEventLatencyMetrics() func(agentName string, modelName string) {
	start := time.Now()
	return func(agentName string, modelName string) {
		tag := &CodeAssistAgentLLMTag{
			AgentName: agentName,
			Model:     modelName,
		}
		genDeltaEventLatency := time.Since(start).Milliseconds()
		_ = d.AgentLLMGenDeltaEventLatency.WithTags(tag).Observe(float64(genDeltaEventLatency))
	}
}

// ReportAgentLLMThroughputMetrics 上报Agent LLM吞吐量指标
func (d *CodeAssistMetricSet) ReportAgentLLMThroughputMetrics(agentName string, modelName string, isError bool, err error) {
	status := lo.Ternary(isError, CodeAssistActorStatusFailed, CodeAssistActorStatusSuccess)
	errorReason := ""
	if err != nil {
		errorReason = "api_error"
		if strings.Contains(err.Error(), "timeout") {
			errorReason = "timeout"
		} else if strings.Contains(err.Error(), "rate") {
			errorReason = "rate_limit"
		} else if strings.Contains(err.Error(), "token") {
			errorReason = "token_limit"
		}
	}

	tag := &CodeAssistAgentLLMThroughputTag{
		AgentName:   agentName,
		Model:       modelName,
		Status:      status,
		ErrorReason: errorReason,
	}
	_ = d.AgentLLMThroughput.WithTags(tag).Add(1)
}

// ReportAgentLLMParseErrorMetrics 上报Agent LLM解析错误指标
func (d *CodeAssistMetricSet) ReportAgentLLMParseErrorMetrics(agentName string, errorReason string) {
	tag := &CodeAssistAgentLLMParseErrorTag{
		AgentName:   agentName,
		ErrorReason: errorReason,
	}
	_ = d.AgentLLMParseErrorThroughput.WithTags(tag).Add(1)
}

// ReportAgentLLMTokensMetrics 上报Agent LLM Token统计指标
func (d *CodeAssistMetricSet) ReportAgentLLMTokensMetrics(agentName string, modelName string, inputTokens int, outputTokens int) {
	tag := &CodeAssistAgentLLMTag{
		AgentName: agentName,
		Model:     modelName,
	}
	_ = d.AgentLLMInputTokens.WithTags(tag).Observe(float64(inputTokens))
	_ = d.AgentLLMOutputTokens.WithTags(tag).Observe(float64(outputTokens))
}

// ReportAgentSandboxReadyRetryMetrics 上报Agent沙盒准备重试指标
func (d *CodeAssistMetricSet) ReportAgentSandboxReadyRetryMetrics() func(agentName string, toolName string) {
	start := time.Now()
	return func(agentName string, toolName string) {
		tag := &CodeAssistAgentSandboxReadyRetryTag{
			AgentName: agentName,
			ToolName:  toolName,
		}
		// latency - 记录从开始重试到重试结束的总时间
		_ = d.AgentSandboxReadyRetryLatency.WithTags(tag).Observe(float64(time.Since(start).Milliseconds()))
	}
}

// ReportAgentTrimFailedMetrics 上报Agent消息裁剪失败指标
func (d *CodeAssistMetricSet) ReportAgentTrimFailedMetrics(agentName string, actor string) {
	tag := &CodeAssistAgentTrimFailedTag{
		AgentName: agentName,
		Actor:     actor,
	}
	_ = d.AgentTrimFailedThroughput.WithTags(tag).Add(1)
}
