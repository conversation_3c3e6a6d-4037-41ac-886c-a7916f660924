package util

import "strings"

func RemoveLastLine(s string) string {
	// 按行分割字符串
	lines := strings.Split(s, "\n")
	// 如果字符串为空或只有一行，则返回空字符串
	if len(lines) <= 1 {
		return ""
	}
	// 去掉最后一行
	lines = lines[:len(lines)-1]
	// 将剩余的行重新组合成一个字符串
	return strings.Join(lines, "\n")
}

// SplitFirstLine 将字符串按第一个换行符分割成两部分
func SplitFirstLine(s string) (string, string) {
	// 查找第一个换行符的位置
	index := strings.Index(s, "\n")
	if index == -1 {
		// 如果没有换行符，返回整个字符串作为第一行，并且剩余行为空
		return s, ""
	}
	// 返回第一行及剩余行
	return s[:index], s[index+1:]
}

// SplitLinesKeepEnds 将字符串按行分割，同时保留行尾的换行符
func SplitLinesKeepEnds(s string) []string {
	var lines []string
	var lineStart int
	for i := 0; i < len(s); i++ {
		if s[i] == '\n' {
			lines = append(lines, s[lineStart:i+1])
			lineStart = i + 1
		}
	}
	// 处理最后一行（如果没有以换行符结尾）
	if lineStart < len(s) {
		lines = append(lines, s[lineStart:])
	}
	return lines
}

// GetLastNUnicodeChars 模仿python字符串按字符切片的逻辑 —— 超出数组长度时，返回整个数组
func GetLastNUnicodeChars(s string, n int) string {
	if len(s) <= n {
		return s
	}
	tmp := []rune(s)
	var result []rune
	if len(tmp) < n {
		result = tmp
	} else {
		result = tmp[len(tmp)-n:]
	}
	return string(result)
}

// GetFirstNUnicodeChars 模仿python字符串按字符切片的逻辑 —— 超出数组长度时，返回整个数组
func GetFirstNUnicodeChars(s string, n int) string {
	if len(s) <= n {
		return s
	}
	tmp := []rune(s)
	var result []rune
	if len(tmp) < n {
		result = tmp
	} else {
		result = tmp[:n]
	}
	return string(result)
}
