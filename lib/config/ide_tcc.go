package config

import (
	"time"

	libtcc "code.byted.org/devgpt/kiwis/lib/tcc"
)

type IDETCCConfig struct {
	// Config for sensitive content filter.
	AntiDirtConfig *libtcc.GenericConfig[AntidirtConfig] `tcc:"key:antidirt_config;format:yaml;space:default"`
	// Config for API CORS.
	CORSConfig *libtcc.GenericConfig[CORSConfig] `tcc:"key:cors_config;format:yaml;space:default"`
	// Config for LLM auth and secrets.
	ModelAuthConfig *libtcc.GenericConfig[ModelDispatchConfig] `tcc:"key:model_auth_config;format:yaml;space:default"`
	// Codebase service account token.
	CodebaseServiceAccountSecret *libtcc.GenericConfig[CodebaseServiceJWTConfig] `tcc:"key:codebase_service_account_secret;format:string;space:default"`
	// Config for user identity authentication.
	AuthConfig *libtcc.GenericConfig[AuthTCCConfig] `tcc:"key:auth_config;format:yaml;space:default"`
	// Config for app register.
	AppRegisterConfig  *libtcc.GenericConfig[IDEAppRegisterConfig]  `tcc:"key:app_register_config;format:yaml;space:default"`
	ChatLLMRetryConfig *libtcc.GenericConfig[IDEChatLLMRetryConfig] `tcc:"key:chat_llm_retry;format:json;space:default"`
	// Config for model router
	ModelRouterConfig *libtcc.GenericConfig[ModelRouterConfig] `tcc:"key:model_router_config;format:yaml;space:default"`
	// Config for model supplemental
	ModelSupplementalConfig *libtcc.GenericConfig[ModelSupplementalConfig] `tcc:"key:model_supplemental_config;format:yaml;space:default"`

	// Config for feature.
	FeatureConfig *libtcc.GenericConfig[IDEFeatureConfig] `tcc:"key:feature_config;format:yaml;space:default"`
	//Config for provider.
	ProvidersModelConfig *libtcc.GenericConfig[ProvidersModelConfig] `tcc:"key:providers_model_config;format:yaml;space:default"`

	// 代码补全：发给插件的全局默认配置，按 version code 和 channel 分割
	AIHubPluginConfigDefault *libtcc.GenericConfig[DefaultPluginConfig] `tcc:"key:ai_hub_plugin_config_default;format:json;space:default"`
	// 代码补全：按模型、version code、channel 划分的插件侧配置，相比全局默认配置，此处记载的是已有字段的修改部分
	AIHubPluginConfig *libtcc.GenericConfig[ModelPluginConfig] `tcc:"key:ai_hub_plugin_config;format:json;space:default"`
	// 代码补全：全局默认前后处理参数
	AIHubProcessConfigDefault *libtcc.GenericConfig[ProcessParamDefault] `tcc:"key:ai_hub_process_param_default;format:json;space:default"`
	// 代码补全：按模型名划分的模型服务调用参数、前后处理参数，其中前后处理参数相比全局默认的，记录的是已有字段的修改部分
	AIHubModelConfig *libtcc.GenericConfig[CompletionModelConfig] `tcc:"key:ai_hub_model_config;format:json;space:default"`
	// 代码补全：多点编辑场景的模型配置信息 按模型名划分的模型服务调用参数、前后处理参数，其中前后处理参数相比全局默认的，记录的是已有字段的修改部分
	AIHubMultiPointModelConfig *libtcc.GenericConfig[MultiPointCompletionModelConfig] `tcc:"key:ai_hub_multi_point_model_config;format:json;space:default"`

	ReportTCCConfig *libtcc.GenericConfig[ReportTCCConfig] `tcc:"key:report_config;format:yaml;space:default"`

	LLMSecurityTCCConfig *libtcc.GenericConfig[LLMSecurityConfig] `tcc:"key:llm_security_config;format:yaml;space:default"`
	// 代码补全：引入流式补全以后，区分channel，version的language-model映射关系表
	CompletionLanguageMap *libtcc.GenericConfig[CompletionLanguageConfig] `tcc:"key:completion_language_map;format:json;space:default"`

	// 动态域名配置
	DomainsTCCConfig *libtcc.GenericConfig[DomainsConfig] `tcc:"key:domains_config;format:yaml;space:default"`

	Knowledgebase *libtcc.GenericConfig[KnowledgebaseTCC] `tcc:"key:knowledgebase;format:yaml;space:default"`
	// 知识库问答相关策略、算法
	CkgAlgorithm *libtcc.GenericConfig[CkgAlgorithmTCCConfig] `tcc:"key:ckg_algorithm;format:yaml;space:default"`

	// Config for user growth activity
	ActivityTCCConfig *libtcc.GenericConfig[ActivityTCCConfig] `tcc:"key:ug_activity_config;format:yaml;space:default"`
	MarscodeTccConfig *libtcc.GenericConfig[MarscodeTccConfig] `tcc:"key:marscode_config;format:json;space:default"`
	SecretsConfig     *libtcc.GenericConfig[IDESecrets]        `tcc:"key:secrets_config;format:yaml;space:default"`
	AgentConfig       *libtcc.GenericConfig[IDEAgentTCCConfig] `tcc:"key:agent_config;format:yaml;space:default"`

	// Config for user growth
	UserGrowthConfig *libtcc.GenericConfig[IDEUserGrowthTCCConfig] `tcc:"key:user_growth_config;format:yaml;space:default"`
	PlaygroundConfig *libtcc.GenericConfig[PlaygroundTCCConfig]    `tcc:"key:playground_config;format:yaml;space:default"`
	TQSConfig        *libtcc.GenericConfig[TQSConfig]              `tcc:"key:tqs_config;format:yaml;space:default"`

	// Config for llm proxy, this is used for AI-Learning currently.
	LLMProxyConfig *libtcc.GenericConfig[IDELLMProxyConfig] `tcc:"key:llm_proxy_config;format:yaml;space:default"`

	// Config for multimodal resource manage
	ResourceManageTCCConfig *libtcc.GenericConfig[ResourceManageTCCConfig] `tcc:"key:resource_manage_config;format:json;space:default"`

	// ModelPromptMapConfig is the config for model prompt.
	ModelPromptMapConfig *libtcc.GenericConfig[ModelPromptMapConfig] `tcc:"key:model_construct_prompt_config;format:json;space:default"`

	// Config for fornax
	IDEFornaxTCCConfig *libtcc.GenericConfig[IDEFornaxTCCConfig] `tcc:"key:ide_fornax_config;format:yaml;space:default"`
	// Config for Country blocklist
	CountryBlocklistTCCConfig *libtcc.GenericConfig[CountryBlocklistTCCConfig] `tcc:"key:country_blocklist_config;format:json;space:default"`

	// Config for AI Limit Queue
	AILimitQueueTCCConfig *libtcc.GenericConfig[AILimitQueueTCCConfig] `tcc:"key:ai_limit_queue_config;format:yaml;space:default"`

	// Config for AI Agent
	LLMRawChatConfig *libtcc.GenericConfig[LLMRawChatConfig] `tcc:"key:llm_raw_chat_config;format:json;space:default"`

	// Secret Key Config
	SecretKeyConfig *libtcc.GenericConfig[SecretKeyConfig] `tcc:"key:secret_key_config;format:json;space:default"`

	// bytecloud iam secret
	ByteCloudIamConfig *libtcc.GenericConfig[ByteCloudIamConfig] `tcc:"key:byte_cloud_iam_config;format:yaml;space:default"`

	// JournalConfig is the config for journal log
	JournalConfig *libtcc.GenericConfig[JournalTCCConfig] `tcc:"key:journal_config;format:json;space:default"`

	EntitlementTaskTCCConfig *libtcc.GenericConfig[EntitlementTaskTCCConfig] `tcc:"key:entitlement_task_config;format:yaml;space:default"`

	// JournalConfig is the config for journal log
	TraeUserGrowthTCCConfig *libtcc.GenericConfig[TraeUserGrowthTCCConfig] `tcc:"key:trae_user_growth_config;format:json;space:default"`

	// Config for Error Code
	ErrorCodeConfig *libtcc.GenericConfig[ErrorCodeConfig] `tcc:"key:error_code_config;format:yaml;space:default"`
}

type IDEAppRegisterConfig struct {
	Apps []*IDEAppConfig `yaml:"Apps"`
}

// GetAppConfig returns the app config by appID and version, if the version is not found, return the default version.
func (i *IDEAppRegisterConfig) GetAppConfig(appID, version string, abConfigs any) (*IDEAppConfig, bool) {
	return nil, false
}

type IDEChatLLMRetryConfig struct {
	ModelRetryEnableMap map[string]bool `json:"ModelRetryEnableMap"`
}

func (i *IDEChatLLMRetryConfig) EnableRetry(model string) bool {
	if i.ModelRetryEnableMap != nil {
		if _, exists := i.ModelRetryEnableMap[model]; exists {
			return i.ModelRetryEnableMap[model]
		} else {
			return false
		}
	} else {
		return false
	}
}

type ModelRouterConfig struct {
	Models []*RouterModel `yaml:"Models"`
}

type RouterModel struct {
	ModelName     string          `yaml:"ModelName"`
	Enable        bool            `yaml:"Enable"`
	AllowedAppIDs []string        `yaml:"AllowedAppIDs"`
	Providers     []ModelProvider `yaml:"Providers"`
}

type ModelProvider struct {
	Name   string `yaml:"Name"`
	Weight int    `yaml:"Weight"`
}

type IDEAppConfig struct {
	ID      string `yaml:"ID"`
	Name    string `yaml:"Name"`
	Version string `yaml:"Version"`

	// ABToken is the AB token for this app used in Libra.
	ABToken string `yaml:"ABToken"`
	// CKGABToken is the AB token for ckg used in Libra.
	CKGABToken string `yaml:"CKGABToken"`
	// BDAppID is the app ID in Bytedance.
	BDAppID string `yaml:"BDAppID"`
	// LLMOpsConfName is the ops config name for this app.
	LLMOpsConfName string `yaml:"LLMOpsConfName"`

	// Overwrite some configs for this request if the expression returns true.
	OverwriteIf []OverwriteIfConfig `yaml:"OverwriteIf"`
	// Config for content filter.
	LLMFilter LLMFilterConfig `yaml:"LLMFilter"`

	// PlanPrompt is the prompt for intent detection.
	PlanPrompt string `yaml:"PlanPrompt"`
	// ContextPrompt is the prompt for context selection.
	ContextPrompt string `yaml:"ContextPrompt"`
	// EntityPrompt is the prompt for entity detect.
	EntityPrompt string `yaml:"EntityPrompt"`
	// NotesPrompt is the prompt for notes generation.
	NotesPrompt IDENotesPromptConfig `yaml:"NotesPrompt"`
	// FastApplyPrompt is the prompt for fast apply.
	FastApplyPrompt FastApplyPromptConfig `yaml:"FastApplyPrompt"`
	// Models is the model config for different use cases, such as chat, intent detection, etc.
	Models IDEModelConfig `yaml:"Models"`
	// Intents includes all the intent definitions.
	Intents                []IDEIntentConfig       `yaml:"Intents"`
	QueryRewritePrompts    []IDEQueryRewriteConfig `yaml:"QueryRewritePrompts"`
	NewContextSelectConfig NewContextSelectConfig  `yaml:"NewContextSelectConfig"`
	// Handlers includes all the intent handlers.
	Handlers []IDEHandlerConfig `yaml:"Handlers"`
	// Resolvers includes all the context resolvers and its prompt template.
	Resolvers []IDEResolverConfig `yaml:"Resolvers"`
	// ClientConfig includes IDE and plugin init AI config, like commands.
	ClientConfig IDEClientConfig `yaml:"ClientConfig"`
	// GenTitlePrompt is the prompt for practice coding conversation title generation.
	GenTitlePrompt       IDEGenTitlePromptConfig  `yaml:"GenTitlePrompt"`
	SuggestedPrompt      IDESuggestedPromptConfig `yaml:"SuggestedPrompt"`
	GenFailureCasePrompt IDEGenTitlePromptConfig  `yaml:"GenFailureCasePrompt"`

	ChatPromptPolicy    string           `yaml:"ChatPromptPolicy"`
	ChatPromptTemplates []PromptTemplate `yaml:"ChatPromptTemplates"`
	// LineDeduplicationPolicy is the config to decide how to deduplicate lines in resolver contexts.
	LineDeduplicationPolicy string `yaml:"LineDeduplicationPolicy"`

	// ModelList is a list of supported models.
	ModelList []ModelInfo `yaml:"ModelList"`

	// EnableIPCountryBlock is whether to enable IP Country block.
	EnableIPCountryBlock bool `yaml:"EnableIPCountryBlock"`
}

type ModelInfo struct {
	// Name is unique ID.
	Name string `yaml:"Name"`
	// ModelName is the model name used in the internal.
	ModelName string `yaml:"ModelName"`
	// DisplayName is shown in the UI.
	DisplayName string `yaml:"DisplayName"`
	// ModelType is the model type used in the request.
	ModelType string `yaml:"ModelType"`
	// WhiteList is the white list for this model.
	WhiteList []string `yaml:"WhiteList"`
	// only config in bytedance
	DepartmentWhiteListForBytedance []string `yaml:"DepartmentWhiteListForBytedance"`
	// IDE版本类型是否可用
	EnabledIDEVersionType []string `yaml:"EnabledIDEVersionType"`
	// Multimodal is whether this model supports multimodal.
	Multimodal         bool   `yaml:"Multimodal"`
	MultimodalDataType string `yaml:"MultimodalDataType"`
	// IsDefault is whether this model is the default model.
	IsDefault bool `yaml:"IsDefault"`
	// HyperParam is the hyper parameters for this model.
	HyperParams ModelDetailConfig `yaml:"HyperParams"`
	// CustomConfig is the custom config for this model.
	CustomConfig map[string]any `yaml:"CustomConfig"`
	// Pipeline is the pipeline for this model.
	Pipeline            PipelineConfig   `yaml:"Pipeline"`
	ChatPromptPolicy    string           `yaml:"ChatPromptPolicy"`
	ChatPromptTemplates []PromptTemplate `yaml:"ChatPromptTemplates"`
	// UnitPipeline is the unit test pipeline for this model.
	UnitPipeline string `yaml:"UnitPipeline"`
	//control modellist api return
	Invisible bool `yaml:"Invisible"`
	// ModelCapability is the model capability.
	// For example, "reasoning_model"，"chat_model"
	ModelCapability    string `yaml:"ModelCapability"`
	AvaibleVersionCode string `yaml:"AvaibleVersionCode"`
	// IsNew is whether this model is new.
	IsNew bool `yaml:"IsNew"`
}

type PromptTemplate struct {
	Label    string `yaml:"Label"`
	Template string `yaml:"Template"`
}

type IDENotesPromptConfig struct {
	// MethodNotesPrompt is the prompt for method notes generation.
	MethodNotesPrompt string `yaml:"MethodNotesPrompt"`
	// ClassNotesPrompt is the prompt for class notes generation.
	ClassNotesPrompt string `yaml:"ClassNotesPrompt"`
	// FileNotesPrompt is the prompt for file notes generation.
	FileNotesPrompt string `yaml:"FileNotesPrompt"`
}

type IDEModelConfig struct {
	ChatModel      ModelDetailConfig `yaml:"ChatModel"`
	PlanModel      ModelDetailConfig `yaml:"PlanModel"`
	ContextModel   ModelDetailConfig `yaml:"ContextModel"`
	KeywordModel   ModelDetailConfig `yaml:"KeywordModel"`
	EmbeddingModel ModelDetailConfig `yaml:"EmbeddingModel"`
	EntityModel    ModelDetailConfig `yaml:"EntityModel"`
	RerankModel    ModelDetailConfig `yaml:"RerankModel"`
	GenTitleModel  ModelDetailConfig `yaml:"GenTitleModel"`
	GenCaseModel   ModelDetailConfig `yaml:"GenCaseModel"`
	SuggestedModel ModelDetailConfig `yaml:"SuggestedModel"`
	FastApplyModel ModelDetailConfig `yaml:"FastApplyModel"`

	DedicatedModels []ModelDetailConfig `yaml:"DedicatedModels"`
	// EnabledModels is the model names which are enabled for this app.
	// if the list is empty, all models will be enabled.
	// if the list is not empty, only the models in the list will be enabled.
	EnabledModels []string `yaml:"EnabledModels"`
}

type ModelDetailConfig struct {
	Name        string  `yaml:"Name"`
	Temperature float32 `yaml:"Temperature"`
	// MaxToken is the max token of model output which will be used in model request.
	// For example: in intent detect, the max token is 10.
	// in chat, the max token is 2000.
	MaxTokens int `yaml:"MaxTokens"`
	// PromptMaxTokens is the max token length for prompt which will be used to crop prompt.
	// seed_moe_stream: 4k
	// gpt-35-turbo-16k: 14k
	// gpt-4-0613: 6k
	PromptMaxTokens int `yaml:"PromptMaxTokens"`
	// The PromptMaxTokens for ckg scenario.
	CKGPromptMaxTokens int     `yaml:"CKGPromptMaxTokens"`
	TopP               float32 `yaml:"TopP"`
	TopK               int     `yaml:"TopK"`
	// Min tokens to generate, 0 for using default value.
	MinNewTokens int `yaml:"MinNewTokens"`
	// Repetition penalty, 0 for using default value.
	RepetitionPenalty float32  `yaml:"RepetitionPenalty"`
	EnabledModels     []string `yaml:"EnabledModels"`
	// chat模型流式输出停止标志字段
	Stop []string `yaml:"Stop"`
	// 排序模型阈值字段
	Threshold *float32 `yaml:"Threshold"`
	// FallbackModels is the model names which will be retried if the first model is failed(currently only rate limit error will be retried).
	// You must ensure the parameters(such as max_tokens, prompt_max_tokens) are compatible for all fallback models,
	// or the retrying will be failed or not work as expected.
	// And the prompts will be constructed base on the main model not the fallback models.
	// Use after reviewed and tested carefully.
	FallbackModels []string `yaml:"FallbackModels"`
	// retry times while request model error before fallback model
	RetryTimesBeforeFallback int `yaml:"RetryTimesBeforeFallback"`
	// psm call by process server
	Psm    string `yaml:"Psm"`
	PsmEnv string `yaml:"PsmEnv"`
	// 控制模型thinking开关和think长度
	Thinking *ThinkingConfig `yaml:"Thinking"`
}

type ThinkingConfig struct {
	Type         string `json:"type" yaml:"Type"`
	BudgetTokens int    `json:"budget_tokens" yaml:"BudgetTokens"`

	IncludeThoughts bool `json:"include_thoughts" yaml:"IncludeThoughts"`
}

type IDEIntentConfig struct {
	// ID is the intent ID which is global for all models, such as "explain_code".
	ID string `yaml:"ID"`
	// Name is the intent name which is unique for each model.
	// such as "代码解释" for skylark-lite-intent, "explain_code" for gpt.
	Name string `yaml:"Name"`
	// Description is the intent description which might different for different model.
	Description string `yaml:"Description"`
	Handler     string `yaml:"Handler"`
}

type IDEQueryRewriteConfig struct {
	ID          string            `yaml:"ID"`
	Prompt      string            `yaml:"Prompt"`
	Model       ModelDetailConfig `yaml:"Model"`
	Description string            `yaml:"Description"`
}

type NewContextSelectResolver struct {
	ResolverID  string `yaml:"resolver_id"`
	Description string `yaml:"description"`
}

type NewContextSelectConfig struct {
	Prompt        string                     `yaml:"Prompt"`
	Model         ModelDetailConfig          `yaml:"Model"`
	Resolvers     []NewContextSelectResolver `yaml:"Resolvers"`
	ParseStrategy string                     `yaml:"ParseStrategy"`
}

type IDEHandlerConfig struct {
	HandlerID string         `yaml:"ID"`
	Pipeline  PipelineConfig `yaml:"Pipeline"`
}

type OverwriteIfConfig struct {
	If        string  `yaml:"If"`
	ModelName *string `yaml:"ModelName"`
	Pipeline  *string `yaml:"Pipeline"`
	Model     *string `yaml:"Model"`
}

type OverwritePromptLabelConfig struct {
	If                          string  `yaml:"If"`
	Label                       *string `yaml:"Label"`
	InlineChatAfterIntentDetect *bool   `yaml:"InlineChatAfterIntentDetect"`
}

type ResolverValidateConfig struct {
	Rule    string `yaml:"Rule"`
	Message string `yaml:"Message"`
}

type PipelineConfig struct {
	BasePrompt             string              `yaml:"BasePrompt"`
	History                string              `yaml:"History"`
	UserInput              string              `yaml:"UserInput"`
	EnabledResolvers       []string            `yaml:"EnabledResolvers"`
	CoreResolvers          CoreResolversConfig `yaml:"CoreResolvers"`
	PromptKey              string              `yaml:"PromptKey"`
	PromptLabel            string              `yaml:"PromptLabel"`
	Locations              []string            `yaml:"Locations"`
	InlineChatResolvers    []string            `yaml:"InlineChatResolvers"`
	SideChatResolvers      []string            `yaml:"SideChatResolvers"`
	UserInputInstructions  string              `yaml:"UserInputInstructions"`
	EnabledServerResolvers []string            `yaml:"EnabledServerResolvers"`
}

type CoreResolversConfig struct {
	InlineChatResolvers []string `yaml:"InlineChatResolvers"`
	SideChatResolvers   []string `yaml:"SideChatResolvers"`
}

type IDEResolverConfig struct {
	// Name is the resolver name.
	Name string `yaml:"Name"`
	// Template is the default prompt template for this resolver.
	Template  string                   `yaml:"Template"`
	Validates []ResolverValidateConfig `yaml:"Validates"`
	// IntentsTemplates is the prompt templates for different intents.
	// If the intent is not found in this list, the default template will be used.
	IntentsTemplates []IntentsTemplate `yaml:"Intents"`
}

type IDEClientConfig struct {
	Commands            []CommandConfig      `yaml:"Commands"`
	HashContexts        []HashContextsConfig `yaml:"HashContexts"`
	AdditionalResolvers []string             `yaml:"AdditionalResolvers"`
}

type CommandConfig struct {
	Command         string   `yaml:"Command"`
	DefaultIntent   string   `yaml:"DefaultIntent"`
	Locations       []string `yaml:"Locations"`
	IsDetectNeed    bool     `yaml:"IsDetectNeed"`
	IsShowInCommand bool     `yaml:"IsShowInCommand"`
	Description     string   `yaml:"Description"`
}

type HashContextsConfig struct {
	HashType            string   `yaml:"HashType"`
	SideChatResolvers   []string `yaml:"SideChatResolvers"`
	InlineChatResolvers []string `yaml:"InlineChatResolvers"`
}

type IntentsTemplate struct {
	// ID is the intent ID, such as "explain_code".
	ID string `yaml:"ID"`
	// Template is the prompt template for this intent.
	Template string `yaml:"Template"`
}

type KnowledgebaseTCC struct {
	RestrictSaveMaxFileCount int `yaml:"RestrictSaveMaxFileCount"`
}

type CkgAlgorithmTCCConfig struct {
	IsRouterOn   bool                         `yaml:"IsRouterOn"`
	IsAdjustOn   bool                         `yaml:"IsAdjustOn"`
	IsFilterOn   bool                         `yaml:"IsFilterOn"`
	RouterThresh CkgAlgorithmRecallTypeThresh `yaml:"RouterThresh"`
	AdjustThresh CkgAlgorithmRecallTypeThresh `yaml:"AdjustThresh"`
	PostThresh   CkgAlgorithmPostThresh       `yaml:"PostThresh"`
	ReturnNum    map[string]int               `yaml:"ReturnNum"`
}

const (
	DefaultChunkingMethodBaseline = "v1"
)

type IDEFeatureConfig struct {
	// Features is the feature config for different features.
	Features map[string]Feature `yaml:"Features"`
}

type Feature struct {
	Enabled       bool           `yaml:"Enabled"`
	DefaultConfig map[string]any `yaml:"DefaultConfig"`
}

type ActivityTCCConfig struct {
	IsStart              bool                    `yaml:"IsStart"`
	IsTestMode           bool                    `yaml:"IsTestMode"`
	UseMockUsername      bool                    `yaml:"UseMockUsername"`
	MockIDStart          int                     `yaml:"MockIDStart"`
	MockIDRange          int                     `yaml:"MockIDRange"`
	OutputMockName       bool                    `yaml:"OutputMockName"`
	ActivityName         string                  `yaml:"ActivityName"`
	ActivityTriggerKey   string                  `yaml:"ActivityTriggerKey"`
	ActivityDrawKey      string                  `yaml:"ActivityDrawKey"`
	LuckyPrizeName       string                  `yaml:"LuckyPrizeName"`
	FourthPrizeName      string                  `yaml:"FourthPrizeName"`
	ThirdPrizeName       string                  `yaml:"ThirdPrizeName"`
	SecondPrizeName      string                  `yaml:"SecondPrizeName"`
	FirstPrizeName       string                  `yaml:"FirstPrizeName"`
	SuperPrizeName       string                  `yaml:"SuperPrizeName"`
	StageNum             int                     `yaml:"StageNum"`
	LuckyRate            []float32               `yaml:"LuckyRate"`
	CumulativeNumByStage ActivityCumulativeValue `yaml:"CumulativeNumByStage"`
	TriggerInvite        string                  `yaml:"TriggerInvite"`
	DrawInvite           string                  `yaml:"DrawInvite"`
	FuncInvitePattern    string                  `yaml:"FuncInvitePattern"`
	FuncKey              []string                `yaml:"FuncKey"`
	FuncInstruction      []string                `yaml:"FuncInstruction"`
	FuncLuckyRes         []string                `yaml:"FuncLuckyRes"`
	LuckyResPattern      string                  `yaml:"LuckyResPattern"`
	FuncDefaultRes       []string                `yaml:"FuncDefaultRes"`
	DefaultResPattern    string                  `yaml:"DefaultResPattern"`
	ReTriggerDefault     string                  `yaml:"ReTriggerDefault"`
	ReTriggerLucky       string                  `yaml:"ReTriggerLucky"`
	Link                 string                  `yaml:"Link"`
	SleepTimeBaseInMS    int                     `yaml:"SleepTimeBaseInMS"`
	SleepTimeRangeInMS   int                     `yaml:"SleepTimeRangeInMS"`
}

type ActivityCumulativeValue struct {
	UserNum        []int `yaml:"UserNum"`
	LuckyPrizeNum  []int `yaml:"LuckyPrizeNum"`
	FourthPrizeNum []int `yaml:"FourthPrizeNum"`
	ThirdPrizeNum  []int `yaml:"ThirdPrizeNum"`
	SecondPrizeNum []int `yaml:"SecondPrizeNum"`
	FirstPrizeNum  []int `yaml:"FirstPrizeNum"`
	SuperPrizeNum  []int `yaml:"SuperPrizeNum"`
}

type FastApplyPromptConfig struct {
	PromptKey   string `yaml:"PromptKey"`
	PromptLabel string `yaml:"PromptLabel"`
}

type LLMFilterConfig struct {
	// 是否启用输入安全过滤，默认启用
	DisableInputFilter bool `yaml:"DisableInputFilter"`

	// 是否启用输出安全过滤，默认关闭，对用户体验有影响
	EnableOutputFilter bool `yaml:"EnableOutputFilter"`
	// 是否启用补全输出安全过滤，默认关闭，对链路时延有影响
	EnableCodeCompletionOutputFilter bool `yaml:"EnableCodeCompletionOutputFilter"`
	// 滑动窗口，每次滑动多少 chunk
	OutputFilterChunkSize int `yaml:"OutputFilterChunkSize"`
	// 滑动窗口，窗口最大字符数量
	OutputFilterWindowSize int `yaml:"OutputFilterWindowSize"`
}

type MarscodeTccConfig struct {
	DatasetID     int64   `json:"dataset_id"`
	DataSourceIDs []int64 `json:"datasource_ids"`
	TopK          int32   `json:"top_k"`
	MarscodeHost  string  `json:"marscode_host"`
}

// IDESecrets contains misc secrets for IDE AI.
type IDESecrets struct {
	TeaEventCollectorToken string `yaml:"TeaEventCollectorToken"`
	// TenantIPSecret is the IP secret for different tenant.
	TenantIPSecret map[string]string `yaml:"TenantIPSecret"`
}

type LLMRawChatConfig struct {
	MessageSecret string `yaml:"MessageSecret"`
	RecordDB      bool   `yaml:"RecordDB"`
}

type SecretKeyConfig struct {
	CustomModelKey CustomModelKey `json:"custom_model"`
}

type CustomModelKey struct {
	SecretKey string `json:"secret_key"`
	RandomKey string `json:"random"`
	Timestamp string `json:"timestamp"`
}

type ByteCloudIamConfig struct {
	Host         string `yaml:"Host"`
	DomainHeader string `yaml:"DomainHeader"`
	Account      string `yaml:"Account"`
	Secret       string `yaml:"Secret"`
}

type IDEGenTitlePromptConfig struct {
	PromptKey   string `yaml:"PromptKey"`
	PromptLabel string `yaml:"PromptLabel"`
}

type IDESuggestedPromptConfig struct {
	PromptKey   string `yaml:"PromptKey"`
	PromptLabel string `yaml:"PromptLabel"`
}

type PlaygroundTCCConfig struct {
	RiskAppID int `yaml:"RiskAppID"`
	// LoggedUserLimit 登录用户限制
	LoggedUserLimit int `yaml:"LoggedUserLimit"`
	// LoggedUserResetMinutes 登录用户限制重置时间(分钟)
	LoggedUserResetMinutes int `yaml:"LoggedUserResetMinutes"`
	// NotLoggedUserLimit 未登录用户限制
	NotLoggedUserLimit int `yaml:"NotLoggedUserLimit"`
	// NotLoggedUserResetMinutes 未登录用户限制重置时间
	NotLoggedUserResetMinutes int `yaml:"NotLoggedUserResetMinutes"`
	// DownloadGuideInterval 每使用 X 次提示用户一次下载引导信息
	DownloadGuideInterval int `yaml:"DownloadGuideInterval"`
	// ReposList PlayGround 仓库列表
	ReposList []PlaygroundRepoList `yaml:"ReposList"`
	// CKGTopN ckg resolver top n
	CKGTopN int `yaml:"CKGTopN"`
	// IntentDetectList intent detect list
	IntentDetectList []string `yaml:"IntentDetectList"`
}

type PlaygroundRepoList struct {
	RepoName           string   `yaml:"RepoName"`
	FromGithub         bool     `yaml:"FromGithub"`
	SuggestedQuestions []string `yaml:"SuggestedQuestions"`
	CdnURL             string   `yaml:"CdnURL"`
	Description        string   `yaml:"Description"`
}

type IDELLMProxyConfig struct {
	SignKeyRandomSalt string                   `yaml:"SignKeyRandomSalt"`
	UserMaxAPIKeys    int                      `yaml:"UserMaxAPIKeys"`
	APIBaseURL        string                   `yaml:"APIBaseURL"`
	Models            []IDELLMProxyConfigModel `yaml:"Models"`

	AllowedSignKeyServiceAccounts []string `yaml:"AllowedSignKeyServiceAccounts"`
}

type IDELLMProxyConfigModel struct {
	Name          string                                 `yaml:"Name"`
	Type          string                                 `yaml:"Type"`
	ModelIDs      []string                               `yaml:"ModelIDs"`
	Quota         IDELLMProxyConfigModelQuota            `yaml:"Quota"`
	UserWhiteList map[string]IDELLMProxyConfigModelQuota `yaml:"UserWhiteList"`
}

type IDELLMProxyConfigModelQuota struct {
	MaxTokens   int64 `yaml:"MaxTokens"`
	MaxRequests int64 `yaml:"MaxRequests"`
	QPM         int64 `yaml:"QPM"`
	TPM         int64 `yaml:"TPM"`
}

type ModelPromptMapConfig struct {
	ConstructPromptTypeMap   map[string]PromptTypeConfig `json:"construct_prompt_type"`
	PostProcessPromptTypeMap map[string]PromptTypeConfig `json:"post_process_prompt_type"`
}

type PromptTypeConfig []string
type FornaxPromptVersionList []string
type IDEFornaxTCCConfig struct {
	AccessKey                string                                `yaml:"AccessKey"`
	SecretKey                string                                `yaml:"SecretKey"`
	Timeout                  int                                   `yaml:"Timeout"`
	MarsCodePromptKey        string                                `yaml:"MarsCodePromptKey"`
	CursorPromptKey          string                                `yaml:"CursorPromptKey"`
	LLMOPSPromptNameKeyMap   map[string]string                     `yaml:"LLMOPSPromptNameKeyMap"`
	FornaxPromptLabelMap     map[string]OverwritePromptLabelConfig `yaml:"FornaxPromptLabelMap"`
	IDEPromptValidVersionMap map[string]FornaxPromptVersionList    `yaml:"IDEPromptValidVersionMap"`
}

type CountryBlocklistTCCConfig struct {
	CountryBlocklist []int64 `json:"CountryBlocklist"`
}

type AILimitQueueTCCConfig struct {
	// 是否启动排队功能
	Enable    bool     `yaml:"Enable"`
	EnableApp []string `yaml:"EnableApp"`

	// 启用计费功能APP，跟普通限流排队是互斥的（计费功能）
	AppForFee []string `yaml:"AppForFee"`
	// 免费用户进入慢队列阈值（计费功能）
	FreeUserHitSlowQueueThreshold    int64 `yaml:"SlowQueueThresholdForFee"`
	NoQuotaErrorCodeOldVersionForFee int   `yaml:"NoQuotaErrorCodeOldVersionForFee"`

	// 每个用户在某个单位时间内的请求量(比如, 24小时内30个请求), 这个先暂时按照1天设置, 后续可以加duration来确定控制QPX
	UserQuotaPerDay int `yaml:"UserQuotaPerDay"`
	// 是否启用周期性的按照最大超时时间清理队列元素
	EnableLoopCleanExpiredRequest bool `yaml:"EnableCleanExpiredRequest"`
	//跳过限流排队的白名单用户
	SkipLimitQueueWhiteList []string `yaml:"SkipLimitQueueWhiteList"`
	//builder每轮已排队状态保留TTL
	BuilderQueuedStatusReserveTimeout time.Duration `yaml:"BuilderQueuedStatusReserveTimeout"`
	//builder每轮已排队状态最多使用次数
	MaxBuilderQueuedStatusUses int `yaml:"MaxBuilderQueuedStatusUses"`
	//builder是否支持自定义优先级
	BuilderEnableCustomScore bool `yaml:"BuilderEnableCustomScore"`
	//builder多轮请求的时间增量，控制后续请求插队的程度
	BuilderSessionDeltaTime time.Duration `yaml:"BuilderSessionDeltaTime"`
	//队列轮训间隔
	LoopInterval time.Duration `yaml:"LoopInterval"`
	// -----以下三个元素可以在Modes内置自定义, 这边先统一了 ----
	// 元素在队列中的超时时间(当前时间减去入队时间)
	WaitTimeoutInQueue time.Duration `yaml:"WaitTimeoutInQueue"`
	// 元素的最大超时时间(外围的loop时间, 或者因为某些异常情况导致队列堆积的元素的时间)
	MaxWaitTimeoutInQueue time.Duration `yaml:"MaxWaitTimeoutInQueue"`
	// 元素心跳超时时间(外围的loop timer到达会刷新心跳)
	MaxHeartbeatTimeoutInQueue time.Duration `yaml:"MaxHeartbeatTimeoutInQueue"`
	//定时清理任务每次最多清理的请求数
	MaxCleanExpiredRequest int `yaml:"MaxCleanExpiredRequest"`
	//可主动清理前面过期请求的排位，减少redis的压力 大部份情况下都是无用功有兜底的清理任务
	MaxCleanExpiredRequestRankPosition int `yaml:"MaxCleanExpiredRequestRankPosition"`
	// 请求队列的最大长度(超过这个请求长度后丢弃)
	MaxQueueSize int `yaml:"MaxQueueSize"`
	// 请求队列的最大长度(超过这个请求长度后丢弃)
	EnableHeartBeat bool `yaml:"EnableHeartBeat"`
	// -----以上三个元素可以在Modes内置自定义, 这边先统一了 ----

	// 不同模式的配置, Claude 3.5 or GPT-4o
	Modes map[string]AILimitQueueModeTCCConfig `yaml:"Modes"`

	// 计费功能单独的模型队列配置, Claude 3.5 or GPT-4o
	ModesForFee map[string]AILimitQueueModeTCCConfig `yaml:"ModesForFee"`
}

type AILimitQueueModeTCCConfig struct {
	// 慢请求队列的QPM(超过这个QPM后进入队列等待), 这个先暂时按照1分钟设置, 后续可以加duration来确定控制QPX
	SlowQueueRequestQuota int `yaml:"SlowQueueRequestQuota"`
	// 快请求队列的QPM(超过这个QPM后进入队列等待), 这个先暂时按照1分钟设置, 后续可以加duration来确定控制QPX
	FastQueueRequestQuota int `yaml:"FastQueueRequestQuota"`
	// 慢请求队列的名称, 比如slow_request_queue
	SlowQueueName string `yaml:"SlowQueueName"`
	// 快请求队列的名称, 比如fast_request_queue
	FastQueueName string `yaml:"FastQueueName"`
	// 只启用慢队列 全部都需要排队的场景，claude3.7
	OnlyEnableSlowQueue bool `yaml:"OnlyEnableSlowQueue"`

	//模型单独配置的超时时间
	WaitTimeoutInQueue time.Duration `yaml:"WaitTimeoutInQueue"`

	//计费特有逻辑
	//触发限流直接拒绝请求不进入排队，FastApply以及CodeCompletionStream不接入排队 限流 不通过直接返回错误码
	RejectDirectlyIfRateLimit bool `yaml:"RejectDirectlyIfRateLimit"`
	//触发限流直接拒绝请求不进入排队，FastApply以及CodeCompletionStream 不限流
	DisableRateLimit bool `yaml:"DisableRateLimit"`
}

type EntitlementTaskTCCConfig struct {
	RDSOperationLimit int `yaml:"RDSOperationLimit"`
	TaskInterval      int `yaml:"TaskInterval"`
}

type JournalTypeConfig struct {
	Disable []string `json:"disable"`
}

type JournalTCCConfig struct {
	PromptCompletionConfig     JournalTypeConfig `json:"prompt_completion"`
	CodeCompletionConfig       JournalTypeConfig `json:"code_completion"`
	CompletionMultiPointConfig JournalTypeConfig `json:"completion_multi_point"`
	PromptPipelineConfig       JournalTypeConfig `json:"prompt_pipeline"`
}

type ProvidersModelConfig struct {
	// 模型服务商列表
	Providers []ProvidersConfig `yaml:"Providers"`
}

type ProvidersConfig struct {
	Name string `yaml:"Name"`
	ID   string `yaml:"Id"`
	Type string `yaml:"Type"`
	// 服务商所提供的模型信息
	Models    []ProviderModelsConfig `yaml:"Models"`
	BaseURL   string                 `yaml:"BaseURL"`
	Endpoint  string                 `yaml:"Endpoint"`
	ApiKeyDoc string                 `yaml:"ApiKeyDoc"`
	// 是否为客户端直连
	ClientConnection bool `yaml:"ClientConnection"`
	// 默认兜底Icon链接
	Icon map[string]string `yaml:"Icon"`
}

type ProviderModelsConfig struct {
	Name       string            `yaml:"Name"`
	Multimodal bool              `yaml:"Multimodal"`
	Builder    bool              `yaml:"Builder"`
	ModelType  string            `yaml:"ModelType"`
	Describe   map[string]string `yaml:"Describe"`
	Icon       map[string]string `yaml:"Icon"`
}

// Trae 用户增长相关配置
type TraeUserGrowthTCCConfig struct {
	// 启用AddTraeTaskRecord RPC调用
	EnableAddTraeTaskRecord bool `yaml:"EnableAddTraeTaskRecord"`
	// TraeAPPID
	TraeAppID string `yaml:"TraeAppID"`
}

type ErrorCodeTypeConfig struct {
	ModelRateLimited        []any `yaml:"ModelRateLimited"`
	ModelServiceUnavailable []any `yaml:"ModelServiceUnavailable"`
}

type ErrorCodeConfig struct {
	AzureErrorCodeMap ErrorCodeTypeConfig `yaml:"Azure"`
}

type ModelSupplementalConfig struct {
	Models map[string]*ModelSupplementalConfigItem `yaml:"Models"`
}

type ModelSupplementalConfigItem struct {
	// UseCustomAzureClient: 只要有supplemental配置，全部都是true
	UseCustomAzureClient              bool `yaml:"UseCustomAzureClient"`
	RequireManualPromptCachingControl bool `yaml:"RequireManualPromptCachingControl"`
	// CallFormat 见 llm_request_dto_adapter.go
	CallFormat string `yaml:"CallFormat"`
}
