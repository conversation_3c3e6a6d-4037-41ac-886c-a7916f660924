package config

type ResourceManageTCCConfig struct {
	CurrentRegion           string          `json:"CurrentRegion"`
	ImageXTCCConfig         ImageXTCCConfig `json:"ImageXTCCConfig"`
	MaterialImageXTCCConfig ImageXTCCConfig `json:"MaterialImageXTCCConfig"`
}

type ImageXTCCConfig struct {
	PSM       string `json:"PSM"`
	ServiceID string `json:"ServiceID"`
	// AK.
	AccessKey string `json:"AccessKey"`
	// SK.
	SecretKey string `json:"SecretKey"`

	UploadTokenExpires int64 `json:"UploadTokenExpires"`

	Region string `json:"Region"`

	//对外分发资源签名配置，端上使用
	PublicK3sCli K3sCliTCCConfig `json:"PublicK3sCli"`

	//分发资源内部服务使用，Chat
	InnerK3sCli K3sCliTCCConfig `json:"InnerK3sCli"`
	//单位秒
	InnerDownloadTimeout int64 `json:"InnerDownloadTimeout"`

	DefaultTemplateID string `json:"DefaultTemplateID"`

	DefaultDomain string `json:"DefaultDomain"`
}

type K3sCliTCCConfig struct {
	//分发资源签名配置 内部服务使用chat的时候拉资源使用
	Domain string `json:"Domain"`
	// K3s AK.
	AccessKey string `json:"AccessKey"`
	// K3s SK.
	SecretKey string `json:"SecretKey"`
	// K3s type  token/ak.
	Type string `json:"Type"`
	// k3s token
	Token string `json:"Token"`
	// 签发URL有效期，单位分钟
	Expires int64 `json:"Expires"`
}
