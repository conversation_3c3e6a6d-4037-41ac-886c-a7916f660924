package di

import (
	"reflect"
	"testing"

	"github.com/stretchr/testify/require"
)

type Interface1 interface {
	FuncA(s string) string
}

type Interface1Imp struct{}

func (i *Interface1Imp) FuncA(s string) string {
	return s
}

type NotInterface1Imp struct{}

func TestBind(t *testing.T) {
	type args struct {
		structValue    interface{}
		interfaceValue interface{}
	}
	tests := []struct {
		name   string
		args   args
		want   interface{}
		failed bool
	}{
		{
			name: "normal case",
			args: args{
				structValue:    new(Interface1Imp),
				interfaceValue: new(Interface1),
			},
			want:   new(Interface1),
			failed: false,
		},
		{
			name: "bad case: not implement",
			args: args{
				structValue:    new(NotInterface1Imp),
				interfaceValue: new(Interface1),
			},
			want:   nil,
			failed: true, // Should panic, as the given struct not implement the interface.
		},
		{
			name: "bad case: not interface",
			args: args{
				structValue:    new(NotInterface1Imp),
				interfaceValue: new(NotInterface1Imp),
			},
			want:   nil,
			failed: true, // Should panic, as not pass in an interface value.
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.failed {
				require.Panics(t, func() {
					got := Bind(tt.args.structValue, tt.args.interfaceValue)
					reflect.ValueOf(got).Call([]reflect.Value{reflect.ValueOf(tt.args.structValue)})
				})
			} else {
				got := Bind(tt.args.structValue, tt.args.interfaceValue)
				// Call the returned constructing function.
				gotVal := reflect.ValueOf(got).Call([]reflect.Value{reflect.ValueOf(tt.args.structValue)})

				wantVal := reflect.ValueOf(tt.want).Elem()
				if len(gotVal) != 1 || !gotVal[0].CanConvert(wantVal.Type()) {
					t.Errorf("failed to convert")
				}
			}
		})
	}
}

type Struct1 struct {
	S2 *Struct2
	S3 *Struct3
}

type Struct2 struct {
	Name string
}

type Struct3 struct {
	Name string
}

func TestStructConstructor(t *testing.T) {
	type args struct {
		structPointer any
		members       []any
	}
	var (
		s2 = new(Struct2)
		s3 = new(Struct3)
	)
	tests := []struct {
		name   string
		args   args
		want   any
		failed bool
	}{
		{
			name: "normal case",
			args: args{
				structPointer: new(Struct1),
				members:       []any{s2, s3},
			},
			want: &Struct1{
				S2: s2,
				S3: s3,
			},
		},
		{
			name: "bad case with not struct given",
			args: args{
				structPointer: new(Interface1),
				members:       nil,
			},
			want:   nil,
			failed: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.failed {
				require.Panics(t, func() {
					StructConstructor(tt.args.structPointer)
				})
			} else {
				got := StructConstructor(tt.args.structPointer)
				gotVal := reflect.ValueOf(got)
				var argVals []reflect.Value
				for _, arg := range tt.args.members {
					argVals = append(argVals, reflect.ValueOf(arg))
				}
				gotRes := gotVal.Call(argVals)
				if len(gotRes) != 1 {
					t.Errorf("got unexpected returned value")
				}
				if !reflect.DeepEqual(gotRes[0].Interface(), tt.want) {
					t.Errorf("StructConstructor() = %v, want %v", gotRes[0].Interface(), tt.want)
				}
			}
		})
	}
}

type Str1 struct {
	S2 *Str2
	S3 Str3
}

type Str2 struct {
	Name string
}

type Str3 struct {
	Name string
}

func TestStructProvider(t *testing.T) {
	testCases := []struct {
		Name      string
		Provider  any
		Consumers []any
		Failed    bool
	}{
		{
			Name: "normal struct provider",
			Provider: &Str1{
				S2: &Str2{
					Name: "hi",
				},
				S3: Str3{
					Name: "me",
				},
			},
			Consumers: []any{
				&Str2{
					Name: "hi",
				},
				Str2{
					Name: "hi",
				},
				Str3{
					Name: "me",
				},
				&Str3{
					Name: "me",
				},
			},
			Failed: false,
		},
		{
			Name:      "not struct pointer",
			Provider:  Struct1{},
			Consumers: nil,
			Failed:    true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.Name, func(t *testing.T) {
			if tc.Failed {
				require.Panics(t, func() {
					StructProvider(tc.Provider, true)
				})
			} else {
				res := StructProvider(tc.Provider, true)
				vals := reflect.ValueOf(res).Call([]reflect.Value{reflect.ValueOf(tc.Provider)})
				require.Equal(t, len(tc.Consumers), len(vals))
				for idx := range vals {
					if !reflect.DeepEqual(tc.Consumers[idx], vals[idx].Interface()) {
						t.Errorf("StructProvider() = %v, want %v", vals[idx].Interface(), tc.Consumers[idx])
					}
				}
			}
		})
	}
}
