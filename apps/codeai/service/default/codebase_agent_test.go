package defaultcodeaiservice

import (
	"context"
	"errors"
	"fmt"
	"reflect"
	"testing"

	"code.byted.org/devgpt/kiwis/lib/mapstructure"
	"github.com/alicebob/miniredis"
	"github.com/samber/lo"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	"gorm.io/gorm"

	"code.byted.org/devgpt/kiwis/apps/codeai/dal"
	defaultdal "code.byted.org/devgpt/kiwis/apps/codeai/dal/default"
	"code.byted.org/devgpt/kiwis/apps/codeai/dal/po"
	"code.byted.org/devgpt/kiwis/apps/codeai/entity"
	"code.byted.org/devgpt/kiwis/apps/codeai/service"
	chattest "code.byted.org/devgpt/kiwis/bitscopilot/chat/test"
	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/tcc"
	"code.byted.org/devgpt/kiwis/port/codebase"
	codebasemock "code.byted.org/devgpt/kiwis/port/codebase/mock"
	"code.byted.org/devgpt/kiwis/port/db"
	llmreviewer "code.byted.org/devgpt/kiwis/port/llmreviewer/mock"
	"code.byted.org/devgpt/kiwis/port/redis"
)

func structToMap(input interface{}) (map[string]any, error) {
	if reflect.ValueOf(input).Kind() != reflect.Struct {
		return nil, fmt.Errorf("input must be a struct")
	}

	var result map[string]any
	err := mapstructure.Decode(input, &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func TestCodebaseAgent_CreateAIFix(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	ChatService := chattest.NewMockChatService(ctrl)
	codebaseCli := codebasemock.NewMockClient(ctrl)
	codeAIConfig, _ := tcc.NewConfig[config.BitsCodeAIConfig](
		config.BitsCodeAIConfig{
			DevGPTHost: "",
			CodeReview: config.CodeReviewConfig{
				RulesMappingIssue: []config.RulesMappingIssueConfig{
					{
						Rules: []string{
							"sqlclosecheck",
						},
						Issue:   "代码缺陷-资源未释放/资源泄漏.",
						IssueEN: "代码缺陷-资源未释放/资源泄漏.",
					},
				},
			},
		}, "", "", "", tcc.ConfigFormatYAML)
	s := &CodebaseAgent{
		ChatService:  ChatService,
		CodebaseCli:  codebaseCli,
		BitsAISecret: "test",
		CodeAIConfig: codeAIConfig,
	}

	t.Run("error 1", func(t *testing.T) {
		ChatService.EXPECT().Invoke(gomock.Any(), gomock.Any()).Return(nil, errors.New("error"))

		_, err := s.CreateAIFix(context.Background(), service.AiFixOption{
			AppID:        "test",
			FunctionID:   "test",
			ErrorMessage: "test",
			Locale:       lo.ToPtr("test"),
			Path:         "test",
			Range: service.Range{
				Start: service.RangeNode{
					Character: 1,
					Line:      1,
				},
				End: service.RangeNode{
					Character: 1,
					Line:      1,
				},
			},
			RepoName: "test",
			Revision: "test",
			RuleName: "test",
			ChangeID: lo.ToPtr(int64(1001)),
			Account: &authentity.Account{
				Username: "test",
			},
			NeedPrompt: lo.ToPtr(true),
			SourceSHA:  "test",
			TargetSHA:  "test",
		})
		require.Error(t, err)
	})

	t.Run("error 2", func(t *testing.T) {
		codebaseCli.EXPECT().GetBlobByPath(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("error"))
		_, err := s.processingResult(context.Background(), service.AiFixOption{
			RepoName: "test",
			Revision: "test",
			Path:     "test",
		}, service.AiFixRes{
			FixRange: "1:10",
		}, "test")
		require.Error(t, err)
	})

	t.Run("success 1", func(t *testing.T) {
		codebaseCli.EXPECT().GetBlobByPath(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&codebase.BlobContent{
			Content: "cGFja2FnZSBjbG9zZWQKCmltcG9ydCAoCgkibG9nIgoJInN0cmluZ3MiCikKCi8vIHJvd3NJbmNvcnJlY3ROb0VyciBwcm92aWRlcyBhbiBleGFtcGxlIG9mIGluY29ycmVjdCBjbG9zaW5nIGJ5IG5vdCBjYWxsaW5nIEVycigpCmZ1bmMgcm93c0luY29ycmVjdE5vRXJyKCkgewoJYWdlIDo9IDQwCglyb3dzLCBlcnIgOj0gZGIuUXVlcnlDb250ZXh0KGN0eCwgIlNFTEVDVCBuYW1lIEZST00gdXNlcnMgV0hFUkUgYWdlPT8iLCBhZ2UpIC8vIHdhbnQgIlJvd3MvU3RtdC9OYW1lZFN0bXQgd2FzIG5vdCBjbG9zZWQiCglpZiBlcnIgIT0gbmlsIHsKCQlsb2cuRmF0YWwoZXJyKQoJfQoKCW5hbWVzIDo9IFtdc3RyaW5ne30KCglmb3Igcm93cy5OZXh0KCkgewoJCXZhciBuYW1lIHN0cmluZwoJCWlmIGVyciA6PSByb3dzLlNjYW4oJm5hbWUpOyBlcnIgIT0gbmlsIHsKCQkJbG9nLkZhdGFsKGVycikKCQl9CgoJCW5hbWVzID0gYXBwZW5kKG5hbWVzLCBuYW1lKQoJfQoKCS8vIENoZWNrIGZvciBlcnJvcnMgZnJvbSBpdGVyYXRpbmcgb3ZlciByb3dzLgoJLy8gaWYgZXJyIDo9IHJvd3MuRXJyKCk7IGVyciAhPSBuaWwgewoJLy8gCWxvZy5GYXRhbChlcnIpCgkvLyB9CgoJbG9nLlByaW50ZigiJXMgYXJlICVkIHllYXJzIG9sZCIsIHN0cmluZ3MuSm9pbihuYW1lcywgIiwgIiksIGFnZSkKCglyb3dzLkNsb3NlKCkKfQ==",
		}, nil)
		res, err := s.processingResult(context.Background(), service.AiFixOption{
			RepoName: "test",
			Revision: "test",
			Path:     "test",
		}, service.AiFixRes{
			FixRange: "9:35",
		}, "<issue_content>在函数 `rowsIncorrectNoErr` 中，`rows` 没有使用 `defer` 关键字来确保关闭资源，导致潜在的资源泄露。</issue_content>\n<code_fix_explain>使用 `defer` 关键字确保 `rows` 在函数结束时被关闭。</code_fix_explain>\n<fixed_code_content>\n```go\n### golang/closecheck/sqlclosecheck/sql/rows_incorrect_no_err.go\n<<<<<<< SEARCH\n        rows, err := db.QueryContext(ctx, \"SELECT name FROM users WHERE age=?\", age) // want \"Rows/Stmt/NamedStmt was not closed\"\n=======\n        rows, err := db.QueryContext(ctx, \"SELECT name FROM users WHERE age=?\", age)\n>>>>>>> REPLACE\n```\n\n```go\n### golang/closecheck/sqlclosecheck/sql/rows_incorrect_no_err.go\n<<<<<<< SEARCH\n        if err != nil {\n                log.Fatal(err)\n        }\n=======\n        if err != nil {\n                log.Fatal(err)\n        }\n        defer rows.Close()\n>>>>>>> REPLACE\n```\n\n```go\n### golang/closecheck/sqlclosecheck/sql/rows_incorrect_no_err.go\n<<<<<<< SEARCH\n        // Check for errors from iterating over rows.\n        // if err := rows.Err(); err != nil {\n        //      log.Fatal(err)\n        // }\n\n        log.Printf(\"%s are %d years old\", strings.Join(names, \", \"), age)\n\n        rows.Close()\n=======\n        // Check for errors from iterating over rows.\n        // if err := rows.Err(); err != nil {\n        //      log.Fatal(err)\n        // }\n\n        log.Printf(\"%s are %d years old\", strings.Join(names, \", \"), age)\n\n        // rows.Close()\n>>>>>>> REPLACE\n```\n</fixed_code_content>")
		require.NoError(t, err)
		require.Equal(t, res.FixRange, "11:14")
	})

	t.Run("success 2", func(t *testing.T) {
		_, comment := s.renderCommentByUsingSuggestion(Review{
			Issue:       "在 `rowsIncorrectNoErr` 函数中，`rows` 是通过 `db.QueryContext` 方法获取的数据库查询结果集。在使用完 `rows` 后，需要调用 `rows.Close()` 方法来释放相关资源。但当前代码中 `rows.Close()` 是在循环结束后直接调用的，这样可能会因为在循环过程中出现错误而导致 `rows` 没有被正确关闭，从而造成资源泄漏。",
			IssueType:   "sqlclosecheck",
			Suggestions: "func rowsIncorrectNoErr() {\n\tage := 40\n\trows, err := db.QueryContext(ctx, \"SELECT name FROM users WHERE age=?\", age) // want \"Rows/Stmt/NamedStmt was not closed\"\n\tif err != nil {\n\t\tlog.Fatal(err)\n\t}\n    defer rows.Close()\n\n\tnames := []string{}\n\n\tfor rows.Next() {\n\t\tvar name string\n\t\tif err := rows.Scan(&name); err != nil {\n\t\t\tlog.Fatal(err)\n\t\t}\n\n\t\tnames = append(names, name)\n\t}\n\n\t// Check for errors from iterating over rows.\n\t// if err := rows.Err(); err != nil {\n    //  log.Fatal(err)\n\t// }\n\n\tlog.Printf(\"%s are %d years old\", strings.Join(names, \", \"), age)\n}",
		}, "zh", "使用 `defer` 关键字来确保 `rows.Close()` 方法在函数返回时一定会被调用，这样即使在循环过程中出现错误，`rows` 也能被正确关闭。", false)
		require.Equal(t, comment, "📝 **评审类型：代码缺陷-资源未释放/资源泄漏.**.\n在 `rowsIncorrectNoErr` 函数中，`rows` 是通过 `db.QueryContext` 方法获取的数据库查询结果集。在使用完 `rows` 后，需要调用 `rows.Close()` 方法来释放相关资源。但当前代码中 `rows.Close()` 是在循环结束后直接调用的，这样可能会因为在循环过程中出现错误而导致 `rows` 没有被正确关闭，从而造成资源泄漏。\n\n❇️ **评审建议**：\n<details ><summary>View details</summary>\n\n使用 `defer` 关键字来确保 `rows.Close()` 方法在函数返回时一定会被调用，这样即使在循环过程中出现错误，`rows` 也能被正确关闭。\n```suggestion\nfunc rowsIncorrectNoErr() {\n\tage := 40\n\trows, err := db.QueryContext(ctx, \"SELECT name FROM users WHERE age=?\", age) // want \"Rows/Stmt/NamedStmt was not closed\"\n\tif err != nil {\n\t\tlog.Fatal(err)\n\t}\n    defer rows.Close()\n\n\tnames := []string{}\n\n\tfor rows.Next() {\n\t\tvar name string\n\t\tif err := rows.Scan(&name); err != nil {\n\t\t\tlog.Fatal(err)\n\t\t}\n\n\t\tnames = append(names, name)\n\t}\n\n\t// Check for errors from iterating over rows.\n\t// if err := rows.Err(); err != nil {\n    //  log.Fatal(err)\n\t// }\n\n\tlog.Printf(\"%s are %d years old\", strings.Join(names, \", \"), age)\n}\n```\n</details>\n")
	})

	t.Run("success 3", func(t *testing.T) {
		ChatService.EXPECT().Invoke(gomock.Any(), gomock.Any()).Return(nil, errors.New("error"))
		fixCode, fixExplain, _, _ := s.aiFixComment(context.Background(), Review{
			Issue: "test",
			StartLine: &Line{
				IntVal: 1,
			},
			EndLine: &Line{
				IntVal: 2,
			},
			IssueType: "test",
		}, service.AIFileReviewInvokeParams{
			Locale:    "zh",
			Path:      "test",
			SourceRev: "test",
			TargetRev: "test",
			RepoName:  "test",
		})
		require.Equal(t, fixCode, "")
		require.Equal(t, fixExplain, "")
	})

	t.Run("error 4", func(t *testing.T) {
		s.createComment(context.Background(), service.AiFixRes{
			IssueExplain: "在 `rowsIncorrectNoErr` 函数中，`rows` 是通过 `db.QueryContext` 方法获取的数据库查询结果集。在使用完 `rows` 后，需要调用 `rows.Close()` 方法来释放相关资源。但当前代码中 `rows.Close()` 是在循环结束后直接调用的，这样可能会因为在循环过程中出现错误而导致 `rows` 没有被正确关闭，从而造成资源泄漏。",
			FixExplain:   "使用 `defer` 关键字来确保 `rows.Close()` 方法在函数返回时一定会被调用，这样即使在循环过程中出现错误，`rows` 也能被正确关闭。",
		}, service.AiFixOption{
			RuleName:  "sqlclosecheck",
			TargetSHA: "test",
			SourceSHA: "test",
			Path:      "test",
			ChangeID:  lo.ToPtr(int64(1)),
		}, "func rowsIncorrectNoErr() {\n\tage := 40\n\trows, err := db.QueryContext(ctx, \"SELECT name FROM users WHERE age=?\", age) // want \"Rows/Stmt/NamedStmt was not closed\"\n\tif err != nil {\n\t\tlog.Fatal(err)\n\t}\n    defer rows.Close()\n\n\tnames := []string{}\n\n\tfor rows.Next() {\n\t\tvar name string\n\t\tif err := rows.Scan(&name); err != nil {\n\t\t\tlog.Fatal(err)\n\t\t}\n\n\t\tnames = append(names, name)\n\t}\n\n\t// Check for errors from iterating over rows.\n\t// if err := rows.Err(); err != nil {\n    //  log.Fatal(err)\n\t// }\n\n\tlog.Printf(\"%s are %d years old\", strings.Join(names, \", \"), age)\n}",
			9, 35)
	})

	t.Run("success 4", func(t *testing.T) {
		codebaseCli.EXPECT().ListComments(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("error"))
		codebaseCli.EXPECT().CreateComment(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("error"))
		s.createComment(context.Background(), service.AiFixRes{
			IssueExplain: "在 `rowsIncorrectNoErr` 函数中，`rows` 是通过 `db.QueryContext` 方法获取的数据库查询结果集。在使用完 `rows` 后，需要调用 `rows.Close()` 方法来释放相关资源。但当前代码中 `rows.Close()` 是在循环结束后直接调用的，这样可能会因为在循环过程中出现错误而导致 `rows` 没有被正确关闭，从而造成资源泄漏。",
			FixExplain:   "使用 `defer` 关键字来确保 `rows.Close()` 方法在函数返回时一定会被调用，这样即使在循环过程中出现错误，`rows` 也能被正确关闭。",
		}, service.AiFixOption{
			RuleName:  "sqlclosecheck",
			TargetSHA: "test",
			SourceSHA: "test",
			Path:      "test",
			ChangeID:  lo.ToPtr(int64(1)),
		}, "\tif err != nil {\n\t\tlog.Fatal(err)\n\t}\n    defer rows.Close()",
			12, 14)
	})

}

func TestCodebaseAgent_CommentCURD(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	miniRedis, err := miniredis.Run()
	require.NoError(t, err)
	defer miniRedis.Close()
	redisCli, err := redis.NewBytedRedisClient(config.RedisConfig{
		PSM:  "",
		Addr: miniRedis.Addr(),
	})
	ctx := context.Background()
	tests := []struct {
		name string
		f    func(t *testing.T, s *CodebaseAgent)
	}{
		{
			name: "CURD normal",
			f: func(t *testing.T, s *CodebaseAgent) {
				var err error
				type inputParams struct {
					Teststr string
					Testint int64
				}
				input := inputParams{
					Teststr: "test string",
					Testint: int64(1001),
				}
				params, err := structToMap(input)
				fmt.Println("params = ", params)
				// create needs mock chat service, skip for now, using DAO directly.
				PO, err := s.DAO.CreateAsyncTask(ctx, dal.CreateAsyncTaskOption{
					Params: &entity.Parameters{
						Params: params,
					},
					User:     "Alice",
					AppID:    "",
					Status:   entity.StatusCodebaseAIReplyReady.String(),
					TaskType: entity.TaskTypeCodebaseAIReply,
				})
				require.NoError(t, err)
				require.Equal(t, map[string]interface{}{"Testint": int64(1001), "Teststr": "test string"}, PO.Params.Params)
				// require.Equal(t, "alice", PO.User)
				require.Equal(t, "ai_reply_ready", PO.Status)

				id := PO.ID
				// update
				PO2, err := s.UpdateStatusUponLLMRespond(ctx, service.UpdateTaskOption{
					TaskID:      id,
					LLMResponse: lo.ToPtr("this is a llm testing response"),
				})
				require.NoError(t, err)
				require.Equal(t, id, PO2.ID)
				require.Equal(t, "codebase_ai_reply", PO2.TaskType)
				require.Equal(t, "ai_reply_respond", PO2.Status)
				// The Go encoding/json package uses float64 as the default type for unmarshalling any JSON numbers
				// that are not otherwise constrained by the destination's type.
				require.Equal(t, map[string]interface{}{"Testint": float64(1001), "Teststr": "test string"}, PO2.Params.Params)

				// get
				PO3, err := s.GetTaskInfo(ctx, id)
				require.NoError(t, err)
				require.Equal(t, id, PO3.ID)

				// delete
				err = s.CancelTask(ctx, id, lo.ToPtr(""))
				require.NoError(t, err)
				// get
				_, err = s.GetTaskInfo(ctx, id)
				require.ErrorIs(t, err, gorm.ErrRecordNotFound)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cli, err := db.NewMockSQLiteClient(&po.AgentAsyncTaskPO{})
			require.NoError(t, err)

			dao := defaultdal.NewAsyncTaskDal(cli)
			codebaseCli := codebasemock.NewMockClient(ctrl)
			llmReviewerCli := llmreviewer.NewMockClient(ctrl)
			bitsAIAppConfig, _ := tcc.NewConfig[config.BitsAIAppConfig](config.BitsAIAppConfig{
				"code_ai": config.AppConfig{
					AppID: "appID",
				},
			}, "", "", "", tcc.ConfigFormatYAML)
			codeAIConfig, _ := tcc.NewConfig[config.BitsCodeAIConfig](
				config.BitsCodeAIConfig{}, "", "", "", tcc.ConfigFormatYAML)
			bitsAIJWTConfig, _ := tcc.NewConfig[config.CodebaseBitsAIJWTConfig]("", "", "", "", tcc.ConfigFormatString)
			s, err := NewCodebaseAgent(dao, codebaseCli, redisCli, nil, nil, llmReviewerCli, nil,
				bitsAIJWTConfig, bitsAIAppConfig, nil, nil, codeAIConfig, nil, nil)
			require.NoError(t, err)
			tt.f(t, s)
		})
	}
}

func TestCodebaseAgent_AIReviewQuota(t *testing.T) {
	ctx := context.Background()

	miniRedis, err := miniredis.Run()
	require.NoError(t, err)
	defer miniRedis.Close()
	redisCli, err := redis.NewBytedRedisClient(config.RedisConfig{
		PSM:  "",
		Addr: miniRedis.Addr(),
	})
	require.NoError(t, err)
	bitsAIAppConfig, _ := tcc.NewConfig[config.BitsAIAppConfig](config.BitsAIAppConfig{
		"code_ai": config.AppConfig{
			AppID: "appID",
		},
	}, "", "", "", tcc.ConfigFormatYAML)
	codeAIConfig, _ := tcc.NewConfig[config.BitsCodeAIConfig](
		config.BitsCodeAIConfig{}, "", "", "", tcc.ConfigFormatYAML)
	bitsAIJWTConfig, _ := tcc.NewConfig[config.CodebaseBitsAIJWTConfig]("testsecret", "", "", "", tcc.ConfigFormatString)
	s, err := NewCodebaseAgent(nil, nil, redisCli, nil, nil, nil, nil,
		bitsAIJWTConfig, bitsAIAppConfig, nil, nil, codeAIConfig,
		nil, nil)
	require.NoError(t, err)

	_, err = s.RedisClient.Get(ctx, DefaultAIFileReviewRequestKey)
	require.Error(t, err)
}

// Simulate broken YAML scenarios to validate correction logic.
func TestTryFixYAML(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		wantData map[string]interface{}
		wantBool bool
	}{
		{
			name:  "No changes needed - well formatted",
			input: "relevant line: |-\n        some code",
			wantData: map[string]interface{}{
				"relevant line": "some code",
			},
			wantBool: true,
		},
		{
			name:  "Requires fixing - missing |",
			input: "relevant line: some code\n",
			wantData: map[string]interface{}{
				"relevant line": "some code",
			},
			wantBool: true,
		},
		{
			name:  "Multiple lines, some require fixing",
			input: "relevant line: some code\nsuggestion content: another suggestion\nrelevant file: |-\n        file path",
			wantData: map[string]interface{}{
				"relevant line":      "some code",
				"suggestion content": "another suggestion",
				"relevant file":      "file path",
			},
			wantBool: true,
		},
		{
			name:     "Failed scenarios, return nil data",
			input:    "broken yaml data, that doesn't conform!",
			wantData: nil,
			wantBool: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ok, data := tryFixYAML(tt.input)
			require.Equal(t, tt.wantBool, ok)
			require.Equal(t, tt.wantData, data)
		})
	}
}

func TestSerializeMetadataFunctions(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx := context.Background()
	tests := []struct {
		name string
		f    func(t *testing.T)
	}{
		{
			name: "Serialize AIFileReviewMetadata",
			f: func(t *testing.T) {
				v := AIFileReviewMetadata{
					FilePath:          "/path/to/file",
					CommentCount:      5,
					ThreadIDs:         []int64{100001, 100002},
					FilteredThreadIDs: []int64{100002},
				}

				_, err := SerializeMetadata(ctx, v)
				require.NoError(t, err, "the serialization should succeed without error")
			},
		},
		{
			name: "Serialize MRWalkthroughMetadata",
			f: func(t *testing.T) {
				v := MRWalkthroughMetadata{
					PromptID: 12345,
					MRWalkthrough: MRWalkthrough{
						PRFiles: []entity.PRFile{
							{
								Filename:       "example.go",
								ChangesSummary: "Introduced memory management improvements",
								ChangesTitle:   "Enhance memory usage",
								ChangeType:     "improvement",
								Added:          150,
								Removed:        10,
							},
						},
					},
				}

				_, err := SerializeMRWalkThroughMetadata(ctx, v)
				require.NoError(t, err, "the serialization should succeed without error")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.f(t)
		})
	}
}
