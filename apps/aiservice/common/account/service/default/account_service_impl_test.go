package defaultaccountservice

import (
	"context"
	"reflect"
	"testing"

	"go.uber.org/mock/gomock"
	"gorm.io/gorm"

	"code.byted.org/devgpt/kiwis/apps/aiservice/common/account/dal"
	"code.byted.org/devgpt/kiwis/apps/aiservice/common/account/entity"
	"code.byted.org/devgpt/kiwis/apps/aiservice/common/account/service"
)

func TestAccountService_UpsertAction(t *testing.T) {
	type args struct {
		opt service.UpsertAccountOption
	}
	tests := []struct {
		name     string
		args     args
		mockFunc mockFunc
		want     *entity.Account
		wantErr  bool
	}{
		{
			name: "create account case",
			args: args{
				opt: service.UpsertAccountOption{
					Username:    "alice",
					Name:        "Alice",
					Type:        entity.AccountTypePerson,
					Department:  "department1",
					Email:       "<EMAIL>",
					Description: "description",
				},
			},
			mockFunc: func(t *testing.T, opt mockAccountServiceOption) {
				opt.DAO.EXPECT().
					GetAccountByUsername(gomock.Any(), "alice").
					Return(nil, gorm.ErrRecordNotFound)
				opt.DAO.EXPECT().CreateAccount(gomock.Any(), dal.CreateAccountOption{
					Username:    "alice",
					Name:        "Alice",
					Type:        entity.AccountTypePerson,
					Department:  "department1",
					Email:       "<EMAIL>",
					Description: "description",
				}).Return(&entity.Account{
					ID:          1,
					Username:    "alice",
					Name:        "Alice",
					Type:        entity.AccountTypePerson,
					Department:  "department1",
					Email:       "<EMAIL>",
					Description: "description",
				}, nil)
			},
			want: &entity.Account{
				ID:          1,
				Username:    "alice",
				Name:        "Alice",
				Type:        entity.AccountTypePerson,
				Department:  "department1",
				Email:       "<EMAIL>",
				Description: "description",
			},
			wantErr: false,
		},
		{
			name: "update account case",
			args: args{
				opt: service.UpsertAccountOption{
					Username:    "alice",
					Name:        "Alice",
					Type:        entity.AccountTypePerson,
					Department:  "department2",
					Email:       "<EMAIL>",
					Description: "description",
				},
			},
			mockFunc: func(t *testing.T, opt mockAccountServiceOption) {
				opt.DAO.EXPECT().GetAccountByUsername(gomock.Any(), "alice").Return(&entity.Account{
					ID:          1,
					Username:    "alice",
					Name:        "Alice",
					Type:        entity.AccountTypePerson,
					Department:  "department1",
					Email:       "<EMAIL>",
					Description: "description",
				}, nil)
				opt.DAO.EXPECT().UpdateAccountByUsername(gomock.Any(), "alice", gomock.Any()).
					Return(&entity.Account{
						ID:          1,
						Username:    "alice",
						Name:        "Alice",
						Type:        entity.AccountTypePerson,
						Department:  "department2",
						Email:       "<EMAIL>",
						Description: "description",
					}, nil)
			},
			want: &entity.Account{
				ID:          1,
				Username:    "alice",
				Name:        "Alice",
				Type:        entity.AccountTypePerson,
				Department:  "department2",
				Email:       "<EMAIL>",
				Description: "description",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			withTestAccountService(t, tt.mockFunc, func(s *AccountService) {
				got, err := s.UpsertAccount(context.Background(), tt.args.opt)
				if (err != nil) != tt.wantErr {
					t.Errorf("UpsertAccount() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("UpsertAccount() got = %v, want %v", got, tt.want)
				}
			})
		})
	}
}
