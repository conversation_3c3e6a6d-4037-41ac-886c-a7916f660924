package hertz_handler

import (
	"context"
	"fmt"
	"net/http"
	"strings"

	"code.byted.org/gopkg/logs/v2/log"
	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/apps/aiservice/common/auth/entity"
	"code.byted.org/devgpt/kiwis/apps/aiservice/common/auth/service"
	"code.byted.org/devgpt/kiwis/lib/hertz"
)

type AuthMiddleware struct {
	AuthService service.AuthService
}

const accountKey = "_auth_account"

func (m *AuthMiddleware) getCodebaseJWT(ctx context.Context, c *app.RequestContext) (string, bool) {
	return lo.Coalesce(
		string(c.<PERSON>("Codebase-User-JWT")),
		strings.TrimPrefix(string(c.<PERSON><PERSON>ead<PERSON>("Authorization")), "Codebase-User-JWT "),
	)
}

func (m *AuthMiddleware) getByteCloudJWT(ctx context.Context, c *app.RequestContext) (string, bool) {
	return lo.Coalesce(
		string(c.Cookie("X-JWT-TOKEN")),
		string(c.GetHeader("X-Jwt-Token")),
		strings.TrimPrefix(string(c.GetHeader("Authorization")), "Bearer "), // Compatible with OpenAI.
	)
}

func (m *AuthMiddleware) authCodebaseJWT(ctx context.Context, c *app.RequestContext, account *entity.Account) (*entity.Account, error) {
	jwt, ok := m.getCodebaseJWT(ctx, c)
	// Already authenticated, just set the jwt.
	if account != nil && ok {
		account.SetCodebaseJWT(jwt)
		return account, nil
	}
	if !ok {
		return account, errors.New("no Codebase credentials found")
	}

	account, err := m.AuthService.AuthCodebaseJWT(ctx, jwt)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to auth Codebase jwt")
	}

	return account, nil
}

func (m *AuthMiddleware) authByteCloudJWT(ctx context.Context, c *app.RequestContext, account *entity.Account) (*entity.Account, error) {
	jwt, ok := m.getByteCloudJWT(ctx, c)
	// Already authenticated, just set the jwt.
	if account != nil && ok {
		account.SetByteCloudJWT(jwt)
		return account, nil
	}
	if !ok {
		return account, errors.New("no ByteCloud credentials found")
	}

	account, err := m.AuthService.AuthByteCloudJWT(ctx, jwt)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to auth ByteCloud jwt")
	}

	if codebaseJWT := account.CodebaseAuthJWT(); len(codebaseJWT) != 0 {
		c.SetCookie("Codebase-User-JWT", account.CodebaseAuthJWT(), 3600*24, "/",
			string(c.Request.Host()), protocol.CookieSameSiteDefaultMode,
			true, true,
		)
		c.Header("Codebase-User-JWT", codebaseJWT)
	}

	return account, nil
}

type authenticator func(ctx context.Context, c *app.RequestContext, account *entity.Account) (*entity.Account, error)

func (m *AuthMiddleware) RequireAccount() app.HandlerFunc {
	return func(ctx context.Context, c *app.RequestContext) {
		var (
			account *entity.Account
			err     error
		)

		for _, f := range []authenticator{m.authCodebaseJWT, m.authByteCloudJWT} {
			account, err = f(ctx, c, account)
		}

		if account == nil {
			log.V1.CtxInfo(ctx, "no credentials found: %v", err)
			hertz.JSONMessage(c, http.StatusUnauthorized, -1, fmt.Sprintf("credentials required"))
			c.Abort()
			return
		}

		log.V1.CtxInfo(ctx, "user authenticated as %s", account.Username)
		c.Set(accountKey, account)
	}
}

func (m *AuthMiddleware) GetAccount(ctx context.Context, c *app.RequestContext) (*entity.Account, bool) {
	a, ok := c.Get(accountKey)
	if !ok {
		return nil, false
	}

	acc, ok := a.(*entity.Account)
	if !ok {
		return nil, false
	}

	return acc, true
}
