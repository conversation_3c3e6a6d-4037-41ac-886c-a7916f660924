// Code generated by MockGen. DO NOT EDIT.
// Source: code.byted.org/devgpt/kiwis/llmstack/aiservice/journal/service (interfaces: JournalService)
//
// Generated by this command:
//
//	mockgen -destination journal_service_gen.go -package journaltest code.byted.org/devgpt/kiwis/llmstack/aiservice/journal/service JournalService
//

// Package journaltest is a generated GoMock package.
package journaltest

import (
	context "context"
	reflect "reflect"

	entity "code.byted.org/devgpt/kiwis/apps/aiservice/aiservice/journal/entity"
	"code.byted.org/devgpt/kiwis/apps/aiservice/aiservice/journal/service"
	"code.byted.org/devgpt/kiwis/bitscopilot/chat/dal"

	gomock "go.uber.org/mock/gomock"
)

// MockJournalService is a mock of JournalService interface.
type MockJournalService struct {
	ctrl     *gomock.Controller
	recorder *MockJournalServiceMockRecorder
}

// MockJournalServiceMockRecorder is the mock recorder for MockJournalService.
type MockJournalServiceMockRecorder struct {
	mock *MockJournalService
}

// NewMockJournalService creates a new mock instance.
func NewMockJournalService(ctrl *gomock.Controller) *MockJournalService {
	mock := &MockJournalService{ctrl: ctrl}
	mock.recorder = &MockJournalServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockJournalService) EXPECT() *MockJournalServiceMockRecorder {
	return m.recorder
}

// Log mocks base method.
func (m *MockJournalService) Log(arg0 context.Context, arg1 service.CreateLogOption) (*entity.PromptCompletion, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Log", arg0, arg1)
	ret0, _ := ret[0].(*entity.PromptCompletion)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Log indicates an expected call of Log.
func (mr *MockJournalServiceMockRecorder) Log(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Log", reflect.TypeOf((*MockJournalService)(nil).Log), arg0, arg1)
}

// LogByMessages mocks base method.
func (m *MockJournalService) LogByMessages(arg0 context.Context, arg1 string, arg2 ...dal.CreateMessageOption) ([]*entity.PromptCompletion, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "LogByMessages", varargs...)
	ret0, _ := ret[0].([]*entity.PromptCompletion)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LogByMessages indicates an expected call of LogByMessages.
func (mr *MockJournalServiceMockRecorder) LogByMessages(arg0, arg1 any, arg2 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LogByMessages", reflect.TypeOf((*MockJournalService)(nil).LogByMessages), varargs...)
}

// UpdateLog mocks base method.
func (m *MockJournalService) UpdateLog(arg0 context.Context, arg1 service.UpdateLogOption) (*entity.PromptCompletion, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateLog", arg0, arg1)
	ret0, _ := ret[0].(*entity.PromptCompletion)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateLog indicates an expected call of UpdateLog.
func (mr *MockJournalServiceMockRecorder) UpdateLog(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateLog", reflect.TypeOf((*MockJournalService)(nil).UpdateLog), arg0, arg1)
}

// UpsertFeedback mocks base method.
func (m *MockJournalService) UpsertFeedback(arg0 context.Context, arg1 service.CreateFeedbackOption) (*entity.Feedback, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertFeedback", arg0, arg1)
	ret0, _ := ret[0].(*entity.Feedback)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertFeedback indicates an expected call of UpsertFeedback.
func (mr *MockJournalServiceMockRecorder) UpsertFeedback(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertFeedback", reflect.TypeOf((*MockJournalService)(nil).UpsertFeedback), arg0, arg1)
}
