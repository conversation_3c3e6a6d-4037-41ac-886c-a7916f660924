package github

import (
	"code.byted.org/gopkg/logs/v2"
	"context"
	"fmt"
	"github.com/pkg/errors"
	"net/http"
	"time"
)

type TokenDetail struct {
	ID       int32     `json:"id"`
	URL      string    `json:"url"`
	Scope    []string  `json:"scopes"`
	UpdateAt time.Time `json:"updated_at"`
	CreateAt time.Time `json:"created_at"`
	ExpireAt time.Time `json:"expires_at"`
	App      struct {
		Name string `json:"name"`
		URL  string `json:"url"`
	} `json:"app"`
	User struct {
		Login     string `json:"login"`
		ID        int32  `json:"id"`
		NodeID    string `json:"node_id"`
		AvatarURL string `json:"avatar_url"`
		URL       string `json:"url"`
		Type      string `json:"type"`
	} `json:"user"`
}

func (c *githubClient) GetToken(ctx context.Context, accessToken string) (*TokenDetail, error) {
	req, err := c.newRequest(ctx, http.MethodPost, fmt.Sprintf("applications/%s/token", c.clientID), &AuthInfo{
		AuthType: "basic",
	}, map[string]interface{}{
		"access_token": accessToken,
	}, nil)
	if err != nil {
		logs.CtxInfo(ctx, "GetToken[:6]: err = %v", err)
		return nil, err
	}

	var result = TokenDetail{}
	_, err = c.httpCall(ctx, req, &result, "GetToken")
	if err != nil {
		logs.CtxInfo(ctx, "GetToken[:12]: err = %v", err)
		if respErr, is := err.(*ErrorResponse); is && respErr.GetHTTPCode() == http.StatusNotFound {
			return nil, errors.New(ErrCodeGithubTokenInvalid.MessageFormat)
		}
		return nil, ConvGithubError(ctx, err)
	}

	return &result, nil
}

func (c *githubClient) RevokeAuth(ctx context.Context, accessToken string) (*TokenDetail, error) {
	req, err := c.newRequest(ctx, http.MethodDelete, fmt.Sprintf("/applications/%s/grant", c.clientID), &AuthInfo{
		AuthType: AuthTypeBasic,
	}, map[string]interface{}{
		"access_token": accessToken,
	}, nil)
	if err != nil {
		logs.CtxInfo(ctx, "RevokeAuth[:6]: err = %v", err)
		return nil, err
	}

	var result = TokenDetail{}
	_, err = c.httpCall(ctx, req, &result, "RevokeAuth")
	if err != nil {
		logs.CtxInfo(ctx, "RevokeAuth[:12]: err = %v", err)
		return nil, ConvGithubError(ctx, err)
	}

	return &result, nil
}

type Token struct {
	AccessToken           string `json:"access_token"`
	RefreshToken          string `json:"refresh_token"`
	TokenType             string `json:"token_type"`
	Scope                 string `json:"scope"`
	TokenExpiresIn        int64  `json:"expires_in"`               // access_token 过期前需经历的秒数
	RefreshTokenExpiresIn int64  `json:"refresh_token_expires_in"` // refresh_token 过期前需经历的秒数
	Error                 string `json:"error"`
	ErrorDescription      string `json:"error_description"`
	ErrorURI              string `json:"error_uri"`
}

func (c *githubClient) ExchangeAccessToken(ctx context.Context, code string) (*Token, error) {
	req, err := c.newRequest(ctx, http.MethodPost, "login/oauth/access_token", nil, map[string]interface{}{
		"client_id":     c.clientID,
		"client_secret": c.clientSecret,
		"code":          code,
	}, nil)
	if err != nil {
		logs.CtxInfo(ctx, "ExchangeAccessToken[:6]: code = %+v, err = %v", code, err)
		return nil, err
	}

	var result = Token{}
	_, err = c.httpCall(ctx, req, &result, "ExchangeAccessToken")
	if err != nil {
		logs.CtxInfo(ctx, "ExchangeAccessToken[:12]: code = %+v, err = %v", code, err)
		return nil, ConvGithubError(ctx, err)
	}

	if result.Error != "" {
		err := fmt.Errorf("access token error %s: %s: %s", result.Error, result.ErrorDescription, result.ErrorURI)
		if githubTokenInvalidErrMsgs[result.Error] {
			return nil, errors.New(ErrCodeGithubTokenInvalid.MessageFormat)
		}
		return nil, err
	}
	return &result, nil
}

const (
	// RefreshToken 有点特殊，一旦调用失败重试也没用，用户只能走重新授权，所以超时时间特意设置长一点
	refreshTokenTimeout = 40 * time.Second
)

func (c *githubClient) RefreshAccessToken(ctx context.Context, refreshToken string) (*Token, error) {
	req, err := c.newRequest(ctx, http.MethodPost, "login/oauth/access_token", nil, map[string]interface{}{
		"client_id":     c.clientID,
		"client_secret": c.clientSecret,
		"grant_type":    "refresh_token",
		"refresh_token": refreshToken,
	}, nil)
	if err != nil {
		logs.CtxInfo(ctx, "RefreshAccessToken[:7]: err = %v", err)
		return nil, err
	}

	var result = Token{}
	_, err = c.httpCall(ctx, req, &result, "RefreshAccessToken", refreshTokenTimeout)
	if err != nil {
		logs.CtxInfo(ctx, "RefreshAccessToken[:13]: err = %v", err)
		return nil, ConvGithubError(ctx, err)
	}

	if result.Error != "" {
		err := fmt.Errorf("refresh access token error %s: %s: %s", result.Error, result.ErrorDescription, result.ErrorURI)
		if githubTokenInvalidErrMsgs[result.Error] {
			return nil, errors.New(ErrCodeGithubTokenInvalid.MessageFormat)
		}
		return nil, err
	}

	return &result, nil
}

var githubTokenInvalidErrMsgs = map[string]bool{
	"bad_refresh_token":     true,
	"bad_verification_code": true,
}
