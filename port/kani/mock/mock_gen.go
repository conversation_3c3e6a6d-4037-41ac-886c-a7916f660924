// Code generated by MockGen. DO NOT EDIT.
// Source: code.byted.org/devgpt/kiwis/port/kani (interfaces: Client)
//
// Generated by this command:
//
//	mockgen -destination mock/mock_gen.go -typed -package mockkani . Client
//

// Package mockkani is a generated GoMock package.
package mockkani

import (
	context "context"
	reflect "reflect"

	kani "code.byted.org/devgpt/kiwis/port/kani"
	gomock "go.uber.org/mock/gomock"
)

// MockClient is a mock of Client interface.
type MockClient struct {
	ctrl     *gomock.Controller
	recorder *MockClientMockRecorder
}

// MockClientMockRecorder is the mock recorder for MockClient.
type MockClientMockRecorder struct {
	mock *MockClient
}

// NewMockClient creates a new mock instance.
func NewMockClient(ctrl *gomock.Controller) *MockClient {
	mock := &MockClient{ctrl: ctrl}
	mock.recorder = &MockClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClient) EXPECT() *MockClientMockRecorder {
	return m.recorder
}

// BindPermission mocks base method.
func (m *MockClient) BindPermission(arg0 context.Context, arg1 *kani.PermissionBindRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BindPermission", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BindPermission indicates an expected call of BindPermission.
func (mr *MockClientMockRecorder) BindPermission(arg0, arg1 any) *MockClientBindPermissionCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BindPermission", reflect.TypeOf((*MockClient)(nil).BindPermission), arg0, arg1)
	return &MockClientBindPermissionCall{Call: call}
}

// MockClientBindPermissionCall wrap *gomock.Call
type MockClientBindPermissionCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientBindPermissionCall) Return(arg0 error) *MockClientBindPermissionCall {
	c.Call = c.Call.Return(arg0)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientBindPermissionCall) Do(f func(context.Context, *kani.PermissionBindRequest) error) *MockClientBindPermissionCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientBindPermissionCall) DoAndReturn(f func(context.Context, *kani.PermissionBindRequest) error) *MockClientBindPermissionCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// CreateResource mocks base method.
func (m *MockClient) CreateResource(arg0 context.Context, arg1 *kani.CreateResourceRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateResource", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateResource indicates an expected call of CreateResource.
func (mr *MockClientMockRecorder) CreateResource(arg0, arg1 any) *MockClientCreateResourceCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateResource", reflect.TypeOf((*MockClient)(nil).CreateResource), arg0, arg1)
	return &MockClientCreateResourceCall{Call: call}
}

// MockClientCreateResourceCall wrap *gomock.Call
type MockClientCreateResourceCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientCreateResourceCall) Return(arg0 error) *MockClientCreateResourceCall {
	c.Call = c.Call.Return(arg0)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientCreateResourceCall) Do(f func(context.Context, *kani.CreateResourceRequest) error) *MockClientCreateResourceCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientCreateResourceCall) DoAndReturn(f func(context.Context, *kani.CreateResourceRequest) error) *MockClientCreateResourceCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// CreateResourcePermissions mocks base method.
func (m *MockClient) CreateResourcePermissions(arg0 context.Context, arg1 *kani.CreatePermissionsRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateResourcePermissions", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateResourcePermissions indicates an expected call of CreateResourcePermissions.
func (mr *MockClientMockRecorder) CreateResourcePermissions(arg0, arg1 any) *MockClientCreateResourcePermissionsCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateResourcePermissions", reflect.TypeOf((*MockClient)(nil).CreateResourcePermissions), arg0, arg1)
	return &MockClientCreateResourcePermissionsCall{Call: call}
}

// MockClientCreateResourcePermissionsCall wrap *gomock.Call
type MockClientCreateResourcePermissionsCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientCreateResourcePermissionsCall) Return(arg0 error) *MockClientCreateResourcePermissionsCall {
	c.Call = c.Call.Return(arg0)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientCreateResourcePermissionsCall) Do(f func(context.Context, *kani.CreatePermissionsRequest) error) *MockClientCreateResourcePermissionsCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientCreateResourcePermissionsCall) DoAndReturn(f func(context.Context, *kani.CreatePermissionsRequest) error) *MockClientCreateResourcePermissionsCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// DeleteResource mocks base method.
func (m *MockClient) DeleteResource(arg0 context.Context, arg1 *kani.ResourcesDeleteRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteResource", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteResource indicates an expected call of DeleteResource.
func (mr *MockClientMockRecorder) DeleteResource(arg0, arg1 any) *MockClientDeleteResourceCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteResource", reflect.TypeOf((*MockClient)(nil).DeleteResource), arg0, arg1)
	return &MockClientDeleteResourceCall{Call: call}
}

// MockClientDeleteResourceCall wrap *gomock.Call
type MockClientDeleteResourceCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientDeleteResourceCall) Return(arg0 error) *MockClientDeleteResourceCall {
	c.Call = c.Call.Return(arg0)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientDeleteResourceCall) Do(f func(context.Context, *kani.ResourcesDeleteRequest) error) *MockClientDeleteResourceCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientDeleteResourceCall) DoAndReturn(f func(context.Context, *kani.ResourcesDeleteRequest) error) *MockClientDeleteResourceCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// ReadIdentityResourcesPermission mocks base method.
func (m *MockClient) ReadIdentityResourcesPermission(arg0 context.Context, arg1 *kani.ReadIdentityResourcesPermissionsRequest) (*kani.ReadIdentityResourcesPermissionsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReadIdentityResourcesPermission", arg0, arg1)
	ret0, _ := ret[0].(*kani.ReadIdentityResourcesPermissionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReadIdentityResourcesPermission indicates an expected call of ReadIdentityResourcesPermission.
func (mr *MockClientMockRecorder) ReadIdentityResourcesPermission(arg0, arg1 any) *MockClientReadIdentityResourcesPermissionCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReadIdentityResourcesPermission", reflect.TypeOf((*MockClient)(nil).ReadIdentityResourcesPermission), arg0, arg1)
	return &MockClientReadIdentityResourcesPermissionCall{Call: call}
}

// MockClientReadIdentityResourcesPermissionCall wrap *gomock.Call
type MockClientReadIdentityResourcesPermissionCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientReadIdentityResourcesPermissionCall) Return(arg0 *kani.ReadIdentityResourcesPermissionsResponse, arg1 error) *MockClientReadIdentityResourcesPermissionCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientReadIdentityResourcesPermissionCall) Do(f func(context.Context, *kani.ReadIdentityResourcesPermissionsRequest) (*kani.ReadIdentityResourcesPermissionsResponse, error)) *MockClientReadIdentityResourcesPermissionCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientReadIdentityResourcesPermissionCall) DoAndReturn(f func(context.Context, *kani.ReadIdentityResourcesPermissionsRequest) (*kani.ReadIdentityResourcesPermissionsResponse, error)) *MockClientReadIdentityResourcesPermissionCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// ReadResource mocks base method.
func (m *MockClient) ReadResource(arg0 context.Context, arg1 *kani.ReadResourceRequest) (*kani.ReadResourceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReadResource", arg0, arg1)
	ret0, _ := ret[0].(*kani.ReadResourceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReadResource indicates an expected call of ReadResource.
func (mr *MockClientMockRecorder) ReadResource(arg0, arg1 any) *MockClientReadResourceCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReadResource", reflect.TypeOf((*MockClient)(nil).ReadResource), arg0, arg1)
	return &MockClientReadResourceCall{Call: call}
}

// MockClientReadResourceCall wrap *gomock.Call
type MockClientReadResourceCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientReadResourceCall) Return(arg0 *kani.ReadResourceResponse, arg1 error) *MockClientReadResourceCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientReadResourceCall) Do(f func(context.Context, *kani.ReadResourceRequest) (*kani.ReadResourceResponse, error)) *MockClientReadResourceCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientReadResourceCall) DoAndReturn(f func(context.Context, *kani.ReadResourceRequest) (*kani.ReadResourceResponse, error)) *MockClientReadResourceCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// ResourceRelatedMembers mocks base method.
func (m *MockClient) ResourceRelatedMembers(arg0 context.Context, arg1 *kani.ResourceRelatedMembersRequest) (*kani.ResourceRelatedMembersResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResourceRelatedMembers", arg0, arg1)
	ret0, _ := ret[0].(*kani.ResourceRelatedMembersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResourceRelatedMembers indicates an expected call of ResourceRelatedMembers.
func (mr *MockClientMockRecorder) ResourceRelatedMembers(arg0, arg1 any) *MockClientResourceRelatedMembersCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResourceRelatedMembers", reflect.TypeOf((*MockClient)(nil).ResourceRelatedMembers), arg0, arg1)
	return &MockClientResourceRelatedMembersCall{Call: call}
}

// MockClientResourceRelatedMembersCall wrap *gomock.Call
type MockClientResourceRelatedMembersCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientResourceRelatedMembersCall) Return(arg0 *kani.ResourceRelatedMembersResponse, arg1 error) *MockClientResourceRelatedMembersCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientResourceRelatedMembersCall) Do(f func(context.Context, *kani.ResourceRelatedMembersRequest) (*kani.ResourceRelatedMembersResponse, error)) *MockClientResourceRelatedMembersCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientResourceRelatedMembersCall) DoAndReturn(f func(context.Context, *kani.ResourceRelatedMembersRequest) (*kani.ResourceRelatedMembersResponse, error)) *MockClientResourceRelatedMembersCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// UnbindPermission mocks base method.
func (m *MockClient) UnbindPermission(arg0 context.Context, arg1 *kani.PermissionUnbindRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnbindPermission", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnbindPermission indicates an expected call of UnbindPermission.
func (mr *MockClientMockRecorder) UnbindPermission(arg0, arg1 any) *MockClientUnbindPermissionCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnbindPermission", reflect.TypeOf((*MockClient)(nil).UnbindPermission), arg0, arg1)
	return &MockClientUnbindPermissionCall{Call: call}
}

// MockClientUnbindPermissionCall wrap *gomock.Call
type MockClientUnbindPermissionCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientUnbindPermissionCall) Return(arg0 error) *MockClientUnbindPermissionCall {
	c.Call = c.Call.Return(arg0)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientUnbindPermissionCall) Do(f func(context.Context, *kani.PermissionUnbindRequest) error) *MockClientUnbindPermissionCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientUnbindPermissionCall) DoAndReturn(f func(context.Context, *kani.PermissionUnbindRequest) error) *MockClientUnbindPermissionCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// UpdateResource mocks base method.
func (m *MockClient) UpdateResource(arg0 context.Context, arg1 *kani.ResourcesUpdateRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateResource", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateResource indicates an expected call of UpdateResource.
func (mr *MockClientMockRecorder) UpdateResource(arg0, arg1 any) *MockClientUpdateResourceCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateResource", reflect.TypeOf((*MockClient)(nil).UpdateResource), arg0, arg1)
	return &MockClientUpdateResourceCall{Call: call}
}

// MockClientUpdateResourceCall wrap *gomock.Call
type MockClientUpdateResourceCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientUpdateResourceCall) Return(arg0 error) *MockClientUpdateResourceCall {
	c.Call = c.Call.Return(arg0)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientUpdateResourceCall) Do(f func(context.Context, *kani.ResourcesUpdateRequest) error) *MockClientUpdateResourceCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientUpdateResourceCall) DoAndReturn(f func(context.Context, *kani.ResourcesUpdateRequest) error) *MockClientUpdateResourceCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}
