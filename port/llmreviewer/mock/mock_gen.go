// Code generated by MockGen. DO NOT EDIT.
// Source: code.byted.org/devgpt/kiwis/port/llmreviewer (interfaces: Client)
//
// Generated by this command:
//
//	mockgen -destination mock/mock_gen.go -package llmreviewer . Client
//

// Package llmreviewer is a generated GoMock package.
package llmreviewer

import (
	context "context"
	reflect "reflect"

	llmreviewer "code.byted.org/devgpt/kiwis/port/llmreviewer"
	gomock "go.uber.org/mock/gomock"
)

// MockClient is a mock of Client interface.
type MockClient struct {
	ctrl     *gomock.Controller
	recorder *MockClientMockRecorder
}

// MockClientMockRecorder is the mock recorder for MockClient.
type MockClientMockRecorder struct {
	mock *MockClient
}

// NewMockClient creates a new mock instance.
func NewMockClient(ctrl *gomock.Controller) *MockClient {
	mock := &MockClient{ctrl: ctrl}
	mock.recorder = &MockClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClient) EXPECT() *MockClientMockRecorder {
	return m.recorder
}

// GetCodeReviewComment mocks base method.
func (m *MockClient) GetCodeReviewComment(arg0 context.Context, arg1 llmreviewer.Task) (*llmreviewer.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCodeReviewComment", arg0, arg1)
	ret0, _ := ret[0].(*llmreviewer.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCodeReviewComment indicates an expected call of GetCodeReviewComment.
func (mr *MockClientMockRecorder) GetCodeReviewComment(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCodeReviewComment", reflect.TypeOf((*MockClient)(nil).GetCodeReviewComment), arg0, arg1)
}
