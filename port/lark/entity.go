package lark

import "time"

type UserToken struct {
	AccessToken  string
	RefreshToken string

	AccessExpireAt  time.Time
	RefreshExpireAt time.Time
	Scope           string
}

type ExportTaskJobStatus int

const (
	ExportTaskJobStatusSucceed ExportTaskJobStatus = 0
	ExportTaskJobStatusInit    ExportTaskJobStatus = 1
	ExportTaskJobStatusRunning ExportTaskJobStatus = 2
)

type SheetData struct {
	Revision         int                `json:"revision"`
	SpreadsheetToken string             `json:"spreadsheetToken"`
	TotalCells       int                `json:"totalCells"`
	ValueRanges      []*SheetValueRange `json:"valueRanges"`
}

type SheetValueRange struct {
	MajorDimension *string `json:"majorDimension,omitempty"` //读取数据时存在，写入数据时不存在
	Range          string  `json:"range"`
	Revision       *int    `json:"revision,omitempty"` //读取数据时存在，写入数据时不存在
	Values         [][]any `json:"values"`
}

type ReceiveIDType string

const (
	ReceiveIDTypeOpenID  ReceiveIDType = "open_id"
	ReceiveIDTypeUserID  ReceiveIDType = "user_id"
	ReceiveIDTypeUnionID ReceiveIDType = "union_id"
	ReceiveIDTypeEmail   ReceiveIDType = "email"
	ReceiveIDTypeChatID  ReceiveIDType = "chat_id"
)

type UserIDType string

const (
	UserIDTypeOpenID  UserIDType = "open_id"
	UserIDTypeUnionID UserIDType = "union_id"
	UserIDTypeUserID  UserIDType = "user_id"
)

// RootFolderMeta represents the metadata of the root folder
type RootFolderMeta struct {
	Token  string `json:"token"`
	ID     string `json:"id"`
	UserID string `json:"user_id"`
}

type JSApiTicket struct {
	Ticket   string `json:"ticket"`
	ExpireIn int32  `json:"expire_in"`
}

type SheetMarkdownData struct {
	ValueRanges []*ValueRange `json:"value_ranges"`
}

type ValueRange struct {
	Range  string          `json:"range"`
	Values [][][]SheetCell `json:"values"`
}

type Error struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

type AddImageCellForLarkSheetData struct {
	Revision         int    `json:"revision"`
	SpreadsheetToken string `json:"spreadsheetToken"`
	UpdateRange      string `json:"updateRange"`
}
type AddImageCellForLarkSheetResp struct {
	Error
	Data *AddImageCellForLarkSheetData `json:"data"`
}

type AddRowForLarkSheetUpdates struct {
	Revision         int    `json:"revision"`
	SpreadsheetToken string `json:"spreadsheetToken"`
	UpdatedCells     int    `json:"updatedCells"`
	UpdatedColumns   int    `json:"updatedColumns"`
	UpdatedRange     string `json:"updatedRange"`
	UpdatedRows      int    `json:"updatedRows"`
}

type AddRowForLarkSheetData struct {
	Revision         int                       `json:"revision"`
	SpreadsheetToken string                    `json:"spreadsheetToken"`
	TableRange       string                    `json:"tableRange"`
	Updates          AddRowForLarkSheetUpdates `json:"updates"`
}
type AddRowForLarkSheetResp struct {
	Error
	Data *AddRowForLarkSheetData `json:"data"`
}

// BatchUpdateSheetValuesRequest 批量更新表格值的请求体
type BatchUpdateSheetValuesRequest struct {
	ValueRanges []*ValueRange `json:"value_ranges"`
}

// BatchUpdateSheetValuesRespData 批量更新表格值的响应数据
type BatchUpdateSheetValuesRespData struct {
	Revision         int      `json:"revision"`
	SpreadsheetToken string   `json:"spreadsheet_token"`
	UpdatedCells     int      `json:"updated_cells"`
	UpdatedColumns   int      `json:"updated_columns"`
	UpdatedRows      int      `json:"updated_rows"`
	UpdatedRanges    []string `json:"updated_ranges"`
}
