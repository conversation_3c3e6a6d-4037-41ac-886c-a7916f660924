package lark

import (
	"context"
	"io"

	larkbaike "github.com/larksuite/oapi-sdk-go/v3/service/baike/v1"
	larkbitable "github.com/larksuite/oapi-sdk-go/v3/service/bitable/v1"
	larkboard "github.com/larksuite/oapi-sdk-go/v3/service/board/v1"
	larkcontact "github.com/larksuite/oapi-sdk-go/v3/service/contact/v3"
	larkdocx "github.com/larksuite/oapi-sdk-go/v3/service/docx/v1"
	larkdrive "github.com/larksuite/oapi-sdk-go/v3/service/drive/v1"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	larklingo "github.com/larksuite/oapi-sdk-go/v3/service/lingo/v1"
	larksheets "github.com/larksuite/oapi-sdk-go/v3/service/sheets/v3"
	larkwiki "github.com/larksuite/oapi-sdk-go/v3/service/wiki/v2"
)

type Option struct {
	UserAccessToken string
	UserIDType      UserIDType
}

//go:generate go run go.uber.org/mock/mockgen -destination mock/lark_mock_gen.go -typed -package  lark . Client
type Client interface {
	GetLarkDocBlock(ctx context.Context, documentID, operator string, option ...Option) (*DocContent, error)
	GetLarkDocxRawContent(ctx context.Context, documentID, larkAccessToken string) (*DocxContent, error)
	GetLarkDocxBlock(ctx context.Context, documentID, operator string, options ...Option) ([]*larkdocx.Block, error)
	ListWikiNodes(ctx context.Context, spaceID, parentNodeToken, operator string) ([]*larkwiki.Node, error)
	GetWikiNode(ctx context.Context, nodeToken, operator string, options ...Option) (*larkwiki.Node, error)
	GetLarkDocx(ctx context.Context, documentID, operator string, option ...Option) (*larkdocx.GetDocumentRespData, error)
	GetLarkDoc(ctx context.Context, documentID, operator string, option ...Option) (*DocMeta, error)
	BatchUpdateDocxBlocks(ctx context.Context, documentID string, requests []*larkdocx.UpdateBlockRequest, larkAccessToken string) (*larkdocx.BatchUpdateDocumentBlockRespData, error)
	BatchDeleteDocxBlockChildren(ctx context.Context, documentID, blockID string, startIndex, endIndex int, larkAccessToken string) (*larkdocx.BatchDeleteDocumentBlockChildrenRespData, error)
	GetDocMeta(ctx context.Context, docs []*RequestDoc, operator string, option ...Option) (*larkdrive.BatchQueryMetaRespData, error)
	GetRootFolderMeta(ctx context.Context, operator string) (*RootFolderMeta, error)
	GetLarkBaikePhrases(ctx context.Context, text string) ([]*larkbaike.Phrase, error)
	GetLarkEntityDesc(ctx context.Context, entityIDs []string) ([]*EntityInfo, error)
	GetLarkEntity(ctx context.Context, entityID string) (*larklingo.Entity, error)
	ListLarkUsers(ctx context.Context, userIDs []string, userIDType string, options ...Option) ([]*larkcontact.User, error)
	SearchLarkData(ctx context.Context, topK int, query, userToken string, disableSearch *PassageDisableSearch) (*SearchMeta, error)
	SearchLarkObject(ctx context.Context, req *SearchLarkObjectRequest, userToken string) (resp *SearchLarkObjectRespData, err error)
	SearchWikiNode(ctx context.Context, req *SearchWikiNodeRequest, userToken string) (resp *SearchWikiNodeData, err error)
	GetUserAccessToken(ctx context.Context, code string) (userToken UserToken, err error)
	GetJSApiTicket(ctx context.Context, larkAccessToken string) (ticket JSApiTicket, err error)
	RefreshToken(ctx context.Context, refreshToken string) (userToken UserToken, err error)
	UploadLarkFile(ctx context.Context, fileName, parentNode string, size int, file io.Reader) (*string, error)
	CreateExportTasks(ctx context.Context, fileExtension, fileToken, fileType, subID, larkAccessToken string) (*string, error)
	GetExportTask(ctx context.Context, fileToken, taskID, larkAccessToken string) (bool, string, string, error)
	DownLoadExportFile(ctx context.Context, fileToken, larkAccessToken string) (*larkdrive.DownloadExportTaskResp, error)
	AddLarkFilePermission(ctx context.Context, fileToken, email, perm, fileType string) error
	SendLarkApplicationMessage(ctx context.Context, receiveIDType ReceiveIDType, receiverEmail, msgType, content string) (data *larkim.CreateMessageRespData, err error)
	SendLarkApplicationReplyMessage(ctx context.Context, replayID string, msgType, content string) (data *larkim.ReplyMessageRespData, err error)
	GetWikiNodeInfo(ctx context.Context, fileToken, larkAccessToken string) (*larkwiki.GetNodeSpaceResp, error)
	GetSheetsSubIDs(ctx context.Context, fileToken, larkAccessToken string) (*larksheets.QuerySpreadsheetSheetResp, error)
	AddRowForLarkSheet(ctx context.Context, spreadsheetToken string, valueRange string, values [][]interface{}) (*AddRowForLarkSheetResp, error)
	AddImageCellForLarkSheet(ctx context.Context, spreadsheetToken string, valueRange string, image []byte, name string) (*AddImageCellForLarkSheetResp, error)
	ReadBatchLarkSheet(ctx context.Context, fileToken, valueRanges string, larkAccessToken string) (*SheetData, error)
	WriteBatchLarkSheet(ctx context.Context, fileToken string, valueRanges []*SheetValueRange) error
	MGetUserIDByEmail(ctx context.Context, emails []string, idType UserIDType) (map[string]string, error)
	CreateLarkDocx(ctx context.Context, folderToken, title, larkAccessToken string) (*larkdocx.CreateDocumentRespData, error)
	UploadLarkMedia(ctx context.Context, body *larkdrive.UploadAllMediaReqBody, larkAccessToken string) (*string, error)
	InsertDocxBlockDescendants(ctx context.Context, documentID, blockID string, index int, childrenID []string, descendants []*larkdocx.Block, larkAccessToken string) (*DocxBlockDescendantsResponse, error)
	CreateImportTask(ctx context.Context, req *larkdrive.CreateImportTaskReq, larkAccessToken string) (*string, error)
	GetImportTask(ctx context.Context, taskID, larkAccessToken string) (*larkdrive.GetImportTaskRespData, error)
	TransferOwnerPermissionMember(ctx context.Context, req *larkdrive.TransferOwnerPermissionMemberReq, larkAccessToken string) (string, error)
	ListPermissionMember(ctx context.Context, req *larkdrive.ListPermissionMemberReq, larkAccessToken string) (*larkdrive.ListPermissionMemberRespData, error)
	GetLarkFileStatisticsInfo(ctx context.Context, fileToken, fileType, token string, option ...Option) (FileStatistics, error)
	GetFilesMeta(ctx context.Context, docs []*RequestDoc, token string, option ...Option) (response *larkdrive.BatchQueryMetaRespData, err error)
	DownloadWhiteboardAsImage(ctx context.Context, whiteboardID string, larkAccessToken string) (*larkboard.DownloadAsImageWhiteboardResp, error)
	DownloadLarkMedia(ctx context.Context, fileToken, larkAccessToken string) (*larkdrive.DownloadMediaResp, error)
	CreateFolder(ctx context.Context, name, folderToken string, option ...Option) (*larkdrive.CreateFolderFileRespData, error)
	ListFiles(ctx context.Context, folderToken, pageToken string, pageSize int, orderBy, direction string, option ...Option) (*larkdrive.ListFileRespData, error)
	CreateSheet(ctx context.Context, title string, folder string) (response *larksheets.CreateSpreadsheetRespData, err error)
	GetLarkDocxBlocks(ctx context.Context, documentID string, larkAccessToken string) (resp []*larkdocx.Block, err error)
	GetLarkDocComments(ctx context.Context, solved bool, fileToken, fileType string, larkAccessToken string) (resp []*larkdrive.FileComment, err error)
	QuerySheet(ctx context.Context, token string, accessToken string) (response *larksheets.QuerySpreadsheetSheetRespData, err error)
	ListBitableAppTable(ctx context.Context, appToken string, pageToken string, pageSize int, accessToken string) (response *larkbitable.ListAppTableRespData, err error)
	SearchBitableRecord(ctx context.Context, appToken string, tableID string, userIDType string, pageToken string, pageSize int, accessToken string) (response *larkbitable.SearchAppTableRecordRespData, err error)
	ListAppTableField(ctx context.Context, appToken string, tableID string, pageToken string, pageSize int, accessToken string) (response *larkbitable.ListAppTableFieldRespData, err error)
	GetSheetMarkdownContent(ctx context.Context, spreadsheetToken string, sheetID string, ranges []string, accessToken string) (response *SheetMarkdownData, err error)
	GetMessageResource(ctx context.Context, messageID string, fileKey string, fileType string) (data *larkim.GetMessageResourceResp, err error)
	BatchUpdateSheetValues(ctx context.Context, spreadsheetToken, sheetID string, valueRanges []*ValueRange, userIDType string, larkAccessToken string) (*BatchUpdateSheetValuesRespData, error)
}
