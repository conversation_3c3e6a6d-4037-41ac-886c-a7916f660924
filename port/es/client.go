// Package es provides connection to elasticsearch clusters.
package es

import (
	"context"
	"encoding/json"
	"time"

	"code.byted.org/toutiao/elastic/v7"

	"code.byted.org/devgpt/kiwis/lib/dsn"
)

// Client defines the methods from elastic.Client for testing mock.
//
// Methods are not completed, add more methods if you need.
type Client interface {
	IsRunning() bool
	PerformRequest(ctx context.Context, opt elastic.PerformRequestOptions) (*elastic.Response, error)
	Index() *elastic.IndexService
	Get() *elastic.GetService
	MultiGet() *elastic.MgetService
	Mget() *elastic.MgetService
	Delete() *elastic.DeleteService
	DeleteByQuery(indices ...string) *elastic.DeleteByQueryService
	Update() *elastic.UpdateService
	UpdateByQuery(indices ...string) *elastic.UpdateByQueryService
	Bulk() *elastic.BulkService
	BulkProcessor() *elastic.BulkProcessorService
	Reindex() *elastic.ReindexService
	Search(indices ...string) *elastic.SearchService
	MultiSearch() *elastic.MultiSearchService
	Count(indices ...string) *elastic.CountService
	Exists() *elastic.ExistsService
	Scroll(indices ...string) *elastic.ScrollService
	ClearScroll(scrollIds ...string) *elastic.ClearScrollService
	OpenPointInTime(indices ...string) *elastic.OpenPointInTimeService
	ClosePointInTime(id string) *elastic.ClosePointInTimeService
	IndexAnalyze() *elastic.IndicesAnalyzeService
	Refresh(indices ...string) *elastic.RefreshService
	Flush(indices ...string) *elastic.IndicesFlushService
	SyncedFlush(indices ...string) *elastic.IndicesSyncedFlushService
	ClearCache(indices ...string) *elastic.IndicesClearCacheService
	Alias() *elastic.AliasService
	Aliases() *elastic.AliasesService
	TasksCancel() *elastic.TasksCancelService
	TasksList() *elastic.TasksListService
	TasksGetTask() *elastic.TasksGetTaskService
	ElasticsearchVersion(url string) (string, error)
	IndexNames() ([]string, error)
	Ping(url string) *elastic.PingService
	CreateIndex(name string) *elastic.IndicesCreateService
	DeleteIndex(indices ...string) *elastic.IndicesDeleteService
	IndexExists(indices ...string) *elastic.IndicesExistsService
	Start()
	Stop()
}

// client implements Client.
type client struct {
	*elastic.Client
}

const (
	// DefaultAPITimeout is the default elasticsearch API timeout value.
	DefaultAPITimeout = time.Second
)

// NewClientOpt contains parameters to create an elasticsearch client.
type NewClientOpt struct {
	// Username and Password are required if GDPR authentication is disabled for the cluster.
	Username string
	Password string
	// PSM and Cluster are required if we are using ByteES clusters.
	PSM     string
	Cluster string
	// URL is required if we are connecting to elasticsearch clusters directly.
	URL     string
	Timeout time.Duration
	// NOTICE: only support psm client.
	ConTimeout time.Duration
	// Secure indicates if enable https for API requests.
	Secure bool
}

func NewClientWithDSN(conf *dsn.Config) (Client, error) {
	opt := NewClientOpt{
		Username: conf.Username,
		Password: conf.Password,
		PSM:      "",
		Cluster:  conf.Params.ESCluster,
		URL:      "",
		Timeout:  conf.Params.Timeout,
		Secure:   conf.Params.ESSecure,
	}

	switch conf.Net {
	case dsn.NetSD:
		opt.PSM = conf.Host
	case dsn.NetTcp:
		opt.URL = conf.Addr
	}

	return NewClient(opt)
}

// NewClient creates an elasticsearch client according to the given options.
func NewClient(opt NewClientOpt) (Client, error) {
	if opt.Timeout == 0 {
		opt.Timeout = DefaultAPITimeout
	}

	var opts []elastic.ClientOptionFunc
	if opt.Username != "" && opt.Password != "" {
		opts = append(opts, elastic.SetBasicAuth(opt.Username, opt.Password))
	}

	var (
		cli *elastic.Client
		err error
	)

	// Connect to elasticsearch clusters directly.
	if opt.URL != "" {
		if opt.Secure {
			opts = append(opts, elastic.SetScheme("https://"))
		}

		opts = append(opts, elastic.SetURL(opt.URL), elastic.SetSniff(false))
		cli, err = elastic.NewClient(opts...)
	} else {
		// Via consul/mesh.
		optInterfaces := make([]interface{}, 0, len(opts))
		for _, opt := range opts {
			optInterfaces = append(optInterfaces, opt)
		}
		if opt.ConTimeout > 0 {
			optInterfaces = append(optInterfaces, elastic.WithConTimeout(opt.ConTimeout))
		}
		if opt.Secure {
			cli, err = elastic.NewPSMHTTPSClient(opt.PSM, opt.Cluster, opt.Timeout, optInterfaces...)
		} else {
			cli, err = elastic.NewPSMClient(opt.PSM, opt.Cluster, opt.Timeout, optInterfaces...)
		}
	}

	if err != nil {
		return nil, err
	}
	return &client{Client: cli}, nil
}

func NewKNNQuery(field string, vectors []float64, k int) elastic.Query {
	data, _ := json.Marshal(map[string]any{
		"knn": map[string]any{
			field: map[string]any{
				"vector": vectors,
				"k":      k,
			},
		},
	})

	return elastic.NewSafeRawStringQuery(string(data))
}
