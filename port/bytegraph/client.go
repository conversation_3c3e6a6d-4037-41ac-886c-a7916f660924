package bytegraph

import (
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/inf/bytegraph_gremlin_go/driver"
	"code.byted.org/inf/bytegraph_gremlin_go/structure"
	"context"
)

//go:generate go run go.uber.org/mock/mockgen -destination mock/bytegraph_mock_gen.go -package bytegraph . Client
type Client interface {
	BatchOperate(ctx context.Context, query []driver.GraphTraversal) ([]structure.Element, []error)
	Search(ctx context.Context, query driver.GraphTraversal, binding driver.Binding) (structure.Element, error)
}

func NewClient(conf config.ByteGraphConfig) (Client, error) {
	return NewByteGraphClient(conf)
}
