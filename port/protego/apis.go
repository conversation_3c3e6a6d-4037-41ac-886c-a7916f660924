package protego

import (
	"context"
	"net/http"
	"strings"
	"time"

	"code.byted.org/gopkg/logs/v2/log"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/lib/hertz"
)

// doTransaction does the batch policy entity request in transaction
// to prevent data from being in an intermediate state.
func (c *client) doTransaction(ctx context.Context, queries []*TransactionRequest, notes ...string) ([]*TransactionRequest, error) {
	req := Object{
		"queries": queries,
	}
	resp := new(struct {
		Data map[string][]*TransactionRequest `json:"data"`
	})

	_, err := c.cli.DoJSONReq(ctx, http.MethodPost, "/transaction",
		hertz.ReqOption{
			ExpectedCode: http.StatusOK,
			Body:         req,
			Result:       resp,
			Notes:        strings.Join(notes, "|"),
			Headers:      nil,
		})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to do transaction")
	}

	return resp.Data["queries"], nil
}

func (c *client) GetResource(ctx context.Context, key string) (*Resource, error) {
	resList, err := c.listResourcesByKeys(ctx, EntityTypeResourceObject, c.resourceNS, key)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to list resources")
	}

	resource, ok := resList[key]
	if !ok {
		return nil, errors.New("resource not found")
	}

	res, err := getResourceFromEntity(resource)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to parse entity")
	}

	return res, nil
}

func (c *client) listResourcesByKeys(ctx context.Context, typ EntityType, ns string, keys ...string) (map[string]*PolicyEntity, error) {
	req := &PolicyEntityRequest{
		EntityType: typ,
		Entities:   make([]*PolicyEntity, 0, len(keys)),
		Relation:   lo.ToPtr(RelationEquivalent),
	}
	for _, key := range keys {
		key = strings.ToLower(key)
		req.Entities = append(req.Entities, &PolicyEntity{
			Ns:         ns,
			PathV2:     NewKVs("key", key),
			LocationV2: c.location,
		})
	}

	resp := new(policyEntityResponse)

	_, err := c.cli.DoJSONReq(ctx, http.MethodPost, "/policy_entity/read", hertz.ReqOption{
		ExpectedCode: http.StatusOK,
		Body:         req,
		Result:       resp,
		Notes:        "ls_res",
		Headers:      nil,
	})
	if err != nil {
		return nil, err
	}

	res := make(map[string]*PolicyEntity, len(resp.Data.Entities))
	for _, entity := range resp.Data.Entities {
		if len(entity.PathV2) < 1 {
			continue
		}
		res[entity.PathV2[0].Val] = entity
	}

	return res, nil
}

type CreateResourceOption struct {
	Key           string
	Type          string
	Name          string
	EnName        string
	Description   string
	SecurityLevel string
	Region        string
	AdminURL      string
	ResourceURL   string
}

type CreateActionOption Action

func NewCreateAction(key, name string, isAutoApproval bool) CreateActionOption {
	return CreateActionOption{
		Ns:             "",
		Resource:       "",
		Key:            key,
		Name:           name,
		IsAutoApproval: isAutoApproval,
		Enabled:        false,
	}
}

func (c *client) CreateResource(ctx context.Context, opt CreateResourceOption, actions []CreateActionOption) (*Resource, error) {
	actionResources := make([]*PolicyEntity, len(actions))
	for idx, action := range actions {
		actionResources[idx] = &PolicyEntity{
			Ns: c.resourceNS,
			Attributes: Object{
				"name":             action.Name,
				"is_auto_approval": lo.Ternary(action.IsAutoApproval, 1, 0), // Only valid when the resource is L2.
			},
			PathV2:     NewKVs("resource", opt.Key, "action", action.Key),
			LocationV2: c.location,
			IsEnable:   lo.ToPtr(true),
		}
	}

	queries := []*TransactionRequest{
		{
			Operation:  OperationCreate,
			EntityType: lo.ToPtr(EntityTypeResourceObject),
			Entities: []*PolicyEntity{
				{
					Ns:       c.resourceNS,
					Metadata: []Object{},
					Attributes: Object{
						"admin_url":      opt.AdminURL,
						"description":    opt.Description,
						"en_name":        opt.EnName,
						"name":           opt.Name,
						"region_type":    0, // 0 for no region.
						"resource_url":   opt.ResourceURL,
						"security_level": getIntSecurityLevel(opt.SecurityLevel),
						"authorizable":   1,
					},
					PathV2:     NewKVs("key", opt.Key),
					LocationV2: c.location,
					IsEnable:   lo.ToPtr(true),
				},
			},
			ReturnCreatedResults: lo.ToPtr(true),
			UpdateOnExist:        lo.ToPtr(true),
			Relation:             lo.ToPtr(RelationEquivalent),
		},
		{
			Operation:            OperationCreate,
			EntityType:           lo.ToPtr(EntityTypeRoleAction),
			Entities:             actionResources,
			UpdateOnExist:        lo.ToPtr(true),
			ReturnCreatedResults: lo.ToPtr(false),
			Relation:             lo.ToPtr(RelationEquivalent),
		},
	}

	results, err := c.doTransaction(ctx, queries, "init_res")
	if err != nil {
		return nil, err
	}

	for _, res := range results {
		return getResourceFromEntity(res.Entities[0])
	}

	return nil, nil
}

func (c *client) ListResourcePolicies(ctx context.Context, keys ...string) (map[string][]*Policy, error) {
	req := &PolicyRequest{
		Policies: make([]*Policy, 0, len(keys)),
		Relation: lo.ToPtr(RelationEquivalent),
	}
	for _, key := range keys {
		if len(key) == 0 {
			continue
		}
		req.Policies = append(req.Policies, &Policy{
			Resource: &PolicyEntity{
				Ns:         c.resourceNS,
				PathV2:     NewKVs("key", key),
				LocationV2: c.location,
			},
			ResourceType: lo.ToPtr(EntityTypeResourceObject),
		})
	}

	resp := new(policyResponse)
	_, err := c.cli.DoJSONReq(ctx, http.MethodPost, "/policy/read",
		hertz.ReqOption{
			ExpectedCode: http.StatusOK,
			Body:         req,
			Result:       resp,
			Notes:        "ls_res_polices",
		})
	if err != nil {
		return nil, err
	}

	result := make(map[string][]*Policy)
	for _, policy := range resp.Data.Policies {
		resKey := GetKeyFromKVs(policy.Resource.PathV2, "key")
		result[resKey] = append(result[resKey], policy)
	}

	return result, nil
}

func (c *client) DeletePoliciesByIDs(ctx context.Context, ids []int64) error {
	if len(ids) == 0 {
		return nil
	}
	req := &PolicyRequest{
		Policies: make([]*Policy, 0, len(ids)),
		Relation: lo.ToPtr(RelationEquivalent),
	}
	for _, id := range ids {
		if id <= 0 {
			continue
		}
		req.Policies = append(req.Policies, &Policy{
			ID: &id,
			Resource: &PolicyEntity{
				Ns:         c.resourceNS,
				LocationV2: c.location,
			},
		})
	}

	resp := new(policyResponse)
	_, err := c.cli.DoJSONReq(ctx, http.MethodPost, "/policy/delete",
		hertz.ReqOption{
			ExpectedCode: http.StatusOK,
			Body:         req,
			Result:       resp,
			Notes:        "delete_res_polices_by_ids",
		})
	if err != nil {
		return err
	}

	return nil
}

func (c *client) DeleteResourcePolicies(ctx context.Context, keys ...string) error {
	req := &PolicyRequest{
		Policies: make([]*Policy, 0, len(keys)),
		Relation: lo.ToPtr(RelationEquivalent),
	}
	for _, key := range keys {
		if len(key) == 0 {
			continue
		}
		req.Policies = append(req.Policies, &Policy{
			Resource: &PolicyEntity{
				Ns:         c.resourceNS,
				PathV2:     NewKVs("key", key),
				LocationV2: c.location,
			},
			ResourceType: lo.ToPtr(EntityTypeResourceObject),
		})
	}

	resp := new(policyResponse)
	_, err := c.cli.DoJSONReq(ctx, http.MethodPost, "/policy/delete",
		hertz.ReqOption{
			ExpectedCode: http.StatusOK,
			Body:         req,
			Result:       resp,
			Notes:        "delete_res_polices",
		})
	if err != nil {
		return err
	}

	return nil
}

func (c *client) DeleteResource(ctx context.Context, keys ...string) error {
	// List resources' related policies.
	polices, err := c.ListResourcePolicies(ctx, keys...)
	if err != nil {
		return errors.WithMessage(err, "failed to list resource polices")
	}

	log.V1.CtxInfo(ctx, "got %d policies to delete", len(polices))

	// Get policies' ids.
	ids := lo.Map(lo.Flatten(lo.Values(polices)), func(item *Policy, index int) int64 { return lo.FromPtr(item.ID) })
	// Delete policies by ids.
	if err := c.DeletePoliciesByIDs(ctx, ids); err != nil {
		return errors.WithMessage(err, "failed to delete resource policies")
	}

	entities := make([]*PolicyEntity, 0, len(keys))
	for _, key := range keys {
		if len(key) == 0 {
			continue
		}
		entities = append(entities, &PolicyEntity{
			Ns:         c.resourceNS,
			PathV2:     NewKVs("key", key),
			LocationV2: c.location,
		})
	}
	req := &PolicyEntityRequest{
		EntityType: EntityTypeResourceObject,
		Entities:   entities,
		Relation:   lo.ToPtr(RelationEquivalent),
	}
	_, err = c.cli.DoJSONReq(ctx, http.MethodPost, "/policy_entity/delete",
		hertz.ReqOption{
			ExpectedCode: http.StatusOK,
			Body:         req,
			Result:       nil,
			Notes:        "delete_resource",
		})
	if err != nil {
		return errors.WithMessage(err, "failed to delete resource")
	}

	return nil
}

func (c *client) getAdminAction() *PolicyEntity {
	return &PolicyEntity{
		Ns:         "reftype_" + c.resourceNS,
		PathV2:     NewKVs("action", ActionAdmin),
		LocationV2: c.location,
	}
}

func (c *client) getUniGroupNs() string {
	return "group_kani_0" // "全领域自建组"
}

func (c *client) getGroupNs() string {
	return "group_" + c.resourceNS
}

func (c *client) getRoleNs() string {
	return "group_role_" + c.resourceNS
}

func (c *client) getDepartmentNs() string {
	return "group_ldap"
}

func (c *client) getUserNs() string {
	return "user"
}

func (c *client) getIdentity(identity Identity) *PolicyEntity {
	if identity.UserKey != nil {
		return &PolicyEntity{
			Ns:     c.getUserNs(),
			PathV2: NewKVs("id", *identity.UserKey),
		}
	} else if identity.GroupKey != nil {
		return &PolicyEntity{
			Ns:     c.getGroupNs(),
			PathV2: NewKVs("name", *identity.GroupKey),
		}
	} else if identity.DepartmentKey != nil {
		return &PolicyEntity{
			Ns:     c.getDepartmentNs(),
			PathV2: NewKVs("id", *identity.DepartmentKey),
		}
	} else if identity.RoleKey != nil {
		return &PolicyEntity{
			Ns:     c.getRoleNs(),
			PathV2: NewKVs("name", *identity.RoleKey),
		}
	}
	return nil
}

type CreatePolicyOption struct {
	// ResourceKey is the resource key(not name).
	ResourceKey string
	// IdentityKey refers to a user/group/department.
	IdentityKey Identity
	// ExpireTime is the expiring time point, 0 for permanent.
	ExpireTime time.Time
	Role       Action
	// Operator refers to who do this operation.
	Operator string
}

// CreatePolicies creates policies applied to the resources.
//
// Creating policies a.k.a. granting permissions in RBAC.
func (c *client) CreatePolicies(ctx context.Context, opts ...CreatePolicyOption) error {
	policies := make([]*Policy, len(opts))
	for idx, opt := range opts {
		policy := &Policy{
			Identity:     c.getIdentity(opt.IdentityKey),
			IdentityType: lo.ToPtr(EntityTypeIdentityPrincipal),
			Role: &PolicyEntity{
				Ns:         c.resourceNS,
				PathV2:     NewKVs("resource", opt.ResourceKey, "action", opt.Role.Key),
				LocationV2: c.location,
			},
			RoleType: lo.ToPtr(EntityTypeRoleAction),
			Resource: &PolicyEntity{
				Ns:         c.resourceNS,
				PathV2:     NewKVs("key", opt.ResourceKey),
				LocationV2: c.location,
			},
			ResourceType: lo.ToPtr(EntityTypeResourceObject),
			ExpireTime:   lo.ToPtr(lo.Ternary(opt.ExpireTime.IsZero(), int64(0), opt.ExpireTime.UnixNano())),
			Condition:    nil, // No conditions used at current.
			IsEnable:     lo.ToPtr(true),
			Information: map[string]interface{}{
				// Not the final plan to record operator.
				// TODO(caoyunxiang): TBD.
				"last_operator": opt.Operator,
			},
		}
		// Admin is a global role.
		if opt.Role.Key == ActionAdmin {
			policy.Role = c.getAdminAction()
		}
		if policy.Identity == nil {
			return errors.New("group/user/department key required")
		}
		policies[idx] = policy
	}
	req := &PolicyRequest{
		Policies:             policies,
		UpdateOnExist:        lo.ToPtr(true),
		ReturnCreatedResults: lo.ToPtr(true),
		Relation:             lo.ToPtr(RelationEquivalent),
	}
	resp := new(policyResponse)
	if _, err := c.cli.DoJSONReq(ctx, http.MethodPost, "/policy/create",
		hertz.ReqOption{
			ExpectedCode: http.StatusOK,
			Body:         req,
			Result:       resp,
			Notes:        "add_policy",
			Headers:      nil,
		}); err != nil {
		return err
	}

	return nil
}

type DeletePolicyOption struct {
	// ResourceKey should be the resource key(not the name).
	ResourceKey string
	// IdentityKey refers to a user/group/department.
	IdentityKey Identity
	Role        Action
	// Operator refers to who do the operation, should be the same user.
	Operator string
}

func (c *client) DeletePolicies(ctx context.Context, opts ...DeletePolicyOption) error {
	if len(opts) == 0 {
		return nil
	}
	operator := opts[0].Operator
	policies := make([]*Policy, len(opts))
	for idx, opt := range opts {
		opt.ResourceKey = strings.ToLower(opt.ResourceKey)
		policy := &Policy{
			Identity:     c.getIdentity(opt.IdentityKey),
			IdentityType: lo.ToPtr(EntityTypeIdentityPrincipal),
			Role: &PolicyEntity{
				Ns:         c.resourceNS,
				PathV2:     NewKVs("resource", opt.ResourceKey, "action", opt.Role.Key),
				LocationV2: c.location,
			},
			RoleType: lo.ToPtr(EntityTypeRoleAction),
			Resource: &PolicyEntity{
				Ns:         c.resourceNS,
				PathV2:     NewKVs("key", opt.ResourceKey),
				LocationV2: c.location,
			},
			ResourceType: lo.ToPtr(EntityTypeResourceObject),
		}
		// Admin is a global role.
		if opt.Role.Key == ActionAdmin {
			policy.Role = c.getAdminAction()
		}
		if policy.Identity == nil {
			return errors.New("group/user/department key required")
		}
		policies[idx] = policy
	}
	req := &PolicyRequest{
		Policies:             policies,
		UpdateOnExist:        lo.ToPtr(true),
		ReturnCreatedResults: lo.ToPtr(true),
		BasePolicy: &Policy{
			IsEnable: lo.ToPtr(false), // Just disable it, not to delete it.
			Information: map[string]interface{}{
				"last_operator": operator,
			},
		},
		Relation: lo.ToPtr(RelationEquivalent),
	}
	resp := new(policyResponse)
	if _, err := c.cli.DoJSONReq(ctx, http.MethodPost, "/policy/update",
		hertz.ReqOption{
			ExpectedCode: http.StatusOK,
			Body:         req,
			Result:       resp,
			Notes:        "disable_policy",
			Headers:      nil,
		}); err != nil {
		return err
	}

	return nil
}

func (c *client) GetUserByUsername(ctx context.Context, username string) (*User, error) {
	req := &PolicyEntityRequest{
		EntityType: EntityTypeIdentityPrincipal,
		Entities: []*PolicyEntity{
			{
				Ns:     c.getUserNs(),
				PathV2: NewKVs("id", username),
			},
		},
		Relation: lo.ToPtr(RelationEquivalent),
	}
	resp := new(policyEntityResponse)
	if _, err := c.cli.DoJSONReq(ctx, http.MethodPost, "/policy_entity/read",
		hertz.ReqOption{
			ExpectedCode: http.StatusOK,
			Body:         req,
			Result:       resp,
			Notes:        "get_user",
			Headers:      nil,
		}); err != nil {
		return nil, err
	}

	if len(resp.Data.Entities) != 1 || resp.Data.Entities[0].ID == nil {
		return nil, errors.WithMessage(ErrNotFound, "failed to find user in protego")
	}

	return getUserFromEntity(resp.Data.Entities[0]), nil
}

func (c *client) CheckPerm(ctx context.Context, resKey string, id Identity, action Action) (bool, error) {
	task := &AuthItem{
		ResourceKey: resKey,
		Identity:    id,
		Action:      action,
		Allowed:     false,
	}
	_, err := c.CheckMultiPerms(ctx, []*AuthItem{task})
	if err != nil {
		return false, err
	}

	return task.Allowed, nil
}

type AuthItem struct {
	ResourceKey string
	Identity    Identity
	Action      Action

	// Allowed is the auth result.
	Allowed bool
}

// CheckMultiPerms checks if the given (resource, identity, actions) is allowed.
func (c *client) CheckMultiPerms(ctx context.Context, tasks []*AuthItem) ([]*AuthItem, error) {
	if len(tasks) == 0 {
		return []*AuthItem{}, nil
	}
	authTasks := make([]AuthTask, len(tasks))
	for idx, task := range tasks {
		authTasks[idx] = AuthTask{
			Object: &PolicyEntity{
				Ns:         c.resourceNS,
				PathV2:     NewKVs("key", task.ResourceKey),
				LocationV2: c.location,
				Attributes: Object{
					"country": []string{}, // All country is ok.
				},
			},
			Action: &PolicyEntity{
				Ns:         c.resourceNS,
				PathV2:     NewKVs("resource", task.ResourceKey, "action", task.Action.Key),
				LocationV2: c.location,
			},
			Principal: c.getIdentity(task.Identity),
		}
		if task.Action.Key == ActionAdmin {
			authTasks[idx].Action = c.getAdminAction()
		}
	}
	req := Object{
		"option": Object{
			// Enable protego server cache(about 30 seconds).
			"is_strong_consistency": false, // false or nil for cache.
			"is_exact_match":        true,
		},
		"request": Object{
			"tasks": authTasks,
		},
	}
	resp := new(authResponse)
	if _, err := c.cli.DoJSONReq(ctx, http.MethodGet, "/batch_permissions",
		hertz.ReqOption{
			ExpectedCode: http.StatusOK,
			Body:         req,
			Result:       resp,
			Notes:        "check_multi_perms",
			Headers:      nil,
		}); err != nil {
		return nil, errors.WithMessage(err, "failed to check perms")
	}

	for idx, result := range resp.Data.PermissionResponses {
		tasks[idx].Allowed = result.IsAllowed
		tasks[idx].Action = Action{
			Ns:             result.MatchedWorkload.ActionAttr.Ns,
			Resource:       result.MatchedWorkload.ActionAttr.Path.Resource,
			Key:            result.MatchedWorkload.ActionAttr.Path.Action,
			Name:           result.MatchedWorkload.ActionAttr.Name,
			IsAutoApproval: result.MatchedWorkload.ActionAttr.IsAutoApproval == 1,
			Enabled:        true,
		}
	}

	return tasks, nil
}

var (
	ErrNotFound = errors.New("not found")
)

func (c *client) GetGroup(ctx context.Context, groupName string) (*Group, error) {
	req := &PolicyEntityRequest{
		EntityType: EntityTypeIdentityPrincipal,
		Entities: []*PolicyEntity{
			{
				Ns:         c.getGroupNs(),
				PathV2:     NewKVs("name", groupName),
				LocationV2: c.location,
				IsEnable:   lo.ToPtr(true),
			},
		},
		Relation: lo.ToPtr(RelationEquivalent),
	}
	resp := new(policyEntityResponse)
	if _, err := c.cli.DoJSONReq(ctx, http.MethodPost, "/policy_entity/read",
		hertz.ReqOption{
			ExpectedCode: http.StatusOK,
			Body:         req,
			Result:       resp,
			Notes:        "get_group",
			Headers:      nil,
		}); err != nil {
		return nil, err
	}

	if len(resp.Data.Entities) == 0 {
		return nil, errors.WithMessage(ErrNotFound, "no group found")
	}

	return getGroupFromEntity(resp.Data.Entities[0]), nil
}

type CreateGroupOption struct {
	Key         string
	Name        string
	EnName      string
	Description string
}

func (c *client) CreateGroups(ctx context.Context, opts ...CreateGroupOption) ([]*Group, error) {
	req := &PolicyEntityWithReferenceOperation{
		EntityType:    EntityTypeIdentityPrincipal,
		Entities:      []*PolicyEntityWithReference{},
		UpdateOnExist: lo.ToPtr(true),
	}
	for _, opt := range opts {
		req.Entities = append(req.Entities, &PolicyEntityWithReference{
			Entity: &PolicyEntity{
				Ns: c.getGroupNs(),
				Attributes: Object{
					"group_name":      opt.Name,
					"group_en_name":   opt.EnName,
					"description":     opt.Description,
					"confidentiality": 0, // 0 for public group, 9 for confidential private group.
				},
				PathV2:     NewKVs("name", opt.Key),
				LocationV2: c.location,
				IsEnable:   lo.ToPtr(true),
			},
			//References: []*Reference{ // Assign group's owner or member, we don't need.
			//	{
			//		Operation:            0,
			//		RefType:              0,
			//		BaseRefType:          0,
			//		RefEntities:          nil,
			//		StartID:              nil,
			//		BatchSize:            nil,
			//		UpdateOnExist:        nil,
			//		ReturnCreatedResults: nil,
			//		IgnoreNotFound:       nil,
			//		HardDelete:           nil,
			//	},
			//},
		})
	}
	resp := new(policyEntityWithRefResponse)
	if _, err := c.cli.DoJSONReq(ctx, http.MethodPost, "/policy_entity_ref/create",
		hertz.ReqOption{
			ExpectedCode: http.StatusOK,
			Body:         req,
			Result:       resp,
			Notes:        "create_groups",
			Headers:      nil,
		}); err != nil {
		return nil, err
	}

	if len(resp.Data.EntitiesWithRefs) == 0 {
		return nil, errors.Errorf("malformed response")
	}

	var groups []*Group
	for _, r := range resp.Data.EntitiesWithRefs {
		groups = append(groups, getGroupFromEntity(r.Entity))
	}

	return groups, nil
}

type CreateRoleOption struct {
	Key            string
	Name           string
	EnName         string
	Description    string
	IsAutoApproval bool
	Owners         []string // Owner usernames.
	Members        []string // Member usernames.
}

func (c *client) CreateRoles(ctx context.Context, opts ...CreateRoleOption) ([]*Role, error) {
	if len(opts) == 0 {
		return nil, errors.Errorf("options are required")
	}
	req := &PolicyEntityWithReferenceOperation{
		EntityType:    EntityTypeIdentityPrincipal,
		Entities:      []*PolicyEntityWithReference{},
		UpdateOnExist: lo.ToPtr(true),
	}
	for _, opt := range opts {
		var (
			ownerRefs  []*ReferenceEntity
			memberRefs []*ReferenceEntity
		)
		for _, owner := range opt.Owners {
			ownerRefs = append(ownerRefs, &ReferenceEntity{
				Entity:     c.getIdentity(NewUserIdentity(owner)),
				EntityType: EntityTypeIdentityPrincipal,
				RefInfo: &RefInfo{
					ExpireTime: lo.ToPtr(int64(0)), // No expiration.
					IsEnable:   lo.ToPtr(true),
				},
			})
		}
		for _, member := range opt.Members {
			memberRefs = append(memberRefs, &ReferenceEntity{
				Entity:     c.getIdentity(NewUserIdentity(member)),
				EntityType: EntityTypeIdentityPrincipal,
				RefInfo: &RefInfo{
					ExpireTime: lo.ToPtr(int64(0)), // No expiration.
					IsEnable:   lo.ToPtr(true),
				},
			})
		}
		req.EntitiesWithRefs = append(req.EntitiesWithRefs, &PolicyEntityWithReference{
			Entity: &PolicyEntity{
				Ns: c.getRoleNs(),
				Attributes: Object{
					"group_name":       opt.Name,
					"group_en_name":    opt.EnName,
					"description":      opt.Description,
					"is_auto_approval": lo.Ternary(opt.IsAutoApproval, 1, 0),
				},
				PathV2:     NewKVs("name", opt.Key),
				LocationV2: c.location,
				IsEnable:   lo.ToPtr(true),
			},
			References: []*Reference{
				{
					Operation:     OperationCreate,
					RefType:       HasAdmin,
					RefEntities:   ownerRefs,
					UpdateOnExist: lo.ToPtr(true),
				},
				{
					Operation:     OperationCreate,
					RefType:       IsParent,
					RefEntities:   memberRefs,
					UpdateOnExist: lo.ToPtr(true),
				},
			},
		})
	}
	resp := new(policyEntityWithRefResponse)
	if _, err := c.cli.DoJSONReq(ctx, http.MethodPost, "/policy_entity_ref/create",
		hertz.ReqOption{
			ExpectedCode: http.StatusOK,
			Body:         req,
			Result:       resp,
			Notes:        "create_roles",
			Headers:      nil,
		}); err != nil {
		return nil, err
	}

	if len(resp.Data.EntitiesWithRefs) == 0 {
		return nil, errors.Errorf("malformed response")
	}

	var roles []*Role
	for _, r := range resp.Data.EntitiesWithRefs {
		roles = append(roles, getRoleFromEntity(r.Entity))
	}

	return roles, nil
}

func (c *client) GetRoles(ctx context.Context, keys ...string) ([]*Role, error) {
	if len(keys) == 0 {
		return []*Role{}, nil
	}
	req := &PolicyEntityRequest{
		EntityType: EntityTypeIdentityPrincipal,
		Entities:   make([]*PolicyEntity, 0, len(keys)),
		Relation:   lo.ToPtr(RelationEquivalent),
	}
	for _, key := range keys {
		if len(key) == 0 {
			continue
		}
		req.Entities = append(req.Entities, &PolicyEntity{
			Ns:         c.getRoleNs(),
			PathV2:     NewKVs("name", key),
			LocationV2: c.location,
			//IsEnable:   lo.ToPtr(true),
		})
	}
	resp := new(policyEntityResponse)
	if _, err := c.cli.DoJSONReq(ctx, http.MethodPost, "/policy_entity/read",
		hertz.ReqOption{
			ExpectedCode: http.StatusOK,
			Body:         req,
			Result:       resp,
			Notes:        "get_roles",
			Headers:      nil,
		}); err != nil {
		return nil, err
	}

	if len(resp.Data.Entities) == 0 {
		return []*Role{}, nil
	}

	var roles []*Role
	for _, r := range resp.Data.Entities {
		roles = append(roles, getRoleFromEntity(r))
	}

	return roles, nil
}

func (c *client) DeleteRoles(ctx context.Context, keys ...string) error {
	roles, err := c.GetRoles(ctx, keys...)
	if err != nil {
		return errors.WithMessage(err, "failed to get roles")
	}

	if len(roles) == 0 {
		return nil
	}

	entities := make([]*PolicyEntity, 0, len(keys))
	for _, role := range roles {
		entities = append(entities, &PolicyEntity{
			ID:         &role.ID,
			Ns:         c.getRoleNs(),
			LocationV2: c.location,
		})
	}

	entityRefs := make([]*PolicyEntityWithReference, 0, len(entities))
	for _, entity := range entities {
		entityRefs = append(entityRefs, &PolicyEntityWithReference{
			Entity: entity,
			References: []*Reference{
				{
					Operation:      OperationDelete,
					RefType:        HasAdmin,
					IgnoreNotFound: lo.ToPtr(true),
					HardDelete:     lo.ToPtr(true),
				},
				{
					Operation:      OperationDelete,
					RefType:        IsParent,
					IgnoreNotFound: lo.ToPtr(true),
					HardDelete:     lo.ToPtr(true),
				},
			},
		})
	}

	refReq := &PolicyEntityWithReferenceOperation{
		EntityType:       EntityTypeIdentityPrincipal,
		EntitiesWithRefs: entityRefs,
		IgnoreNotFound:   lo.ToPtr(true),
		HardDelete:       lo.ToPtr(true),
	}
	_, err = c.cli.DoJSONReq(ctx, http.MethodPost, "/policy_entity_ref/read",
		hertz.ReqOption{
			ExpectedCode: http.StatusOK,
			Body:         refReq,
			Result:       nil,
			Notes:        "delete_roles_refs",
		})
	if err != nil {
		return errors.WithMessage(err, "failed to delete roles refs")
	}

	req := &PolicyEntityRequest{
		EntityType: EntityTypeIdentityPrincipal,
		Entities:   entities,
		Relation:   lo.ToPtr(RelationEquivalent),
	}
	_, err = c.cli.DoJSONReq(ctx, http.MethodPost, "/policy_entity/delete",
		hertz.ReqOption{
			ExpectedCode: http.StatusOK,
			Body:         req,
			Result:       nil,
			Notes:        "delete_roles",
		})
	if err != nil {
		return errors.WithMessage(err, "failed to delete roles")
	}

	return nil
}

func (c *client) ListResourceOwners(ctx context.Context, key string) ([]*User, error) {
	req := &PolicyRequest{
		Policies: []*Policy{
			{
				Identity: &PolicyEntity{
					Ns:       c.getUserNs(), // Only get users.
					IsEnable: lo.ToPtr(true),
				},
				Role: c.getAdminAction(),
				Resource: &PolicyEntity{
					Ns:         c.resourceNS,
					PathV2:     NewKVs("key", key),
					LocationV2: c.location,
				},
				IsEnable: lo.ToPtr(true),
			},
		},
		Relation: lo.ToPtr(RelationEquivalent),
	}

	resp := new(policyResponse)
	_, err := c.cli.DoJSONReq(ctx, http.MethodPost, "/policy/read",
		hertz.ReqOption{
			ExpectedCode: http.StatusOK,
			Body:         req,
			Result:       resp,
			Notes:        "ls_res_owner",
		})
	if err != nil {
		return nil, err
	}

	result := make([]*User, 0, len(resp.Data.Policies))
	for _, policy := range resp.Data.Policies {
		result = append(result, getUserFromEntity(policy.Identity))
	}

	return result, nil
}
