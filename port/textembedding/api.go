package textembedding

import (
	"context"
	"net/http"

	"github.com/pkg/errors"

	"code.byted.org/devgpt/kiwis/lib/hertz"
)

func (cli *client) RankWithWeight(ctx context.Context, req RankWithWeightRequest, opts ...Option) (*RankWithWeightResponse, error) {
	Results := new(RankWithWeightResponse)
	_, err := cli.cli.DoJSONReq(ctx, http.MethodPost, "/langchain/v3/lark_ranker_with_weight",
		applyOption(opts, &hertz.ReqOption{
			ExpectedCode: http.StatusOK,
			Body:         req,
			Result:       Results,
			Notes:        "rank_with_weight",
			Headers:      nil,
		}))
	if err != nil {
		return nil, err
	}

	return Results, nil
}

// RankWithWeightRequest indicates text content to compare for query and expected max return count.
type RankWithWeightRequest struct {
	Query   string   `json:"query"`
	Content []string `json:"content"`
	TopK    int      `json:"top_k"`
}

type ResultElem struct {
	Content string  `json:"content"`
	Score   float64 `json:"score"`
}

type RankWithWeightResponse struct {
	Message string       `json:"msg"`
	Results []ResultElem `json:"results"`
	Status  int          `json:"status"`
}

func (cli *client) CalculateEmbedding(ctx context.Context, content []string) ([][]float32, error) {
	ret := &CalculateM3eEmbeddingResponse{}
	_, err := cli.cli.DoJSONReq(ctx, http.MethodPost, "",
		hertz.ReqOption{
			ExpectedCode: http.StatusOK,
			Body:         map[string]interface{}{"input": content},
			Result:       ret,
			Notes:        "rank_with_weight",
			Headers:      nil,
		})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to calculate m3e embedding")
	}
	if ret.Message != "success" {
		return nil, errors.Errorf("failed to calculate m3e embedding, message: %s", ret.Message)
	}
	return ret.Data, nil
}

type CalculateM3eEmbeddingResponse struct {
	Code    string      `json:"code"`
	Message string      `json:"msg"`
	Data    [][]float32 `json:"data"`
}
