package meegosdk

import (
	"testing"
)

func TestGetPluginToken(t *testing.T) {
	//// 创建SDK客户端
	//api, err := New()
	//client := api.(*client)
	//require.NoError(t, err, "创建SDK客户端不应有错误")
	//require.NotNil(t, client, "SDK客户端不应为nil")
	//
	//// 创建请求参数
	//request := &GetPluginTokenRequest{
	//	PluginID:     internal.MeegoBitsPluginID,
	//	PluginSecret: internal.MeegoBitsPluginSecret,
	//	Type:         0, // 使用默认类型
	//}
	//
	//// 设置超时上下文
	//ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	//defer cancel()
	//
	//// 调用GetPluginToken方法
	//tokenInfo, err := client.GetPluginToken(ctx, request)
	//require.NoError(t, err, "获取插件令牌不应有错误")
	//require.NotNil(t, tokenInfo, "令牌信息不应为nil")
	//
	//// 验证返回的令牌信息
	//assert.NotEmpty(t, tokenInfo.Token, "令牌不应为空")
	//assert.Greater(t, tokenInfo.ExpireTime, 0, "过期时间应大于0")
	//
	//t.Logf("成功获取插件令牌，过期时间为：%d秒", tokenInfo.ExpireTime)
}
