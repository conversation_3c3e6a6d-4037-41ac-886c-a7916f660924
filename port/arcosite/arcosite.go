package arcosite

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"code.byted.org/devgpt/kiwis/lib/util"
	"code.byted.org/gopkg/logs/v2/log"
	"github.com/pkg/errors"
)

const arcoSiteBaseURL = "https://arcosite-openapi.bytedance.net/api/arcosite"

type client struct {
	cli        *http.Client
	arcoSiteAK string
	arcoSiteSK string
}

func NewClient(arcoSiteAK, arcoSiteSK string) Client {
	return &client{
		cli:        http.DefaultClient,
		arcoSiteAK: arcoSiteAK,
		arcoSiteSK: arcoSiteSK,
	}
}

// GetDocByID 根据 Id 获取文档详情，主要为了获取 DSL
func (c *client) GetDocByID(ctx context.Context, appID, documentID string) (*Document, error) {
	resp, err := c.doHTTPRequestArcoSite(ctx, "GetDocDetailById", map[string]string{
		"app_id": appID,
		"id":     documentID,
	})
	if err != nil {
		return nil, err
	}
	getDocResp := struct {
		Result *Document `json:"Result"`
	}{}
	err = json.Unmarshal(resp, &getDocResp)
	if err != nil {
		return nil, err
	}
	return getDocResp.Result, nil
}

// ConvertMD 将 dsl 转换为 markdown
func (c *client) ConvertMD(ctx context.Context, appID string, content any) (string, error) {
	resp, err := c.doHTTPRequestArcoSite(ctx, "ConvertMd", map[string]any{
		"app_id":  appID,
		"content": content,
	})
	if err != nil {
		return "", err
	}
	getDocResp := struct {
		Result string `json:"Result"`
	}{}
	err = json.Unmarshal(resp, &getDocResp)
	if err != nil {
		return "", err
	}
	return getDocResp.Result, nil
}

func (c *client) GetSite(ctx context.Context, appID string) (*Site, error) {
	resp, err := c.doHTTPRequestArcoSite(ctx, "GetSite", map[string]string{
		"app_id": appID,
	})
	if err != nil {
		return nil, err
	}

	getSiteResp := struct {
		Result *Site `json:"Result"`
	}{}
	err = json.Unmarshal(resp, &getSiteResp)
	if err != nil {
		return nil, err
	}
	return getSiteResp.Result, nil
}

func (c *client) GetSiteByDomain(ctx context.Context, domain string) (*Site, error) {
	resp, err := c.doHTTPRequestArcoSite(ctx, "GetSiteByDomain", map[string]string{
		"domain":     domain,
		"req_source": "site",
	})
	if err != nil {
		return nil, err
	}

	getSiteResp := struct {
		Result *Site `json:"Result"`
	}{}
	err = json.Unmarshal(resp, &getSiteResp)
	if err != nil {
		return nil, err
	}
	return getSiteResp.Result, nil
}

// GetBusiness 获取手册列表
func (c *client) GetBusiness(ctx context.Context, appID string) ([]*Business, error) {
	resp, err := c.doHTTPRequestArcoSite(ctx, "GetBusiness", map[string]string{
		"app_id": appID,
	})

	if err != nil {
		return nil, err
	}

	getBusinessResp := struct {
		Result struct {
			List []*Business `json:"list"`
		} `json:"Result"`
	}{}
	err = json.Unmarshal(resp, &getBusinessResp)
	if err != nil {
		return nil, err
	}
	return getBusinessResp.Result.List, nil
}

// GetBusStructureByID 根据 BusinessID 获取一个手册的文件结构
func (c *client) GetBusStructureByID(ctx context.Context, businessID, appID string) ([]*DocumentAbstract, error) {
	resp, err := c.doHTTPRequestArcoSite(ctx, "GetBusStructureById", map[string]string{
		"app_id":      appID,
		"business_id": businessID,
		"req_source":  "site",
	})

	if err != nil {
		return nil, err
	}

	getBusinessResp := struct {
		Result []*DocumentAbstract `json:"Result"`
	}{}
	err = json.Unmarshal(resp, &getBusinessResp)
	if err != nil {
		return nil, err
	}
	return getBusinessResp.Result, nil
}

func (c *client) GetPages(ctx context.Context, appID string) ([]*Page, error) {
	resp, err := c.doHTTPRequestArcoSite(ctx, "GetPages", map[string]string{
		"app_id":     appID,
		"req_source": "site",
	})
	if err != nil {
		return nil, err
	}

	getPagesResp := struct {
		Result []*Page `json:"Result"`
	}{}
	err = json.Unmarshal(resp, &getPagesResp)
	if err != nil {
		return nil, err
	}
	return getPagesResp.Result, nil
}

func (c *client) GetDocEffective(ctx context.Context, documentIDs []string, appID string) (*EffectiveDocumentRespData, error) {
	resp, err := c.doHTTPRequestArcoSite(ctx, "GetDocEffective", map[string]interface{}{
		"app_id":    appID,
		"doc_ids":   documentIDs,
		"checkLink": true,
	})
	if err != nil {
		return nil, err
	}
	var getDocEffective EffectiveDocumentRespData
	err = json.Unmarshal(resp, &getDocEffective)
	if err != nil {
		return nil, err
	}
	return &getDocEffective, nil
}

// ArcoSite 的 OpenAPI 可以参考：https://doc-v2.arcosite.bytedance.com/5zuq2n5x/3bo7z9iu
func (c *client) doHTTPRequestArcoSite(ctx context.Context, action string, body interface{}) ([]byte, error) {
	var httpReq *http.Request
	u := arcoSiteBaseURL
	var reqBody []byte
	reqBody, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	log.V1.CtxInfo(ctx, "doHTTPRequestArcoSite action: %s, body: %s", action, string(reqBody))

	httpReq, err = http.NewRequest("POST", u, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, err
	}

	bodyMD5 := util.MD5(reqBody)
	httpReq.Header.Set("content-type", "application/json;charset=utf-8")
	httpReq.Header.Set("x-arcosite-action", action)
	httpReq.Header.Set("x-arcosite-content", bodyMD5)
	// signString 为 arcoSite 需要构建的特殊认证字符串
	signString := fmt.Sprintf("%s\n%s\n%s\n%s", "POST", "/api/arcosite", "x-arcosite-action:"+action, "x-arcosite-content:"+bodyMD5)

	// 对 signString 进行 HmacSHA1，与 AK 一起拼装成 authorization header
	authorization := fmt.Sprintf("Arcosite %s:%s", c.arcoSiteAK, util.HmacSHA1([]byte(signString), c.arcoSiteSK))
	httpReq.Header.Set("authorization", authorization)

	httpResp, err := c.cli.Do(httpReq)
	if err != nil {
		return nil, err
	}
	defer httpResp.Body.Close()

	respRaw, err := io.ReadAll(httpResp.Body)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to read response body")
	}

	if httpResp.StatusCode < http.StatusOK || httpResp.StatusCode >= http.StatusBadRequest {
		return nil, fmt.Errorf("doHTTPRequestArcoSite error, code %v, message %v", httpResp.StatusCode, string(respRaw))
	}

	resp := struct {
		ResponseMetadata struct {
			Error *struct {
				Message string `json:"Message"`
				Code    string `json:"Code"`
			} `json:"Error,omitempty"`
		} `json:"ResponseMetadata"`
		Result interface{} `json:"Result,omitempty"`
	}{}

	err = json.Unmarshal(respRaw, &resp)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to unmarshal response")
	}

	// 这东西 error 特别多态，只能多写两个判断。。
	if resp.ResponseMetadata.Error != nil {
		switch resp.ResponseMetadata.Error.Code {
		case "DOC_NOT_PUBLISH":
			return nil, ErrDocNotPublish
		case "SITE_NOT_EXIST":
			return nil, ErrSiteNotExist
		case "DOC_NOT_EXIST":
			return nil, ErrDocNotExist
		default:
			return nil, fmt.Errorf("doHTTPRequestArcoSite error: %v", string(respRaw))
		}
	}

	if resp.Result != nil {
		if res, ok := resp.Result.(map[string]interface{}); ok && res["status"] == "error" {
			return nil, fmt.Errorf("doHTTPRequestArcoSite error: %v", string(respRaw))
		}
	}

	return respRaw, nil
}
