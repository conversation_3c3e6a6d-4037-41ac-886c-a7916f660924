package sandbox

import "fmt"

const (
	// VeFaaSAction
	VeFaaSActionListFunctionInstances   = "ListFunctionInstances"    // 请求参数为 FunctionId
	VeFaaSActionMigrateFunctionInstance = "MigrateFunctionInstances" // 请求参数为 FunctionId 和 Name
)

type GetSandboxRequestOpt struct {
	URL          string
	Body         interface{}
	FunctionID   string
	InstanceName string
}

type GetVeFaaSApiRequestOpt struct {
	Method  string // Get/Post
	Action  string // API名称
	Version string
	Path    string // 路径 默认 /
	Body    map[string]interface{}
}

// VeFaaSAPIResponseMetadata 通用的沙盒响应元数据结构
type VeFaaSAPIResponseMetadata struct {
	RequestId string `json:"RequestId"`
	Action    string `json:"Action"`
	Version   string `json:"Version"`
	Service   string `json:"Service"`
	Region    string `json:"Region"`
	Error     *struct {
		CodeN   int    `json:"CodeN"`
		Code    string `json:"Code"`
		Message string `json:"Message"`
	} `json:"Error"`
}

// ListFunctionInstancesResponse 获取函数下的沙盒列表响应结构
type ListFunctionInstancesResponse struct {
	ResponseMetadata VeFaaSAPIResponseMetadata `json:"ResponseMetadata"`

	Result struct {
		Items []FunctionInstanceItem `json:"Items"`
		Total int                    `json:"Total"`
	} `json:"Result"`
}

type FunctionInstancesList struct {
	InstanceItems []FunctionInstanceItem
}

type FunctionInstanceItem struct {
	Id             string `json:"Id"`
	CreationTime   string `json:"CreationTime"`
	InstanceName   string `json:"InstanceName"`
	InstanceStatus string `json:"InstanceStatus"`
	RevisionNumber int    `json:"RevisionNumber"`
}

// RunCodeOption 下游沙盒请求
type RunCodeOption struct {
	FunctionID    string                `json:"function_id,omitempty" description:"functionID"`
	InstanceName  string                `json:"container_id,omitempty" description:"containerID"`
	SessionID     string                `json:"session_id,omitempty" description:"sessionID"`
	ExecuteDetail *RunCodeExecuteDetail `json:"execute_details" description:"Details of the function execution"`
}

// RunCodeExecuteDetail 下游沙盒执行详情
type RunCodeExecuteDetail struct {
	Code               *string            `json:"code,omitempty"`
	CopyRuntimeTimeout float64            `json:"copy_runtime_timeout,omitempty" description:"Time in seconds to wait for a copy run to finish"`
	CompileTimeout     float64            `json:"compile_timeout,omitempty" description:"compile timeout for compiled languages"`
	RunTimeout         float64            `json:"run_timeout,omitempty" description:"code run timeout"`
	Stdin              *string            `json:"stdin,omitempty" description:"optional string to pass into stdin"`
	Language           string             `json:"language" description:"the language or execution mode to run the code"`
	Files              map[string]*string `json:"files,omitempty" description:"a dict from file path to base64 encoded file content"`
	FetchFiles         []string           `json:"fetch_files,omitempty" description:"a list of file paths to fetch after code execution"`
	LinkPrefix         string             `json:"link_prefix" description:"link prefix for all files"`
	SessionID          string             `json:"session_id" description:"session id"`
	CWD                string             `json:"cwd,omitempty" description:"current working directory"`
}

type Metadata struct {
	Message         string `json:"message"`
	ExecutorPodName string `json:"executor_pod_name"`
}

type ErrorInfo struct {
	ErrorCode    string `json:"error_code,omitempty"`
	ErrorMessage string `json:"error_message,omitempty"`
	RequestID    string `json:"request_id,omitempty"`
}

type RunCodeResponse struct {
	CompileResult *RunCodeCommandResult `json:"compile_result"`
	RunResult     *RunCodeCommandResult `json:"run_result"`
	Files         map[string]string     `json:"files"` // assuming files are in the form of key-value pairs (e.g., filename: file content)
	Status        string                `json:"status"`
	Metadata      `json:",inline"`
	// return when reach rate limit or instance not exist
	ErrorInfo `json:",inline"` // 内嵌 ErrorInfo，实现复用且序列化平铺
}

type RunJupyterResponse struct {
	RunResult       []*RunCodeCommandResult `json:"run_result,omitempty"`        // 使用指针切片处理可选项
	ExecutorPodName string                  `json:"executor_pod_name,omitempty"` // Executor pod name, optional
	Status          string                  `json:"status"`
	Metadata        `json:",inline"`
	// return only reach rate limit or instance not exist
	ErrorInfo `json:",inline"` // 内嵌 ErrorInfo，实现复用且序列化平铺
}

type RunCodeCommandResult struct {
	Type          string                   `json:"type"`
	Status        string                   `json:"status"`
	ExecutionTime float64                  `json:"execution_time"`
	ReturnCode    int                      `json:"return_code"`
	Stdout        string                   `json:"stdout"`
	Stderr        string                   `json:"stderr"`
	Display       []map[string]interface{} `json:"display,omitempty"`
}

type RunCodeErrorCode int

const (
	ErrorCodeFunctionConcurrencyLimitExceeded RunCodeErrorCode = 1
	ErrorCodeInstanceNotExist                 RunCodeErrorCode = 2
	ErrorCodeInvalidRequest                   RunCodeErrorCode = 3
	ErrorCodeInternalServerError              RunCodeErrorCode = 4
)

type SandboxError struct {
	Code    RunCodeErrorCode
	Message string
}

func (e *SandboxError) Error() string {
	return fmt.Sprintf("ErrorCode: %d, ErrorMessage: %s", e.Code, e.Message)
}

type UploadFilesOption struct {
	FunctionID   string             `json:"function_id,omitempty" description:"functionID"`
	InstanceName string             `json:"container_id,omitempty" description:"containerID"`
	Files        map[string]*string `json:"files"`
}

type UploadFilesResponse struct {
	Status    map[string]bool   `json:"status"`
	Errors    map[string]string `json:"errors"`
	Metadata  `json:",inline"`
	ErrorInfo `json:",inline"` // 内嵌 ErrorInfo，实现复用且序列化平铺
}

type DownloadFileOption struct {
	FunctionID   string `json:"function_id,omitempty" description:"functionID"`
	InstanceName string `json:"container_id,omitempty" description:"containerID"`
	Path         string `json:"path"`
}

type DownloadFileResponse struct {
	Content    []byte `json:"content"`
	FileSize   int64  `json:"file_size"`
	TotalLines int64  `json:"total_lines"`
	Status     int    `json:"status"`
	Metadata   `json:",inline"`
	ErrorInfo  `json:",inline"` // 内嵌 ErrorInfo，实现复用且序列化平铺
}

type ReadFileOption struct {
	FunctionID   string `json:"function_id,omitempty" description:"functionID"`
	InstanceName string `json:"container_id,omitempty" description:"containerID"`
	Path         string `json:"path"`
	Offset       int64  `json:"offset,omitempty" description:"offset"`
	Limit        int64  `json:"limit,omitempty" description:"line limit"`
}

type ReadFileResponse struct {
	Content     []byte `json:"content"` // 文件内容
	ContentSize int64  `json:"content_size"`
	FileSize    int64  `json:"file_size"`
	LineCount   int64  `json:"line_count"`
	TotalLines  int64  `json:"total_lines"`
	Metadata    `json:",inline"`
	ErrorInfo   `json:",inline"` // 内嵌 ErrorInfo，实现复用且序列化平铺
}

type WriteFileOption struct {
	FunctionID   string `json:"function_id,omitempty" description:"functionID"`
	InstanceName string `json:"container_id,omitempty" description:"containerID"`
	Path         string `json:"path"`    // 文件路径
	Content      []byte `json:"content"` // 写入的文件内容
}

type WriteFileResponse struct {
	FileSize   int64 `json:"file_size"`
	TotalLines int64 `json:"total_lines"`
	Status     int   `json:"status"`
	Metadata   `json:",inline"`
	ErrorInfo  `json:",inline"` // 内嵌 ErrorInfo，实现复用且序列化平铺
}

type EditFileOption struct {
	FunctionID   string `json:"function_id,omitempty" description:"functionID"`
	InstanceName string `json:"container_id,omitempty" description:"containerID"`
	Command      string `json:"command,omitempty"` // 编辑指令，如 replace / insert / view 等，默认 "str_replace"
	Path         string `json:"path"`              // 要编辑的文件路径
	OldStr       string `json:"old_str,omitempty"` // 需要替换的旧字符串
	NewStr       string `json:"new_str,omitempty"` // 用于替换的新字符串
}

type EditFileResponse struct {
	FileSize   int64                  `json:"file_size"`
	TotalLines int64                  `json:"total_lines"`
	Result     string                 `json:"result"`
	Data       map[string]interface{} `json:"data,omitempty"`
	Status     int                    `json:"status"`
	Metadata   `json:",inline"`
	ErrorInfo  `json:",inline"` // 内嵌 ErrorInfo，实现复用且序列化平铺
}

type DeleteInstanceSessionOption struct {
	FunctionID   string `json:"function_id,omitempty" description:"functionID"`
	InstanceName string `json:"container_id,omitempty" description:"containerID"`
	SessionID    string `json:"session_id,omitempty" description:"sessionID"`
}
