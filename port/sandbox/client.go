package sandbox

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/url"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/tcc"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/security/go-polaris/request"
)

//go:generate go run go.uber.org/mock/mockgen -destination mock/sandbox_mock_gen.go -package sandboxmock . Client
type Client interface {
	RunCode(ctx context.Context, opt *RunCodeOption) (*RunCodeResponse, error)
	RunJupyter(ctx context.Context, opt *RunCodeOption) (*RunJupyterResponse, error)
	DownloadFile(ctx context.Context, opt *DownloadFileOption) (*DownloadFileResponse, error)
	ReadFile(ctx context.Context, opt *ReadFileOption) (*ReadFileResponse, error)
	WriteFile(ctx context.Context, opt *WriteFileOption) (*WriteFileResponse, error)
	EditFile(ctx context.Context, opt *EditFileOption) (*EditFileResponse, error)
	DeleteInstanceSession(ctx context.Context, opt *DeleteInstanceSessionOption) error
	GetFunctionInstanceList(ctx context.Context, functionID string) (*FunctionInstancesList, error)
	MigrateInstance(ctx context.Context, functionID string, instanceName string) (*VeFaaSAPIResponseMetadata, error)
}

type sandboxClient struct {
	SandboxAuthConfig *tcc.GenericConfig[config.SandboxAuthConfig]
	SandboxAPIConfig  *tcc.GenericConfig[config.SandboxAPIConfig]
}

func NewSandboxClient(
	authConfig *tcc.GenericConfig[config.SandboxAuthConfig],
	apiConfig *tcc.GenericConfig[config.SandboxAPIConfig],
) Client {
	return &sandboxClient{
		SandboxAuthConfig: authConfig,
		SandboxAPIConfig:  apiConfig,
	}
}

func (t *sandboxClient) RunCode(ctx context.Context, opt *RunCodeOption) (r *RunCodeResponse, err error) {
	logs.V1.CtxInfo(ctx, "[RunCode] execReq: %v", opt)
	e := &SandboxError{}
	client, req, err := t.getSandboxRequest(ctx, GetSandboxRequestOpt{
		URL:          fmt.Sprintf("http://%s/run_code", t.SandboxAPIConfig.GetValue().CommonSandboxDomainName),
		Body:         t.mapLanguage(opt.ExecuteDetail),
		FunctionID:   opt.FunctionID,
		InstanceName: opt.InstanceName,
	})
	logs.V1.CtxInfo(ctx, "[RunCode] detail: %v, body: %v", opt.ExecuteDetail, req)
	if err != nil {
		logs.V1.CtxError(ctx, "[RunCode] Failed to create HTTP request: %v", err)
		e.Code, e.Message = ErrorCodeInternalServerError, "failed to create HTTP request"
		return nil, e
	}
	resp, err := client.Do(req)
	logs.V1.CtxInfo(ctx, "[RunCode] HTTP request result: %v", resp)
	if err != nil {
		logs.V1.CtxError(ctx, "[RunCode] Error calling ListFunctionInstances, functionID: %v, instanceName: %v, err: %v", opt.FunctionID, opt.InstanceName, err)
		e.Code, e.Message = ErrorCodeInternalServerError, "sandbox internal execute code failed"
		return nil, e
	}
	defer resp.Body.Close()

	r = &RunCodeResponse{}
	err = json.NewDecoder(resp.Body).Decode(r)
	if err != nil {
		logs.V1.CtxError(ctx, "[RunCode] Error decode resp body, functionID: %v, instanceName: %v, err: %v", opt.FunctionID, opt.InstanceName, err)
		e.Code, e.Message = ErrorCodeInternalServerError, "json unmarshal failed"
		return nil, e
	}
	if r.ErrorCode != "" {
		logs.V1.CtxError(ctx, "[RunCode] Error rate limit, functionID: %v, instanceName: %v, err: %v", opt.FunctionID, opt.InstanceName, r.ErrorCode)
		if r.ErrorCode == "instance_not_found" {
			e.Code, e.Message = ErrorCodeInstanceNotExist, "instance not exist"
		} else {
			e.Code, e.Message = ErrorCodeFunctionConcurrencyLimitExceeded, "user reach rate limit exceeded"
		}
		return nil, e
	}
	logs.V1.CtxInfo(ctx, "[RunCode] resp:%v", resp)
	return r, nil
}

func (t *sandboxClient) RunJupyter(ctx context.Context, opt *RunCodeOption) (r *RunJupyterResponse, err error) {
	logs.V1.CtxInfo(ctx, "[RunJupyter] execReq: %v", opt)
	e := &SandboxError{}
	client, req, err := t.getSandboxRequest(ctx, GetSandboxRequestOpt{
		URL:          fmt.Sprintf("http://%s/run_jupyter", t.SandboxAPIConfig.GetValue().CommonSandboxDomainName),
		Body:         opt.ExecuteDetail,
		FunctionID:   opt.FunctionID,
		InstanceName: opt.InstanceName,
	})
	if err != nil {
		logs.V1.CtxError(ctx, "[RunJupyter] Failed to create HTTP request: %v", err)
		e.Code, e.Message = ErrorCodeInternalServerError, "failed to create HTTP request"
		return nil, e
	}
	resp, err := client.Do(req)
	if err != nil {
		logs.V1.CtxError(ctx, "[RunJupyter] Error calling ListFunctionInstances, functionID: %v, instanceName: %v, err: %v", opt.FunctionID, opt.InstanceName, err)
		e.Code, e.Message = ErrorCodeInternalServerError, "sandbox internal execute code failed"
		return nil, e
	}
	defer resp.Body.Close()

	r = &RunJupyterResponse{}
	err = json.NewDecoder(resp.Body).Decode(r)
	if err != nil {
		logs.V1.CtxError(ctx, "[RunJupyter] Error decode resp body, functionID: %v, instanceName: %v, err: %v", opt.FunctionID, opt.InstanceName, err)
		e.Code, e.Message = ErrorCodeInternalServerError, "json unmarshal failed"
		return nil, e
	}
	if r.ErrorCode != "" {
		logs.V1.CtxError(ctx, "[RunCode] Error rate limit, functionID: %v, instanceName: %v, err: %v", opt.FunctionID, opt.InstanceName, r.ErrorCode)
		if r.ErrorCode == "instance_not_found" {
			e.Code, e.Message = ErrorCodeInstanceNotExist, "instance not exist"
		} else {
			e.Code, e.Message = ErrorCodeFunctionConcurrencyLimitExceeded, "user reach rate limit exceeded"
		}
		return nil, e
	}
	logs.V1.CtxInfo(ctx, "[RunJupyter] resp:%v", resp)
	return r, nil
}

func (t *sandboxClient) DeleteInstanceSession(ctx context.Context, opt *DeleteInstanceSessionOption) (err error) {
	logs.V1.CtxInfo(ctx, "[DeleteInstanceSession] execReq: %v", opt)
	e := &SandboxError{}
	client, req, err := t.getSandboxRequest(ctx, GetSandboxRequestOpt{
		URL:          fmt.Sprintf("http://%s/delete_session", t.SandboxAPIConfig.GetValue().CommonSandboxDomainName),
		FunctionID:   opt.FunctionID,
		InstanceName: opt.InstanceName,
		Body:         opt.SessionID,
	})
	if err != nil {
		logs.V1.CtxError(ctx, "[DeleteInstanceSession] Failed to create HTTP request: %v", err)
		e.Code, e.Message = ErrorCodeInternalServerError, "failed to create HTTP request"
		return e
	}
	resp, err := client.Do(req)
	if err != nil {
		logs.V1.CtxError(ctx, "[DeleteInstanceSession] Error delete session, functionID: %v, instanceName: %v, err: %v", opt.FunctionID, opt.InstanceName, err)
		e.Code, e.Message = ErrorCodeInternalServerError, "sandbox internal delete session failed"
		return e
	}
	defer resp.Body.Close()
	return nil
}

func (t *sandboxClient) DownloadFile(ctx context.Context, opt *DownloadFileOption) (r *DownloadFileResponse, err error) {
	logs.V1.CtxInfo(ctx, "[DownloadFiles] execReq: %v", opt)

	client, req, err := t.getSandboxRequest(ctx, GetSandboxRequestOpt{
		URL: fmt.Sprintf("http://%s/file/download", t.SandboxAPIConfig.GetValue().CommonSandboxDomainName),
		Body: map[string]interface{}{
			"path": opt.Path, // 这里不需要取地址符号 &
		},
		FunctionID:   opt.FunctionID,
		InstanceName: opt.InstanceName,
	})
	if err != nil {
		logs.V1.CtxError(ctx, "[DownloadFiles] Failed to create HTTP request: %v", err)
		return nil, errors.New(err.Error())
	}

	resp, err := client.Do(req)
	if err != nil {
		logs.V1.CtxError(ctx, "[DownloadFiles] HTTP request failed: %v", err)
		return nil, errors.New(err.Error())
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		logs.V1.CtxError(ctx, "[DownloadFiles] HTTP request failed with status code: %v", resp)
		return nil, errors.New(resp.Status)
	}

	// 读取响应体全部内容（二进制文件内容）
	content, err := io.ReadAll(resp.Body)
	if err != nil {
		logs.V1.CtxError(ctx, "[DownloadFiles] Read response body failed: %v", err)
		return nil, errors.New(err.Error())
	}

	if resp.Header.Get("status") != "0" {
		logs.V1.CtxInfo(ctx, "[DownloadFiles] Download response failed with status code: %v", resp)
		return nil, &SandboxError{
			Code:    ErrorCodeInternalServerError,
			Message: string(content),
		}
	}
	// 读取响应头信息
	fileSizeStr := resp.Header.Get("file_size")
	totalLinesStr := resp.Header.Get("total_lines")

	var fileSize int64 = 0
	var totalLines int64 = 0
	_, _ = fmt.Sscanf(fileSizeStr, "%d", &fileSize)
	_, _ = fmt.Sscanf(totalLinesStr, "%d", &totalLines)

	r = &DownloadFileResponse{
		Content:    content,
		FileSize:   fileSize,
		TotalLines: totalLines,
	}

	return r, nil
}

func (t *sandboxClient) ReadFile(ctx context.Context, opt *ReadFileOption) (*ReadFileResponse, error) {
	logs.V1.CtxInfo(ctx, "[ReadFile] execReq: %v", opt)

	client, req, err := t.getSandboxRequest(ctx, GetSandboxRequestOpt{
		URL: fmt.Sprintf("http://%s/file/read", t.SandboxAPIConfig.GetValue().CommonSandboxDomainName),
		Body: struct {
			Path   string `json:"path"`
			Offset int64  `json:"offset"`
			Limit  int64  `json:"limit"`
		}{
			Path:   opt.Path,
			Offset: opt.Offset,
			Limit:  opt.Limit,
		},
		FunctionID:   opt.FunctionID,
		InstanceName: opt.InstanceName,
	})
	if err != nil {
		logs.V1.CtxError(ctx, "[ReadFile] Failed to create HTTP request: %v", err)
		return nil, errors.New(err.Error())
	}

	resp, err := client.Do(req)
	if err != nil {
		logs.V1.CtxError(ctx, "[ReadFile] Error calling sandbox, err: %v", err)
		return nil, errors.New(err.Error())
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		logs.V1.CtxError(ctx, "[ReadFile] HTTP request failed with status code: %v", resp)
		return nil, errors.New(resp.Status)
	}

	// 读取响应体内容
	content, err := io.ReadAll(resp.Body)
	if err != nil {
		logs.V1.CtxError(ctx, "[ReadFile] Failed to read response body: %v", err)
		return nil, errors.New(err.Error())
	}

	if resp.Header.Get("status") != "0" {
		logs.V1.CtxInfo(ctx, "[ReadFile] Read response failed with status code: %v", resp)
		return nil, &SandboxError{
			Code:    ErrorCodeInternalServerError,
			Message: string(content),
		}
	}

	// 从 header 获取附加信息
	fileSize, _ := strconv.ParseInt(resp.Header.Get("file_size"), 10, 64)
	totalLines, _ := strconv.ParseInt(resp.Header.Get("total_lines"), 10, 64)

	// 统计本次读取的行数（按 \n 分割）
	lineCount := int64(bytes.Count(content, []byte("\n")))

	return &ReadFileResponse{
		Content:     content,
		ContentSize: int64(len(content)),
		FileSize:    fileSize,
		LineCount:   lineCount,
		TotalLines:  totalLines,
	}, nil
}

func (t *sandboxClient) WriteFile(ctx context.Context, opt *WriteFileOption) (*WriteFileResponse, error) {
	logs.V1.CtxInfo(ctx, "[WriteFile] execReq: %+v", opt)
	e := &SandboxError{}

	// 创建 multipart writer
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// path 字段（普通文本字段）
	if err := writer.WriteField("path", opt.Path); err != nil {
		logs.V1.CtxError(ctx, "[WriteFile] failed to write 'path' field: %v", err)
		e.Code, e.Message = ErrorCodeInternalServerError, fmt.Sprintf("failed to write 'path' field: %s", err.Error())
		return nil, e
	}

	// file 字段（文件字段，文件名用上传的文件名或 opt.Path 的文件名）
	fileWriter, err := writer.CreateFormFile("file", filepath.Base(opt.Path))
	if err != nil {
		logs.V1.CtxError(ctx, "[WriteFile] failed to create form file: %v", err)
		return nil, errors.New(err.Error())
	}

	// 写入文件内容
	if _, err := fileWriter.Write(opt.Content); err != nil {
		logs.V1.CtxError(ctx, "[WriteFile] failed to write file content: %v", err)
		return nil, errors.New(err.Error())
	}

	// 关闭 multipart writer，写入结尾boundary
	if err := writer.Close(); err != nil {
		logs.V1.CtxError(ctx, "[WriteFile] failed to close multipart writer: %v", err)
		return nil, errors.New(err.Error())
	}

	// 创建请求
	req, err := http.NewRequest("POST", fmt.Sprintf("http://%s/file/write", t.SandboxAPIConfig.GetValue().CommonSandboxDomainName), &buf)
	if err != nil {
		logs.V1.CtxError(ctx, "[WriteFile] Failed to create HTTP request: %v", err)
		return nil, errors.New(err.Error())
	}

	// 设置 multipart/form-data Content-Type
	req.Header.Set("Content-Type", writer.FormDataContentType())

	// 设置认证和其他头
	req.Header.Set("Authorization", t.SandboxAuthConfig.GetValue().CommonSandboxAuthKey)
	req.Header.Set("FunctionId", opt.FunctionID)
	req.Header.Set("X-Faas-Instance-Name", opt.InstanceName)

	// 发送请求
	transport := request.NewTransport()
	_ = transport.AllowDomainPort(fmt.Sprintf("%s:80", t.SandboxAPIConfig.GetValue().CommonSandboxDomainName))
	client := request.NewClientWithTransport(transport)
	resp, err := client.Do(req)
	if err != nil {
		logs.V1.CtxError(ctx, "[WriteFile] Error calling /file/write: %v", err)
		return nil, errors.New(err.Error())
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		logs.V1.CtxError(ctx, "[WriteFile] HTTP request failed with status code: %v", resp)
		return nil, errors.New(resp.Status)
	}

	// 解析响应
	var r WriteFileResponse
	if err := json.NewDecoder(resp.Body).Decode(&r); err != nil {
		logs.V1.CtxError(ctx, "[WriteFile] Failed to decode response body: %v", err)
		return nil, errors.New(err.Error())
	}

	if r.ErrorCode != "" {
		logs.V1.CtxError(ctx, "[WriteFile] Sandbox error: %v", r.ErrorCode)
		return nil, errors.New(r.ErrorCode)
	}
	if r.Status != 0 {
		logs.V1.CtxError(ctx, "[WriteFile] Sandbox response failed with status code: %v", r.Status)
		return nil, &SandboxError{
			Code:    ErrorCodeInternalServerError,
			Message: r.Message,
		}
	}

	logs.V1.CtxInfo(ctx, "[WriteFile] success: %+v", r)
	return &r, nil
}

func (t *sandboxClient) EditFile(ctx context.Context, opt *EditFileOption) (r *EditFileResponse, err error) {
	logs.V1.CtxInfo(ctx, "[EditFile] execReq: %v", opt)
	client, req, err := t.getSandboxRequest(ctx, GetSandboxRequestOpt{
		URL: fmt.Sprintf("http://%s/file/edit", t.SandboxAPIConfig.GetValue().CommonSandboxDomainName),
		Body: struct {
			Path   string `json:"path"`
			OldStr string `json:"old_str"`
			NewStr string `json:"new_str"`
		}{
			Path:   opt.Path,
			OldStr: opt.OldStr,
			NewStr: opt.NewStr,
		},
		FunctionID:   opt.FunctionID,
		InstanceName: opt.InstanceName,
	})
	if err != nil {
		logs.V1.CtxError(ctx, "[EditFile] Failed to create HTTP request: %v", err)
		return nil, errors.New(err.Error())
	}
	resp, err := client.Do(req)
	if err != nil {
		logs.V1.CtxError(ctx, "[EditFile] Error calling ListFunctionInstances, functionID: %v, instanceName: %v, err: %v", opt.FunctionID, opt.InstanceName, err)
		return nil, errors.New(err.Error())
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, errors.New(resp.Status)
	}

	r = &EditFileResponse{}
	err = json.NewDecoder(resp.Body).Decode(r)
	if err != nil {
		logs.V1.CtxError(ctx, "[EditFile] Error decode resp body, functionID: %v, instanceName: %v, err: %v", opt.FunctionID, opt.InstanceName, err)
		return nil, errors.New(err.Error())
	}
	if r.ErrorCode != "" {
		logs.V1.CtxError(ctx, "[EditFile] Error rate limit, functionID: %v, instanceName: %v", opt.FunctionID, opt.InstanceName)
		return nil, errors.New(r.ErrorCode)
	}
	if r.Status != 0 {
		logs.V1.CtxError(ctx, "[EditFile] Sandbox response failed with status code: %v", resp)
		return nil, &SandboxError{
			Code:    ErrorCodeInternalServerError,
			Message: r.Message,
		}
	}
	logs.V1.CtxInfo(ctx, "[EditFile] resp:%v", resp)
	return r, nil
}

func (t *sandboxClient) GetFunctionInstanceList(ctx context.Context, functionID string) (*FunctionInstancesList, error) {
	listReq, err := t.getVeFaaSAPIRequest(ctx, GetVeFaaSApiRequestOpt{
		Method:  http.MethodPost,
		Action:  VeFaaSActionListFunctionInstances,
		Version: t.SandboxAPIConfig.GetValue().ListFunctionInstancesVersion,
		Path:    "/",
		Body: map[string]interface{}{
			"FunctionId": functionID,
		},
	})
	if err != nil {
		logs.V1.CtxError(ctx, "[GetFunctionInstanceList] Error creat VeFaaS request, functionID: %v, err: %v", functionID, err)
		return nil, err
	}
	client := request.NewClient()
	resp, err := client.Do(listReq)
	if err != nil {
		logs.V1.CtxError(ctx, "[GetFunctionInstanceList] Error calling ListFunctionInstances, instanceName: %v, err: %v", functionID, err)
		return nil, err
	}
	defer resp.Body.Close()

	var result ListFunctionInstancesResponse
	err = json.NewDecoder(resp.Body).Decode(&result)
	if err != nil {
		logs.V1.CtxError(ctx, "[GetFunctionInstanceList] Error decode resp body, functionID: %v, err: %v", functionID, err)
		return nil, err
	}
	return &FunctionInstancesList{InstanceItems: result.Result.Items}, nil
}

func (t *sandboxClient) MigrateInstance(ctx context.Context, functionID string, instanceName string) (*VeFaaSAPIResponseMetadata, error) {
	migrateReq, err := t.getVeFaaSAPIRequest(ctx, GetVeFaaSApiRequestOpt{
		Method:  http.MethodPost,
		Action:  VeFaaSActionMigrateFunctionInstance,
		Version: t.SandboxAPIConfig.GetValue().MigrateFunctionInstanceVersion,
		Path:    "/",
		Body: map[string]interface{}{
			"FunctionId": functionID,
			"Name":       instanceName,
		},
	})
	if err != nil {
		return nil, err
	}
	client := request.NewClient()
	resp, err := client.Do(migrateReq)
	if err != nil {
		logs.V1.CtxError(ctx, "[MigrateFunctionInstances] Error calling ListFunctionInstances, instanceName: %v, err: %v", instanceName, err)
		return nil, err
	}
	defer resp.Body.Close()

	var result = &struct {
		ResponseMetadata VeFaaSAPIResponseMetadata
	}{}
	err = json.NewDecoder(resp.Body).Decode(&result)
	if err != nil {
		logs.V1.CtxError(ctx, "[MigrateFunctionInstances] Error decode resp body, functionID: %v, err: %v", functionID, err)
		return nil, err
	}
	logs.V1.CtxInfo(ctx, "[MigrateFunctionInstances] success, functionID: %v, instanceName: %v, result: %v", functionID, instanceName, result)
	return &result.ResponseMetadata, nil
}

// getSandboxRequest Sandbox调用请求构造工厂
func (t *sandboxClient) getSandboxRequest(ctx context.Context, opt GetSandboxRequestOpt) (*http.Client, *http.Request, error) {
	logs.V1.CtxInfo(ctx, "[getSandboxRequest] opt: %v", opt)
	if opt.InstanceName == "" || opt.FunctionID == "" {
		return nil, nil, errors.New("[getSandboxRequest] InstanceName && FunctionID is required")
	}
	body, err := json.Marshal(opt.Body)
	if err != nil {
		logs.V1.CtxError(ctx, "[getSandboxRequest] Error marshal body, err: %v", err)
		return nil, nil, err
	}
	transport := request.NewTransport()
	_ = transport.AllowDomainPort(fmt.Sprintf("%s:80", t.SandboxAPIConfig.GetValue().CommonSandboxDomainName))
	client := request.NewClientWithTransport(transport)
	req, err := http.NewRequest("POST", opt.URL, bytes.NewBuffer(body))
	if err != nil {
		logs.V1.CtxError(ctx, "[getSandboxRequest] Failed to create HTTP request: %v", err)
		return nil, nil, err
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", t.SandboxAuthConfig.GetValue().CommonSandboxAuthKey)
	req.Header.Set("FunctionId", opt.FunctionID)
	req.Header.Set("X-Faas-Instance-Name", opt.InstanceName)
	logs.V1.CtxInfo(ctx, "[getSandboxRequest] req: %v", req)
	return client, req, nil
}

// getVeFaaSAPIRequest VeFaaS API调用请求构造工厂
func (t *sandboxClient) getVeFaaSAPIRequest(ctx context.Context, opt GetVeFaaSApiRequestOpt) (*http.Request, error) {
	query := make(url.Values)
	query.Set("Action", opt.Action)
	query.Set("Version", opt.Version)
	requestAddr := fmt.Sprintf("%s%s?%s", t.SandboxAPIConfig.GetValue().VeFaaSDomainName, opt.Path, query.Encode())
	bodyBytes, err := json.Marshal(opt.Body)
	if err != nil {
		return nil, err
	}
	faasRequest, err := http.NewRequest(opt.Method, requestAddr, bytes.NewBuffer(bodyBytes))
	if err != nil {
		return nil, err
	}

	now := time.Now()
	date := now.UTC().Format("20060102T150405Z")
	authDate := date[:8]
	faasRequest.Header.Set("X-Date", date)

	payload := hex.EncodeToString(t.hashSHA256(ctx, bodyBytes))
	faasRequest.Header.Set("X-Content-Sha256", payload)
	faasRequest.Header.Set("Content-Type", "application/json")

	queryString := strings.Replace(query.Encode(), "+", "%20", -1)
	signedHeaders := []string{"host", "x-date", "x-content-sha256", "content-type"}
	headerList := lo.Map(signedHeaders, func(header string, _ int) string {
		if header == "host" {
			return header + ":" + faasRequest.Host
		}
		return header + ":" + strings.TrimSpace(faasRequest.Header.Get(header))
	})
	headerString := strings.Join(headerList, "\n")

	canonicalString := strings.Join([]string{
		opt.Method,
		opt.Path,
		queryString,
		headerString + "\n",
		strings.Join(signedHeaders, ";"),
		payload,
	}, "\n")

	hashedCanonicalString := hex.EncodeToString(t.hashSHA256(ctx, []byte(canonicalString)))

	credentialScope := authDate + "/" + t.SandboxAPIConfig.GetValue().VeFaaSRegion + "/" + t.SandboxAPIConfig.GetValue().VeFaaSService + "/request"
	signString := strings.Join([]string{
		"HMAC-SHA256",
		date,
		credentialScope,
		hashedCanonicalString,
	}, "\n")

	signedKey := t.getSignedKey(t.SandboxAuthConfig.GetValue().VeFaaSSecretAccessKey, authDate, t.SandboxAPIConfig.GetValue().VeFaaSRegion, t.SandboxAPIConfig.GetValue().VeFaaSService)
	signature := hex.EncodeToString(t.hmacSHA256(signedKey, signString))

	authorization := "HMAC-SHA256" +
		" Credential=" + t.SandboxAuthConfig.GetValue().VeFaaSAccessKeyID + "/" + credentialScope +
		", SignedHeaders=" + strings.Join(signedHeaders, ";") +
		", Signature=" + signature
	faasRequest.Header.Set("Authorization", authorization)
	return faasRequest, nil
}

func (t *sandboxClient) hashSHA256(ctx context.Context, data []byte) []byte {
	hash := sha256.New()
	if _, err := hash.Write(data); err != nil {
		logs.V1.CtxError(ctx, "input hash err:%s", err.Error())
	}

	return hash.Sum(nil)
}

func (t *sandboxClient) hmacSHA256(key []byte, content string) []byte {
	mac := hmac.New(sha256.New, key)
	mac.Write([]byte(content))
	return mac.Sum(nil)
}

func (t *sandboxClient) getSignedKey(secretKey, date, region, service string) []byte {
	kDate := t.hmacSHA256([]byte(secretKey), date)
	kRegion := t.hmacSHA256(kDate, region)
	kService := t.hmacSHA256(kRegion, service)
	kSigning := t.hmacSHA256(kService, "request")

	return kSigning
}

func (t *sandboxClient) mapLanguage(opt *RunCodeExecuteDetail) *RunCodeExecuteDetail {
	switch opt.Language {
	case "tsx":
		opt.Language = "react"
	case "javascript":
		opt.Language = "js"
	default:
	}
	return opt
}
