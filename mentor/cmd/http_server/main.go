package main

import (
	"go.uber.org/fx"

	"code.byted.org/devgpt/kiwis/copilotstack/llm"
	"code.byted.org/devgpt/kiwis/mentor/cmd"
	"code.byted.org/devgpt/kiwis/port/bytedoc"
	"code.byted.org/devgpt/kiwis/port/redis"
	tccmod "code.byted.org/devgpt/kiwis/port/tcc"
)

func main() {
	app := fx.New(
		HertzModel,
		tccmod.Module,
		cmd.ConfigModule,
		cmd.Module,
		//llmstack.Module,
		llm.Module,
		redis.Module,
		bytedoc.Module,
	)
	app.Run()
}
