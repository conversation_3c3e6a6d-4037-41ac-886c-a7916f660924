package utils

import (
	"testing"

	"github.com/stretchr/testify/require"

	"code.byted.org/devgpt/kiwis/lib/config"
)

func TestGetWordCloud(t *testing.T) {
	t.Skip("only for local API test")
	wordcloudConfig := &config.WordCloudConfig{
		URL: "https://a4z3wunh.fn-boe.bytedance.net",
	}

	valid_input_text := "hello world"
	valid_output := "img_v3_"
	invalid_input_text := ""
	invalid_output := ""

	testCases := []struct {
		Name         string
		InputConfig  *config.WordCloudConfig
		InputText    string
		ExpectOutput string
		ExpectError  bool
	}{{Name: "Valid input", InputConfig: wordcloudConfig, InputText: valid_input_text, ExpectOutput: valid_output, ExpectError: false},
		{Name: "Invalid input", InputConfig: wordcloudConfig, InputText: invalid_input_text, ExpectOutput: invalid_output, ExpectError: true}}

	for _, tc := range testCases {
		response, err := GetWordCloud(tc.InputConfig, tc.InputText)
		require.Equal(t, tc.ExpectError, err != nil, tc.Name)
		require.Contains(t, response, tc.ExpectOutput, tc.Name)
	}
}
