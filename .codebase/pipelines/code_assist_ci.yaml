template: go
name: Code Assist CI
trigger:
  change:
    paths:
      - ".codebase/pipelines/code_assist_ci.yaml"
      - "codeassist/**"
      - "api/idl/codeassist/**"
go_version: "1.23"
cache_key_prefix: "0423-"
commands:
  - go install gotest.tools/gotestsum@latest
  - cd codeassist && gotestsum --junitfile=report.xml -- -coverprofile=coverage.out -coverpkg=./... ./...
coverage_file_name: codeassist/coverage.out
coverage_config:
  status:
    project: # 全量代码
      code_assist:
        threshold: 50%
        if_not_found: "error"
        base: "change"
        paths: &paths
          - "!*/main.go" # not testable
          - "!*mock_gen.go" # Not calculate test coverage for generated mock files.
          - "!*mock_gen_test.go" # Not calculate test coverage for generated mock files.
          - "!*/test/*"
          - "!*/cmd/*"
          - "!*/hertz_handler/*"
          - "!*/kitex_gen/*"
          - "!*/hertz_gen/*"
    diff: # 增量代码
      code_assist: # 每次提交的增量代码，覆盖率不少于 20% - 1%
        minimum_coverage: 0%
        threshold: 1%
        if_not_found: error
        paths: *paths
