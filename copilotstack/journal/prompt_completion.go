package journal

import (
	"time"

	"code.byted.org/devgpt/kiwis/lib/config"
)

// PromptCompletion 模型调用历史记录.
type PromptCompletion struct {
	ID              int64                            `json:"id"`
	AppID           *string                          `json:"app_id"`
	Username        string                           `json:"username"`
	ModelName       string                           `json:"model_name"`
	Status          PromptCompletionStatus           `json:"status"`
	SessionID       string                           `json:"session_id"`
	Prompt          *string                          `json:"prompt"`
	Type            PromptCompletionType             `json:"type"`
	ContentRaw      *string                          `json:"content_raw"`
	RequestMetadata *PromptCompletionRequestMetadata `json:"request_metadata"`
	ContextVariable *PromptCompletionContextVariable `json:"context_variable"`
	CreatedAt       time.Time                        `json:"created_at"`
	UpdatedAt       time.Time                        `json:"updated_at"`
	DeletedAt       *time.Time                       `json:"deleted_at"`
}

// PromptCompletionRequestMetadata 模型调用信息，如token,role,latency等.
type PromptCompletionRequestMetadata struct {
	// Model params:
	Stream            *bool    `json:"stream"`
	MaxTokens         *int     `json:"max_tokens"`
	Temperature       *float32 `json:"temperature"`
	TopP              *float32 `json:"top_p"`
	TopK              *int     `json:"top_k"`
	N                 *int     `json:"n"`
	FrequencyPenalty  *float32 `json:"frequency_penalty"`
	PresencePenalty   *float32 `json:"presence_penalty"`
	MinNewTokens      *int     `json:"min_new_tokens"`
	MaxPromptTokens   *int     `json:"max_prompt_tokens"`
	RepetitionPenalty *float32 `json:"repetition_penalty"`

	ThinkingConfig *config.ThinkingConfig `json:"thinking_config"`

	// Response:
	ID                *string        `json:"id"`
	Usage             *Usage         `json:"usage"`
	Latency           *time.Duration `json:"latency"`
	FirstTokenLatency *time.Duration `json:"first_token_latency"`
	Error             *string        `json:"error"`
	FinishReason      *string        `json:"finish_reason"`
	LogID             *string        `json:"log_id"`
	// Bits AI params:
	FunctionID *string `json:"function_id"`

	// TODO: remove ide related data
	// ContextVariable records the context variables of the request.
	ContextVariables         map[string]any `json:"context_variables"`
	UserInputTokens          *int           `json:"user_input_tokens"`
	IntentName               *string        `json:"intent_name"`
	ProgrammingLanguage      *string        `json:"programming_language"`
	FallbackModels           []string       `json:"fallback_models"`
	RetriedTimes             int            `json:"retried_times"`
	CacheCreationInputTokens int            `json:"cache_creation_input_tokens,omitempty"`
	CacheReadInputTokens     int            `json:"cache_read_input_tokens,omitempty"`
}

// Usage 模型 token 使用情况.
type Usage struct {
	PromptTokens             int `json:"prompt_tokens"`
	CompletionTokens         int `json:"completion_tokens"`
	ReasoningTokens          int `json:"reasoning_tokens"`
	TotalTokens              int `json:"total_tokens"`
	CacheCreationInputTokens int `json:"cache_creation_input_tokens,omitempty"`
	CacheReadInputTokens     int `json:"cache_read_input_tokens,omitempty"`
}

type PromptCompletionStatus string

const (
	// StatusSuccess success for the model request
	StatusSuccess PromptCompletionStatus = "success"
	// StatusFail fail for the model request
	StatusFail PromptCompletionStatus = "fail"
)

// PromptCompletionType 对话类型，意图识别/context选择/对话/关键字生成.
type PromptCompletionType = string

const (
	// TypeIntentDetect 意图识别.
	TypeIntentDetect PromptCompletionType = "intent_detect"
	// TypePromptPipeline 对话.
	TypePromptPipeline PromptCompletionType = "prompt_pipeline"
	// TypeCustomModelPipeline 用户自定义模型的对话.
	TypeCustomModelPipeline PromptCompletionType = "custom_model"
	// TypeFastApply 快速应用.
	TypeFastApply PromptCompletionType = "fast_apply"
	// TypeContextSelection context选择.
	TypeContextSelection PromptCompletionType = "context_selection"
	// TypeNewContextSelection 新context选择，与TypeContextSelection共存
	TypeNewContextSelection PromptCompletionType = "new_context_selection"
	// TypeKeywordGeneration 关键字生成.
	TypeKeywordGeneration PromptCompletionType = "keyword_generation"
	// TypeChatCompletion raw chat completion.
	TypeChatCompletion PromptCompletionType = "chat_completion"
	// TypeEntityDetect 实体识别.
	TypeEntityDetect PromptCompletionType = "entity_detect"
	// TypeEntityRerank 实体重排.
	TypeEntityRerank PromptCompletionType = "entity_rerank"
	// TypeQueryRewrite query改写.
	TypeQueryRewrite PromptCompletionType = "query_rewrite"
	// TypeQueryGeneration 相关query生成.
	TypeQueryGeneration PromptCompletionType = "query_generation"
	// TypeNotes 笔记生成.
	TypeNotes PromptCompletionType = "notes"
	// aiagent模式调用LLMRawChat
	TypeAiAgent PromptCompletionType = "ai_agent"
)

type PromptCompletionContextVariable struct {
	// Resolvers records the context resolvers metadata.
	Resolvers []ResolverMetadata `json:"resolvers"`
}

type ResolverMetadata struct {
	ID               string         `json:"id"`
	Tokens           int            `json:"tokens"`
	ContextVariables map[string]any `json:"variables"`
	IsCropped        bool           `json:"is_cropped"`
	Error            string         `json:"error"`
}

type SessionEnvMetadata struct {
	Channel          string `json:"channel"`
	IDEVersion       string `json:"ide_version"`
	ExtensionVersion string `json:"extension_version"`
	VersionCode      int32  `json:"version_code"`
	GitURL           string `json:"git_url"`
	Language         string `json:"language"`
	Region           string `json:"region"`
	Environment      string `json:"environment"`
	AbVersion        string `json:"ab_version"`
}
