package journal

import (
	"context"

	"github.com/pkg/errors"
)

var ErrFeedbackNotUpdated = errors.New("feedback is not updated")

type JournalService interface {
	CreateLog(ctx context.Context, opt CreateLogOption) (*PromptCompletion, error)
	UpdateLog(ctx context.Context, opt UpdateLogOption) (*PromptCompletion, error)
	UpsertFeedback(ctx context.Context, opt CreateFeedbackOption) (*Feedback, error)
}

type CreateLogOption struct {
	AppId           *string
	Username        string
	ModelName       string
	Status          PromptCompletionStatus
	SessionId       string
	Prompt          *string
	Type            PromptCompletionType
	ContentRaw      *string
	RequestMetadata PromptCompletionRequestMetadata
	ContextVariable PromptCompletionContextVariable
}

type UpdateLogOption struct {
	ID              int64
	AppId           *string
	Username        *string
	ModelName       *string
	Status          *PromptCompletionStatus
	SessionId       *string
	Prompt          *string
	Type            *PromptCompletionType
	ContentRaw      *string
	RequestMetadata *PromptCompletionRequestMetadata
}

type CreateFeedbackOption struct {
	Username  string
	SessionId string
	Type      *ActionType
	Content   *string
}
