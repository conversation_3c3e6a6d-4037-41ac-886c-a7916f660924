package render

import (
	"fmt"
	"sort"
	"strings"
	"unicode"

	"code.byted.org/devgpt/kiwis/copilotstack/entity"
	"code.byted.org/gopkg/logs/v2"

	"code.byted.org/gopkg/pkg/errors"
	"github.com/mohae/deepcopy"
	"github.com/samber/lo"
)

func Render(root PromptNode, tokenizer Tokenizer, tokenLimit int) (*Result, int, error) {
	priorities := computeAllPriorities(root)

	// 先渲染所有的 isolate node
	err := hydrateIsolates(root, tokenizer)
	if err != nil {
		return nil, -1, errors.WithMessage(err, "error hydrate isolates node")
	}

	// 二分搜索选择最合适的 priority
	lowerIndex := -1
	upperIndex := len(priorities) - 1
	for lowerIndex < upperIndex-1 {
		candidatePriorityIndex := (lowerIndex + upperIndex) / 2
		candidatePriority := priorities[candidatePriorityIndex]

		tokenCount, err := countTokenWithPriority(root, candidatePriority, tokenizer)
		if err != nil {
			return nil, -1, errors.WithMessage(err, "error count token with priority")
		}

		if tokenCount > tokenLimit {
			lowerIndex = candidatePriorityIndex
		} else {
			upperIndex = candidatePriorityIndex
		}
	}

	targetPriority := priorities[upperIndex]
	r, err := renderWithPriority(root, targetPriority, 0)
	return r, targetPriority, err
}

func renderWithPriority(root PromptNode, priority int, index int) (result *Result, err error) {
	if root.GetAbsolutePriority() < priority {
		return nil, errors.New("priority is too low")
	}

	defer func() {
		if err == nil {
			root.isRendered(result)
		}
	}()

	switch root.GetType() {
	case PromptNodeTypeTemplate:
		node := root.(*TemplatePromptNode)
		variables := make(map[string]string)
		variables["index"] = fmt.Sprintf("%d", index)
		var multiContent []*entity.Multimedia
		for tag, child := range node.Children {
			if child.GetAbsolutePriority() < priority {
				variables[tag] = ""
				continue
			}
			result, err = renderWithPriority(child, priority, index)
			if err != nil {
				return nil, err
			}
			if result.MultiContent != nil {
				if multiContent == nil {
					multiContent = make([]*entity.Multimedia, 0)
				}
				multiContent = append(multiContent, result.MultiContent...)
			}

			variables[tag] = result.Content
		}

		for k, v := range node.defaultVariables {
			variables[k] = v
		}

		sb := new(strings.Builder)
		err = node.parsedTemplate.Execute(sb, variables)
		if err != nil {
			return nil, errors.WithMessage(err, "error execute template node's template")
		}
		return &Result{
			Content:      sb.String(),
			MultiContent: multiContent,
		}, nil
	case PromptNodeTypeIsolate:
		node := root.(*IsolatePromptNode)
		if node.RenderResult == nil {
			return nil, errors.New("isolate node should have been rendered")
		}

		return node.RenderResult, nil
	case PromptNodeTypeScope:
		child := root.GetChildren()
		if len(child) == 0 {
			return &Result{
				Content:      "",
				MultiContent: nil,
			}, nil
		}
		return renderWithPriority(child[0], priority, index)
	case PromptNodeTypeMessage:
		child := root.GetChildren()
		if len(child) == 0 || child[0].GetAbsolutePriority() < priority {
			root.(*MessagePromptNode).result = &Result{
				Content: "",
			}
		} else {
			result, err = renderWithPriority(child[0], priority, index)
			if err != nil {
				return result, err
			}
			root.(*MessagePromptNode).result = result
		}
		return root.(*MessagePromptNode).result, nil
	case PromptNodeTypeGroup:
		const groupReplacer = "...\n"
		childResult := make([]*Result, 0)

		childIndex := 0
		for _, child := range root.GetChildren() {
			if child.GetAbsolutePriority() < priority {
				if len(childResult) > 0 && childResult[len(childResult)-1].Content != groupReplacer {
					childResult = append(childResult, &Result{Content: groupReplacer})
				} else if len(childResult) == 0 {
					childResult = append(childResult, &Result{Content: groupReplacer})
				}
				continue
			}

			result, err = renderWithPriority(child, priority, childIndex)
			if err != nil {
				return nil, err
			}
			childIndex++
			childResult = append(childResult, result)
		}
		groupResult := &Result{
			Content: strings.Join(lo.Map(childResult, func(item *Result, index int) string {
				return item.Content
			}), ""),
			MultiContent: make([]*entity.Multimedia, 0),
		}
		for _, chRes := range childResult {
			groupResult.MultiContent = append(groupResult.MultiContent, chRes.MultiContent...)
		}
		return groupResult, nil

	case PromptNodeTypeAST:
		node := root.(*ASTPromptNode)
		childToBeRemoved := make([]*ASTPromptNode, 0)
		_ = dfs(node, func(child PromptNode) (bool, error) {
			if child.GetAbsolutePriority() < priority {
				childToBeRemoved = append(childToBeRemoved, child.(*ASTPromptNode))
				return false, nil
			}
			return true, nil
		})

		sort.Slice(childToBeRemoved, func(i, j int) bool {
			return childToBeRemoved[i].StartOffset < childToBeRemoved[j].StartOffset
		})

		type RemoveRange struct {
			StartOffset    uint32
			EndOffset      uint32
			ReplaceContent string
		}
		removeRanges := make([]*RemoveRange, 0)
		for _, child := range childToBeRemoved {
			if len(removeRanges) == 0 {
				removeRanges = append(removeRanges, &RemoveRange{
					StartOffset:    child.StartOffset,
					EndOffset:      child.EndOffset,
					ReplaceContent: child.ReplaceContent,
				})
				continue
			}

			previous := removeRanges[len(removeRanges)-1]
			if previous.EndOffset > child.StartOffset {
				return nil, errors.New("ast node has overlap")
			}

			if previous.ReplaceContent == child.ReplaceContent &&
				(previous.EndOffset == child.StartOffset ||
					isBlank(node.Content[previous.EndOffset:child.StartOffset])) {
				previous.EndOffset = child.EndOffset
				continue
			}

			removeRanges = append(removeRanges, &RemoveRange{
				StartOffset:    child.StartOffset,
				EndOffset:      child.EndOffset,
				ReplaceContent: child.ReplaceContent,
			})
		}

		renderedContent := string(deepcopy.Copy(node.Content).([]byte))
		for i := len(removeRanges) - 1; i >= 0; i-- {
			rangeToRemove := removeRanges[i]
			renderedContent = renderedContent[:rangeToRemove.StartOffset] +
				rangeToRemove.ReplaceContent + renderedContent[rangeToRemove.EndOffset:]
		}

		return &Result{
			Content: renderedContent,
		}, nil
	case PromptNodeTypeText:
		return &Result{
			Content: root.(*TextPromptNode).Content,
		}, nil
	case PromptNodeTypeImage:
		// 图片节点处理下载失败
		currentNode := root.(*ImageNode)
		if currentNode.multiContent != nil &&
			(strings.Contains(currentNode.multiContent.ResourceType, "image") && currentNode.multiContent.Data == "") {
			return nil, errors.New("image download failed")
		}
		return &Result{
			MultiContent: []*entity.Multimedia{root.(*ImageNode).multiContent},
		}, nil
	}
	return nil, nil
}

func countTokenWithPriority(root PromptNode, priority int, tokenizer Tokenizer) (int, error) {
	countToken := 0

	err := dfs(root, func(node PromptNode) (bool, error) {
		switch node.GetType() {
		case PromptNodeTypeAST:
			if node.GetAbsolutePriority() < priority {
				return false, nil
			}

			currentToken, err := node.GetNodeToken(tokenizer)
			if err != nil {
				return false, nil
			}

			childToBeRemoved := make([]PromptNode, 0)
			_ = dfs(node, func(child PromptNode) (bool, error) {
				if child.GetAbsolutePriority() < priority {
					childToBeRemoved = append(childToBeRemoved, child)
					return false, nil
				}
				return true, nil
			})

			for _, child := range childToBeRemoved {
				childToken, err := child.GetNodeToken(tokenizer)
				if err != nil {
					return false, err
				}
				currentToken -= childToken
			}
			countToken += currentToken
			return false, nil // 跳过子节点
		case PromptNodeTypeIsolate:
			currentToken, err := node.GetNodeToken(tokenizer)
			if err != nil {
				return false, err
			}
			countToken += currentToken
			return false, nil
		default:
			if node.GetAbsolutePriority() < priority {
				return false, nil
			}
			currentToken, err := node.GetNodeToken(tokenizer)
			if err != nil {
				return false, err
			}
			countToken += currentToken
			return true, nil
		}
	})
	if err != nil {
		return 0, err
	}

	return countToken, nil
}

func hydrateIsolates(root PromptNode, tokenizer Tokenizer) error {
	if root == nil {
		return nil
	}

	switch root.GetType() {
	case PromptNodeTypeTemplate, PromptNodeTypeScope, PromptNodeTypeGroup, PromptNodeTypeMessage:
		for _, child := range root.GetChildren() {
			err := hydrateIsolates(child, tokenizer)
			if err != nil {
				return err
			}
		}
	case PromptNodeTypeIsolate:
		// render the isolate node
		node := root.(*IsolatePromptNode)
		logs.Debugf("[history_scope: PromptNodeTypeIsolate]node.RenderResult: %v", node.RenderResult)
		if node.RenderResult == nil {
			renderResult, _, err := Render(node.Child, tokenizer, node.TokenLimit)
			if err != nil {
				return err
			}
			node.RenderResult = renderResult
		}
	case PromptNodeTypeAST, PromptNodeTypeText:
		return nil
	case PromptNodeTypeImage:
		return nil
	}

	return nil
}

func computeAllPriorities(root PromptNode) []int {
	priorities := make(map[int]struct{})

	_ = dfs(root, func(node PromptNode) (bool, error) {
		priorities[node.GetAbsolutePriority()] = struct{}{}
		return true, nil
	})

	result := lo.Keys(priorities)
	sort.Slice(result, func(i, j int) bool {
		return result[i] < result[j]
	})
	return result
}

func dfs(root PromptNode, visit func(node PromptNode) (bool, error)) error {
	stack := make([]PromptNode, 0)
	stack = append(stack, root)

	for len(stack) > 0 {
		current := stack[len(stack)-1]
		stack = stack[:len(stack)-1]
		if current == nil {
			continue
		}

		visitChildren, err := visit(current)
		if err != nil {
			return err
		}
		if visitChildren {
			stack = append(stack, current.GetChildren()...)
		}
	}

	return nil
}

func CollectAllMessageNodes(root PromptNode, priority int) []*MessagePromptNode {
	if root.GetAbsolutePriority() < priority {
		return []*MessagePromptNode{}
	}
	if root.GetType() == PromptNodeTypeMessage {
		currentSolidNode := root.(*MessagePromptNode)
		if currentSolidNode.result == nil ||
			(len(currentSolidNode.result.Content) == 0 && len(currentSolidNode.result.MultiContent) == 0) {
			return []*MessagePromptNode{}
		}

		return []*MessagePromptNode{root.(*MessagePromptNode)}
	}

	result := make([]*MessagePromptNode, 0)

	children := root.GetChildren()
	for _, child := range children {
		result = append(result, CollectAllMessageNodes(child, priority)...)
	}

	return result
}

func isBlank(data []byte) bool {
	for _, char := range data {
		if !unicode.IsSpace(rune(char)) {
			return false
		}
	}
	return true
}
