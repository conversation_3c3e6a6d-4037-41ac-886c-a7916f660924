package entity

import "code.byted.org/devgpt/kiwis/api/idl/kitex_gen/idecopilot"

// StringOutputs refers to a results contains a string response and a schemaless object.
// ReasoningContent is the thinking content for model
type StringOutputs struct {
	Response         string
	ReasoningContent string
	Outputs          map[string]any
	FinishReason     string
}

// NamedPrompt is a LLM prompt with a name.
// Name indicates which LLM request it refers to.
type NamedPrompt struct {
	Name string `json:"name"`
	Prompt
}

// NamedTokenUsage is token usage with a name.
// Name indicates which LLM request it refers to.
type NamedTokenUsage struct {
	Name string `json:"name"`
	TokenUsage
}

type ProgressNotice struct {
	Content    string         `json:"content"`
	Additional map[string]any `json:"additional"`
}

// NamedProgressNotice is a progress notice with a name.
type NamedProgressNotice struct {
	Name    string `json:"name"`
	Content string `json:"content"`
}

type LLMCallMetadata struct {
	Model              string `json:"model"`
	SessionID          string `json:"session_id"`
	PromptCompletionID int64  `json:"prompt_completion_id"`
	AppVersion         string `json:"app_version"`
}

type LLMSuggestedQuestions struct {
	SuggestedQuestions []*idecopilot.SuggestedQuestion `json:"suggested_questions"`
}

type References struct {
	Name      string         `json:"name"`
	Reference map[string]any `json:"reference"`
}

// Event is a general stream chunk struct for common usage.
// Chunk is consist of one of the fields.
type Event struct {
	Output             *StringOutputs         `json:"output,omitempty"`
	ProgressNotice     *NamedProgressNotice   `json:"progress_notice,omitempty"`
	Prompt             *NamedPrompt           `json:"prompt,omitempty"`
	TokenUsage         *NamedTokenUsage       `json:"token_usage,omitempty"`
	Metadata           *LLMCallMetadata       `json:"metadata,omitempty"`
	SuggestedQuestions *LLMSuggestedQuestions `json:"suggested_questions,omitempty"`
	References         *References            `json:"references,omitempty"`
}
