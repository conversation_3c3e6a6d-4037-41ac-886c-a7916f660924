package entity

import (
	"encoding/json"
	"time"

	"code.byted.org/devgpt/kiwis/lib/config"
)

// runtime provides a set of rpc methods for server
// unlike AgentRunEvent, rpc calls wait for response
type AgentRunRPCMethod string

const (
	RPCKeepAlive               AgentRunRPCMethod = "runtime/keepalive"                 // heartbeat to ensure server is alive
	RPCRunAgent                AgentRunRPCMethod = "runtime/run"                       // user runs an agent
	RPCAbortAgent              AgentRunRPCMethod = "runtime/abort"                     // user stops the run
	RPCPauseAgent              AgentRunRPCMethod = "runtime/pause"                     // user pauses the run
	RPCResumeAgent             AgentRunRPCMethod = "runtime/resume"                    // user resumes the run
	RPCSyncPatchArtifact       AgentRunRPCMethod = "workspace/sync_patch_artifact"     // download artifact files, commit, push and complete artifact
	RPCGetAgentEventStream     AgentRunRPCMethod = "agent/get_stream"                  // get event stream of agent run
	RPCRetrieveEvents          AgentRunRPCMethod = "agent/retrieve"                    // retrieve events since index, and enable this stream
	RPCAgentEvent              AgentRunRPCMethod = "agent/event"                       // report agent events
	RPCSubmitToolCallResults   AgentRunRPCMethod = "agent/submit_tool"                 // submit tool call results
	RPCAgentSendMessage        AgentRunRPCMethod = "runtime/agent_send_message"        // user send new message to runtime
	RPCAgentGenerateExperience AgentRunRPCMethod = "runtime/agent_generate_experience" // agent generate experience
	RPCPing                    AgentRunRPCMethod = "runtime/ping"                      // ping runtime to check if it is alive
)

func (r AgentRunRPCMethod) String() string {
	return string(r)
}

type PingRequest = struct {
	Timestamp time.Time `json:"timestamp" mapstructure:"timestamp"`
}

type PingResponse = struct {
	Message string `json:"message" mapstructure:"message"`
}

type KeepAliveRequest = struct {
	RunID string `json:"run_id" mapstructure:"run_id"`
}

type AbortRunRequest = struct {
	RunID string `json:"run_id" mapstructure:"run_id"`
}

type PauseRunRequest = struct {
	RunID string `json:"run_id" mapstructure:"run_id"`
	// Exit indicates that if the run should exit.
	// false: the currently task will be canceled, and the agent is idle.
	// true: the currently task will be canceled, and the agent run will be persist into disk or DB, the process will exit.
	Exit bool `json:"exit" mapstructure:"exit"`
}

type ResumeRunRequest = struct {
	RunID string `json:"run_id" mapstructure:"run_id"`
}

type AddMessageRequest = struct {
	RunID   string            `json:"run_id" mapstructure:"run_id"`
	Input   *Message          `json:"input" mapstructure:"input"`
	Environ map[string]string `json:"environ,omitempty"` // refresh environ (primarily user credentials) for agent
}

type AddMessageResponse = struct {
	Messages []*Message `json:"messages" mapstructure:"messages"` // messages stored in agent
}

type RetrieveEventsRequest struct {
	RunID string `json:"run_id" mapstructure:"run_id"`
	Since int64  `json:"since" mapstructure:"since"`
	// id of (agent) event bus to retrieve events
	ReportTo string `json:"report_to" mapstructure:"report_to"`
}

type SubmitToolCallResultRequest struct {
	RunID   string            `json:"run_id" mapstructure:"run_id"`
	CallID  string            `json:"id" mapstructure:"id"`
	Name    string            `json:"name" mapstructure:"name"`
	Results map[string]any    `json:"results" mapstructure:"results"`
	Error   *string           `json:"error" mapstructure:"error"`
	Environ map[string]string `json:"environ,omitempty"`              // refresh environ (primarily user credentials) for agent
	Message *Message          `json:"message" mapstructure:"message"` // 如果有 message 则从 message 中获取参数
}

type SyncPatchArtifactRequest = struct {
	ArtifactID string `json:"artifact_id" mapstructure:"artifact_id"`
	// credential used to push commit to codebase
	CodebaseUserJWT string `json:"codebase_user_jwt" mapstructure:"codebase_user_jwt"`
}

type GetAgentEventStreamRequest = struct {
	RunID string `json:"run_id" mapstructure:"run_id"`
	// indicates that this connection consumes events and will persist data in db
	Server bool `json:"server" mapstructure:"server"`
}

type GetAgentEventStreamResult struct {
	BusURI string `json:"bus_uri" mapstructure:"bus_uri"`
}

type AckResult struct {
	Harmful     bool   `json:"harmful" mapstructure:"harmful"`         // whether the message is harmful and should not be counted for planning
	Intercepted bool   `json:"intercepted" mapstructure:"intercepted"` // whether the message is intercepted and should not be executed
	ReplyTo     string `json:"reply_to" mapstructure:"reply_to"`
	Response    string `json:"response" mapstructure:"response"`
}

type GenerateExperienceRequest struct {
	RunID      string `json:"run_id" mapstructure:"run_id"`
	TemplateID string `json:"template_id" mapstructure:"template_id"`
	// Checkpoint 目前使用 unix 时间戳，表示用于生成经验的任务数据截止点。
	Checkpoint string `json:"checkpoint" mapstructure:"checkpoint"`

	UserQueryTemplate *ExpUserQueryTemplate `json:"user_query_template" mapstructure:"user_query_template"`

	// 要生成的经验类型：
	// user_query_template: 生成 query 模版和占位符（默认生成）
	// progress_plan: 生成 plan 级别经验（可选）
	// exp_sop: 生成 SOP/Workflow 级别经验（可选）
	GenerateTypes GenerateExperienceTypes `json:"generate_types" mapstructure:"generate_types"`

	// 是否异步生成经验，将耗时长的生成完成后，再通过回调提交结果。
	// 目前主要是 progress_plan 和 exp_sop.
	Async bool `json:"async" mapstructure:"async"`
}

type GenerateExperienceTypes struct {
	ProgressPlan bool `json:"progress_plan" mapstructure:"progress_plan"`
	SOP          bool `json:"sop" mapstructure:"sop"`
}

type GenerateExperienceResponse struct {
	UserQueryTemplate *ExpUserQueryTemplate `json:"user_query_template" mapstructure:"user_query_template"`
	ExpSOP            *ExpSOP               `json:"exp_sop" mapstructure:"exp_sop"`
	ProgressPlan      *string               `json:"progress_plan" mapstructure:"progress_plan"`
}

type RuntimeReadyStatus string

const (
	RuntimeReadyStatusUnknown RuntimeReadyStatus = "unknown"
	RuntimeReadyStatusReady   RuntimeReadyStatus = "ready"
	RuntimeReadyStatusFailed  RuntimeReadyStatus = "failed"
)

// Environment variables used to create runtime server and agent process
const (
	// PER session environment variables
	RuntimeEnvironDebug              = "IRIS_ENABLE_DEBUG"
	RuntimeEnvironSessionID          = "IRIS_SESSION_ID"
	RuntimeEnvironPort               = "IRIS_RUNTIME_PORT"
	RuntimeEnvironAPIBaseURL         = "IRIS_RUNTIME_API_BASE_URL"
	RuntimeEnvironLLMBaseURL         = "IRIS_LLM_API_BASE_URL"
	RuntimeEnvironCodebaseAPIBaseURL = "IRIS_CODEBASE_API_BASE_URL"
	RuntimeEnvironSUID               = "IRIS_RUNTIME_SUID" // setuid for secure agent execution
	RuntimeDockerArch                = "IRIS_DOCKER_ARCH"  // SCM 编译的二进制是 amd64 的，容器初始化脚本需要判断 host arch
	RuntimeEnvironCubeID             = "CUBE_ID"

	// PER run environment variables
	RuntimeEnvironRunID              = "IRIS_RUN_ID"
	RuntimeEnvironLLMToken           = "IRIS_LLM_TOKEN"
	RuntimeEnvironUserCloudJWT       = "IRIS_USER_CLOUD_JWT"
	RuntimeEnvironUserCodebaseJWT    = "IRIS_USER_CODEBASE_JWT"
	RuntimeEnvironServiceCodebaseJWT = "IRIS_SERVICE_CODEBASE_JWT"
	RuntimeEnvironAgentToken         = "IRIS_AGENT_TOKEN"
	RuntimeEnvironWorkspacePath      = "IRIS_WORKSPACE_PATH"
	RuntimeEnvironBrowserHTTPProxy   = "IRIS_BROWSER_HTTP_PROXY"
	RuntimeEnvironBrowserHTTPSProxy  = "IRIS_BROWSER_HTTPS_PROXY"
	RuntimeEnvironBrowserNoProxy     = "IRIS_BROWSER_NO_PROXY"
	RuntimeEnvironBrowserHeadless    = "IRIS_BROWSER_HEADLESS" // true or false
	RuntimeEnvironSessionType        = "IRIS_SESSION_TYPE"
	RunTimeLarkAppID                 = "IRIS_LARK_APP_ID"
	RunTimeLarkAppSecret             = "IRIS_LARK_APP_Secret"
	RunTimeLarkUserAccessToken       = "IRIS_LARK_USER_ACCESS_TOKEN"
	RunTimeLarkUserOpenID            = "IRIS_LARK_USER_OPEN_ID"
	RunTimeLarkByteCloudServiceToken = "IRIS_LARK_BYTE_CLOUD_SERVICE_TOKEN"
	RunTimeLarkRedirectURL           = "IRIS_LARK_REDIRECT_URL"
	RuntimeNextCodeAppID             = "IRIS_NEXT_CODE_APP_ID"
	RuntimeNextCodeSecret            = "IRIS_NEXT_CODE_SECRET"
	RuntimeNextCodeUserJWT           = "IRIS_NEXT_CODE_USER_JWT"
	RuntimeTCEEnv                    = "TCE_ENV"
	RuntimeTCEHostEnv                = "TCE_HOST_ENV"

	// DeepWiki environment variables
	RuntimeDeepWikiNextCodeAppID      = "DEEPWIKI_NEXT_CODE_APP_ID"
	RuntimeDeepWikiNextCodeSecret     = "DEEPWIKI_NEXT_CODE_SECRET"
	RuntimeDeepWikiCodebaseServiceJWT = "DEEPWIKI_CODEBASE_SERVICE_JWT"
)

type RuntimeParameterKey string

// Agent Run Common Parameters
const (
	RuntimeParametersUserLocale          RuntimeParameterKey = "locale"                // string, e.g. "zh-CN", "en-US"
	RuntimeParametersEnableInternalTools RuntimeParameterKey = "enable_internal_tools" // bool, enable internal tools
	RuntimeParametersMCPs                RuntimeParameterKey = "mcps"
	RuntimeParametersRunWithExperience   RuntimeParameterKey = "run_with_experience"
	RuntimeParametersDetectionResult     RuntimeParameterKey = "detection_result"
	// bool, disable tool call summarizer and summarize at server side
	RuntimeParametersDisableToolSummarizer RuntimeParameterKey = "disable_tool_summarizer"
	RuntimeParametersDatasetID             RuntimeParameterKey = "dataset_id"
	RuntimeParametersSpaceID               RuntimeParameterKey = "space_id"
)

type RuntimeParameters struct {
	UserLocale          string `json:"locale" mapstructure:"locale"`                               // string, e.g. "zh-CN", "en-US"
	EnableInternalTools bool   `json:"enable_internal_tools" mapstructure:"enable_internal_tools"` // bool, enable internal tools
}

// general agent version name
const (
	GeneralAgentVersionIntern = "intern"         // 小帅
	GeneralAgentVersionNewbie = "newbie"         // 小美
	GeneralAgentVersionExpert = "invited_expert" // 大壮
)

type PromptConfigMetadata struct {
	Key     string `json:"key"`
	ID      string `json:"id"`
	Version int    `json:"version"`
}

type PromptConfig struct {
	Prompts []*PromptConfigMetadata `json:"prompts"`
}

type AgentRunConfig struct {
	CustomConfig *json.RawMessage `json:"custom_config,omitempty" yaml:"custom_config,omitempty"`
	PromptConfig *PromptConfig    `json:"prompt_config,omitempty" yaml:"prompt_config,omitempty"`
}

// UserInfo contains extra information about a real user
type UserInfo struct {
	Email string `json:"email"`
	Name  string `json:"name"`
}

type RunAgentRequest struct {
	AgentName      string                      `json:"agent_name" yaml:"agent_name"`
	AssignmentID   string                      `json:"assignment_id" yaml:"assignment_id"`
	RunID          string                      `json:"run_id" yaml:"run_id"`
	SessionID      string                      `json:"session_id" yaml:"session_id"`
	User           User                        `json:"user" yaml:"user"`
	UserInfo       *UserInfo                   `json:"user_info,omitempty" yaml:"user_info,omitempty"`
	Input          *Message                    `json:"input,omitempty" yaml:"input,omitempty"`
	Config         *config.AgentRunConfig      `json:"config,omitempty" yaml:"config,omitempty"` // server provided config to control agent behavior
	AgentRunConfig *AgentRunConfig             `json:"agent_run_config,omitempty" yaml:"agent_run_config,omitempty"`
	Parameters     map[RuntimeParameterKey]any `json:"parameters,omitempty" yaml:"parameters,omitempty"` // user provided parameters
	Environ        map[string]string           `json:"environ,omitempty" yaml:"environ,omitempty"`       // extra environment variables to pass to agent
	ExitOnFinish   bool                        `json:"exit_on_finish" yaml:"exit_on_finish"`             // DEBUG option to stop runtime after finish
	Restart        bool                        `json:"restart" yaml:"restart"`
}

type RunAgentResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

type RequireConnectionRequest struct {
	RunID     string `json:"run_id"`
	SessionID string `json:"session_id"`
}

type RequireConnectionResponse struct {
	Message string `json:"message"`
}

type TokenType string

var (
	TokenTypeCodebase TokenType = "codebase"
	TokenTypeNextCode TokenType = "nextcode"
)

type RefreshTokenRequest struct {
	TokenType TokenType `json:"token_type"`
	Token     string    `json:"token"`
}

type RefreshTokenResponse struct {
	Token string `json:"token"`
}

// RuntimeClientName is the name(identifier) of the agent runtime
// used in codfish.CtxCallSession to send message to the runtime over event bus
const RuntimeClientName = "iris-runtime"
