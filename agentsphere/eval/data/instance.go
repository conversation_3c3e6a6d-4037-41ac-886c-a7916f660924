package evaldata

import (
	"strings"

	"code.byted.org/devgpt/kiwis/agentsphere/eval/entity"
)

// combined the entity.Instance
type Instance struct {
	entity.Instance
}

func NewInstance(s string) *Instance {
	i := new(Instance)
	i.Content = s
	if i.ID == "" {
		i.ID = extractID(s)
	}
	return i
}
func extractID(content string) string {
	strs := strings.Split(content, "error_id=")
	if len(strs) > 1 {
		return strs[1]
	}
	return content[max(0, len(content)-8):]
}
