package runtimehandler

import (
	"context"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"time"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/middleware/hertz/pkg/app"
	"github.com/hertz-contrib/sse"
	"github.com/samber/lo"
	"github.com/sashabaranov/go-openai"

	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	runtimeservice "code.byted.org/devgpt/kiwis/agentsphere/runtime/service"
	serverservice "code.byted.org/devgpt/kiwis/agentsphere/server/service"
	sessionservice "code.byted.org/devgpt/kiwis/agentsphere/session/service"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/agentsphere"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/common"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/devgpt/kiwis/copilotstack/common/auth/handler"
	"code.byted.org/devgpt/kiwis/copilotstack/llm"
	"code.byted.org/devgpt/kiwis/lib/apierror"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/devgpt/kiwis/port/rocketmq"
)

type Handler struct {
	Service        *runtimeservice.Service
	ServerService  *serverservice.Service
	SessionService *sessionservice.Service
	OrchestratorMQ rocketmq.Client `name:"runtime_orchestrator_mq"`
	AuthM          *handler.AuthMiddleware
	LLMService     llm.Service
}

func (h *Handler) UpdateAgentRunRuntimeState(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[agentsphere.UpdateRuntimeStateRequest](ctx, c)
	if req == nil {
		return
	}

	session, err := h.Service.UpdateRuntimeState(ctx, runtimeservice.UpdateRuntimeStateOption{
		SessionID:              req.SessionID,
		Status:                 (*entity.SessionStatus)(&req.Status),
		RuntimeProcessURI:      &req.URI,
		RuntimeProcessPID:      lo.ToPtr(int(req.PID)),
		RuntimeProcessInitCost: time.Duration(req.InitTimeCost) * time.Millisecond,
		RuntimeProcessMessage:  &req.Message,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to update agent run state: %+v", err)
		c.JSON(http.StatusInternalServerError, agentsphere.UpdateRuntimeStateResponse{
			Message: err.Error(),
		})
		return
	}

	// containers created by AgentSpherePlatform are pre-created and does not have an agent run
	if req.Status == agentsphere.SessionStatusRunning && !session.CreatorID.Is(entity.AgentSpherePlatform) {
		// send init event to runtime if agent run is not started
		run, err := h.Service.GetAgentRun(ctx, runtimeservice.GetAgentRunOption{
			SessionID: req.SessionID,
			Sync:      true,
		})
		if err != nil {
			log.V1.CtxError(ctx, "failed to get agent run by session %s: %v", req.SessionID, err)
			c.JSON(http.StatusInternalServerError, agentsphere.UpdateRuntimeStateResponse{
				Message: err.Error(),
			})
			return
		}

		// if agent run is [not started, running], send init event to runtime
		// running may be caused by runtime crash, so we need to send init event to restart it
		if !run.Status.IsStopped() {
			log.V1.CtxInfo(ctx, "agent run has not started, send init event to runtime")
			go h.Service.InitAgentRun(context.Background(), runtimeservice.InitAgentRunOption{
				SessionID: req.SessionID,
				RunID:     run.ID,
			})
		}
	}

	c.JSON(200, agentsphere.UpdateRuntimeStateResponse{
		Message: "success",
	})
}

func (h *Handler) ConnectAndProcessEvents(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[entity.RequireConnectionRequest](ctx, c)
	if req == nil {
		return
	}
	err := h.Service.ProcessEventsForAgentRun(ctx, runtimeservice.ProcessEventsForAgentRunOption{
		SessionID: req.SessionID,
		RunID:     req.RunID,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to connect runtime: %+v", err)
		c.JSON(http.StatusInternalServerError, entity.RequireConnectionResponse{
			Message: err.Error(),
		})
		return
	}
	c.JSON(200, entity.RequireConnectionResponse{
		Message: "success",
	})
}

func (h *Handler) RuntimeAction(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[agentsphere.RuntimeActionRequest](ctx, c)
	if req == nil {
		return
	}
	switch req.Action {
	case agentsphere.RuntimeActionStartWorkspace:
		_, err := h.Service.StartRuntimeWorkspace(ctx, runtimeservice.StartRuntimeWorkspaceOption{
			SessionID: req.SessionID,
		})
		if err != nil {
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "%s", err.Error())
			return
		}
		c.JSON(200, agentsphere.RuntimeActionStartResponse{})
	case agentsphere.RuntimeActionStopWorkspace:
		err := h.Service.StopRuntimeWorkspace(ctx, runtimeservice.StopRuntimeWorkspaceOption{
			SessionID: req.SessionID,
		})
		if err != nil {
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "%s", err.Error())
			return
		}
		c.JSON(200, agentsphere.RuntimeActionStopResponse{})
	case agentsphere.RuntimeActionSyncPatch:
		var params agentsphere.RuntimeActionSyncPatchRequest
		err := json.Unmarshal([]byte(req.Parameters), &params)
		if err != nil {
			hertz.JSONMessage(c, http.StatusBadRequest, apierror.ErrParamInvalid.Code, "%s", err.Error())
			return
		}
		account, _ := h.AuthM.GetAccount(ctx, c)
		resp, err := h.Service.SyncPatchArtifact(ctx, runtimeservice.SyncPatchArtifactOption{
			User:       account,
			SessionID:  req.SessionID,
			ArtifactID: params.ArtifactID,
		})
		if err != nil {
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "%s", err.Error())
			return
		}
		c.JSON(200, agentsphere.RuntimeActionSyncPatchResponse{Artifact: resp.Artifact})
	default:
		hertz.JSONMessage(c, http.StatusBadRequest, apierror.ErrParamInvalid.Code, "action not supported: %s", req.Action)
	}
}

func (h *Handler) SubmitToolCallResults(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[agentsphere.SubmitToolCallResultsRequest](ctx, c)
	if req == nil {
		return
	}

	// TODO(cyx): check authorization.
	err := h.Service.SubmitToolCallResults(ctx, runtimeservice.SubmitToolCallResultsOption{
		SessionID: req.SessionID,
		ID:        req.ID,
		Results:   conv.MapStringAny(req.Results),
		Err:       req.Error,
	})
	if err != nil {
		log.V1.CtxWarn(ctx, "failed to submit tool call results: %+v", err)
		c.JSON(http.StatusBadRequest, agentsphere.SubmitToolCallResultsResponse{
			Message: err.Error(),
		})
		return
	}

	c.JSON(200, agentsphere.SubmitToolCallResultsResponse{
		Message: "success",
	})
}

type ChatRequest struct {
	openai.ChatCompletionRequest
	Thinking *config.ThinkingConfig `json:"thinking,omitempty"`
}

// ChatStream is compatible with openai chat/completion.
func (h *Handler) ChatStream(ctx context.Context, c *app.RequestContext) {
	var req ChatRequest
	if err := c.Bind(&req); err != nil {
		log.V1.CtxWarn(ctx, "[Chat] bind param error:%s", err.Error())
		hertz.JSONMessage(c, http.StatusBadRequest, apierror.ErrParamInvalid.Code, err.Error())
		return
	}
	llmServiceReq := llm.GetChatCompletionsRequestFromOpenAI(req.ChatCompletionRequest)
	llmServiceReq.Tag = string(c.GetHeader(framework.LLMTagKey))
	if req.Thinking != nil {
		llmServiceReq.Thinking = &llm.ThinkingOpt{
			Type:            req.Thinking.Type,
			BudgetTokens:    req.Thinking.BudgetTokens,
			IncludeThoughts: req.Thinking.IncludeThoughts,
		}
	}
	res, err := h.LLMService.ChatCompletion(ctx, llmServiceReq)
	if err != nil {
		log.V1.CtxError(ctx, "failed to do chat: %v", err)
		hertz.JSONMessage(c, http.StatusBadRequest, apierror.GetErrorCode(err), err.Error())
		return
	}
	defer res.Close(ctx)

	if !req.Stream {
		aggRes, err := res.Aggregation(ctx)
		if err != nil {
			log.V1.CtxError(ctx, "failed to aggregate response: %v", err)
			hertz.JSONMessage(c, http.StatusBadRequest, apierror.GetErrorCode(err), err.Error())
			return
		}
		c.JSON(http.StatusOK, llm.GetOpenAIChatCompletionsResponse(*aggRes))
		return
	}

	c.SetContentType("text/event-stream")
	s := sse.NewStream(c)
	defer s.Publish(&sse.Event{Data: []byte("[DONE]")})
	for {
		chunk, err := res.NextChunk(ctx)
		if err != nil {
			if errors.Is(err, io.EOF) {
				return
			}
			log.V1.CtxWarn(ctx, "failed to get next chunk: %v", err)
			return
		}
		openaiChunk := llm.GetOpenAIChatCompletionsStreamResponse(*chunk)
		data := conv.JSONBytes(openaiChunk)

		if err := s.Publish(&sse.Event{
			// Add space. openai process stream requires "data: {\"id":...}", while hertz-contrib/sse send "data:{\"id":...}" with no space.
			Data: append([]byte(" "), data...),
		}); err != nil { // Stop if client is disconnected, or it will get OOM.
			log.V1.CtxWarn(ctx, "failed to publish chunk: %v", err)
			return
		}
	}
}
