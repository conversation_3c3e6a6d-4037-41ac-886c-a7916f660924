package runtimeservice

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"runtime/debug"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors/response"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/memory"
	queryrecognizer "code.byted.org/devgpt/kiwis/agentsphere/agents/planact/query_preprocessor"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/util"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	nextdal "code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/signal"
	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/gopkg/logs/v2/log"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/sashabaranov/go-openai"
	"github.com/sirupsen/logrus"
)

type LogrusHook struct {
	ctx context.Context
}

var _ logrus.Hook = &LogrusHook{}

func (h *LogrusHook) Levels() []logrus.Level {
	return []logrus.Level{logrus.InfoLevel}
}

func (h *LogrusHook) Fire(entry *logrus.Entry) error {
	switch entry.Level {
	case logrus.DebugLevel:
		log.V1.CtxDebug(h.ctx, entry.Message)
	case logrus.InfoLevel:
		log.V1.CtxInfo(h.ctx, entry.Message)
	case logrus.ErrorLevel:
		log.V1.CtxError(h.ctx, entry.Message)
	case logrus.WarnLevel:
		log.V1.CtxWarn(h.ctx, entry.Message)
	}
	return nil
}

type NewRunContextOption struct {
	SessionID string
	Config    *config.AgentRunConfig
	Environ   *iris.RunEnviron
}

// TODO: serve agent runtime & context
func (s *Service) NewRunContext(ctx context.Context, opt NewRunContextOption) (*iris.AgentRunContext, error) {
	llm, err := framework.NewAzureOpenAILLM(os.Getenv("OPENAI_API_KEY"), fmt.Sprintf("%s%s/runtime", s.runtimeAPIConfig.APIBaseURL, s.runtimeAPIConfig.APIPrefix), func(conf *openai.ClientConfig) {
		conf.HTTPClient = util.OpenAIHTTPClientWithHeaders(conf.HTTPClient, func(req *http.Request) {
			req.Header.Add(entity.SessionIDHeader, opt.SessionID)
			logID := ctxvalues.LogIDDefault(req.Context())
			req.Header.Add("X-TT-LOGID", logID)
			req.Header.Add(framework.LLMTagKey, conv.DefaultAny[string](req.Context().Value(framework.LLMTagKey)))
			req.Header.Add(framework.LLMTraceIDKey, conv.DefaultAny[string](req.Context().Value(framework.LLMTraceIDKey)))
		})
	})
	if err != nil {
		return nil, err
	}
	logger := logrus.New()
	logger.SetOutput(io.Discard)
	logger.AddHook(&LogrusHook{ctx: ctx})
	state := &iris.AgentRunState{
		SessionID: opt.SessionID,
		Status:    iris.AgentRunStatusCreated,
		Memory: &memory.AgentMemory{
			ActionMemory: []*memory.ActionMemoryItem{},
			StatusMemory: map[string]*memory.StatusMemoryItem{},
		},
		Conversation: iris.NewConversation(),
		Signals:      signal.NewNotifier(),
		Variables:    make(map[string]any),
		Store:        make(map[string]any),
	}
	env := opt.Environ
	if env == nil {
		env = &iris.RunEnviron{
			Map: make(map[string]string),
		}
	}
	params := make(map[entity.RuntimeParameterKey]any)
	return iris.NewRunContext(ctx, nil, llm, logger, state, params, env, opt.Config, nil, nil,
		nil, nil, nil, nil, nil, nil, nil), nil
}

type ExtractMentionsOption struct {
	SessionID string
	Message   *iris.Message
	Account   *authentity.Account
}

func (s *Service) ExtractMentions(ctx context.Context, opt ExtractMentionsOption) ([]iris.Mention, error) {
	userCodebaseJWT := ""
	if opt.Account != nil {
		userCodebaseJWT = opt.Account.CodebaseUserJWT
	}
	run, err := s.NewRunContext(ctx, NewRunContextOption{
		SessionID: opt.SessionID,
		Config:    &config.AgentRunConfig{},
		Environ: &iris.RunEnviron{
			Map: map[string]string{
				string(entity.RuntimeEnvironUserCodebaseJWT): userCodebaseJWT,
			},
		},
	})
	if err != nil {
		return nil, errors.Errorf("failed to create run context: %v", err)
	}
	recognizer := queryrecognizer.URIRecognizer{}
	message := &iris.Message{
		ID:       opt.Message.ID,
		From:     iris.MessageFromUser,
		Content:  opt.Message.Content,
		Mentions: opt.Message.Mentions,
	}
	err = recognizer.Recognize(run, []*iris.Message{message})
	if err != nil {
		return nil, errors.Errorf("failed to recognize mentions: %v", err)
	}
	return message.Mentions, nil
}

type RunDetectAgentOption struct {
	SessionID string
	Message   entity.Message
}

func (s *Service) RunDetectAgent(ctx context.Context, opt RunDetectAgentOption) error {
	res, err := s.detect(ctx, opt)
	if err != nil {
		return err
	}

	resultStr := conv.JSONString(res)
	err = s.redis.Set(ctx, fmt.Sprintf("session:%s:intent_detect", opt.SessionID), resultStr, time.Hour*24)
	if err != nil {
		return errors.Errorf("failed to store detect agent result: %v", err)
	}
	return nil
}

func (s *Service) GetDetectAgentResult(ctx context.Context, sessionID string) (*entity.AckResult, error) {
	resStr, err := s.redis.Get(ctx, fmt.Sprintf("session:%s:intent_detect", sessionID))
	if err != nil {
		return nil, errors.Errorf("failed to get detect agent result: %v", err)
	}
	var res entity.AckResult
	err = json.Unmarshal([]byte(resStr), &res)
	if err != nil {
		return nil, errors.Errorf("failed to unmarshal detect agent result: %v", err)
	}
	return &res, nil
}

func (s *Service) detect(ctx context.Context, opt RunDetectAgentOption) (*entity.AckResult, error) {
	defer func() {
		if r := recover(); r != nil {
			log.V1.CtxError(ctx, "session %s: detect agent, panic: %v, stack: %s", opt.SessionID, r, string(debug.Stack()))
		}
	}()
	session, err := s.nextserverDao.GetSession(ctx, nextdal.GetSessionOption{
		ID:   opt.SessionID,
		Sync: true,
	})
	if err != nil {
		return nil, errors.Errorf("failed to get session %s: %v", opt.SessionID, err)
	}
	agentConfig := serverservice.GetGeneralAgentByRole(session.Role, session.RuntimeMetaData.AgentConfigVersionID)
	conf, err := s.getNextAgentConfig(ctx, opt.SessionID)
	if conf == nil {
		return nil, errors.Errorf("unknown agent %s@%s", agentConfig.Name, agentConfig.Version)
	}
	if err != nil {
		return nil, errors.Errorf("failed to get session %s: %v", opt.SessionID, err)
	}
	log.V1.CtxInfo(ctx, "session %s: run detect agent, agent: %s, version: %s, config: %+v", opt.SessionID, agentConfig.Name, agentConfig.Version, conf)
	var runConfig config.AgentRunConfig
	err = json.Unmarshal(*conf.CustomConfig, &runConfig)
	if err != nil {
		return nil, errors.Errorf("failed to unmarshal agent config: %v", err)
	}
	c, err := s.NewRunContext(ctx, NewRunContextOption{
		SessionID: opt.SessionID,
		Config:    &runConfig,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to create agent run context: %v", err)
		return nil, err
	}
	c.State.Conversation.AddUserMessage(&iris.Message{
		ID:      opt.Message.ID,
		From:    iris.MessageFromUser,
		Content: opt.Message.Content.Content,
		Attachments: lo.Map(opt.Message.Attachments, func(attachment entity.AttachmentMeta, _ int) iris.Attachment {
			return iris.Attachment{
				Type: iris.AttachmentTypeFile,
				Path: attachment.Filename,
			}
		}),
	})
	harmful, intercepted, replyTo, response := response.AckAndRespond(c)
	log.V1.CtxInfo(ctx, "session %s: detect agent, intercepted: %v, replyTo: %s, response: %s", opt.SessionID, intercepted, replyTo, response)
	return &entity.AckResult{
		Harmful:     harmful,
		Intercepted: intercepted,
		ReplyTo:     replyTo,
		Response:    response,
	}, nil
}
