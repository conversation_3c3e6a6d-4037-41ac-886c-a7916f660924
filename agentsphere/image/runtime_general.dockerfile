FROM hub.byted.org/base/debian.bookworm.python311

ARG TARGETARCH

ENV PATH="$PATH:/root/.local/bin" \
    HTTP_PROXY=http://sys-proxy-rd-relay.byted.org:3128 \
    HTTPS_PROXY=http://sys-proxy-rd-relay.byted.org:3128 \
    CHROME_BIN=/usr/bin/chromium \
    CHROMIUM_FLAGS="--no-sandbox --headless --disable-gpu --disable-software-rasterizer --disable-dev-shm-usage"

# libc:amd64 for M1 mac to test agent in local container
RUN dpkg --add-architecture amd64 && \
  apt update && \
  apt install -y\
    build-essential \
    libc6:amd64 \
    jq \
    sshpass \
    curl \
    wget \
    xz-utils \
    zip \
    unzip \
    tar \
    p7zip-full \
    ripgrep \
    bvc \
    git \
    file \
    x11vnc \
    xvfb \
    fluxbox \
    # browser-use related dependencies
    xfce4 \
    dbus-x11 \
    tigervnc-standalone-server \
    tigervnc-tools \
    chromium \
    chromium-driver \
    # video codec
    ffmpeg \
    # fonts
    fonts-freefont-ttf \
    fonts-ipafont-gothic \
    fonts-wqy-zenhei \
    fonts-thai-tlwg \
    fonts-kacst \
    fonts-symbola \
    fonts-noto-color-emoji \
    fonts-noto-cjk \
    graphviz \
    pandoc && \
    # install nodejs \
    if [ "${TARGETARCH}" = "amd64" ]; then NODE_ARCH="linux-x64"; \
      elif [ "${TARGETARCH}" = "arm64" ]; then NODE_ARCH="linux-arm64"; \
      else echo "Unsupported arch: ${TARGETARCH}" && exit 1;  \
    fi \
    && NODE_VERSION=22.17.0 \
    && wget -qO- "https://nodejs.org/dist/v${NODE_VERSION}/node-v${NODE_VERSION}-${NODE_ARCH}.tar.xz" | tar -xJ -C /usr/local \
    && mv /usr/local/node-v${NODE_VERSION}-${NODE_ARCH} /usr/local/node && \
    export PATH=/usr/local/node/bin:$PATH && \
    # proxy-login-automator
    npm i -g proxy-login-automator && \
    apt clean && \
    rm -rf /var/lib/apt/lists/* && \
    rm -rf /var/cache/apt/* && \
    # extra fonts for pdf generation, Source Han Sans (adobe) = Noto Sans CJK (google)
    mkdir -p /usr/share/fonts/truetype/source-han-sans && curl -LsSf https://github.com/adobe-fonts/source-han-sans/raw/release/Variable/TTF/SourceHanSansSC-VF.ttf -o /usr/share/fonts/truetype/source-han-sans/SourceHanSansSC-VF.ttf && \
    mkdir -p /usr/share/fonts/truetype/source-han-serif && curl -LsSf https://github.com/adobe-fonts/source-han-serif/raw/release/Variable/TTF/SourceHanSerifSC-VF.ttf -o /usr/share/fonts/truetype/source-han-serif/SourceHanSerifSC-VF.ttf && \
    mkdir -p /usr/share/fonts/truetype && curl -LsSf https://github.com/StellarCN/scp_zh/raw/refs/heads/master/fonts/SimHei.ttf -o /usr/share/fonts/truetype/SimHei.ttf

ENV PATH="$PATH:/usr/local/node/bin"

# Preinstall go since it's highly likely to fail to let llms install and configure goproxy themselves
# Preinstall go with cross-platform support (amd64 and arm64)
RUN if [ "${TARGETARCH}" = "amd64" ]; then \
      GO_ARCH="amd64"; \
    elif [ "${TARGETARCH}" = "arm64" ]; then \
      GO_ARCH="arm64"; \
    else \
      echo "Unsupported architecture: ${TARGETARCH}" && exit 1; \
    fi \
    && curl -LsSf https://go.dev/dl/go1.24.1.linux-${GO_ARCH}.tar.gz -o go.tar.gz \
    && tar -C /usr/local -xzf go.tar.gz \
    && rm go.tar.gz \
    && echo 'export PATH=$PATH:/usr/local/go/bin' >> /etc/profile \
    && echo 'export GOPATH=$HOME/go' >> /etc/profile


RUN curl -LsSf https://astral.sh/uv/install.sh | sh && \
  uv venv --python 3.11 && \
  uv pip install --upgrade pip && \
  # data analysis toolkits; install to default python as llms are tend to use default interpreter
  pip install \
    pandas \
    numpy \
    matplotlib \
    pillow \
    folium \
    requests \
    beautifulsoup4 \
    geopy \
    jinja2 \
    seaborn \
    plotly \
    graphviz \
    pydot \
    statsmodels \
    openpyxl && \
    # 修改 matplotlib 类库中所有 .mplstyle 文件，删除所有 font.family 和 font.sans-serif 相关的行
    find /usr/local/lib/python3.11/site-packages/matplotlib/mpl-data/stylelib -type f -name "*.mplstyle" -exec sh -c 'for file; do cp "$file" "$file.bak"; grep -Ev "^\s*(font\.family|font\.sans-serif)\s*:" "$file" > "${file}.tmp" && mv -f "${file}.tmp" "$file"; done' _ {} +

COPY agentsphere/agents/actions/external/browser_use /opt/browser-use

RUN cd /opt/browser-use && \
  uv sync && \
  uv run playwright install --with-deps --no-shell chromium

# third party mcp servers
RUN mkdir -p /opt/mcp_servers && \
  cd /opt/mcp_servers && \
# install servers
# arxiv \
  bvc clone devinfra/arxivmcpserver/server arxiv-mcp-server && \
  cd arxiv-mcp-server && \
  uv venv --python 3.11 && \
  uv pip install . && \
  uv run arxiv-mcp-server --storage-path . && \
  cd .. && \
# unsplash
  git clone https://github.com/hellokaton/unsplash-mcp-server.git && \
  cd unsplash-mcp-server && \
  uv venv --python 3.11 && \
  uv pip install . && \
  cd .. && \
# ppt mcp
  git clone https://gitlab+deploy-token-9675:<EMAIL>/bits/pptx_mcp_server.git && \
  cd pptx_mcp_server && \
  uv venv --python 3.11 && \
  uv pip install . && \
  cd .. && \
# figma\
  git clone https://github.com/ArchimedesCrypto/figma-mcp-chunked.git && \
  cd figma-mcp-chunked && \
  npm install && \
  npm run build && \
  cd .. && \
# google image search \
  # must -g (install global, then client can run anywhere)
  npm i -g @babel/parser \
    @apify/actors-mcp-server \
    @amap/amap-maps-mcp-server \
    @modelcontextprotocol/server-google-maps \
    @mermaid-js/mermaid-cli \
    @byted/build-mcp --registry=https://bnpm.byted.org \
    @pagepass/mcp --registry=https://bnpm.byted.org

# build from repo root
COPY agentsphere/image/install.sh /opt/install.sh

RUN chmod +x /opt/install.sh && /opt/install.sh

COPY agentsphere/image/gitconfig /etc/gitconfig

# set default font for matplotlib
RUN echo 'export MPLCONFIGDIR=/root/.matplotlib' >> /root/.bashrc \
    && mkdir -p /root/.matplotlib \
    && echo "font.family: sans-serif" > /root/.matplotlib/matplotlibrc \
    && echo "font.sans-serif: Noto Sans CJK JP" >> /root/.matplotlib/matplotlibrc

# 安装code-server
RUN apt install -y sshpass jq gettext-base && \
    curl -OL https://tosv.byted.org/obj/dolphin-inner/cusk/aime/code-server/code-server-4.100.3-linux-amd64.tar.gz && \
    tar xvf code-server-4.100.3-linux-amd64.tar.gz && \
    mv code-server-4.100.3-linux-amd64 /usr/lib/code-server && \
    rm -rf code-server-4.100.3-linux-amd64.tar.gz && \
    bvc clone stratos/cube/code_server /home/<USER>/.config/ && \
    echo '#!/usr/bin/env sh' > /usr/bin/code-server && \
    echo 'exec /usr/lib/code-server/bin/code-server "$@"' > /usr/bin/code-server && \
    chmod a+x /usr/bin/code-server && \
    echo '{}' > /home/<USER>/.config/code-server/vscode/coder.json && \
    rm -rf /home/<USER>/.config/code-server/vscode/logs/* && \
    rm -rf /home/<USER>/.config/code-server/*.log

# 预装nvm，pnpm，rush
ENV NVM_DIR=/root/.nvm
RUN curl -o- 'https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.2/install.sh' | bash && \
    \. ${NVM_DIR}/nvm.sh && \
    npm config set registry https://bnpm.byted.org && \
    touch ${NVM_DIR}/default-packages && \
    echo "pnpm" >> $NVM_DIR/default-packages && \
    echo "yarn" >> $NVM_DIR/default-packages && \
    echo "@microsoft/rush" >> $NVM_DIR/default-packages && \
    echo "@ies/eden-monorepo" >> $NVM_DIR/default-packages && \
    curl -fsSL https://get.pnpm.io/install.sh | bash - && \
    npm i @microsoft/rush -g && \
    npm i @ies/eden-monorepo -g && \
    npm i vite@6.0.1 -g

# 设置BASH_ENV使非交互式bash默认加载/root/.bash_profile
ENV BASH_ENV="/root/.bash_profile"
COPY agentsphere/image/bash_profile $BASH_ENV

ENV HTTPS_PROXY= \
    HTTP_PROXY= \
    PATH="$PATH:/usr/local/go/bin" \
    GOPATH="/root/go" \
    GOPROXY="https://goproxy.byted.org|direct" \
    GOPRIVATE="*.byted.org,*.everphoto.cn,git.smartisan.com" \
    GOSUMDB="sum.golang.google.cn" \
    MPLCONFIGDIR="/root/.matplotlib"

# install cosy
COPY agentsphere/image/install_cosy.sh /opt/install_cosy.sh
RUN chmod +x /opt/install_cosy.sh && /opt/install_cosy.sh
COPY agentsphere/image/config.cosy_rpc.local.toml /usr/local/cosy/config.cosy_rpc.local.toml
ENV PATH="$PATH:/usr/local/cosy"

# install aime_project_templates
RUN mkdir -p /tmp/aime_project_templates && wget -O /tmp/aime_project_templates/package.zip https://tosv.byted.org/obj/codeverse-code/aime/aime_project_templates/aime_project_templates_20250715_232337.zip && \
    cd /tmp/aime_project_templates && \
    unzip package.zip && \
    ls -la && \
    mkdir -p /opt/bin /opt/deploy/templates && \
    cp -r /tmp/aime_project_templates/bin/* /opt/bin/ && \
    cp -r /tmp/aime_project_templates/templates/* /opt/deploy/templates/ && \
    rm -rf /tmp/aime_project_templates
ENV PATH="$PATH:/opt/bin"

CMD ["/usr/local/bin/runtime/agentsphere_runtime"]
