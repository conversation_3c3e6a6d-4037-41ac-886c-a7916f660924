package debug

import (
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	mcpservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/mcp"
	"go.uber.org/fx"
)

type Service struct {
	dao        *dal.DAO
	mcpService mcpservice.Service
}

type CreateServiceOption struct {
	fx.In
	DAO        *dal.DAO
	MCPService mcpservice.Service
}

func NewService(opt CreateServiceOption) *Service {
	return &Service{dao: opt.DAO, mcpService: opt.MCPService}
}
