package lock

import (
	"context"
	"time"

	"code.byted.org/bytedance/redislock"
	"code.byted.org/devgpt/kiwis/port/redis"
)

var _ Lock = &Locker{}

type Lock interface {
	Acquire(ctx context.Context, key string) (redislock.ILocker, error)
}

type Locker struct {
	lock redislock.ILock
}

func NewLocker(bytedRedis *redis.BytedRedis) (*Locker, error) {
	if bytedRedis == nil {
		return nil, nil
	}
	redisClient := redislock.BytedRedis(bytedRedis.GetConn())
	option := redislock.NewDefaultOption()
	option.OpTimeout = 5 * time.Second
	option.MaxSurviveTime = 10 * time.Second
	lock, err := redislock.NewLock(redisClient, option)
	if err != nil {
		return nil, err
	}
	return &Locker{lock: lock}, nil
}

func (l *Locker) Acquire(ctx context.Context, key string) (redislock.ILocker, error) {
	locker, err := l.lock.Acquire(ctx, key)
	if err != nil {
		return nil, err
	}
	return locker, nil
}
