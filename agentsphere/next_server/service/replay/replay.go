package replay

import (
	"context"
	"encoding/json"
	"net/url"
	"strings"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	activityservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/activity"
	artifactservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/artifact"
	larkservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/lark"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/lock"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/uuid"
	"code.byted.org/devgpt/kiwis/port/lark"
	"code.byted.org/gopkg/logs/v2"
	larkdrive "github.com/larksuite/oapi-sdk-go/v3/service/drive/v1"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	poolsdk "github.com/sourcegraph/conc/pool"
	"go.uber.org/fx"
)

type Service struct {
	idGen           uuid.Generator
	dao             *dal.DAO
	artifactService *artifactservice.Service
	activityService *activityservice.Service
	larkClient      lark.Client
	tccConf         *config.AgentSphereTCCConfig
	lock            lock.Lock
}

type CreateServiceOption struct {
	fx.In
	DAO             *dal.DAO
	ArtifactService *artifactservice.Service
	ActivityService *activityservice.Service
	LarkClient      lark.Client
	TCCConf         *config.AgentSphereTCCConfig
	Lock            lock.Lock
}

func NewService(opt CreateServiceOption) (*Service, error) {
	return &Service{
		idGen:           uuid.GetDefaultGenerator(nil),
		dao:             opt.DAO,
		artifactService: opt.ArtifactService,
		activityService: opt.ActivityService,
		larkClient:      opt.LarkClient,
		tccConf:         opt.TCCConf,
		lock:            opt.Lock,
	}, nil
}

type CreateReplayOption struct {
	SessionID                     string
	EventOffsetStart              *int64
	EventOffsetEnd                *int64
	EventReadSync                 bool
	CreateFrom                    string
	SessionCollectionCreatorEmail *string
}

func (s *Service) CreateReplay(ctx context.Context, opt CreateReplayOption) (*entity.Replay, error) {
	// get artifact ids by session id (获取创建时间时最新的artifact id)
	artifactIDs, err := s.getReplaySessionArtifactIds(ctx, opt.SessionID)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get session artifact ids")
	}

	snapshotMeta := entity.ReplaySnapshotMeta{
		ArtifactIDs:      artifactIDs,
		EventOffsetStart: opt.EventOffsetStart,
		EventOffsetEnd:   opt.EventOffsetEnd,
	}
	replay, err := s.dao.CreateReplay(ctx, dal.CreateReplayOption{
		ID:           s.idGen.NewID(),
		SessionID:    opt.SessionID,
		SnapshotMeta: snapshotMeta,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create replay")
	}

	session, err := s.dao.GetSession(ctx, dal.GetSessionOption{ID: replay.SessionID})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get session")
	}

	creatorName := session.Creator

	// 异步将文档的owner权限转移给用户
	go func() {
		transferCtx, cancel := context.WithTimeout(context.Background(), 60*time.Second) // 新建一个ctx，超时时间为60s
		defer func() {
			cancel()
			if r := recover(); r != nil {
				logs.V1.CtxError(transferCtx, "failed to transfer lark doc owner permission: %v", r)
			}
		}()
		if creatorName == "" { // 如果没有传入用户名，则不转移飞书文档的owner权限
			logs.V1.CtxWarn(ctx, "no username provided, skipping transfer")
			return
		}

		larkUser, err := s.dao.GetLarkUser(ctx, creatorName, false)
		if err != nil || larkUser == nil {
			logs.V1.CtxError(transferCtx, "failed to get lark user %s, error: %v", creatorName, lo.Ternary(err != nil, err.Error(), "user not found"))
		}

		userEmail := lo.Ternary(larkUser.Email != "", larkUser.Email, larkUser.Username+"@bytedance.com")
		err = s.transferLarkDocOwnerPermissionMember(transferCtx, userEmail, artifactIDs, opt.SessionCollectionCreatorEmail)
		if err != nil {
			logs.V1.CtxError(transferCtx, "failed to transfer lark doc when create replay: %v", err)
		}
	}()

	// 处理运营活动的创建回放逻辑
	err = s.handleActivityCreateReplay(ctx, replay, creatorName, opt.CreateFrom)
	if err != nil {
		// 只记录错误，不影响创建回放的主逻辑
		logs.V1.CtxError(ctx, "failed to handle activity create replay: %v", err)
	}

	return replay, nil
}

func (s *Service) handleActivityCreateReplay(ctx context.Context, replay *entity.Replay, username string, from string) error {
	activity, _ := s.activityService.GetCurrentActivity(ctx)
	if activity == nil || !activity.IsActivated() || replay == nil {
		return errors.New("activity not found or already inactivated")
	}

	session, err := s.dao.GetSession(ctx, dal.GetSessionOption{ID: replay.SessionID})
	if err != nil {
		return errors.WithMessage(err, "failed to get session")
	}

	if !activity.InActivityTime(session.StartedAt) {
		return errors.New("session started_at is not in activity time")
	}

	if from == string(entity.ReplayFromUser) { // 如果是用户创建的，走激活码逻辑
		return s.activityService.HandleActivityReplayInviteCode(ctx, username, activityservice.ActivityCreateReplayOption{
			ReplayID:   replay.ID,
			SessionID:  replay.SessionID,
			ActivityID: activity.ID,
		})
	} else if from == string(entity.ReplayFromTask) { // 如果是运营创建的，则运营活动彩蛋逻辑
		return s.activityService.HandleActivityReplayTaskEgg(ctx, activityservice.ActivityCreateReplayOption{
			ReplayID:   replay.ID,
			SessionID:  replay.SessionID,
			ActivityID: activity.ID,
			LuckyNum:   lo.Ternary(activity.LuckyEggsPerTask > 0, activity.LuckyEggsPerTask, 1),
		})
	}

	return nil
}

// transferLarkDocOwnerPermissionMember 转移飞书文档的owner权限给指定用户
func (s *Service) transferLarkDocOwnerPermissionMember(ctx context.Context, userEmail string, artifactIDs []string, sessionCollectionCreatorEmail *string) error {
	// 遍历artifacts里的飞书文档列表，转移给用户
	for _, artifactID := range artifactIDs {
		// 检查 context 是否已取消
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		artifact, err := s.artifactService.GetArtifact(ctx, artifactservice.GetArtifactOption{
			ID: artifactID,
		})
		if err != nil {
			// return nil, errors.WithMessage(err, "failed to get artifact")
			logs.V1.CtxWarn(ctx, "failed to get artifact %s: %v", artifactID, err)
			continue
		}
		// 遍历filemeta检查飞书文档
		if artifact.Type != entity.ArtifactTypeLink { //跳过非link的artifact
			continue
		}
		for _, fileMeta := range artifact.FileMetas {
			// 不是链接不需要转移
			if fileMeta.Type != entity.ArtifactTypeLink {
				continue
			}
			// 不是飞书文档也不是飞书表格，不需要转移
			if fileMeta.SubType != entity.LinkArtifactKeySourceLarkDoc.String() &&
				fileMeta.SubType != entity.LinkArtifactKeySourceLarkSheet.String() {
				continue
			}

			// 解析飞书文档的链接
			var linkContent nextagent.LinkContent
			_ = json.Unmarshal([]byte(fileMeta.Content), &linkContent)

			parsedURL, err := url.Parse(linkContent.URL)
			if err != nil {
				return errors.New("invalid lark_url format, link: " + linkContent.URL)
			}

			parts := strings.Split(parsedURL.Path, "/")
			if len(parts) < 2 {
				logs.V1.CtxWarn(ctx, "invalid lark url %s: ", linkContent.URL)
				continue
			}
			docID, docType := parts[len(parts)-1], parts[len(parts)-2]
			docType, ok := larkservice.GetLarkDocType(docType)
			if !ok {
				logs.V1.CtxWarn(ctx, "invalid lark doc type %s: ", linkContent.URL)
				continue
			}

			// 根据机器人的OpenID，判断机器人是否有权限转移改文档的权限，如果没有说明转移过了
			reqDoc := &lark.RequestDoc{
				DocType:  docType,
				DocToken: docID,
			}
			meta, err := s.larkClient.GetFilesMeta(ctx, []*lark.RequestDoc{reqDoc}, "")
			if err != nil || meta == nil || len(meta.Metas) == 0 {
				logs.V1.CtxWarn(ctx, "failed to read lark_doc meta %s: ", linkContent.URL)
				continue
			}

			docMeta := meta.Metas[0]
			conf := s.tccConf.NeumaLarkAppConfig.GetValue()
			if *docMeta.OwnerId != conf.OpenID {
				logs.V1.CtxWarn(ctx, "aime is not the owner of lark_doc %s: ", linkContent.URL)
				continue
			}
			// 如果是session collection创建的回放，需要把session collection的owner也授权
			if sessionCollectionCreatorEmail != nil && lo.FromPtr(sessionCollectionCreatorEmail) != "" {
				err = s.larkClient.AddLarkFilePermission(ctx, docID, lo.FromPtr(sessionCollectionCreatorEmail), "full_access", docType)
				if err != nil {
					logs.V1.CtxWarn(ctx, "failed to add lark file permission for doc %s: %v, session collection creator email %s", docID, err, lo.FromPtr(sessionCollectionCreatorEmail))
				} else {
					logs.V1.CtxInfo(ctx, "successfully add lark file permission for doc %s to %s", docID, *sessionCollectionCreatorEmail)
				}
			}

			err = func() error {
				locker, err := s.lock.Acquire(ctx, larkservice.LockKeyTransferOwner)
				if err != nil {
					logs.V1.CtxWarn(ctx, "failed to acquire lock for transfer owner permission for doc %s: %v", docID, err)
					return err
				}
				defer locker.Release(ctx)

				transReq := larkdrive.NewTransferOwnerPermissionMemberReqBuilder().
					Token(docID).
					Type(docType).
					NeedNotification(false).
					RemoveOldOwner(false).
					Owner(larkdrive.NewOwnerBuilder().
						MemberType("email").
						MemberId(userEmail).
						Build()).
					Build()
				_, err = s.larkClient.TransferOwnerPermissionMember(ctx, transReq, "")
				if err != nil {
					logs.V1.CtxWarn(ctx, "failed to transfer owner permission for doc %s: %v", docID, err)
					return err
				}
				return nil
			}()
			logs.V1.CtxInfo(ctx, "successfully transferred owner permission for doc %s to %s", docID, userEmail)
		}
	}
	return nil
}

func (s *Service) getReplaySessionArtifactIds(ctx context.Context, sessionID string) ([]string, error) {
	parallelPool := poolsdk.New().WithMaxGoroutines(2).WithErrors()
	var (
		nonDisplayArtifactIDs []string
		displayArtifactIDs    []string
	)

	// 获取非结果文件内的artifact
	parallelPool.Go(func() error {
		var err error
		nonDisplayArtifactIDs, err = s.artifactService.GetSessionArtifactIDs(ctx, sessionID, artifactservice.GetSessionArtifactIDsOption{
			Types:   []entity.ArtifactType{entity.ArtifactTypeFile},
			Display: lo.ToPtr(false),
			Sync:    true,
		})
		return err
	})

	// 获取结果文件内的artifact
	parallelPool.Go(func() error {
		var err error
		displayArtifactIDs, err = s.artifactService.GetSessionArtifactIDs(ctx, sessionID, artifactservice.GetSessionArtifactIDsOption{
			Display: lo.ToPtr(true),
			Sync:    true,
		})
		return err
	})

	err := parallelPool.Wait()
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get replay session artifact ids")
	}

	artifactIDs := lo.Uniq(append(nonDisplayArtifactIDs, displayArtifactIDs...))
	return artifactIDs, nil
}

func (s *Service) getSessionArtifactIdsByEvents(ctx context.Context, events []*entity.Event) []string {
	var artifactIDs []string
	processedIDs := make(map[string]struct{}) //去重用
	for _, e := range events {
		// 从MessageCreateEvent类型的消息中取attachment
		if e.EventName != nextagent.EventNameMessageCreate {
			continue
		}

		// 过滤没有attachments的消息
		tmp := conv.DefaultAny[map[string]any](e.EventData.Data)
		data, err := conv.MapToStructByJSONTag[*nextagent.MessageCreateEvent](tmp)
		if err != nil {
			logs.V1.CtxWarn(ctx, "event %s: failed to convert event data to MessageCreateEvent: %v", e.ID, err)
			continue
		}
		if data == nil || data.Message == nil {
			logs.V1.CtxWarn(ctx, "event %s: event data is nil", e.ID)
			continue
		}
		attachments := data.GetMessage().GetAttachments()
		if len(attachments) == 0 {
			logs.V1.CtxWarn(ctx, "event %s: no attachments in event data", e.ID)
			continue
		}

		for _, attachment := range attachments {
			if attachment.ID != "" {
				if _, exists := processedIDs[attachment.ID]; !exists {
					artifactIDs = append(artifactIDs, attachment.ID)
					processedIDs[attachment.ID] = struct{}{}
				}
			}
		}
	}
	return artifactIDs
}
