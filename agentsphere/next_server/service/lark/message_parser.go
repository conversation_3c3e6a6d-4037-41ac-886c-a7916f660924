package lark

import (
	"encoding/json"
	"strings"

	"github.com/pkg/errors"
	"github.com/samber/lo"
)

// RichTextBody 对应飞书富文本的顶层JSON结构
type RichTextBody struct {
	Title   string      `json:"title"`
	Content [][]Element `json:"content"`
}

// Element 对应富文本中的一个具体元素（如文本、链接、@某人等）
type Element struct {
	Tag       string  `json:"tag"`
	Text      *string `json:"text,omitempty"`       // text 和 a 标签
	Href      *string `json:"href,omitempty"`       // a 标签
	UserID    *string `json:"user_id,omitempty"`    // at 标签
	UserName  *string `json:"user_name,omitempty"`  // at 标签
	ImageKey  *string `json:"image_key,omitempty"`  // img 标签
	FileKey   *string `json:"file_key,omitempty"`   // media 标签
	EmojiType *string `json:"emoji_type,omitempty"` // emotion 标签
	Content   *string `json:"content,omitempty"`    // code_block 标签
}

// RichTextToOncallData 将飞书富文本JSON字符串转换为纯文本
// 参考https://open.larkoffice.com/document/server-docs/im-v1/message-content-description/message_content
func RichTextToOncallData(jsonBody string, singleLine bool, withImage bool) (string, []string, error) {
	var body RichTextBody
	if err := json.Unmarshal([]byte(jsonBody), &body); err != nil {
		return "", nil, errors.WithMessage(err, "failed to unmarshal rich text JSON")
	}

	var (
		builder   strings.Builder
		imageKeys []string
	)

	for i, block := range body.Content {
		for _, element := range block {
			switch element.Tag {
			case "text":
				builder.WriteString(lo.FromPtr(element.Text))
			case "a":
				// 对于链接，只提取其可见文本
				builder.WriteString(lo.FromPtr(element.Text))
			case "at":
				// 对于@提及，提取其用户名
				builder.WriteString("@" + lo.FromPtr(element.UserName))
			case "img":
				builder.WriteString("[图片]")
				if withImage && element.ImageKey != nil {
					imageKeys = append(imageKeys, lo.FromPtr(element.ImageKey))
				}
			case "media":
				builder.WriteString("[视频]")
			case "emotion":
				builder.WriteString("[表情]")
			case "code_block":
				// 提取代码块的纯文本内容
				builder.WriteString(lo.FromPtr(element.Content))
			case "hr":
				builder.WriteString("---")
			default:
			}
		}

		if i < len(body.Content)-1 {
			if singleLine {
				builder.WriteString("  ")
			} else {
				builder.WriteString("\n")
			}
		}
	}

	return builder.String(), imageKeys, nil
}
