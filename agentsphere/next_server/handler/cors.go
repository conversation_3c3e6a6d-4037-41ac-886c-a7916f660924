package serverhandler

import (
	"net/http"
	"regexp"

	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/middleware/hertz_ext/v2/middlewares/cors"

	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/tcc"
)

type CORSMiddleware = app.HandlerFunc

func NewCORS(corsConfig *tcc.GenericConfig[config.CORSConfig]) (cors.Config, CORSMiddleware) {
	regs := make([]*regexp.Regexp, 0)
	if corsConfig != nil {
		for _, r := range corsConfig.GetPointer().AllowedOriginsRegex {
			reg := regexp.MustCompile(r)
			regs = append(regs, reg)
		}
	}
	config := cors.Config{
		AllowAllOrigins: false,
		AllowOrigins:    []string{},
		AllowOriginFunc: func(origin string) bool {
			if corsConfig == nil {
				return false
			}
			if isAllowed, ok := corsConfig.GetPointer().AllowOrigins[origin]; ok {
				return isAllowed
			}
			for _, reg := range regs {
				if reg.MatchString(origin) {
					return true
				}
			}
			return false
		},
		AllowMethods: []string{
			http.MethodOptions, http.MethodGet, http.MethodHead,
			http.MethodPost, http.MethodPut, http.MethodPatch, http.MethodDelete},
		AllowHeaders: []string{
			"Content-Type",
			"Authorization",
			"X-TT-LOGID", "X-TT-ENV", "X-JWT-TOKEN", "X-USE-PPE",
			"Pragma", "Cache-Control", "Expires",
		},
		AllowCredentials: true,
		ExposeHeaders:    []string{"Codebase-User-Jwt", "X-TT-LOGID", "X-TT-ENV", "X-JWT-TOKEN", "X-USE-PPE", "Authorization", "Content-Disposition", "X-Aime-Filename"},
	}
	middleware := cors.New(config)

	return config, middleware
}
