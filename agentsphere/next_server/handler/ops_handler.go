package serverhandler

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"strings"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/genexp"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	sessionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/session"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/template"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/trace"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	"code.byted.org/devgpt/kiwis/lib/hertz"

	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/middleware/hertz/pkg/common/utils"
)

type OpsListTemplatesRequest struct {
	TemplateID   *string `query:"template_id"`
	Creator      *string `query:"creator"`
	Name         *string `query:"name"`
	ShareID      *string `query:"share_id"`
	SessionID    *string `query:"session_id"`
	RunSessionID *string `query:"run_session_id"`

	Page     int `query:"page"`
	PageSize int `query:"page_size"`
}

func (h *Handler) OpsListTemplates(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[OpsListTemplatesRequest](ctx, c)
	if req == nil {
		return
	}
	var (
		templates []*entity.TemplateVersion
		err       error
	)
	switch {
	case req.RunSessionID != nil:
		session, err := h.SessionService.GetSession(ctx, sessionservice.GetSessionOption{
			SessionID: *req.RunSessionID,
			Sync:      false,
		})
		if err != nil {
			break
		}
		req.TemplateID = &session.TemplateID
		fallthrough
	case req.TemplateID != nil:
		var template *entity.TemplateVersion
		template, err = h.TemplateService.GetTemplate(ctx, *req.TemplateID)
		templates = append(templates, template)
	case req.ShareID != nil:
		var (
			share    *entity.TemplateShare
			template *entity.TemplateVersion
		)
		share, err = h.TemplateService.GetTemplateShareByID(ctx, *req.ShareID)
		if err == nil {
			template, err = h.TemplateService.GetTemplate(ctx, share.TemplateID)
			templates = append(templates, template)
		}
	default:
		templates, err = h.TemplateService.OpsListTemplates(ctx, template.OpsListTemplatesOption{
			PageNum:    req.Page,
			PageSize:   req.PageSize,
			SearchName: req.Name,
			Creator:    req.Creator,
			SessionID:  req.SessionID,
		})
	}
	if err != nil {
		hertz.JSONMessage(c, http.StatusBadRequest, -1, err.Error())
		return
	}
	c.JSON(http.StatusOK, utils.H{
		"templates": templates,
	})
}

type OpsEditTemplateRequest struct {
	TemplateID                string  `path:"template_id"`
	Name                      *string `json:"name"`
	ExpProgressPlan           *string `json:"progress_plan"`
	ExpSOP                    *string `json:"exp_sop"`
	Expired                   *bool   `json:"expired"`
	Edited                    *bool   `json:"edited"`
	QueryTemplate             *string `json:"query_template"`
	QueryTemplatePlaceholders *string `json:"query_template_placeholders"`
	Scope                     *string `json:"scope"`
	SupportMCPs               *string `json:"support_mcps"`
}

func (h *Handler) OpsEditTemplate(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[OpsEditTemplateRequest](ctx, c)
	if req == nil {
		return
	}
	tmpl, err := h.TemplateService.GetTemplate(ctx, req.TemplateID)
	if err != nil {
		hertz.JSONMessage(c, http.StatusBadRequest, -1, err.Error())
		return
	}
	tmpl, err = h.TemplateService.UpdateTemplate(ctx, template.UpdateTemplateOptions{
		TemplateID: req.TemplateID,
		OpsModifyTemplate: &entity.OpsModifyTemplate{
			Name:            req.Name,
			ExpProgressPlan: req.ExpProgressPlan,
			ExpSOP:          req.ExpSOP,
			Expired:         req.Expired,
			Edited:          req.Edited,
			QueryTemplate:   req.QueryTemplate,
			Scope:           req.Scope,
			SupportMCPs:     req.SupportMCPs,
		},
	})
	if err != nil {
		hertz.JSONMessage(c, http.StatusBadRequest, -1, err.Error())
		return
	}
	c.JSON(http.StatusOK, utils.H{
		"template": tmpl,
	})
}

type OpsBatchFixTemplatesRequest struct {
	TemplateIDs []string `json:"template_ids"`
	// FixType     string   `json:"fix_type"`
}

func (h *Handler) OpsBatchFixTemplates(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[OpsBatchFixTemplatesRequest](ctx, c)
	if req == nil {
		return
	}
	msgs := strings.Builder{}
	for _, id := range req.TemplateIDs {
		tmpl, err := h.TemplateService.GetTemplate(ctx, id)
		if err != nil {
			logs.V1.CtxWarn(ctx, "OpsBatchUpdateTemplates GetTemplate failed, err: %v", err)
			msgs.WriteString("模版" + id + "获取失败: " + err.Error() + "\n")
			continue
		}
		if tmpl.ExpSop == nil {
			msgs.WriteString(fmt.Sprintf("模版 %s 没有经验SOP\n", id))
			continue
		}
		updated := false
		for _, step := range tmpl.ExpSop.PlanSteps {
			for idx, tool := range step.Toolsets {
				if strings.HasPrefix(tool, "SlardarApp_") {
					step.Toolsets[idx] = "SlardarApp"
					msgs.WriteString(fmt.Sprintf("模版%s第%d步工具集%s修复为SlardarApp\n", id, idx, tool))
					updated = true
				}
			}
		}
		if !updated {
			msgs.WriteString(fmt.Sprintf("模版 %s 没有需要修复的工具集\n", id))
			continue
		}
		err = h.TemplateService.UpdateTemplateExperienceSOP(ctx, template.UpdateTemplateExperienceOption{
			TemplateID: id,
			ExpSOP:     tmpl.ExpSop,
		})
		if err != nil {
			logs.V1.CtxWarn(ctx, "OpsBatchUpdateTemplates UpdateTemplateExperienceSOP failed, err: %v", err)
			msgs.WriteString(fmt.Sprintf("模版 %s 更新失败: %s\n", id, err.Error()))
		} else {
			msgs.WriteString(fmt.Sprintf("模版 %s 更新成功\n", id))
		}
	}
	c.JSON(http.StatusOK, utils.H{
		"msg": msgs.String(),
	})
}

type OpsDownloadLogFileRequest struct {
	SessionID string `path:"session_id"`
	FilePath  string `path:"filepath"`
	// Special file key for special files:
	// trace, events, agent_log, iris_log.
	FileKey string `query:"file_key"`
}

var fileKeyToPath = map[string]string{
	"trace":     "trace.jsonl",
	"events":    "events.jsonl",
	"agent_log": "agent.log",
	"iris_log":  "iris.log",
}

// downloadFileFromSession downloads a file from either fileserver (if session is running) or log artifacts
func (h *Handler) downloadFileFromSession(ctx context.Context, session *nextagent.TraceSession, account interface{}, filepath string, filename string) (io.ReadCloser, error) {
	if session.RunDebugStatus == nextagent.Running && len(session.FileServerURL) > 0 {
		// Download the file from the fileserver.
		logs.V1.CtxInfo(ctx, "Downloading file from fileserver: %s", session.FileServerURL)
		length, file, err := h.TraceService.DownloadFileFromFileserver(ctx, trace.DownloadFileFromFileserverOption{
			FileServerURL: session.FileServerURL,
			FilePath:      filepath,
		})
		if err != nil {
			logs.V1.CtxError(ctx, "failed to download file from fileserver: %s, err: %v", filepath, err)
			return nil, err
		}
		logs.V1.CtxInfo(ctx, "Downloaded file from fileserver: %s, length: %d", filepath, length)
		return file, nil
	}

	// If the session runtime container is not running, try downloading from log artifacts.
	logs.V1.CtxInfo(ctx, "Downloading file from log artifacts: %s", filepath)
	_, file, err := h.TraceService.DownloadFileFromLogArtifacts(ctx, trace.DownloadFileFromLogArtifactsOption{
		SessionID: session.ID,
		Account:   account.(*authentity.Account),
		Filename:  filename,
	})
	if err != nil {
		logs.V1.CtxError(ctx, "failed to download file from log artifacts: %s, err: %v", filepath, err)
		return nil, err
	}
	logs.V1.CtxInfo(ctx, "Downloaded file from log artifacts: %s", filepath)
	return file, nil
}

func (h *Handler) OpsDownloadLogFile(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[OpsDownloadLogFileRequest](ctx, c)
	if req == nil {
		return
	}
	// If the session runtime container is still running, download the file from the fileserver.
	// Else try downloading from artifacts.
	traceSession, err := h.TraceService.GetTraceSession(ctx, trace.GetTraceSessionOption{
		SessionID: req.SessionID,
		ReplayID:  "",
	})
	if err != nil {
		hertz.JSONMessage(c, http.StatusBadRequest, -1, err.Error())
		return
	}
	account, _ := h.AuthM.GetAccount(ctx, c)
	// Quick set filepath from file_key for some special files, as these file names are dynamic.
	if len(req.FileKey) > 0 && req.FilePath == "none" {
		switch req.FileKey {
		case "trace":
			req.FilePath = fmt.Sprintf("log/iris-trace-%s.jsonl", traceSession.RunID)
		case "events":
			req.FilePath = fmt.Sprintf("log/iris-events-%s.jsonl", traceSession.RunID)
		case "agent_log":
			req.FilePath = fmt.Sprintf("log/agent-%s.log", traceSession.RunID)
		case "iris_log":
			req.FilePath = "log/iris.log"
		default:
			hertz.JSONMessage(c, http.StatusBadRequest, -1, "invalid file_key")
			return
		}
	}

	// If the session runtime container is not running, try downloading from log artifacts.
	if len(req.FileKey) == 0 {
		hertz.JSONMessage(c, http.StatusBadRequest, -1, "file_key is required")
		return
	}

	filename := fileKeyToPath[req.FileKey]
	if len(filename) == 0 {
		hertz.JSONMessage(c, http.StatusBadRequest, -1, "invalid file_key")
		return
	}

	file, err := h.downloadFileFromSession(ctx, traceSession, account, req.FilePath, filename)
	if err != nil {
		hertz.JSONMessage(c, http.StatusBadRequest, -1, err.Error())
		return
	}
	defer file.Close()

	// SetBodyStream with the file reader
	c.SetBodyStream(file, -1)
}

type TraceSessionTrajectoryRequest struct {
	SessionID string `path:"session_id"`
}

// SessionTrajectory is got a session user trajectory.
func (h *Handler) SessionTrajectory(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[TraceSessionTrajectoryRequest](ctx, c)
	if req == nil {
		return
	}
	// 1. 获取 session 信息
	session, err := h.TraceService.GetTraceSession(ctx, trace.GetTraceSessionOption{
		SessionID: req.SessionID,
		ReplayID:  "",
	})
	if err != nil {
		hertz.JSONMessage(c, http.StatusBadRequest, -1, err.Error())
		return
	}
	account, _ := h.AuthM.GetAccount(ctx, c)
	// 2. 下载 trace 文件
	filename := "trace.jsonl"
	filepath := "log/iris-trace-" + session.RunID + ".jsonl"

	file, err := h.downloadFileFromSession(ctx, session, account, filepath, filename)
	if err != nil {
		hertz.JSONMessage(c, http.StatusBadRequest, -1, "download trace file failed: "+err.Error())
		return
	}
	defer file.Close()

	traceBytes, err := io.ReadAll(file)
	if err != nil {
		hertz.JSONMessage(c, http.StatusInternalServerError, -1, "读取 trace 文件失败: "+err.Error())
		return
	}
	trajectory, err := genexp.ParseTraceFileWithReader(strings.NewReader(string(traceBytes)))
	if err != nil {
		hertz.JSONMessage(c, http.StatusInternalServerError, -1, "parse trace file failed: "+err.Error())
		return
	}
	c.JSON(http.StatusOK, map[string]any{
		"data": trajectory,
	})
}
