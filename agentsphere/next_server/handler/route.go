package serverhandler

import (
	"context"
	"net/http"

	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/middleware/hertz/pkg/app/server"
	"code.byted.org/middleware/hertz/pkg/common/utils"

	"code.byted.org/devgpt/kiwis/lib/hertz"
	lark_hertz "github.com/hertz-contrib/lark-hertz"
)

var _ hertz.Registerer = (*Handler)(nil)

func (h *Handler) Register(hertz *server.Hertz) {
	hertz.GET("/api/agents/v2/ping", func(ctx context.Context, c *app.RequestContext) {
		c.JSON(http.StatusOK, utils.H{
			"message": "pong",
		})
	})

	// showcase 和 replay 不需要鉴权
	hertz.GET("/api/agents/v2/showcases", h.ListShowcases)
	hertz.GET("/api/agents/v2/lark/auth", h.<PERSON>rk<PERSON>)

	// 部署页面资源拉取
	hertz.GET("/api/agents/v2/deployments", h.GetDeployments)
	hertz.GET("/api/agents/v2/deployments/*path", h.GetDeployments)

	// 飞书回放预览不需要鉴权
	hertz.POST("/api/agents/v2/share/replay/call_back", h.ShareReplayPreviewCallback)
	hertz.POST("/api/agents/v2/webhook/event", lark_hertz.NewEventHandlerFunc(h.larkEventHandler()))
	hertz.POST("/api/agents/v2/webhook/callback", lark_hertz.NewEventHandlerFunc(h.larkEventHandler()))

	// 经验生成回调接口不需要鉴权
	hertz.PUT("/api/agents/v2/templates/:template_id/experience", h.UpdateTemplateExperience)
	hertz.POST("/api/agents/v2/templates/:template_id/upload/stream", h.UploadTemplateExperienceFileStream)
	hertz.GET("/api/agents/v2/templates/file/:file_id/raw", h.DownloadTemplateExperienceFile)

	// 获取用户信息先不需要鉴权
	hertz.GET("/api/agents/v2/user/:user_name", h.GetUserInfo)
	hertz.POST("/api/agents/v2/save_event_key", h.SaveEventKey)

	// 获取全局 feature 不需要鉴权
	hertz.GET("/api/agents/v2/features/global", h.GetGlobalFeatures)

	// 获取评测数据，暂时不需要鉴权
	hertz.GET("/api/agents/v2/trace/session/agent_steps", h.ListSessionAgentStep)
	// 临时存储模板表单数据，不需要鉴权
	hertz.POST("/api/agents/v2/templates/shares/form_data", h.SaveTemplateShareFormData)
	hertz.GET("/api/agents/v2/templates/shares/form_data/:id", h.GetTemplateShareFormData)

	v2 := hertz.Group("/api/agents/v2", h.AuthM.RequireAccount())
	{
		v2.GET("/features", h.GetUserFeatures)
		v2.GET("/user/settings", h.GetUserSettings)
		v2.PUT("/user/settings", h.UpdateUserSettings)
		v2.GET("/resource/user/permission", h.GetUserResourcePermissions)

		v2.GET("/roles", h.GetUserRoles)
		v2.POST("/replay", h.CreateReplay)
		v2.GET("/replay/:replay_id", h.GetReplay)

		v2.POST("/sessions/collection", h.SessionCollection)
		v2.GET("/sessions/collection/list", h.ListSessionCollections)
		v2.POST("/sessions/collection/list", h.FilterSessionCollections)
		v2.POST("/sessions/collection/run", h.RunSessionCollections)
		v2.POST("/sessions/collection/download", h.DownloadSessionCollections)
		v2.POST("/sessions/collection/run/notification", h.SendSessionCollectionRunNotification)
		v2.POST("/sessions/collection/close", h.CloseSessionCollections)
		v2.GET("/sessions/collection/notification_templates", h.ListNotificationTemplates)
		v2.POST("/sessions/collection/notification", h.SendSessionCollectionNotification)

		v2.POST("/sessions", h.RequireSpaceID(), h.CreateSession)
		v2.GET("/sessions/:session_id", h.GetSession)
		v2.GET("/sessions", h.RequireSpaceID(), h.ListSessions)
		v2.DELETE("/sessions/:session_id", h.DeleteSession)
		v2.PATCH("/sessions/:session_id", h.UpdateSession)
		v2.POST("/sessions/:session_id/events", h.GetSessionStreamEvents)
		v2.GET("/sessions/:session_id/old_events", h.GetSessionOldEvents)
		v2.POST("/sessions/:session_id/message", h.CreateMessage)
		v2.GET("/sessions/check", h.CheckCreateSession)
		v2.GET("/sessions/partial/list", h.ListSessionPartial)
		v2.GET("/sessions/:session_id/agent_run", h.GetSessionAgentRun)
		v2.POST("/sessions/:session_id/tool_call", h.SubmitToolCall)

		// 快捷任务
		v2.POST("/message_with_template", h.RequireSpaceID(), h.CreateMessageWithTemplate)
		v2.GET("/templates/list", h.ListTemplates)
		v2.GET("/templates/:template_id/history_variables", h.GetHistoryTemplateVariables)
		v2.POST("/templates/draft", h.CreateTemplateDraft)
		v2.GET("/templates/:template_id/draft", h.GetTemplateDraft)
		v2.GET("/templates/:template_id", h.GetTemplate)
		v2.POST("/templates", h.CreateTemplate)
		v2.PUT("/templates/:template_id", h.UpdateTemplate)
		v2.DELETE("/templates/:template_id", h.DeleteTemplate)
		v2.GET("/templates/count", h.CountTemplates)
		// 模板收藏和分享
		v2.POST("/templates/:template_id/star", h.CreateTemplateStar)
		v2.DELETE("/templates/:template_id/star", h.DeleteTemplateStar)
		v2.POST("/templates/:template_id/share", h.CreateShareTemplate)
		v2.POST("/templates/shares/:share_id/user", h.CreateUserShareTemplate)
		v2.DELETE("/templates/shares/:share_id/user", h.DeleteUserShareTemplate)

		// MCP工具管理接口
		v2.POST("/mcp", h.CreateMCP)
		v2.PUT("/mcp/update", h.UpdateMCP)
		v2.POST("/mcp/list", h.ListMCP)
		v2.POST("/mcp/activation", h.ModifyMCPActivation)
		v2.POST("/mcp/validate", h.ValidateMCP)

		v2.GET("/lark/auth/check", h.CheckLarkAuth)
		v2.GET("/lark/auth/ticket", h.GetLarkTicket)
		v2.GET("/lark/get/lark_url", h.GetUserLarkURL)
		v2.POST("/lark/send/replay_link", h.SendLarkReplayLinkMessage)
		v2.GET("/lark/documents/:document_id/blocks", h.GetLarkDocxBlocks)

		v2.POST("/artifacts", h.CreateArtifact)
		v2.PUT("/artifacts/:artifact_id", h.UpdateArtifact)
		v2.PUT("/artifacts/:artifact_id/files/*path", h.UpdateArtifactFile)
		v2.POST("/artifacts/:artifact_id/upload", h.UploadArtifact)
		v2.POST("/artifacts/:artifact_id/upload/stream", h.UploadArtifactStream)
		v2.GET("/artifacts/list", h.ListArtifacts)
		v2.GET("/artifacts/:artifact_id/raw/*path", h.DownloadArtifact)
		v2.POST("/artifacts/download/batch", h.DownloadArtifactBatch)
		v2.POST("/artifacts/:artifact_id/files", h.RetrieveArtifactFiles)
		v2.GET("/artifacts/:artifact_id", h.GetArtifact)

		v2.POST("/deployments", h.CreateDeployments)

		v2.GET("/activity/progress", h.GetUserActivityProgress)
		v2.POST("/activity/verify", h.VerifyActivityAward)
		v2.PUT("/user/invitation_code/:invitation_code", h.BindInvitationCode)
		v2.POST("/user/grant_access", h.GrantUserAccess)
		// 内部开发使用
		v2.POST("/activity/:activity_id/invitation_code", h.RequireDeveloper(), h.CreateInvitationCode)
		v2.POST("/activity/:activity_id/invitation_code/distribute", h.RequireDeveloper(), h.DistributeInvitationCode)

		//knowledgebase
		v2.PUT("/datasets/:dataset_id/documents", h.UploadDocuments)
		v2.DELETE("/datasets/:dataset_id/documents/:document_id", h.DeleteDocument)
		v2.PUT("/datasets/:dataset_id/documents/:document_id", h.UpdateDocument)
		v2.GET("/datasets/:dataset_id/documents/:document_id", h.GetDocument)
		v2.GET("/datasets/:dataset_id/lark_documents", h.SearchLarkDocuments)
		v2.POST("/datasets/:dataset_id/recommend_documents", h.RecommendDocuments)
		v2.POST("/datasets/:dataset_id/documents", h.ListDocuments)
		v2.GET("/datasets/:dataset_id/documents/count", h.CountDocuments)
		// mention
		v2.GET("/mentions", h.SearchMentions)

		// Space相关的用户接口
		v2.GET("/space", h.GetSpace)
		v2.GET("/user/list/space", h.ListUserSpaces)
	}
	trace := v2.Group("trace", h.RequireDeveloper())
	{
		trace.GET("/session", h.TraceSession)
		trace.GET("/session/chat", h.TraceSessionChat)
		trace.POST("/events", h.TraceEvents)
		trace.POST("/actions/resume", h.ResumeRuntime)
		trace.POST("/actions/suspend", h.SuspendRuntime)
		trace.POST("/actions/delete", h.DeleteRuntime)
		trace.GET("/session/log", h.DownloadSessionLog)
		trace.GET("/session/documents", h.ListSessionDocuments)
		trace.POST("/session/documents/convert_to_lark", h.ConvertSessionDocumentToLark)
		// playground
		trace.GET("/models", h.ListModels)
		trace.POST("/:model/chat/completions", h.ChatStream)
	}

	// MCP playground with MCPPartner permission
	v2.POST("/trace/events/mcp", h.RequireMCPPartner(), h.TraceMCPEvents)

	// agent manage
	agent := v2.Group("/agent", h.RequireDeveloper())
	{
		agent.POST("", h.CreateAgent)
		agent.GET("", h.ListAgent)
		agent.GET("/:agent_id", h.GetAgent)
		agent.DELETE("/:agent_id", h.DeleteAgent)
		agent.PUT("/:agent_id", h.UpdateAgent)
		agent.POST("/config", h.CreateAgentConfig)
		agent.PUT("/config/:agent_config_id", h.UpdateAgentConfig)
		agent.DELETE("/config/:agent_config_id", h.DeleteAgentConfig)
		agent.GET("/config/:agent_config_id", h.GetAgentConfig)
		agent.GET("/config", h.ListAgentConfigs)
		agent.POST("/config/version", h.CreateAgentConfigVersion)
		agent.PUT("/config/version/:agent_config_version_id", h.UpdateAgentConfigVersion)
		agent.POST("/config/version/:agent_config_version_id/deploy", h.DeployAgentConfigVersion)
		agent.GET("/config/version", h.ListAgentConfigVersion)
		agent.GET("/config/version/:agent_config_version_id", h.GetAgentConfigVersion)
	}

	// prompt manage
	prompt := v2.Group("/prompt", h.RequireDeveloper())
	{
		prompt.POST("", h.CreatePrompt)
		prompt.GET("/:prompt_id", h.GetPrompt)
		prompt.PUT("/:prompt_id", h.UpdatePrompt)
		prompt.DELETE("/:prompt_id", h.DeletePrompt)
		prompt.GET("", h.ListPrompt)
		prompt.POST("/version", h.CreatePromptVersion)
		prompt.PUT("/version/:prompt_version_id", h.UpdatePromptVersion)
		prompt.GET("/version/download", h.DownloadPrompt)
		prompt.GET("/version", h.ListPromptVersion)
		prompt.GET("/version/:prompt_version_id", h.GetPromptVersion)
	}

	// space manage
	space := v2.Group("/space", h.RequireDeveloper())
	{
		space.POST("", h.CreateSpace)
		space.PUT("", h.UpdateSpace)
		space.POST("/personal", h.CreatePersonalSpace)
		space.GET("/list", h.ListAllProjectSpaces)
		space.DELETE("", h.DeleteSpace)

		// space members
		space.POST("/members", h.AddSpaceMembers)
		space.DELETE("/members", h.RemoveSpaceMembers)
		space.GET("/members", h.ListSpaceMembers)
	}

	internal := hertz.Group("/api/agents/v2/internal", h.SetInternalCall())
	{
		internal.POST("/artifacts", h.CreateArtifact)
		internal.GET("/artifacts/list", h.ListArtifacts)
		internal.GET("/artifacts/:artifact_id", h.GetArtifact)
		internal.PUT("/artifacts/:artifact_id", h.UpdateArtifact)
		internal.POST("/artifacts/:artifact_id/upload", h.UploadArtifact)
		internal.POST("/artifacts/:artifact_id/upload/stream", h.UploadArtifactStream)
		internal.POST("/artifacts/:artifact_id/files", h.RetrieveArtifactFiles)
		internal.GET("/artifacts/:artifact_id/raw/*path", h.DownloadArtifact)
		internal.GET("/artifacts/:artifact_id/export/*path", h.ExportDownloadArtifact, h.DownloadArtifact)
		internal.POST("/deployments", h.CreateDeployments)
		internal.GET("/prompt/version/download", h.DownloadPrompt)
		internal.POST("/datasets/:dataset_id/recall", h.RecallDataset)
	}

	// Build-In MCP manage
	buildInMCP := v2.Group("/build_in_mcp", h.RequireDeveloper())
	{
		buildInMCP.POST("/create", h.CreateBuildInMCP)
		buildInMCP.PUT("/update", h.UpdateBuildInMCP)
	}

	ops := v2.Group("/ops", h.RequireDeveloper())
	{
		// 模版查看与经验编辑
		ops.GET("/templates", h.OpsListTemplates)
		ops.PATCH("/templates/:template_id", h.OpsEditTemplate)
		ops.PATCH("/templates", h.OpsBatchFixTemplates)
		// Trace, Log file downloading.
		ops.GET("/sessions/:session_id/logs/:filepath", h.OpsDownloadLogFile)
		ops.GET("/sessions/:session_id/trajectory", h.SessionTrajectory)
	}

	v3 := hertz.Group("/api/agents/v3", h.AuthM.RequireAccount())
	{
		v3.GET("/sessions", h.RequireSpaceID(), h.ListSpaceSessions)
		v3.GET("/templates/list", h.ListSpaceTemplates)
		v3.GET("/templates/count", h.CountSpaceTemplates)
		v3.POST("/mcp/list", h.ListSpaceMCP)
	}

	debug := v2.Group("/devops")
	{
		debug.GET("/session", h.GetSessionDebugInfo)
		debug.GET("/session/events", h.GetSessionDebugEvents)
		debug.POST("/session/events_stream", h.GetSessionDebugEventsStream)
		debug.POST("/session/debug", h.DebugSessionEvent)
	}
}
