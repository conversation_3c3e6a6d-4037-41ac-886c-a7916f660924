package serverhandler

import (
	"context"
	"encoding/json"
	"net/http"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/session"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/common"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/middleware/hertz/pkg/app"
	"github.com/pkg/errors"
	"github.com/samber/lo"
)

func (h *Handler) GetSessionDebugInfo(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetDebugSessionRequest](ctx, c)
	if req == nil {
		return
	}

	account, _ := h.AuthM.GetAccount(ctx, c)
	_, ok := h.checkSessionPermission(ctx, c, SessionPermissionCheckOption{
		Account:   account,
		SessionID: lo.ToPtr(req.SessionID),
		Action:    entity.PermissionActionSessionTraceRead,
	})
	if !ok {
		return
	}

	session, err := h.SessionService.GetSession(ctx, session.GetSessionOption{
		SessionID: req.SessionID,
		Account:   account,
	})
	if err != nil {
		if errors.Is(err, serverservice.ErrSessionNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "session not found")
			return
		}
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrInternal), "failed to get session")
		return
	}

	mcpList := lo.Map(session.Context.MCPs, func(mcp *entity.MCP, _ int) *nextagent.MCPDetail {
		return &nextagent.MCPDetail{
			ID:          mcp.ID,
			Name:        mcp.Name,
			Description: mcp.Description,
			Source:      nextagent.MCPSource(mcp.Source),
			Tools:       []*nextagent.MCPTool{}, // todo: get tools from mcp service
		}
	})

	c.JSON(http.StatusOK, nextagent.GetDebugSessionResponse{
		ID:    session.ID,
		Title: session.Title,
		Context: &nextagent.DebugSessionContext{
			MCPs: mcpList,
		},
		CreatedAt: session.CreatedAt.Format(time.RFC3339),
		UpdatedAt: session.UpdatedAt.Format(time.RFC3339),
	})
}

func (h *Handler) GetSessionDebugEvents(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetDebugSessionEventsRequest](ctx, c)
	if req == nil {
		return
	}

	account, _ := h.AuthM.GetAccount(ctx, c)
	_, ok := h.checkSessionPermission(ctx, c, SessionPermissionCheckOption{
		Account:   account,
		SessionID: lo.ToPtr(req.SessionID),
		Action:    entity.PermissionActionSessionTraceRead,
	})
	if !ok {
		return
	}

	// mock events
	c.JSON(http.StatusOK, nextagent.GetDebugSessionEventsResponse{
		Events: []*nextagent.DebugSessionEvent{
			{
				ID:        "event_1_start",
				Type:      "round_start",
				CreatedAt: time.Now().Add(-10 * time.Minute).Format(time.RFC3339),
				Data: h.marshalEventData(map[string]interface{}{
					"round_id":     "round_1",
					"round_number": 1,
					"user_query":   "请帮我分析这段代码的性能问题",
				}),
			},
			{
				ID:        "event_1_1",
				Type:      "think",
				CreatedAt: time.Now().Add(-10*time.Minute + 5*time.Second).Format(time.RFC3339),
				Data: h.marshalEventData(map[string]interface{}{
					"round_id":   "round_1",
					"parent_id":  nil,
					"status":     "completed",
					"duration":   "2.3s",
					"content":    "正在分析用户提供的代码，需要检查性能瓶颈",
					"reasoning":  "首先需要理解代码结构，然后识别可能的性能问题",
					"key_points": []string{"代码复杂度", "内存使用", "算法效率"},
					// 供前端展示的额外信息
					"display_info": map[string]interface{}{
						"description": "正在分析用户提供的代码，需要检查性能瓶颈",
						"characters": []map[string]interface{}{
							{
								"name":        "user",
								"description": "用户",
								"parameters": map[string]interface{}{
									"type":    "text",
									"content": "请帮我分析这段代码的性能问题",
								},
							},
						},
						"tools": []map[string]interface{}{
							{
								"name":        "code_analyzer",
								"description": "分析代码复杂度和内存使用情况",
							},
						},
						"result": "分析代码复杂度和内存使用情况",
					},
				}),
			},
			{
				ID:        "event_1_1_1",
				Type:      "tool",
				CreatedAt: time.Now().Add(-10*time.Minute + 5*time.Second).Format(time.RFC3339),
				Data: h.marshalEventData(map[string]interface{}{
					"round_id":    "round_1",
					"parent_id":   "event_1_1",
					"status":      "completed",
					"duration":    "0.8s",
					"tool_name":   "code_analyzer",
					"parameters":  map[string]interface{}{"file_path": "/src/main.py", "analysis_type": "performance"},
					"result":      map[string]interface{}{"complexity": "O(n²)", "memory_usage": "high"},
					"description": "分析代码复杂度和内存使用情况",
				}),
			},
			{
				ID:        "event_1_1_1_1",
				Type:      "chat",
				CreatedAt: time.Now().Add(-10*time.Minute + 5*time.Second).Format(time.RFC3339),
				Data: h.marshalEventData(map[string]interface{}{
					"round_id":    "round_1",
					"parent_id":   "event_1_1_1",
					"status":      "completed",
					"duration":    "0.5s",
					"role":        "assistant",
					"content":     "我已经分析了您的代码，发现了几个性能问题：1. 算法复杂度为O(n²)，建议优化为O(n log n)；2. 内存使用过高，建议使用生成器；3. 可以通过缓存减少重复计算。",
					"model":       "gpt-4",
					"token_count": 125,
				}),
			},
		},
	})
}

func (h *Handler) marshalEventData(data interface{}) string {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return `{"error": "failed to marshal data"}`
	}
	return string(jsonData)
}

func (h *Handler) GetSessionDebugEventsStream(c context.Context, ctx *app.RequestContext) {

}

func (h *Handler) DebugSessionEvent(c context.Context, ctx *app.RequestContext) {

}
