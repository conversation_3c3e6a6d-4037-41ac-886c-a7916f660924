package dal

import (
	"context"

	"code.byted.org/devgpt/kiwis/agentsphere/journal/dal/po"
	entity "code.byted.org/devgpt/kiwis/copilotstack/journal"
	"code.byted.org/devgpt/kiwis/port/db"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gorm.io/datatypes"
)

type DAOImpl struct {
	Conn db.Client
}

var _ JournalDAO = &DAOImpl{}

func (d *DAOImpl) CreateChatCompletionLog(ctx context.Context, opt CreateChatCompletionLogOption) (*entity.PromptCompletion, error) {
	po := &po.NextChatCompletionPO{
		AppID:           opt.AppID,
		Username:        opt.Username,
		ModelName:       opt.ModelName,
		Status:          string(opt.Status),
		SessionID:       opt.SessionID,
		Prompt:          opt.Prompt,
		Type:            opt.Type,
		ContentRaw:      opt.ContentRaw,
		RequestMetadata: lo.ToPtr(datatypes.NewJSONType(opt.RequestMetadata)),
	}

	res := d.Conn.NewRequest(ctx).Create(po)
	if res.Error != nil {
		return nil, res.Error
	}
	return getNextChatCompletionFromPO(po), nil
}

func getNextChatCompletionFromPO(po *po.NextChatCompletionPO) *entity.PromptCompletion {
	return &entity.PromptCompletion{
		ID:              po.ID,
		AppID:           po.AppID,
		Username:        po.Username,
		ModelName:       po.ModelName,
		Status:          entity.PromptCompletionStatus(po.Status),
		SessionID:       po.SessionID,
		Prompt:          po.Prompt,
		Type:            entity.PromptCompletionType(po.Type),
		ContentRaw:      po.ContentRaw,
		RequestMetadata: lo.ToPtr(entity.PromptCompletionRequestMetadata(po.RequestMetadata.Data())),
		CreatedAt:       po.CreatedAt,
		UpdatedAt:       po.UpdatedAt,
	}
}

func (d *DAOImpl) UpdateChatCompletionLog(ctx context.Context, opt UpdateChatCompletionLogOption) (*entity.PromptCompletion, error) {
	po := &po.NextChatCompletionPO{}
	if err := d.Conn.NewRequest(ctx).Where("id = ?", opt.ID).First(po).Error; err != nil {
		return nil, errors.WithMessage(err, "failed to get db record")
	}

	updateMap := map[string]any{}

	if opt.AppID != nil {
		updateMap["app_id"] = opt.AppID
	}
	if opt.Username != nil {
		updateMap["username"] = opt.Username
	}
	if opt.ModelName != nil {
		updateMap["model_name"] = opt.ModelName
	}
	if opt.Status != nil {
		updateMap["status"] = string(*opt.Status)
	}
	if opt.SessionID != nil {
		updateMap["session_id"] = opt.SessionID
	}
	if opt.Prompt != nil {
		updateMap["prompt"] = opt.Prompt
	}
	if opt.Type != nil {
		updateMap["type"] = string(*opt.Type)
	}
	if opt.ContentRaw != nil {
		updateMap["content_raw"] = opt.ContentRaw
	}
	if opt.RequestMetadata != nil {
		if po.RequestMetadata != nil {
			preMetadata := lo.ToPtr(po.RequestMetadata.Data())
			updateMap["request_metadata"] = datatypes.NewJSONType(entity.PromptCompletionRequestMetadata{
				Stream:            preMetadata.Stream,
				MaxTokens:         preMetadata.MaxTokens,
				Temperature:       preMetadata.Temperature,
				TopP:              preMetadata.TopP,
				TopK:              preMetadata.TopK,
				N:                 preMetadata.N,
				FrequencyPenalty:  preMetadata.FrequencyPenalty,
				PresencePenalty:   preMetadata.PresencePenalty,
				MinNewTokens:      preMetadata.MinNewTokens,
				MaxPromptTokens:   preMetadata.MaxPromptTokens,
				RepetitionPenalty: preMetadata.RepetitionPenalty,
				ThinkingConfig:    preMetadata.ThinkingConfig,
				ID:                lo.Ternary(lo.IsEmpty(opt.RequestMetadata.ID), preMetadata.ID, opt.RequestMetadata.ID),
				Usage:             lo.Ternary(lo.IsEmpty(opt.RequestMetadata.Usage), preMetadata.Usage, opt.RequestMetadata.Usage),
				Latency:           lo.Ternary(lo.IsEmpty(opt.RequestMetadata.Latency), preMetadata.Latency, opt.RequestMetadata.Latency),
				FirstTokenLatency: lo.Ternary(lo.IsEmpty(opt.RequestMetadata.FirstTokenLatency), preMetadata.FirstTokenLatency, opt.RequestMetadata.FirstTokenLatency),
				FinishReason:      lo.Ternary(lo.IsEmpty(opt.RequestMetadata.FinishReason), preMetadata.FinishReason, opt.RequestMetadata.FinishReason),
				Error:             lo.Ternary(lo.IsEmpty(opt.RequestMetadata.Error), preMetadata.Error, opt.RequestMetadata.Error),
				LogID:             lo.Ternary(lo.IsEmpty(opt.RequestMetadata.LogID), preMetadata.LogID, opt.RequestMetadata.LogID),
			})
		}
	}

	res := d.Conn.NewRequest(ctx).Model(&po).Where("id = ?", opt.ID).Updates(updateMap)
	if res.Error != nil {
		return nil, res.Error
	}
	return getNextChatCompletionFromPO(po), nil
}

func (d *DAOImpl) ListChatCompletionLog(ctx context.Context, opt ListChatCompletionLogOption) (total int64, items []*entity.PromptCompletion, err error) {
	req := d.Conn.NewRequest(ctx).Model(&po.NextChatCompletionPO{})
	if opt.SessionID != nil && *opt.SessionID != "" {
		req.Where("session_id = ?", *opt.SessionID)
	}
	if opt.Status != nil {
		req.Where("status = ?", string(*opt.Status))
	}
	if opt.Type != nil {
		req.Where("type = ?", string(*opt.Type))
	}
	if opt.AppID != nil && *opt.AppID != "" {
		req.Where("app_id = ?", *opt.AppID)
	}

	if err := req.Count(&total).Error; err != nil {
		return 0, nil, errors.WithMessage(err, "failed to get db record")
	}

	pos := []*po.NextChatCompletionPO{}
	if err := req.Order("id DESC").Limit(int(opt.Limit)).Offset(int(opt.Offset)).Find(&pos).Error; err != nil {
		return 0, nil, errors.WithMessage(err, "failed to get db record")
	}
	return total, lo.Map(pos, func(p *po.NextChatCompletionPO, _ int) *entity.PromptCompletion {
		return getNextChatCompletionFromPO(p)
	}), nil
}
