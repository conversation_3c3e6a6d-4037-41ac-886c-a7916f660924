package journal

import (
	"code.byted.org/devgpt/kiwis/agentsphere/journal/dal"
	journalService "code.byted.org/devgpt/kiwis/copilotstack/journal"
	"code.byted.org/devgpt/kiwis/lib/di"
	"go.uber.org/fx"
)

var ServiceModule = fx.Options(
	fx.Provide(NewJournalService),
	fx.Provide(di.Bind(new(Service), new(journalService.JournalService))),
)

var DALModule = fx.Options(
	fx.Provide(di.StructConstructor(new(dal.DAOImpl))),
	fx.Provide(di.Bind(new(dal.DAOImpl), new(dal.JournalDAO))),
)

var Module = fx.Options(
	DALModule,
	ServiceModule,
)
