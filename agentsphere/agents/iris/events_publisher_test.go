package iris

import (
	_ "embed"
	"strings"
	"testing"

	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
)

func TestTruncateIfNeeded(t *testing.T) {
	logger := logrus.New()
	conf := TruncateConfig{
		MaxEventDataSize: 1 * 300,
		MaxFieldSize:     100,
		MaxArraySize:     10,
	}

	t.Run("小数据不应被截断", func(t *testing.T) {
		// 准备一个小型数据
		smallData := map[string]interface{}{
			"name": "test",
			"age":  30,
		}

		result := TruncateIfNeeded(smallData, logger, conf)

		// 验证数据没有被截断，结构保持完整
		resultMap, ok := result.(map[string]interface{})
		assert.True(t, ok)
		assert.Equal(t, "test", resultMap["name"])
		assert.Equal(t, 30, resultMap["age"])

		// 确保返回的是数据的副本，不是原始数据的引用
		assert.NotSame(t, &smallData, &result)
	})

	t.Run("大字符串应被截断", func(t *testing.T) {
		// 创建一个超过MaxFieldSize大小的字符串
		largeString := strings.Repeat("x", conf.MaxFieldSize+1000)
		data := map[string]interface{}{
			"largeField": largeString,
		}

		result := TruncateIfNeeded(data, logger, conf)

		// 验证字符串被截断
		resultMap, ok := result.(map[string]interface{})
		assert.True(t, ok)
		truncatedString, ok := resultMap["largeField"].(string)
		assert.True(t, ok)
		assert.Less(t, len(truncatedString), len(largeString))
		assert.Contains(t, truncatedString, "truncated")
	})

	t.Run("大数组应被截断", func(t *testing.T) {
		// 创建一个超过MaxArraySize大小的数组
		largeArray := make([]int, conf.MaxArraySize+100)
		for i := range largeArray {
			largeArray[i] = i
		}

		data := map[string]interface{}{
			"largeArray": largeArray,
		}

		result := TruncateIfNeeded(data, logger, conf)

		// 验证数组被截断
		resultMap, ok := result.(map[string]interface{})
		assert.True(t, ok)
		truncatedArray, ok := resultMap["largeArray"].([]int)
		assert.True(t, ok)
		assert.Len(t, truncatedArray, conf.MaxArraySize)
	})

	t.Run("嵌套结构应递归截断", func(t *testing.T) {
		// 创建嵌套的数据结构
		largeString := strings.Repeat("x", conf.MaxFieldSize+1000)
		nestedData := map[string]interface{}{
			"level1": map[string]interface{}{
				"level2": map[string]interface{}{
					"largeField": largeString,
				},
			},
		}

		result := TruncateIfNeeded(nestedData, logger, conf)

		// 验证嵌套结构中的大字符串被截断
		resultMap := result.(map[string]interface{})
		level1 := resultMap["level1"].(map[string]interface{})
		level2 := level1["level2"].(map[string]interface{})
		truncatedString := level2["largeField"].(string)

		assert.Less(t, len(truncatedString), len(largeString))
		assert.Contains(t, truncatedString, "truncated")
	})

	t.Run("测试命名类型", func(t *testing.T) {
		typeData := EventAgentToolCall{
			StepID:  "123",
			Outputs: map[string]interface{}{"test": strings.Repeat("x", conf.MaxFieldSize+1000)},
			Status:  ToolCallStatusCompleted,
		}
		result := TruncateIfNeeded(typeData, logger, conf)

		// 验证结果
		resultData, ok := result.(EventAgentToolCall)
		assert.True(t, ok, "结果应该是 AgentRunToolCall 类型")
		// 验证基本字段保持不变
		assert.Equal(t, "123", resultData.StepID)
		// 验证Outputs被截断
		assert.Less(t, len(resultData.Outputs["test"].(string)), conf.MaxFieldSize+1000)
		// 验证返回的是副本
		assert.NotSame(t, &typeData, &resultData)
		// 验证命名类型保持不变
		assert.Equal(t, ToolCallStatusCompleted, resultData.Status)
	})

}
