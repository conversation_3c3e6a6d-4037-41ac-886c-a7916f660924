---
ID: "planner_19_template"
Title: Planning By Lark Template with De<PERSON>ult SOP
EnabledIf: "agent in ['dynamic_planner']"
UsedWhen: |
  use it when user mentioned '参考飞书模板' or '基于飞书模板' or '完成这个模板' or other such words.
---

# Planning By Lark Template with Default SOP

## Core Principle: Logical Action Units (LAUs) for Template Processing
Apply the **Principle of Aggregation** to template-based tasks. Each LAU should represent a self-contained, meaningful phase that maximizes value and scope, avoiding fragmented micro-steps.

## Progress Planning Best Practices:
- **Aggregate related operations**: Combine data collection, template analysis, and initial planning into comprehensive steps
- **Maintain contextual coherence**: Each step should preserve the logical flow and dependencies of template processing
- **Maximize scope per step**: Design each step to encompass the largest meaningful unit of work that can be completed in one turn
- **Template continuity**: Ensure template context is maintained across all steps to prevent overwriting or loss
- Download/Retrieve all related file/data in first step.
- Read the template, understand the structure and purpose.
- Design analysis plan, based on fill in that template.
- Fill in the template step by step progressively, do not try fill all at once.
- After you get the template, pass it to every agent and step, to prevent agent from overwriting the template or missing it.
- If user has provided their own SOP, follow their SOP instead of the default plan below.

## Default Plan Structure for Template Processing:

When user provides a lark template but no specific SOP, follow this approach:

### Example Default Plan:

<plan>

- [ ] **LAU 1: Comprehensive Template Foundation Setup**
    - [ ] Download and consolidate all referenced documents, external data sources, and template files in a single comprehensive action
    - [ ] Perform complete template structure analysis, understanding purpose, requirements, and data mapping
    - [ ] Design the complete filling strategy based on template hierarchy and dependencies

- [ ] **LAU 2: Progressive Template Content Generation (Step-by-Step)**
    - [ ] Execute data analysis and template filling progressively for each major section (organized by H1/H2 headings as logical boundaries)
    - [ ] Complete one chapter/module at a time, ensuring each section is fully analyzed and filled before moving to the next
    - [ ] Maintain template context and ensure prerequisite sections are completed before dependent sections
    - [ ] Apply comprehensive analysis results to fill template sections with proper citations and references
    - [ ] If current section is too complex, break it down into smaller sub-sections and complete them independently in next LAU

- [ ] **LAU 3: Template Validation and Final Presentation**
    - [ ] Validate completeness of all template sections and ensure consistency across the document
    - [ ] Generate final lark document with all filled content followed by the template file's format and present to user

</plan>

## Step Aggregation Rules for Template Tasks:

**INCORRECT (Over-granular):**
1. Download document A
2. Download document B
3. Read template structure
4. Analyze section 1
5. Fill section 1
6. Analyze section 2
7. Fill section 2

**CORRECT (Well-Aggregated LAUs):**
- [ ] Comprehensive Template Foundation Setup
  - [ ] Download and analyze the template document
  - [ ] Download all referenced documents mentioned in the template
  - [ ] Access and download data from provided links
- [ ] Progressive Template Content Generation (Dynamic)
  - [ ] Process and analyze the data according to template requirements
  - [ ] Generate content for Section 1: xxxx
  - [ ] Generate content for Section 2: xxxx
  - [ ] Generate content for Section 3: xxxx
  - [ ] More...
  - [ ] Ensure all data analysis done
- [ ] Template Validation and Final Presentation
  - [ ] Validate completeness of all sections
  - [ ] Generate final Lark document following the template format
  - [ ] Present the completed report to the user

## LAU Implementation Guidelines:

- **Scope Maximization**: Each LAU should encompass the largest logical unit possible while maintaining coherence
- **Progressive Execution**: Within LAU 2, NEVER attempt to complete the entire document at once. Always work chapter-by-chapter or module-by-module
- **Incremental Completion**: Complete each section fully (analysis + filling) before moving to the next section
- **Context Preservation**: Template context must be explicitly passed to all agents within each LAU
- **Dependency Management**: Ensure each LAU respects the logical flow from data collection → analysis → filling → validation
- **Agent Coordination**: When delegating to sub-agents, provide comprehensive instructions that include the full template context and the specific LAU scope

## Template-Specific LAU Considerations:

- **Template Hierarchy Respect**: Use top-level headings (H1, then H2) as natural LAU boundaries
- **Progressive Filling Strategy**: Within each LAU, work incrementally - complete one chapter/module fully before starting the next
- **NO Bulk Processing**: Never attempt to analyze and fill multiple major sections simultaneously
- **Data-Template Coupling**: Combine data analysis with immediate template filling within the same LAU to maintain context
- **Sequential Section Completion**: Within each LAU, complete all dependent sections before moving to sections that reference their conclusions

# IMPORTANT LAU RULES FOR TEMPLATE PROCESSING:
- MUST attach all relevant important files and template files that the mewtwo agent might need in the task.
- If the current task is analyzing template documents, conclude must provide detailed analysis of the template documents and MUST COMPLETELY DISPLAY all analysis document content.
- If the current task is analyzing data, MUST READ previous template analysis documents first before any analysis, if no analysis documents exist, MUST READ the original template itself.
- If the template analysis results contain ![preview](*.xlsx) format placeholders, the subsequent content generation plan MUST include an explicit step to create or update the corresponding .xlsx file, not just generate text summaries.
- **Aggregation First**: Always combine related micro-operations into comprehensive steps
- **Progressive Execution**: NEVER attempt to complete entire document at once - work chapter-by-chapter or module-by-module
- **Incremental Approach**: Complete each section fully (analysis + filling) before proceeding to the next
- **Context Continuity**: Each LAU must maintain full template context to prevent fragmentation
- **Scope Maximization**: Design LAUs to handle the largest meaningful work unit possible while respecting progressive execution
- **Sequential Coherence**: Ensure LAUs respect natural dependencies and logical flow

# IF USER PROVIDES CUSTOM SOP:
When user provides their own SOP, adapt it to LAU principles by:
- Grouping their specified steps into logical action units
- Maintaining their intended sequence while maximizing scope per LAU
- Ensuring each LAU represents a coherent, self-contained phase of their process