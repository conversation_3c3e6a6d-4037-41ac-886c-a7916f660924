---
ID: "planner_17_slardar_web"
Title: Slardar Bug/Standard Process for Frontend Error Fixing or Performance Analyzing
EnabledIf: "agent in ['planner', 'dynamic_planner']"
UsedWhen: When the user mentions the Slardar web scenario.
---

## Slardar Web Scenario Process

### Workflow for Slardar Web js Error and Blank screen Fixing:
1. if user provide link including "/node/web/js", use the `get_js_error_stack_detail_by_link` method provided by the SlardarWeb tool to get the js error stack detail.
2. if the get_js_error_stack_detail_by_link tool responds with "参数解析失败，请确认链接是否是 slardar 链接: 链接中缺少必需参数"  error, try to give user a second chance to correct the Slardar error detail link.

### Workflow for Slardar web page Performance Issue Analyzing:
Strictly adhere to the following steps:
1. if user provide link including "/node/web/perf", use the `get_perf_page_overview_by_link` method provided by the SlardarWeb tool to get the js error stack detail.
2. if user mentions keywords like "performance" or "latency" and provided a slardar link, use the `get_perf_page_overview_by_link` tool provided by the SlardarWeb tool to get the web page performance metadata and metrics.

## Slardar js issues Standard Fixing Process
When fixing web page bugs or performance issues monitored via Slardar platform, adhere to the following workflow with specialized agent assignments:

### Agent Assignment Strategy
1. **Data Collection Agent**: for JS error environment data acquisition
2. **Analysis & Fix Agent**: for anomaly localization and fix generation  
3. **MR Creation Agent**: for merge request creation and reporting

### Frontend Error Analysis Requirements
the analysis report content should include at least the following parts:
1. **Error Summary**: A concise description of the error's nature
2. **Detailed Analysis**:
   - Triggering scenario
   - Modules/functions involved
   - Possible root causes
   - Consider external factors: Not all frontend errors are caused by code logic issues. Errors like "The user aborted a request", "Network Error", "Failed to fetch", "ChunkLoadError", "Server Error" and similar errors might be caused by temporary network unavailability, device environment issues, or other external factors outside the application's control. Always mention these possibilities when analyzing frontend errors.
3. **Potential Impact**: Description of how the error affects project functionality, user experience, etc.
4. **Fix Suggestions**:
   - Proposed fix approach
   - Code modification suggestions
   - Preventive measures
5. **Verification and Testing**: Methods to verify the fix
6. **References**: If available, supplementary related documentation or resource links

### Workflow for Slardar Web Fixing:
You need to follow the following three steps to complete the repair task, each step follows its own task requirements, and be careful not to confuse the work content and task requirements of different tasks.
Remember to maintain clear boundaries between these tasks and avoid mixing responsibilities across different agents.

1. **JS Error Environment Data Acquisition** (Assign to data collection agent)
   - Repository acquisition: if user specifies codebase and branch, Clone the specific source code repository(s). **Note**: You should only clone the specific branch of the repository. If other repositories need to be viewed, they can be cloned on demand in subsequent analysis steps.
   - if user just specify the codebase repo but no branch specified, use git `remote show origin | grep HEAD` to find the default branch, and then clone the default branch.
   - **Important Note**
     - Generate a file `issue_info_collect.md` that must include the complete error stack trace along with all related repositories and the correspond stack frames(using get_js_error_stack_detail_by_link returned by SlardarWeb), commits, and absolute file paths corresponding to the stack frames.
     - When processing data returned by SlardarWeb, directly read and parse the data in memory without writing any Python code to read the data
     - When processing performance data refer to the community standard metrics.
     - When debugging JavaScript errors, if suspecting the issue relates to a specific open-source library:
      - 1. **Identify Suspicious Library**: Determine the potentially involved library (e.g., React, Vue) via error stack traces or code context.
      - 2. **Community Search**: Search platforms like GitHub Issues, Stack Overflow, and official forums using these keyword combinations:
        - `[Library Name] + [Error Type]` (e.g., "React useState hook not updating")
        - `[Library Version] + [Error Description]` (e.g., "Lodash 4.17.21 map function breaks")
      - 3. **Case Filtering**: Prioritize highly starred/upvoted solutions, officially confirmed bug reports, and resolved threads.
      - 4. **Solution Validation**: Use community-verified fixes (e.g., version downgrades, patch code) as supplementary references.
2. **Anomaly Localization and Fix Generation** (Assign to analysis & fix agent)
   - Search error-related source code: Utilize the information in `issue_info_collect.md` to search codebase and locate code positions of key frames and stack-related classes. **NOTE**: The current workspace and `issue_info_collect.md` may not contain all the code repository information needed to solve the problem. You can get the repositories and file paths corresponding to the stack in `issue_info_collect.md` and use git clone to get the code.
   - Root cause attribution: Conduct comprehensive analysis by integrating searched source code and js error stack information to provide final attribution and source code modification suggestions. When analyzing the code, do not only consider the exception throw point, but carefully examine the entire error stack, use grep to search related code, and find the globally optimal solution. Please think deeply and tell user your thoughts which should include the original stack information, core cause analysis, and all possible fix solutions.
   - Generate fix code: Based on the thoughts generated in the previous step, select the best fix solution, modify the code, and ensure the code changes effectively fix the errors or performance issues. **NOTE**: You can submit modifications in any repository where the stack code is located, but you must choose the optimal modification location and method to resolve the errors or performance issues.
   - When Fixing, you must assume yourself as the real developer of this project, keep the code obey the lint rules and do not modify business logic code without authorization, and **do not write any test case** into user's code files.

3. **JS Fixing MR Creation and Reporting** (Assign to MR creation agent)
- Create merge request(If the user explicitly requests to create an MR): Create MR based on submitted patch with proper title, description, and linking to original issue (To create an MR, codebase and git tools must be assigned)
- Generate attribution report is user's language: follow [Frontend Error Analysis Requirements] and create a report file `final_issue_report.md` including issue data analysis, anomaly causes, solutions, queried related classes, repaired patches, commits, absolute branch and file paths corresponding to the stack frames, and created MR links
- If user not explicitly requests to create an MR, you can at the end emit a ask whether user wants to create an MR, if user wants to create an MR, you can create an MR based on the submitted patch with proper title, description, and linking to original issue (To create an MR, codebase and git tools must be assigned)
- Preventive measures in analysis reports may have uncertainties. When writing code for fixes, such measures should only be added judiciously when the root cause is clear. Do not add various defensive measures and excessive defensive code when the problem is not well understood.
- When submitting an MR, must follow these standards:
  - **MR Naming Format**: `[Fix content title]-[MMDD HH:mm]`
  - **Submission Notification**: Clearly inform in the reply message after mr created, with link format `[MR title](codebase link)`, user can click the link to view the mr details.

## Important Notes
- SlardarWeb tool can access associated links directly without browser participation, When SlardarWeb tool is available, prioritize its usage over browser-based methods
- If an MR needs to be created, codebase and git tools must be assigned. especially, the git tool must be assigned.
- For git cloning a repository with a commit ID, try to directly specify the shallow clone method instead of cloning the repository first and then checking out.
  - It is extremely important that you accurately tell the agent which cloning method to use, such as shallow cloning.
- Try to assign agents according to the standard workflow, and avoid having too many agents (for example, only 3 agents in the above process) to reduce the loss of context transmission.

### Slardar User Query Rules
This takes effect when the user's task is about Slardar Web related monitor related tasks.
- When the user's task is complex, such as involving 7-8 steps, it is necessary to try to follow the above Workflow as much as possible and split it into tasks completed by fewer Agents, such as around 3 Agents.