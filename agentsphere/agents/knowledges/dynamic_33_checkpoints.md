---
ID: "dynamic_32"
Title: Global Planning and Batch Search Strategy
EnabledIf: "agent in ['mewtwo']"
UsedWhen: task involves search or research
---
To maximize efficiency for complex tasks, you must adopt a "plan-then-search" methodology instead of a "search-and-think" loop. The goal is to gather all necessary information in a single, parallelized first step.

# Workflow for Complex Requests:
1. Comprehensive Initial Plan (全局初步规划): Upon receiving a complex task, your first Rationale should be dedicated to deconstructing the task goal into a set of core, researchable sub-questions. Think about all the information you'll need to fully finish the task. Ask yourself: "What are all the pieces of data I need to find before I can start analyzing and building the final report?"
2. Consolidate Search Queries (整合搜索查询): In your first Action, you will not analyze anything. Your sole purpose is to group all the identified sub-questions into a single list.
3. Execute Batch Search (执行批量搜索): Assign this consolidated list of questions to the search or research in a single tool call, if the length of the questions is over the maxim. The task parameter should be a JSON array containing all the search strings. This is your primary information-gathering step.
4. Synthesize and Proceed (综合分析并继续): In subsequent turns, after the batch search is complete, you will have all the necessary data. You can then move on to the analysis, data synthesis, and report generation steps without needing to perform additional, scattered searches. If a critical piece of information is still missing, you may perform a light, targeted search, but this should be the exception, not the rule.

# Guideline for Tool Choosing
1. `research` tool is very time-consuming, ** YOU CAN ONLY USE AT MOST ONCE RESEARCH TOOL. ** 
2. Try to use `search` tool to effectively gather information, but need to consider the token limitation since search tool returns a list of raw, unprocessed search snippets which can be verbose, noisy, and may have a high token count. 

# Guideline for Search tasks Generation
1.  **Generate query effectively:** Do not provide multiple options which are redundant.
2.  **Focus on keywords:** Extract the core subject, intent, and key constraints (like the year). Ignore conversational filler words.
3.  **Be brief:** Avoid unnecessary modifiers if a more common term exists. Use the most standard and direct phrasing.


# Guideline for Research Subtasks Decompose
**Objective:** Generate a structured list of specific research subtasks, which should be designed to collectively gather the necessary information to fully address the research task **with minimal redundancy and optimal search efficiency**.

**Process:**
1.  **Identify Core Information Needs:** Read the task and identify the **main themes** and **key information gaps** required to comprehensively answer the request. Focus on the **essential knowledge** needed.
2.  **Group Related Needs:** Analyze the identified needs. Determine if **closely related information points can be logically grouped** and potentially addressed by a single, well-crafted subtask. For example, instead of separate subtasks for "price of X" and "features of X", consider a single subtask like "price and features of X".
3.  **Formulate Strategic Subtasks:** Create individual research subtasks for each essential information need or logical group. Ensure each subtask is:
    * **Focused yet Comprehensive:** Targets a specific, **logical unit** of information. It might cover closely related sub-points if a single search can efficiently retrieve them.
    * **Clear:** Uses relevant and concise keywords from the original query or implied context.
    * **Non-Redundant:** Avoids creating multiple subtask that target highly similar or overlapping information. Each query should ideally unlock a unique and necessary piece of the puzzle.
4.  **Ensure Coverage and Efficiency:** Review the generated subtask list. Double-check that all crucial aspects of the original User Task are covered. **Critically evaluate if any subtask can be merged or eliminated** without compromising the final answer's quality. Aim for the **fewest subtask necessary** for complete information retrieval, respecting the maximum limit.