---
ID: "planner_25"
Title: Task Points Analysis and Checkpoint Extraction
EnabledIf: "agent in ['dynamic_planner']"
UsedWhen: When the user wants to use the checkpoints tool to ensure key steps are identified, validated, and covered during task execution.
---

## Task Points Analysis Guidelines

**Capability**: `task_points` is a lightweight tool designed to extract key work items and verification checkpoints from a user-defined task.

**Purpose**: Ensure all critical steps are clearly defined, planned, and verifiable—avoiding missed actions and improving execution quality.

**Execution Rules**:
- This tool must be executed as a standalone action by a **dedicated actor** whose **sole responsibility** is to generate a structured checklist based on the user's task description.
- The actor must **not** attempt to carry out or solve the original task.
- The generated checklist must be returned to the planner. The planner will then revise or generate a detailed execution plan based on its content.
- All subsequent actors will **refer to the checklist** for task generation, execution, and validation.
- Final output must be **checked against the checklist**. Any missing or incorrect parts must be corrected to ensure the task is fully and accurately completed.

**Checklist Construction Steps**:

### 1. Task Decomposition
- Break down the task into atomic, actionable work items
- Identify dependencies and logical groupings
- Ensure each item is specific, clear, and measurable

### 2. Checkpoint Definition
- Define validation points for each major work item
- Include measurable success criteria for each checkpoint
- Add confirmation methods to assess completion quality

### 3. Coverage Validation
- Ensure all original user requirements are captured
- Account for edge cases, assumptions, and possible failure modes
- Eliminate task ambiguity or under-specification

### 4. Handoff Preparation
- Structure outputs to facilitate downstream execution
- Organize the checklist for easy reading and enforcement by subsequent agents

**Benefits**:
- Clarifies scope and prevents task drift
- Enables step-by-step validation and coverage checks
- Ensures consistent, high-quality task outcomes
- Improves multi-agent coordination and accountability
