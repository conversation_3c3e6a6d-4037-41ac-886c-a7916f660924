---
ID: "dynamic_25"
Title: guide for preparing content for lark templates reports
EnabledIf: "false"
UsedWhen: |
  use it whenever/wherever preparing content for a lark template or markdown report, analyzing data or generating visualization part for reports, even if the current task is just creating intermediate artifacts or doing analysis that will later be integrated into a final document.
---
# If user provide the Lark template document for you, you must strictly follow the rules:

## ⚠️ CRITICAL TASK EXECUTION RULES - MUST FOLLOW
**These two rules are MANDATORY for all tasks:**

1. **If the current task is analyzing template documents, conclude must provide detailed analysis of the template documents and MUST COMPLETELY DISPLAY all analysis document content.**
2. **If the current task is analyzing data, MUST READ previous template analysis documents first before any analysis, if no analysis documents exist, MUST READ the original template itself.**

## Lark Templates Filling Rules
1. Check if the comment file exists with the same name as the template file when downloaded by `ls -la`.
2. Read the comment file carefully and follow the instructions if it exists. Comments usually contain user's requirements that refer to specific content in the template file.
3. Identify and clarify all data sources, they may come from documents, user query, references, comments, etc.
4. Comments also appear in the template file with `<!-- comment:xxx -->` format. Carefully read these comments and do not render them as content in the final output.
5. Be careful with the occupied/templated areas, you need to fill the expected content in the right place.
    - All the detailed filling rules are contained in `Feishu/Lark Doc Generation` knowledge base.
    - `[正文]` is the main content area, fill with plain text content, this is usually the most flexible part you can modify.
    - `[列表]` is the list area, fill with ordered/unordered list content as appropriate.
    - `[图片]`, `[图例]`, or `[图表]` is the image area, fill with static images, figures, etc.
    - `[表格]` is the table area, MUST fill with correct Lark table format, do not split columns to different lines, do not modify the table structure. Tables cannot embed sub-tables.
    - `[引用]` is the quote area, quotes, links, or previews are all acceptable.
    - `[代码]` is the code area, fill with code content in appropriate syntax highlighting format.
    - `[公式]` is the formula area, must use correct Lark formula format (LaTeX-style math expressions).
    - `[交互式]` or `[HTML]` means a **SINGLE** interactive HTML chart follow the [preview](chart.html) format. The final document will be rendered in a markdown-like page, so you can only embed a single chart, not large pages or multiple charts.
    - `[风神截图]` 风神平台图表截图，区别于`[风神图表嵌出]`：
      - `[风神截图]` + 评论说明图表名称，或
      - `[风神截图:图表名称]` 直接指定图表名称
      - 最终使用markdown图片格式语法渲染
    - `[风神图表嵌出]` 直接使用风神图表的链接，语法为`[preview](风神嵌出链接)`
   - `![preview][xxx.xlsx]` 电子表格格式，需要按用户的要求操作xlsx文件，最终报告中必须保持`![preview][xxx.xlsx]`格式原状，禁止写成markdown表格
    - 对于模板文档中已经存在的 `[preview](url)` ，在最终的报告中必须保证和模版一致，禁止擅自删除或修改 preview的链接或格式
6. Do not modify the template structure, keep the original skeleton/headings intact, and forbidden to write code to generate lark document content.
7. Remove sections that are clearly instructional prompts for content generation (e.g., "guide for generating xxx", "instructions to fill this section").
8. Create the final lark document with `mcp:lark_create_lark_doc` tool.

## IMPORTANT RULES
- List and conclude every data source you found and used (including unused sources) in the template file, origin url is important, don't ignore it:
    - [Data Source1](file_1)
    - [Data Source2](file_2)
    - [Data Url1](url_1)
    - ...
- Strictly follow user comments regarding analysis methods, graph types, content requirements, etc.
- It is **strictly forbidden** to create new template files or files with the same name as the template file.
- If one chapter is too large that you cannot finish it in one task, you can create a single lark md for it and merge it in future.
- You should **only patch/replace content within replaceable markers**, such as `[正文]`, or content surrounded by `{{}}`, `[]`, `【】`, `<!-- -->`, etc.
- **Never** write code to generate lark document content, always output it in file directly.
- **Do not** modify the original text structure, headings, or non-templated content.
- When uncertain about table formats or content requirements, ask the user for specific clarification before proceeding.
- Preserve all non-templated text exactly as written in the original template.
- If the template contains markdown format tables, the generated document **MUST** maintain the table format exactly as specified in the template.
- If you need to fill numbers or calculations in tables, you **MUST** use code-based approaches (such as writing scripts or using computational tools) to ensure accuracy and consistency.
- **Any numerical results appearing in Lark templates are considered examples by default, unless the user explicitly indicates they are real results. You are strictly forbidden from using these example numbers as final results.**
- **Do not arbitrarily modify proper nouns or technical terms in the document, and do not translate English terms into Chinese without explicit user instruction.**
- Note that text formats include font color. Please use appropriate colors in accordance with the requirements of the Lark template.