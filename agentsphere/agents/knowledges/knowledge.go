package knowledges

import (
	"context"
	"runtime/debug"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/agentrace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/gopkg/pkg/errors"
	"code.byted.org/security/zti-jwt-golang/ztijwt/logger"
	"github.com/expr-lang/expr"
	"github.com/expr-lang/expr/vm"
	"github.com/samber/lo"
	"golang.org/x/sync/errgroup"
)

type KnowledgeItem struct {
	ID string `json:"id" yaml:"ID" mapstructure:"ID"`
	// Short title of the knowledge.
	Title string `json:"title" yaml:"Title" mapstructure:"Title"`
	// indicates if always use this knowledge.
	Pinned    bool   `json:"pinned" yaml:"Pinned" mapstructure:"Pinned"`
	EnabledIf string `json:"enabled_if" yaml:"EnabledIf" mapstructure:"EnabledIf"`
	// when to use this knowledge.
	UsedWhen string `json:"used_when" yaml:"UsedWhen" mapstructure:"UsedWhen"`
	// knowledge content.
	Content string `json:"content" yaml:"Content" mapstructure:"Content"`
}

type Knowledgebase interface {
	RetrieveKnowledge(ctx context.Context, opt KgRetrieveOption) ([]KnowledgeItem, error)
}

type KgRetrieveOption struct {
	Query string
	// max number of knowledge items to retrieve.
	Limit int
	Param RetrieveParam
	// 多次召回，取召回结果的并集
	Sampling int
	Timeout  time.Duration
}

type RetrieveParam struct {
	Agent        string   `expr:"agent"`
	Variant      string   `expr:"variant"`
	Tools        []string `expr:"tools"`
	WithCitation bool     `expr:"with_citation"`
}

type SemanticKnowledgebase struct {
	llm        framework.LLM
	knowledges []KnowledgeItem
	compiledIf []*vm.Program
	config     iris.SceneModelConfig

	logger iris.Logger
}

var _ Knowledgebase = &SemanticKnowledgebase{}

func NewSemanticKnowledgebase(run *iris.AgentRunContext, knowledges []KnowledgeItem, llmConfig iris.SceneModelConfig) *SemanticKnowledgebase {
	exprs := make([]*vm.Program, len(knowledges))
	for i, knowledge := range knowledges {
		if len(knowledge.ID) == 0 {
			// Avoid ID collision.
			knowledges[i].ID = strconv.Itoa(len(knowledges) + i)
		}
		// Default to false.
		if knowledge.EnabledIf == "" {
			knowledge.EnabledIf = "false"
		}
		exp, err := expr.Compile(knowledge.EnabledIf, expr.Env(RetrieveParam{}), expr.AsBool())
		if err != nil {
			run.GetLogger().Warnf("failed to compile expr for knowledge: %s, error: %+v, if: %s", knowledge.Title, err, knowledge.EnabledIf)
		} else {
			exprs[i] = exp
		}

	}
	return &SemanticKnowledgebase{
		llm:        run.GetLLM(),
		knowledges: knowledges,
		config:     llmConfig,
		logger:     run.GetLogger(),
		compiledIf: exprs,
	}
}

func (s *SemanticKnowledgebase) recallByLLM(
	ctx context.Context,
	opt KgRetrieveOption,
	knowledges []KnowledgeItem,
) (recalled []KnowledgeItem, thought *iris.Thought, err error) {
	span, ctx := agentrace.GetRuntimeTracerFromContext(ctx).
		StartCustomSpan(
			ctx,
			agentrace.SpanTypeStep,
			"retrieve_knowledge_by_llm",
		)
	defer func() {
		span.UpdateData(agentrace.NewObjectSpanData(map[string]any{
			"recalled": recalled,
		}))
		agentrace.AddErrorTag(span, err)
		span.Finish()
	}()
	messages, err := prompt.ComposeVaryMessages([]prompt.ComposeVaryMessageOption{
		prompt.WithSystemMessage(KnowledgeSystemPromptTemplate, map[string]any{"Knowledges": knowledges}),
		prompt.WithUserMessage(KnowledgeUserPromptTemplate, map[string]any{"Task": opt.Query}),
	})
	if err != nil {
		return nil, nil, errors.WithMessage(err, "failed to compose messages")
	}

	logger.Debugf("knowledge retrieve messages:\n %+v", messages)

	llmOpt := framework.LLMCompletionOption{
		Model:       "deepseek-v3-250324",
		Temperature: 0.3,
		MaxTokens:   500,
		Tag:         "retrieve_knowledge",
	}
	if s.config.Model != "" {
		llmOpt.Model = s.config.Model
		llmOpt.MaxTokens = s.config.MaxTokens
		llmOpt.Temperature = s.config.Temperature
		llmOpt.Thinking = s.config.Thinking
	}
	llmSpan, ctx := agentrace.GetRuntimeTracerFromContext(ctx).
		StartCustomSpan(
			ctx,
			agentrace.SpanTypeLLMCall,
			"retrieve_knowledge",
			agentrace.WithObjectSpanData(map[string]any{
				"prompt_messages": messages,
			}),
		)
	defer llmSpan.Finish()
	res, err := s.llm.ChatCompletion(ctx, messages, llmOpt)
	if err != nil {
		agentrace.AddErrorTag(llmSpan, err)
		return nil, nil, errors.WithMessage(err, "failed to call llm")
	}
	llmSpan.UpdateData(agentrace.NewObjectSpanData(map[string]any{
		"llm_call": res,
	}))
	logger.Debugf("retrieve result:\n%s\n", res.Content)
	thought = &iris.Thought{
		Content: res.Content,
		LLMCall: iris.LLMCall{
			ModelName:    llmOpt.Model,
			Temperature:  float64(llmOpt.Temperature),
			Usage:        res.TokenUsage,
			Prompt:       messages,
			FinishReason: string(res.FinishReason),
		},
	}

	topTags, err := prompt.ParseTopTagsV2(res.Content)
	// if response is incomplete, but some tags are found, we still can use them
	if err != nil && len(topTags) == 0 {
		return nil, thought, errors.WithMessage(err, "failed to parse top tags")
	}

	items := make([]KnowledgeItem, 0, len(topTags))
	for _, item := range knowledges {
		if item.Pinned {
			items = append(items, item)
		}
	}
	for _, tag := range topTags {
		if tag.XMLName.Local != "id" {
			continue
		}
		id := strings.TrimSpace(tag.Content)
		if id == "" {
			continue
		}
		item, ok := lo.Find(knowledges, func(item KnowledgeItem) bool {
			return item.ID == id && !item.Pinned
		})
		if !ok {
			continue
		}
		items = append(items, item)
	}

	return items, thought, nil
}

func (s *SemanticKnowledgebase) prefilter(ctx context.Context, opt KgRetrieveOption) []KnowledgeItem {
	items := map[string]KnowledgeItem{}
	for i, knowledge := range s.knowledges {
		if s.compiledIf[i] == nil {
			continue
		}
		enabled, err := expr.Run(s.compiledIf[i], opt.Param)
		if err != nil {
			s.logger.Warnf("failed to run expr for knowledge: %s, error: %+v, if: %s", knowledge.Title, err, knowledge.EnabledIf)
			continue
		}
		if enabled == false {
			continue
		}
		items[knowledge.ID] = knowledge // if there are multiple items with the same id, the last one will be kept
	}
	return lo.Values(items)
}

// RetrieveKnowledge implements Knowledgebase.
func (s *SemanticKnowledgebase) RetrieveKnowledge(c context.Context, opt KgRetrieveOption) (recalled []KnowledgeItem, err error) {
	span, ctx := agentrace.GetRuntimeTracerFromContext(c).
		StartCustomSpan(
			c,
			agentrace.SpanTypeStep,
			"retrieve_knowledge",
			agentrace.WithObjectSpanData(map[string]any{
				"query":    opt.Query,
				"limit":    opt.Limit,
				"sampling": opt.Sampling,
			}),
		)
	// add a default timeout to prevent llm call from hanging too long
	ctx, cancel := context.WithTimeout(ctx, lo.Ternary(opt.Timeout > 0, opt.Timeout, 30*time.Second))
	defer cancel()
	defer func() {
		span.UpdateData(agentrace.NewObjectSpanData(map[string]any{
			"recalled": recalled,
		}))
		agentrace.AddErrorTag(span, err)
		span.Finish()
	}()
	knowledges := s.prefilter(ctx, opt)
	if len(knowledges) == 0 {
		s.logger.Infof("no knowledge found for option %+v", opt.Param)
		return []KnowledgeItem{}, nil
	}
	if len(opt.Query) == 0 {
		s.logger.Infof("skip knowledge retrieval for empty query")
		return []KnowledgeItem{}, nil
	}
	span.UpdateData(agentrace.NewObjectSpanData(map[string]any{
		"prefiltered": knowledges,
	}))

	// The returned ctx is canceled if the first call to g.Go returns a non-nil error.
	// But here we don't care about the error, so we ignore the returned ctx.
	g, _ := errgroup.WithContext(ctx)
	if opt.Sampling < 1 {
		opt.Sampling = 1
	}
	g.SetLimit(3)
	lock := &sync.Mutex{}

	recalled = make([]KnowledgeItem, 0, opt.Limit)
	thoughts := make([]*iris.Thought, opt.Sampling)
	for i := 0; i < opt.Sampling; i++ {
		g.Go(func() error {
			defer func() {
				if r := recover(); r != nil {
					s.logger.Errorf("recover from panic %+v, stack: %s", r, debug.Stack())
				}
			}()
			items, thought, err := s.recallByLLM(ctx, opt, knowledges)
			thoughts[i] = thought
			if err != nil {
				return err
			}
			lock.Lock()
			defer lock.Unlock()
			recalled = append(recalled, items...)
			return nil
		})
	}
	if err := g.Wait(); err != nil && len(recalled) == 0 {
		return nil, err
	}

	if run, ok := c.(*iris.AgentRunContext); ok && run.State.CurrentStep != nil {
		// Report think if possible.
		for _, thought := range thoughts {
			if thought == nil {
				continue
			}
			step := run.State.CurrentStep
			// Avoid to overwrite original thought.
			originalThought := step.Thought
			step.Thought = thought
			run.GetPublisher().ReportThought(step)
			step.Thought = originalThought
		}
	}

	// 被选择次数多的知识排在前面
	groups := lo.Values(
		lo.GroupBy(
			recalled,
			func(item KnowledgeItem) string {
				return item.ID
			},
		),
	)
	sort.Slice(groups, func(i, j int) bool {
		return len(groups[i]) > len(groups[j])
	})
	recalled = make([]KnowledgeItem, len(groups))
	for idx, group := range groups {
		recalled[idx] = group[0]
	}

	return recalled, nil
}
