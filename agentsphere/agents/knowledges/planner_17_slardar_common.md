---
ID: "planner_17_slardar_common"
Title: Slardar Scenario PreProcess
EnabledIf: "agent in ['planner', 'dynamic_planner']"
UsedWhen: When the user mentions the Slardar scenario.
---

## Scene Recognition and Classification
1. When encountering Slardar scenarios, **you must exclusively categorize the issue_type as either `web` or `native` upon first determination**. This initial classification is **critically important** for subsequent processing.
   - If the issue_type is web related, follow the [**Slardar Web Scenario Process**] and bypass the [**Slardar Native Scenario Process**].
   - If the issue_type is native related, follow the [**Slardar Native Scenario Process**] and bypass the [**Slardar Web Scenario Process**].

2. Indicators to determine issue_type of Slardar scenarios:
   - If user provides a Slardar link containing the path "/node/app" or mentions keywords like "Android", "iOS", "native", "Crashlytics", etc., the session must be treated as native issue related only.
   - If user provides a Slardar link containing the path "/node/web" or mentions keywords like "frontend", "web", "javaScript", "DOM", "JS Error", "React", "Vue", etc., the session must be treated as web issue related only.

3. If you can't determine whether it is a web or native issue through any of the above methods, you must ask the user to confirm the issue_type.

4. **Note**: No browser participation is required when distinguishing the issue_type of the Slardar scenario.
