---
ID: "3"
Title: ECharts Tips
EnabledIf: "agent in ['data_analyzer', 'mewtwo', 'lark_creation']"
UsedWhen: when using echarts
---

## Basic Usage Example

You may refer to the following example to create an ECharts chart:

```html
<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>Duck Company Ecosystem</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
  </head>
  <body>
    <!-- Prepare a DOM with defined width and height for ECharts -->
    <div id="main" style="width: 600px;height:400px;"></div>
    <script>
      function initChart() {
        const dom = document.getElementById("main");

        if (!dom) {
          setTimeout(initChart, 1000);
          return;
        }

        const chart = echarts.init(dom);
        const option = {
          title: {
            text: "Duck Company",
            left: "center",
            textStyle: {
              fontSize: 20,
              fontWeight: "bold",
            },
          },
          tooltip: {
            trigger: "item",
            formatter: "{b}: {c}",
          },
          series: [
            {
              type: "graph",
              layout: "force",
              force: {
                repulsion: 1000,
                edgeLength: 200,
              },
              roam: true,
              label: {
                show: true,
                fontSize: 14,
              },
              edgeLabel: {
                show: true,
                formatter: "{c}",
                fontSize: 12,
              },
              data: [
                {
                  name: "Kangaroo",
                  symbolSize: 70,
                  itemStyle: {
                    color: "#fac858",
                  },
                },
                {
                  name: "Buzz Lightyear",
                  symbolSize: 50,
                  itemStyle: {
                    color: "#ee6666",
                  },
                },
              ],
              edges: [
                {
                  source: "Kangaroo",
                  target: "Buzz Lightyear",
                  label: {
                    show: true,
                    formatter: "Dancing Together",
                  },
                  lineStyle: {
                    width: 3,
                    color: "#ee6666",
                  },
                },
              ],
            },
          ],
        };
        chart.setOption(option);

        // Handle window resize
        const resizeHandler = function () {
          chart.resize();
        };
        window.addEventListener("resize", resizeHandler);
      }

      // Initialize chart
      initChart();
    </script>
  </body>
</html>
```

## Common Issues and Solutions

### ECharts Initialization Timing Issues

When using ECharts, there are two common initialization timing problems:

1. **Tab Switching Issue**: When a tab is switched and the chart container changes from `display:none` to `display:block`, ECharts cannot correctly detect this change
2. **Invisible Container Initialization**: When ECharts initializes in an invisible container, it cannot calculate dimensions correctly

**Solution**: After the container becomes visible, get the chart instance and call the `resize()` method

```javascript
// Tab switching example
tab.addEventListener('click', function() {
    // Update active tab and display corresponding chart container
    document.querySelectorAll('.metric-tab').forEach(t => {
        t.classList.remove('active');
    });
    this.classList.add('active');
    
    document.querySelectorAll('.chart-container').forEach(c => {
        c.style.display = 'none';
    });
    const activeChartContainer = document.getElementById(`chart-${this.dataset.group}`);
    activeChartContainer.style.display = 'block';
    
    // Important: Get chart instance and resize
    const chartInstance = echarts.getInstanceByDom(activeChartContainer);
    if (chartInstance) {
        chartInstance.resize();
    }
});
```

### Values
**don't use binary data in values at any situation**

❌
```json
"values": {
"dtype": "f8",
"bdata": "/GtOI0s2xT+XI..."
}
```
✅
```json
"values":  [ 1, 2, 3, 4, 5 ]
```

This solution ensures that when the container becomes visible, the ECharts chart correctly calculates and adapts to the container's dimensions.
