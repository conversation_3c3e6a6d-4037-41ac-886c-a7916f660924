---
ID: "dynamic_20"
Title: browser tools guide for download/extract data from aeolus(风神) platform
EnabledIf: "agent in ['mewtwo'] and 'browser' in tools"
UsedWhen: when trying to extract dashboard, visualized query data from aeolus(风神) platform.
---

Use the following browser tool as the HIGHEST PRIORITY method whenever you need to retrieve ANY data from the aeolus(风神) platform. This is MANDATORY for ALL aeolus data extraction scenarios, with no exceptions:

**Tool Usage**
Use the `browser_get_aeolus_screenshot_and_data` tool to extract data from the aeolus platform. This tool can handle both dashboard and visualized query data extraction.

**Language Parameter**
The `browser_get_aeolus_screenshot_and_data` tool supports an optional `language` parameter to select the interface language of the Aeolus platform:
- **Default behavior**: Do NOT pass the language parameter (uses default Chinese interface)
- **English only**: Pass `language="en"` ONLY when the user specifically requests English interface

**Examples**

* extract visualized query data from aeolus(风神) platform.
  * for `aeolus#/dataQuery` or `visualized query` or `可视化查询` only
* extract dashboard(仪表盘) data from aeolus(风神) platform.
    * for `aeolus#/dashboard` or `dashboard` or `仪表盘` only

Default usage (Chinese interface):
```
Call browser_get_aeolus_screenshot_and_data tool without language parameter
```

English interface (only when specifically requested):
```
Call browser_get_aeolus_screenshot_and_data tool with language="en" parameter
```

**IMPORTANT**

You should use the browser_get_aeolus_screenshot_and_data tool directly. The tool will:
- Extract the required data (dashboard or visualized query data)
- Return both screenshot and data in the appropriate format

Before using the tool, you MUST first navigate to the appropriate aeolus page using browser_goto.