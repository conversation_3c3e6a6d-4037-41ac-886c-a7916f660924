package interaction

import (
	"context"

	"github.com/pkg/errors"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/util"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
)

var AskUserActionDef = framework.ActionDef{
	ActionName:        "ask_user",
	ActionDescription: "Ask user a question and wait for response. Use this for requesting clarification, gathering additional information.",
	ActionInputSpec: framework.Schema{
		Type:        framework.TypeObject,
		Description: "",
		Properties: map[string]framework.Schema{
			"message": {
				Type:        framework.TypeString,
				Description: "Question text to present to user.",
			},
		},
		Required: []string{"message"},
	},
	ActionOutputSpec: framework.Schema{
		Type: framework.TypeObject,
		Properties: map[string]framework.Schema{
			"answer": {
				Type:        framework.TypeString,
				Description: "User's answer to your question.",
			},
			"attachments": {
				Type:        framework.TypeArray,
				Description: "User's attachments to your question.",
			},
			"message_id": {
				Type:        framework.TypeString,
				Description: "User's response message id.",
			},
		},
		Required: []string{"answer"},
	},
}

func NewAskUserAction() *actions.Tool {
	return actions.ToTool(AskUserActionDef.ActionName, AskUserActionDef.ActionDescription, AskUser)
}

type AskUserInput struct {
	Message string `json:"message" mapstructure:"message"`
}

type AskUserOutput struct {
	Answer      string            `json:"answer" mapstructure:"answer"`
	Attachments []iris.Attachment `json:"attachments" mapstructure:"attachments"`
	MessageID   string            `json:"message_id" mapstructure:"message_id"`
}

// AskUser asks user a question and wait for response.

func AskUser(c *iris.AgentRunContext, input AskUserInput) (map[string]any, error) {
	// FIXME(cyx): support timeout.
	// ctx, cancel := context.WithTimeout(c, time.Minute*10)
	ctx, cancel := context.WithCancel(c)
	defer cancel()
	result, err := c.
		GetRemoteToolExecutor().
		CallRemoteTool(ctx, "", "", AskUserActionDef.ActionName, util.ValueToMap(input))
	if err != nil {
		c.GetLogger().Errorf("failed to call remote tool: %v", err)
		return nil, errors.Errorf("failed to ask user: %v", err)
	}

	return result, nil
}
