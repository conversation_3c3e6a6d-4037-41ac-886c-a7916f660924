package bitsanalysis

import (
	"fmt"
	"os"
	"sort"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/port/bitsanalysis"
	"github.com/goccy/go-json"
	"github.com/pkg/errors"
	"github.com/samber/lo"
)

const batchNum = 100

const (
	ToolListIssues = "list_issues"

	ToolListIssuesDescription = "List issues in a specific repository on the Bits Analysis platform based on given conditions\n" +
		"Notes for using the `list_issues` tools:\n" +
		"* The `repo_name` parameter is required and must specify the name of the git repository.\n" +
		"* The `scene` parameter is required and should be chosen from the available options: quality_analysis, merge_check, deploy_check, resolution_check.\n" +
		"* The `rule_name` parameter is optional and can be provided to filter issues by specific rules.\n" +
		"* The `rule_names` parameter is optional and can be provided to filter issues by multiple rules.\n" +
		"* The `is_my_issue` parameter is optional and can be set to true to query only the issues assigned to the current user.\n" +
		"* The `resolution_id` parameter is required only when `scene` is set to `resolution_check`. In this case, `resolution_id` corresponds to the specific resolution ID.\n"
)

func NewListIssues() iris.Action {
	return actions.ToTool(ToolListIssues, ToolListIssuesDescription, ListIssues)
}

type ListIssuesArgs struct {
	RepoName       string   `json:"repo_name" mapstructure:"repo_name" description:"Required, name of the Git repository"`
	Scene          string   `json:"scene" mapstructure:"scene" description:"Required, scenario name, optional enum values: quality_analysis, merge_check, deploy_check, resolution_check"`
	RuleName       string   `json:"rule_name" mapstructure:"rule_name" description:"Optional, name of the rule corresponding to the issue"`
	RuleNames      []string `json:"rule_names" mapstructure:"rule_names" description:"Optional, list of rule names corresponding to the issue"`
	Severities     []string `json:"severities" mapstructure:"severities" description:"Optional, list of severity levels of the issue"`
	AssigneeNames  []string `json:"assignee_names" mapstructure:"assignee_names" description:"Optional, list of assignee names of the issue"`
	FilePathPrefix *string  `json:"file_path_prefix" mapstructure:"file_path_prefix" description:"Optional, file path prefix of the issue"`
	IsMyIssue      bool     `json:"is_my_issue" mapstructure:"is_my_issue" description:"Optional, whether to query only issues assigned to me"`
	ResolutionID   int64    `json:"resolution_id" mapstructure:"resolution_id" description:"Optional, required only when the scene is resolution_check, specifies the ID of the governance project; not needed for other scenarios"`
}

type ListIssuesOutput = map[string]any

type Issue struct {
	ID                   int64    `json:"id"` // id
	RuleName             string   `json:"rule_name"`
	FilePath             string   `json:"file_path"`
	LineStart            int64    `json:"line_start"`
	LineEnd              int64    `json:"line_end"`
	Message              string   `json:"message"`
	Detail               string   `json:"detail"`
	CodeSnippet          string   `json:"code_snippet"`
	RelatedRuleKnowledge []string `json:"related_rule_knowledge"` // 关联的规则知识库
}

func ListIssues(run *iris.AgentRunContext, args ListIssuesArgs) (*ListIssuesOutput, error) {
	cli, err := bitsanalysis.NewClient()
	if err != nil {
		return nil, iris.NewRecoverable(errors.WithMessagef(err, "unable to create bits analysis client"))
	}

	response, err := cli.ListIssues(run, &bitsanalysis.ListIssuesRequest{
		RepoName:       args.RepoName,
		Scene:          args.Scene,
		RuleName:       args.RuleName,
		RuleNames:      lo.Ternary(len(args.RuleNames) > 0, args.RuleNames, nil),
		Severities:     lo.Ternary(len(args.Severities) > 0, args.Severities, nil),
		AssigneeName:   lo.Ternary(args.IsMyIssue, run.User.Username, ""),
		AssigneeNames:  lo.Ternary(len(args.AssigneeNames) > 0, args.AssigneeNames, nil),
		FilePathPrefix: args.FilePathPrefix,
		ResolutionID:   args.ResolutionID,
	})
	if err != nil {
		return nil, iris.NewRecoverable(errors.WithMessagef(err, "unable to list issues"))
	}

	if len(response.Issues) == 0 {
		return &ListIssuesOutput{
			"description": "no issues found.",
		}, nil
	}

	response.Issues = lo.Slice(response.Issues, 0, batchNum)
	response.Total = int64(len(response.Issues))

	// 基于问题 path 排序
	sort.Slice(response.Issues, func(i, j int) bool {
		return response.Issues[i].FilePath < response.Issues[j].FilePath
	})

	issuesChunks := lo.Chunk(response.Issues, 10)

	// Create a directory based on the current timestamp
	dir := fmt.Sprintf("%d", time.Now().UnixNano())
	err = os.Mkdir(dir, 0755)
	if err != nil {
		return &ListIssuesOutput{
			"error": "failed to create directory for saving issues.",
		}, nil
	}
	// Save each chunk to a separate JSONL file
	issueFiles := make([]string, 0, len(issuesChunks))
	for i, chunk := range issuesChunks {
		issueFile := fmt.Sprintf("%s/issue_%d.jsonl", dir, i)
		issueFiles = append(issueFiles, issueFile)
		file, err := os.Create(issueFile)
		if err != nil {
			return &ListIssuesOutput{
				"error": fmt.Sprintf("failed to create file %s for saving issues: %v", issueFile, err),
			}, nil
		}
		// Write each issue in the chunk as a JSON line
		for _, issue := range chunk {
			marshal, err := json.Marshal(issue)
			if err != nil {
				continue
			}
			_, err = file.Write(append(marshal, '\n'))
			if err != nil {
				continue
			}
		}
		_ = file.Close()
	}

	return &ListIssuesOutput{
		"description":  "list issues successfully. the issues have been split into batches and saved in the following files(references are the file paths).",
		"references":   issueFiles,
		"issues_total": response.Total,
		"important_note": `1. metadata is the metadata of the issue, which is used to describe the issue in detail.
2. the issues have been grouped and saved into reference files.
3. each reference file must be assigned to a mewtwo agent for step-by-step resolution.  
  - For example, if there are 10 reference file, you can assign 10 agents to resolve them one by one.
  - Must Pass reference file path to mewtwo agent, agents must fix issues based on reference files.`,

		"metadata": map[string]string{
			"id":                     "The unique identifier for the issue.",
			"rule_name":              "The name of the rule that triggered this issue.",
			"file_path":              "The file path where the issue was detected.",
			"line_start":             "The starting line number of the issue in the file.",
			"line_end":               "The ending line number of the issue in the file.",
			"message":                "A brief description of the issue.",
			"detail":                 "A detailed explanation of the issue.",
			"code_snippet":           "A snippet of code related to the issue.",
			"related_rule_knowledge": "A collection of knowledge resources or references related to the rule, providing guidance for resolving the issue.",
			"fix_suggestion": "A structured recommendation from the Linter containing exact code to replace and its location. If present, this is the preferred solution and should be prioritized over custom fixes." +
				"It includes the line range to modify and the suggested replacement code.",
		},
	}, nil
}
