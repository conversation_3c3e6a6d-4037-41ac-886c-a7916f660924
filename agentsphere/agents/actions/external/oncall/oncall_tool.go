package oncall

import (
	"context"
	"fmt"
	"net/url"
	"strings"
	"time"

	jsoniter "github.com/json-iterator/go"
	"github.com/xuri/excelize/v2"

	"github.com/pkg/errors"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
)

const (
	PrivateFlowWarning = "此工单不允许获取群聊！请勿访问！"
)

// stageDisplayNameMap 英文阶段名到中文显示名的映射
var stageDisplayNameMap = map[string]string{
	"google":         "Google",
	"not_start":      "流程未开启",
	"doc_search":     "文档搜索",
	"self_diagnose":  "自诊断",
	"in_the_queue":   "等待队列中",
	"launch_oncall":  "人工oncall",
	"redirect":       "重定向",
	"cancel_queue":   "取消排队",
	"join_group":     "加入指定群聊",
	"hot_doc":        "热门文档",
	"history_search": "历史搜索",
	"doc_match":      "文档匹配",
	"switch_tenant":  "跳转至其他租户",
	"async_process":  "等待异步处理",
}

// getStageDisplayName 将英文阶段名转换为中文显示名
func getStageDisplayName(stage string) string {
	if displayName, exists := stageDisplayNameMap[stage]; exists {
		return displayName
	}
	// 如果没有找到对应的中文名，返回原英文值
	return stage
}

const (
	// 工具名称
	ToolListOnCallName = "search_oncall"

	// 工具描述
	ToolListOnCallDescription = "基于租户搜索OnCall数据，支持指定时间范围、租户ID或租户名称，可选获取群聊详情，结果导出为xlsx。租户名称必须准确，系统会自动将租户名称转换为租户ID进行搜索。注意：如果用户提供了OnCall平台的URL链接，请优先使用search_oncall_from_url工具。"

	// 工具名称
	ToolListOnCallFromURLName = "search_oncall_from_url"

	// 工具描述
	ToolListOnCallFromURLDescription = "**优先工具**：当用户提供OnCall平台URL链接时，优先使用此工具。基于OnCall搜索URL（https://oncall.bytedance.net/admin/review/all）直接解析并查询数据，支持指定时间范围，可选获取群聊详情，结果导出为xlsx。URL必须包含filter_fields参数，能够保持用户原始搜索条件。"
)

// ToolListOnCallArgs 搜索OnCall的工具入参
type ToolListOnCallArgs struct {
	TenantID       int    `json:"tenant_id" mapstructure:"tenant_id" description:"租户ID，与租户名称二选一"`
	TenantName     string `json:"tenant_name,omitempty" mapstructure:"tenant_name" description:"租户名称，必须准确，与租户ID二选一。当提供租户名称时，会自动查询对应的租户ID"`
	StartTime      string `json:"start_time,omitempty" mapstructure:"start_time" description:"开始时间，格式：YYYY-MM-DD HH:mm，如不提供默认为5天前"`
	EndTime        string `json:"end_time,omitempty" mapstructure:"end_time" description:"结束时间，格式：YYYY-MM-DD HH:mm，如不提供默认为当前时间"`
	IsSolved       *int   `json:"is_solved,omitempty" mapstructure:"is_solved" description:"是否已解决，1-已解决，0-未解决"`
	NeedChatDetail bool   `json:"need_chat_detail,omitempty" mapstructure:"need_chat_detail" description:"是否需要获取每个Oncall群聊详情，为true则在搜索工单的同时获取所有群聊详情，默认不获取"`
	LimitRecordNum int    `json:"limit_record_num,omitempty" mapstructure:"limit_record_num" description:"填写最多需要获取的记录数目。默认是2000，上限是2000"`
}

func NewListOnCallTool() iris.Action {
	return actions.ToTool(ToolListOnCallName, ToolListOnCallDescription, ToolListOnCall)
}

// generateOnCallExcel 生成OnCall数据的Excel文件
func generateOnCallExcel(data []simpleMeta) (string, error) {
	xlsxPath := fmt.Sprintf("oncall_%d.xlsx", time.Now().UnixNano())
	f := excelize.NewFile()
	sheet := "Sheet1"
	f.SetSheetName("Sheet1", sheet)
	headerList := []string{"工单id", "租户id", "租户名称", "问题概述", "问题分类", "工单等级", "工单阶段", "是否跨租户工单", "源租户id", "源租户名称", "发起人", "处理人", "创建时间", "是否解决", "解决时间", "开始时间", "结束时间", "响应时间", "优化类型", "工单标签", "链接", "群聊详情"}
	for i, h := range headerList {
		col, _ := excelize.ColumnNumberToName(i + 1)
		f.SetCellValue(sheet, col+"1", h)
	}
	for i, item := range data {
		row := i + 2
		f.SetCellValue(sheet, fmt.Sprintf("A%d", row), item.ID)
		f.SetCellValue(sheet, fmt.Sprintf("B%d", row), item.TenantID)
		f.SetCellValue(sheet, fmt.Sprintf("C%d", row), item.TenantName)
		f.SetCellValue(sheet, fmt.Sprintf("D%d", row), item.Name)
		f.SetCellValue(sheet, fmt.Sprintf("E%d", row), item.OncallQuestionTypeName)
		f.SetCellValue(sheet, fmt.Sprintf("F%d", row), item.Level)
		f.SetCellValue(sheet, fmt.Sprintf("G%d", row), getStageDisplayName(item.Stage))
		f.SetCellValue(sheet, fmt.Sprintf("H%d", row), item.IsCrossTenant)
		f.SetCellValue(sheet, fmt.Sprintf("I%d", row), item.CrossTenantID)
		f.SetCellValue(sheet, fmt.Sprintf("J%d", row), item.CrossTenantName)
		f.SetCellValue(sheet, fmt.Sprintf("K%d", row), item.OriginatorUser)
		if item.HandlerUser != nil {
			f.SetCellValue(sheet, fmt.Sprintf("L%d", row), *item.HandlerUser)
		}
		f.SetCellValue(sheet, fmt.Sprintf("M%d", row), item.CreateTime)
		f.SetCellValue(sheet, fmt.Sprintf("N%d", row), item.IsSolved)
		if item.SolveTime != nil {
			f.SetCellValue(sheet, fmt.Sprintf("O%d", row), *item.SolveTime)
		}
		if item.OncallStartTime != nil {
			f.SetCellValue(sheet, fmt.Sprintf("P%d", row), *item.OncallStartTime)
		}
		if item.OncallEndTime != nil {
			f.SetCellValue(sheet, fmt.Sprintf("Q%d", row), *item.OncallEndTime)
		}
		if item.OncallResponseTime != nil {
			f.SetCellValue(sheet, fmt.Sprintf("R%d", row), *item.OncallResponseTime)
		}
		if item.TodoType != nil {
			f.SetCellValue(sheet, fmt.Sprintf("S%d", row), *item.TodoType)
		}
		if len(item.OncallTags) > 0 {
			f.SetCellValue(sheet, fmt.Sprintf("T%d", row), strings.Join(item.OncallTags, ","))
		}
		cli := newClient()
		f.SetCellValue(sheet, fmt.Sprintf("U%d", row), cli.getOnCallPageURL(item.ID))
		f.SetCellValue(sheet, fmt.Sprintf("V%d", row), item.ChatDetail)
	}
	err := f.SaveAs(xlsxPath)
	if err != nil {
		return "", err
	}
	return xlsxPath, nil
}

// ToolListOnCall 处理列出OnCall的请求
func ToolListOnCall(run *iris.AgentRunContext, args ToolListOnCallArgs) (map[string]any, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cancel()
	cli := newClient()

	// 处理租户名称到租户ID的转换
	tenantID := args.TenantID
	if args.TenantName != "" {
		if args.TenantID != 0 {
			return map[string]any{
				"err message": "不能同时指定租户ID和租户名称，请只选择其中一个",
			}, nil
		}
		// 根据租户名称查询租户ID
		id, err := cli.getTenantID(ctx, run, args.TenantName)
		if err != nil {
			run.GetLogger().Errorf("[ToolListOnCall] getTenantID err: %v", err)
			return map[string]any{
				"err message": fmt.Sprintf("根据租户名称 '%s' 查询租户ID失败，错误: %v，请检查租户名称是否准确", args.TenantName, err),
			}, nil
		}
		tenantID = id
	}

	// 如果没有指定开始时间，默认为5天前
	if args.StartTime == "" {
		now := time.Now()
		fiveDaysAgo := now.AddDate(0, 0, -5)
		args.StartTime = fiveDaysAgo.Format("2006-01-02 15:04")
	}
	// 如果没有指定结束时间，默认为当前时间
	if args.EndTime == "" {
		args.EndTime = time.Now().Format("2006-01-02 15:04")
	}
	filterFields := []map[string]interface{}{
		{"field": "create_time", "value": args.StartTime, "operator": "greater_or_equals"},
		{"field": "create_time", "value": args.EndTime, "operator": "less_or_equals"},
		{"field": "stage", "value": []string{"launch_oncall"}, "operator": "in"},
	}
	if tenantID != 0 {
		filterFields = append(filterFields, map[string]interface{}{
			"field": "tenant_id", "value": []int{tenantID}, "operator": "in",
		})
	}
	if args.IsSolved != nil {
		filterFields = append(filterFields, map[string]interface{}{
			"field": "is_solved", "value": *args.IsSolved, "operator": "equals",
		})
	}

	// 设置默认的LimitRecordNum
	if args.LimitRecordNum <= 0 {
		args.LimitRecordNum = 2000
	} else if args.LimitRecordNum > 2000 {
		args.LimitRecordNum = 2000
	}

	// 自动分页搜索所有oncall列表数据
	allData := make([]simpleMeta, 0)
	pageSize := 100
	currentPage := 1
	var totalItems int
	for {
		response, err := cli.listOnCall(ctx, run, filterFields, currentPage, pageSize)
		if err != nil {
			run.GetLogger().Errorf("[ToolListOnCall] listOnCall err: %v", err)
			// 列表搜索失败直接返回，让机器人重试
			return map[string]any{
				"err message": fmt.Sprintf("搜索oncall列表时失败，错误: %v，请判断错误是否可以重试，是则重试", err),
			}, nil
		}
		allData = append(allData, response.Data...)
		totalItems = response.Page.TotalItems
		if response.Page.CurrentPage >= response.Page.TotalPage || len(response.Data) == 0 {
			break
		}
		// 如果达到限制数量，则停止获取
		if len(allData) >= args.LimitRecordNum {
			allData = allData[:args.LimitRecordNum]
			break
		}
		currentPage++
	}

	var hasChatErr bool
	// 如果需要群聊信息，则拉取群聊详情
	if args.NeedChatDetail {
		for i := range allData {
			// 私密模式直接打标记
			if allData[i].IsPrivateFlow {
				allData[i].ChatDetail = PrivateFlowWarning
				continue
			}
			// 访问群聊
			detail, err := cli.getOnCallChatMessageLogs(ctx, run, allData[i].ID, allData[i].IsPrivateFlow)
			if err != nil {
				// 如果失败了，将失败的信息装填到详情，让模型后续判断是否重试
				hasChatErr = true
				allData[i].ChatDetail = fmt.Sprintf("oncall id: %v, 获取群聊失败, 失败原因: %v", allData[i].ID, err)
				run.GetLogger().Errorf("[ToolListOnCall] getOnCallChatMessageLogs err: %v", err)
				continue
			}
			chatJSON, _ := jsoniter.MarshalToString(detail)
			allData[i].ChatDetail = chatJSON
		}
	}

	// 生成Excel文件
	xlsxPath, err := generateOnCallExcel(allData)
	if err != nil {
		return nil, err
	}

	// 构建查询描述信息
	var tenantInfo string
	if args.TenantName != "" {
		tenantInfo = fmt.Sprintf("，租户名称：%s（租户ID：%d）", args.TenantName, tenantID)
	} else if tenantID != 0 {
		tenantInfo = fmt.Sprintf("，租户ID：%d", tenantID)
	} else {
		tenantInfo = "，所有租户"
	}

	res := map[string]any{
		"description":  fmt.Sprintf("已完成OnCall列表查询，查询时间范围为：%s ~ %s%s\n查询到的数据已保存到文件中，文件路径为：%s\n可以使用这些数据进行后续数据分析或数据处理。", args.StartTime, args.EndTime, tenantInfo, xlsxPath),
		"file_path":    xlsxPath,
		"items_count":  len(allData),
		"data_summary": fmt.Sprintf("查询到%d个OnCall工单，包含工单的详细信息及群聊内容（如有）", len(allData)),
		"reference":    xlsxPath,
	}
	if hasChatErr {
		res["err message"] = "当前有部分群聊获取失败，请检查群聊详情关键字：`获取群聊失败, 失败原因`以定位失败群聊。如果需要获取，请使用查询单个oncall重新获取群聊"
	}
	if len(allData) >= args.LimitRecordNum {
		res["warning"] = fmt.Sprintf("由于设置了记录数限制（%d条），实际返回的数据可能少于总数据量。总数据量为：%d条", args.LimitRecordNum, totalItems)
	}
	return res, nil
}

const (
	// ToolGetOnCallChatMsgName get_oncall工具名称
	ToolGetOnCallChatMsgName = "get_oncall_chat_message"

	// ToolGetOnCallDescription get_oncall工具描述
	ToolGetOnCallDescription = "根据oncall ID查询群聊"
)

// ToolGetOnCallArgs 查询单个Oncall的工具入参
type ToolGetOnCallArgs struct {
	ID string `json:"id" mapstructure:"id" description:"oncall id，此处填写oncall id"`
}

func NewGetOnCallChatMessage() iris.Action {
	return actions.ToTool(ToolGetOnCallChatMsgName, ToolGetOnCallDescription, ToolGetOnCallMessage)
}

func ToolGetOnCallMessage(run *iris.AgentRunContext, args ToolGetOnCallArgs) (map[string]any, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 1*time.Minute)
	defer cancel()
	cli := newClient()
	filterFields := []map[string]any{{
		"field": "id", "value": []string{args.ID}, "operator": "in",
	}}

	resp, err := cli.listOnCall(ctx, run, filterFields, 1, 50)
	if err != nil {
		run.GetLogger().Errorf("[ToolGetOnCall] listOnCall err: %v", err)
		return nil, errors.Errorf("查询oncall详情报错: %v", err)
	}
	if len(resp.Data) == 0 {
		return map[string]any{
			"message": "没有此oncall的信息",
		}, nil
	}
	data := resp.Data[0]
	if data.IsPrivateFlow {
		return map[string]any{
			"oncall id":   data.ID,
			"err message": PrivateFlowWarning,
		}, nil
	}
	// 访问群聊
	detail, err := cli.getOnCallChatMessageLogs(ctx, run, data.ID, data.IsPrivateFlow)
	if err != nil {
		run.GetLogger().Errorf("[ToolGetOnCall] getOnCallChatMessageLogs err: %v", err)
		return map[string]any{
			"oncall id":   data.ID,
			"err message": fmt.Sprintf("oncall id: %v, 获取群聊失败, 失败原因: %v", data.ID, err),
		}, nil
	}
	chatJSON, _ := jsoniter.MarshalToString(detail)
	return map[string]any{
		"oncall id": data.ID,
		"群聊详情":      chatJSON,
	}, nil
}

// ToolListOnCallFromURLArgs 基于URL搜索OnCall的工具入参
type ToolListOnCallFromURLArgs struct {
	URL            string `json:"url" mapstructure:"url" description:"[Required] OnCall平台的搜索结果URL。必须是来自 https://oncall.bytedance.net/admin/review/all 页面的完整URL链接，且URL中必须包含filter_fields查询参数。例如：https://oncall.bytedance.net/admin/review/all?tenant_id=123&filter_fields=%5B%7B%22field%22%3A%22create_time%22...%5D"`
	StartTime      string `json:"start_time,omitempty" mapstructure:"start_time" description:"可选，开始时间，格式：YYYY-MM-DD HH:mm，如不提供则使用URL中的时间"`
	EndTime        string `json:"end_time,omitempty" mapstructure:"end_time" description:"可选，结束时间，格式：YYYY-MM-DD HH:mm，如不提供则使用URL中的时间"`
	NeedChatDetail bool   `json:"need_chat_detail,omitempty" mapstructure:"need_chat_detail" description:"是否需要获取每个Oncall群聊详情，为true则在搜索工单的同时获取所有群聊详情，默认不获取"`
	LimitRecordNum int    `json:"limit_record_num,omitempty" mapstructure:"limit_record_num" description:"可选，填写最多需要获取的记录数目。默认是2000，上限是2000"`
}

func NewListOnCallFromURLTool() iris.Action {
	return actions.ToTool(ToolListOnCallFromURLName, ToolListOnCallFromURLDescription, ToolListOnCallFromURL)
}

// parseFilterFieldsFromURL 从URL中解析filterFields参数
func parseFilterFieldsFromURL(rawurl string) ([]map[string]interface{}, error) {
	parsedURL, err := url.Parse(rawurl)
	if err != nil {
		return nil, fmt.Errorf("解析URL失败: %v", err)
	}

	// 获取filter_fields参数
	filterFieldsStr := parsedURL.Query().Get("filter_fields")
	if filterFieldsStr == "" {
		return nil, fmt.Errorf("URL中缺少filter_fields参数")
	}

	// URL解码
	decodedStr, err := url.QueryUnescape(filterFieldsStr)
	if err != nil {
		return nil, fmt.Errorf("解码filter_fields参数失败: %v", err)
	}

	// 解析JSON
	var filterFields []map[string]interface{}
	if err := jsoniter.UnmarshalFromString(decodedStr, &filterFields); err != nil {
		return nil, fmt.Errorf("解析filter_fields JSON失败: %v", err)
	}

	return filterFields, nil
}

// updateTimeInFilterFields 更新filterFields中的时间字段
func updateTimeInFilterFields(filterFields []map[string]interface{}, startTime, endTime string) []map[string]interface{} {
	// 移除现有的时间字段
	newFilterFields := make([]map[string]interface{}, 0, len(filterFields))
	for _, field := range filterFields {
		if field["field"] == "create_time" {
			continue
		}
		newFilterFields = append(newFilterFields, field)
	}

	// 添加新的时间字段
	if startTime != "" {
		newFilterFields = append(newFilterFields, map[string]interface{}{
			"field":    "create_time",
			"value":    startTime,
			"operator": "greater_or_equals",
		})
	}
	if endTime != "" {
		newFilterFields = append(newFilterFields, map[string]interface{}{
			"field":    "create_time",
			"value":    endTime,
			"operator": "less_or_equals",
		})
	}

	return newFilterFields
}

// ToolListOnCallFromURL 处理基于URL列出OnCall的请求
func ToolListOnCallFromURL(run *iris.AgentRunContext, args ToolListOnCallFromURLArgs) (map[string]any, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cancel()
	cli := newClient()

	// 解析URL中的filterFields
	filterFields, err := parseFilterFieldsFromURL(args.URL)
	if err != nil {
		return map[string]any{
			"err message": fmt.Sprintf("解析URL失败: %v", err),
		}, nil
	}

	// 只有在提供了时间参数时才更新时间字段
	if args.StartTime != "" || args.EndTime != "" {
		filterFields = updateTimeInFilterFields(filterFields, args.StartTime, args.EndTime)
	}

	// 设置默认的LimitRecordNum
	if args.LimitRecordNum <= 0 {
		args.LimitRecordNum = 2000
	} else if args.LimitRecordNum > 2000 {
		args.LimitRecordNum = 2000
	}

	// 自动分页搜索所有oncall列表数据
	allData := make([]simpleMeta, 0)
	pageSize := 100
	currentPage := 1
	var totalItems int
	for {
		response, err := cli.listOnCall(ctx, run, filterFields, currentPage, pageSize)
		if err != nil {
			run.GetLogger().Errorf("[ToolListOnCallFromURL] listOnCall err: %v", err)
			return map[string]any{
				"err message": fmt.Sprintf("搜索oncall列表时失败，错误: %v，请判断错误是否可以重试，是则重试", err),
			}, nil
		}
		allData = append(allData, response.Data...)
		totalItems = response.Page.TotalItems
		if response.Page.CurrentPage >= response.Page.TotalPage || len(response.Data) == 0 {
			break
		}
		// 如果达到限制数量，则停止获取
		if len(allData) >= args.LimitRecordNum {
			allData = allData[:args.LimitRecordNum]
			break
		}
		currentPage++
	}

	var hasChatErr bool
	// 如果需要群聊信息，则拉取群聊详情
	if args.NeedChatDetail {
		for i := range allData {
			// 私密模式直接打标记
			if allData[i].IsPrivateFlow {
				allData[i].ChatDetail = PrivateFlowWarning
				continue
			}
			// 访问群聊
			detail, err := cli.getOnCallChatMessageLogs(ctx, run, allData[i].ID, allData[i].IsPrivateFlow)
			if err != nil {
				// 如果失败了，将失败的信息装填到详情，让模型后续判断是否重试
				hasChatErr = true
				allData[i].ChatDetail = fmt.Sprintf("oncall id: %v, 获取群聊失败, 失败原因: %v", allData[i].ID, err)
				run.GetLogger().Errorf("[ToolListOnCallFromURL] getOnCallChatMessageLogs err: %v", err)
				continue
			}
			chatJSON, _ := jsoniter.MarshalToString(detail)
			allData[i].ChatDetail = chatJSON
		}
	}

	// 生成Excel文件
	xlsxPath, err := generateOnCallExcel(allData)
	if err != nil {
		return nil, err
	}

	res := map[string]any{
		"description":  fmt.Sprintf("已从URL中提取到OnCall数据，查询到的数据已保存到文件中，文件路径为：%s\n可以使用这些数据进行后续数据分析或数据处理。", xlsxPath),
		"file_path":    xlsxPath,
		"items_count":  len(allData),
		"data_summary": fmt.Sprintf("查询到%d个OnCall工单，包含工单的详细信息及群聊内容（如有）", len(allData)),
		"reference":    xlsxPath,
	}
	if hasChatErr {
		res["err message"] = "当前有部分群聊获取失败，请检查群聊详情关键字：`获取群聊失败, 失败原因`以定位失败群聊。如果需要获取，请使用查询单个oncall重新获取群聊"
	}
	if len(allData) >= args.LimitRecordNum {
		res["warning"] = fmt.Sprintf("由于设置了记录数限制（%d条），实际返回的数据可能少于总数据量。总数据量为：%d条", args.LimitRecordNum, totalItems)
	}
	return res, nil
}
