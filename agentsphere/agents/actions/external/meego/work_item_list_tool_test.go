package meego

//import (
//	"encoding/json"
//	"testing"
//
//	"github.com/bytedance/mockey"
//	"github.com/sirupsen/logrus"
//
//	"code.byted.org/devgpt/kiwis/agentsphere/entity"
//	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
//
//	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
//)
//
//func TestListWorkItems(t *testing.T) {
//	c := &iris.AgentRunContext{
//		Environ: &iris.RunEnviron{
//			Map: map[string]string{
//				entity.RuntimeEnvironUserCloudJWT: "*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
//			},
//		},
//	}
//
//	llm, err := framework.NewOpenAILLM(
//		"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",
//		"https://openrouter.ai/api/v1")
//	if err != nil {
//		t.Errorf("NewAzureOpenAILLM() error = %v", err)
//	}
//	defer mockey.UnPatchAll()
//
//	mockey.Mock(iris.Logger.Infof).Return().Build()
//	mockey.Mock(iris.Logger.Errorf).Return().Build()
//	mockey.Mock((*iris.AgentRunContext).GetLogger).Return(&logrus.Logger{}).Build()
//
//	mockey.Mock((*iris.AgentRunContext).GetLLM).Return(llm).Build()
//
//	got, err := ListWorkItems(c, ListWorkItemsArgs{
//		SimpleName:     "sys_ste",
//		ResultFilePath: "requirements_sys_ste_2025.xlsx",
//		WorkItemTypes:  []string{"658e92c303a411641c045593"},
//		StartTime:      "2025-01-01",
//		EndTime:        "2025-12-31",
//	})
//	gotJson, err := json.MarshalIndent(got, "", "  ")
//	if err != nil {
//		t.Errorf("ListWorkItems() error = %v", err)
//		return
//	}
//	t.Logf("ListWorkItems() got = %s", string(gotJson))
//}
