package lark

import (
	"context"
	"encoding/json"
	"fmt"
	"html"
	"image"
	_ "image/gif"
	_ "image/jpeg"
	_ "image/png"
	"os"
	"regexp"
	"strconv"
	"strings"
	"time"

	"code.byted.org/gopkg/logs"
	larkdocx "github.com/larksuite/oapi-sdk-go/v3/service/docx/v1"
	larkdrive "github.com/larksuite/oapi-sdk-go/v3/service/drive/v1"
	"github.com/olekukonko/tablewriter"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/devai/data/util"

	"code.byted.org/devgpt/kiwis/port/lark"
)

type LarkParser struct {
	config               *Config
	ImgTokens            []string
	blockMap             map[string]*larkdocx.Block
	larkCli              lark.Client
	larkOption           lark.Option
	userOpenIDRegex      *regexp.Regexp
	counters             map[string]int
	headingLevel         []int  // Track heading hierarchy
	currentParagraph     int    // Track current paragraph number
	insideTable          bool   // Track if we're inside a table
	insideGrid           bool   // Track if we're inside a grid column
	insideCallout        bool   // Track if we're inside a callout
	currentSection       string // Current section heading number
	sectionSentenceCount int    // Count sentences within current section
	userComments         []*larkdrive.FileComment
	c                    *iris.AgentRunContext
}

type Config struct {
	enableImage          bool
	enableUserOpenID     bool
	enableComment        bool
	enableDownloadRefDoc bool // 是否下载引用的文档
	larkOption           lark.Option
}

type OptionFunc func(config *Config)

// WithEnableImage 是否解析图片，默认不解析
func WithEnableImage(enable bool) OptionFunc {
	return func(config *Config) {
		config.enableImage = enable
	}
}

// WithEnableUserOpenID 是否将用户 Open ID 解析为用户邮箱，默认不解析
func WithEnableUserOpenID(enable bool) OptionFunc {
	return func(config *Config) {
		config.enableUserOpenID = enable
	}
}

// WithComment 是否增加注释，标明是第几个表格，图片，段落，句子
func WithComment(enable bool) OptionFunc {
	return func(config *Config) {
		config.enableComment = enable
	}
}

func WithLarkOption(option lark.Option) OptionFunc {
	return func(config *Config) {
		config.larkOption = option
	}
}

// WithEnableDownloadRefDoc 是否下载引用的文档，默认不下载
func WithEnableDownloadRefDoc(enable bool) OptionFunc {
	return func(config *Config) {
		config.enableDownloadRefDoc = enable
	}
}

const userOpenIDPattern = `ou_[a-f0-9]{32}`

func NewLarkParser(c *iris.AgentRunContext, larkCli lark.Client, options ...OptionFunc) *LarkParser {
	config := &Config{
		enableImage:          false,
		enableUserOpenID:     false,
		enableComment:        false,
		enableDownloadRefDoc: false,
	}

	for _, option := range options {
		option(config)
	}

	return &LarkParser{
		config:               config,
		ImgTokens:            make([]string, 0),
		blockMap:             make(map[string]*larkdocx.Block),
		userComments:         make([]*larkdrive.FileComment, 0),
		larkCli:              larkCli,
		userOpenIDRegex:      regexp.MustCompile(userOpenIDPattern),
		larkOption:           config.larkOption,
		counters:             make(map[string]int),
		headingLevel:         make([]int, 10), // Support up to 9 heading levels plus root
		currentParagraph:     0,
		insideTable:          false,
		insideGrid:           false,
		insideCallout:        false,
		currentSection:       "",
		sectionSentenceCount: 0,
		c:                    c,
	}
}

// ParseDocxContent parse new lark document(docType docx)
func (p *LarkParser) ParseDocxContent(ctx context.Context, documentID string, blocks []*larkdocx.Block) string {
	// Reset counters for each new document
	p.counters = make(map[string]int)
	p.headingLevel = make([]int, 10) // Initialize with all zeros
	p.currentParagraph = 0
	p.insideTable = false
	p.insideGrid = false
	p.insideCallout = false
	p.currentSection = ""
	p.sectionSentenceCount = 0

	// Build block map
	p.blockMap = make(map[string]*larkdocx.Block)
	for _, block := range blocks {
		p.blockMap[*block.BlockId] = block
	}

	entryBlock := p.blockMap[documentID]
	return p.ParseUserOpenID(ctx, p.ParseDocxBlock(entryBlock, 0))
}

func (p *LarkParser) ParseUserOpenID(ctx context.Context, content string) string {
	if !p.config.enableUserOpenID || p.larkCli == nil || len(content) == 0 {
		return content
	}
	// 匹配飞书用户 open id 的正则表达式
	openIDs := p.userOpenIDRegex.FindAllString(content, -1)
	if len(openIDs) == 0 {
		return content
	}
	users, err := p.larkCli.ListLarkUsers(ctx, lo.Uniq(openIDs), "")
	if err != nil {
		logs.CtxWarn(ctx, "failed to list lark users, err: %v", err)
		return content
	}
	usersMap := make(map[string]string, len(users))
	for _, user := range users {
		usersMap[*user.OpenId] = *user.Email
	}
	// 将 open id 替换为 @用户邮箱 的形式
	for openID, email := range usersMap {
		content = strings.ReplaceAll(content, openID, fmt.Sprintf("@(%s)", email))
	}
	return content
}

// getHeadingNumber returns the formatted heading number based on current hierarchy
func (p *LarkParser) getHeadingNumber(level int) string {
	if level <= 0 || level > 9 {
		return ""
	}

	// Reset deeper levels when a shallower heading is encountered
	for i := level + 1; i < len(p.headingLevel); i++ {
		p.headingLevel[i] = 0
	}

	// Increment the current level
	p.headingLevel[level]++

	// Build the heading number string, but skip levels with value 0
	var parts []string
	for i := 1; i <= level; i++ {
		if p.headingLevel[i] > 0 {
			parts = append(parts, strconv.Itoa(p.headingLevel[i]))
		}
	}

	return strings.Join(parts, ".")
}

// hasNumberPrefix checks if the text already starts with a number prefix like "1.", "1.2.", "1.2", etc.
func (p *LarkParser) hasNumberPrefix(text string) bool {
	// Match patterns like "1.", "1.2.", "1.2.3.", "1.2", etc.
	matched, _ := regexp.MatchString(`^\d+(\.\d+)*\.?`, strings.TrimSpace(text))
	return matched
}

func (p *LarkParser) ParseDocxBlock(b *larkdocx.Block, indentLevel int) string {
	if b == nil {
		return ""
	}
	buf := new(strings.Builder)
	buf.WriteString(strings.Repeat("\t", indentLevel))
	blockType := *b.BlockType

	// Check if we're entering a table, grid or callout
	wasInsideTable := p.insideTable
	wasInsideGrid := p.insideGrid
	wasInsideCallout := p.insideCallout
	if blockType == lark.DocxBlockTypeTable {
		p.insideTable = true
	} else if blockType == lark.DocxBlockTypeGrid {
		p.insideGrid = true
	} else if blockType == lark.DocxBlockTypeCallout {
		p.insideCallout = true
	}

	// Handle headings and update section tracking
	headingLevel := 0

	switch blockType {
	case lark.DocxBlockTypeHeading1:
		headingLevel = 1
	case lark.DocxBlockTypeHeading2:
		headingLevel = 2
	case lark.DocxBlockTypeHeading3:
		headingLevel = 3
	case lark.DocxBlockTypeHeading4:
		headingLevel = 4
	case lark.DocxBlockTypeHeading5:
		headingLevel = 5
	case lark.DocxBlockTypeHeading6:
		headingLevel = 6
	case lark.DocxBlockTypeHeading7:
		headingLevel = 7
	case lark.DocxBlockTypeHeading8:
		headingLevel = 8
	case lark.DocxBlockTypeHeading9:
		headingLevel = 9
	}

	if headingLevel > 0 {
		p.sectionSentenceCount = 0
		p.currentParagraph++

		// Add heading comment if enabled and not inside a table or grid
		if p.config.enableComment && !p.insideTable && !p.insideGrid {
			// We'll get the heading number later in ParseDocxBlockHeadingText
			buf.WriteString(strings.Repeat("\t", indentLevel))
		}
	}

	// Process the block based on type
	result := ""
	switch blockType {
	case lark.DocxBlockTypePage:
		result = p.ParseDocxBlockContainer(b)
	case lark.DocxBlockTypeText:
		result = p.ParseDocxBlockText(b.Text, blockType) + p.ParseDocxBlockContainer(b)
	case lark.DocxBlockTypeHeading1:
		result = "# " + p.ParseDocxBlockHeadingText(b.Heading1, 1) + p.ParseDocxBlockContainer(b)
	case lark.DocxBlockTypeHeading2:
		result = "## " + p.ParseDocxBlockHeadingText(b.Heading2, 2) + p.ParseDocxBlockContainer(b)
	case lark.DocxBlockTypeHeading3:
		result = "### " + p.ParseDocxBlockHeadingText(b.Heading3, 3) + p.ParseDocxBlockContainer(b)
	case lark.DocxBlockTypeHeading4:
		result = "#### " + p.ParseDocxBlockHeadingText(b.Heading4, 4) + p.ParseDocxBlockContainer(b)
	case lark.DocxBlockTypeHeading5:
		result = "##### " + p.ParseDocxBlockHeadingText(b.Heading5, 5) + p.ParseDocxBlockContainer(b)
	case lark.DocxBlockTypeHeading6:
		result = "###### " + p.ParseDocxBlockHeadingText(b.Heading6, 6) + p.ParseDocxBlockContainer(b)
	case lark.DocxBlockTypeHeading7:
		result = "####### " + p.ParseDocxBlockHeadingText(b.Heading7, 7) + p.ParseDocxBlockContainer(b)
	case lark.DocxBlockTypeHeading8:
		result = "######## " + p.ParseDocxBlockHeadingText(b.Heading8, 8) + p.ParseDocxBlockContainer(b)
	case lark.DocxBlockTypeHeading9:
		result = "######### " + p.ParseDocxBlockHeadingText(b.Heading9, 9) + p.ParseDocxBlockContainer(b)
	case lark.DocxBlockTypeBullet:
		result = p.ParseDocxBlockBullet(b, indentLevel)
	case lark.DocxBlockTypeOrdered:
		result = p.ParseDocxBlockOrdered(b, indentLevel)
	case lark.DocxBlockTypeCode:
		if b.Code.Style.Language != nil {
			result = "```" + lark.DocxCodeLang2MdStr[lark.DocxCodeLanguage(*b.Code.Style.Language)] + "\n" +
				strings.TrimSpace(p.ParseDocxBlockText(b.Code, blockType)) + "\n```\n"
		} else {
			result = "```\n" + strings.TrimSpace(p.ParseDocxBlockText(b.Code, blockType)) + "\n```\n"
		}
	case lark.DocxBlockTypeQuote:
		result = "> " + p.ParseDocxBlockText(b.Quote, blockType)
	case lark.DocxBlockTypeEquation:
		result = "$$\n" + p.ParseDocxBlockText(b.Equation, blockType) + "\n$$\n"
	case lark.DocxBlockTypeTodo:
		if *b.Todo.Style.Done {
			result = "- [x] " + p.ParseDocxBlockText(b.Todo, blockType)
		} else {
			result = "- [ ] " + p.ParseDocxBlockText(b.Todo, blockType)
		}
	case lark.DocxBlockTypeDivider:
		result = "---\n"
	case lark.DocxBlockTypeImage:
		if p.config.enableImage {
			if p.config.enableComment && !p.insideTable && !p.insideGrid {
				p.counters["image"] = p.counters["image"] + 1
				result = fmt.Sprintf("<!-- 图片%d -->\n", p.counters["image"]) + p.ParseDocxBlockImage(b.Image)
			} else {
				result = p.ParseDocxBlockImage(b.Image)
			}
		}
	case lark.DocxBlockTypeSheet:
		result = p.ParseDocxBlockSheet(b.Sheet)
	case lark.DocxBlockTypeTableCell:
		result = p.ParseDocxBlockTableCell(b)
	case lark.DocxBlockTypeTable:
		// Only add comment if not inside another table or grid
		if p.config.enableComment && !wasInsideTable && !wasInsideGrid {
			p.counters["table"] = p.counters["table"] + 1
			result = fmt.Sprintf("<!-- 表格%d -->\n", p.counters["table"]) + p.ParseDocxBlockTableV2(b)
		} else {
			result = p.ParseDocxBlockTableV2(b)
		}
	case lark.DocxBlockTypeQuoteContainer:
		result = p.ParseDocxBlockQuoteContainer(b)
	case lark.DocxBlockTypeCallout:
		result = p.ParseDocxBlockCallout(b)
	case lark.DocxBlockTypeUndefined:
		result = p.ParseDocxBlockContainer(b)
	case lark.DocxBlockTypeBoard:
		result = p.ParseDocxBlockBoard(b)
	case lark.DocxBlockTypeFile:
		result = p.ParseDocxBlockFile(b)
	case lark.DocxBlockTypeGrid:
		result = p.ParseDocxBlockGrid(b)
	case lark.DocxBlockTypeGridColumn:
		result = p.ParseDocxBlockGridColumn(b)
	case lark.DocxBlockTypeLinkPreview:
		if b.LinkPreview != nil && b.LinkPreview.Url != nil {
			result = fmt.Sprintf("[preview](%s)", util.UnescapeURL(*b.LinkPreview.Url))
		}
	case lark.DocxBlockTypeIframe:
		if b.Iframe != nil && b.Iframe.Component != nil && b.Iframe.Component.Url != nil {
			result = fmt.Sprintf("[preview](%s)", util.UnescapeURL(*b.Iframe.Component.Url))
		}
	case lark.DocxBlockTypeView:
		result = p.ParseDocxBlockView(b)
	case lark.DocxBlockTypeSourceSynced:
		result = p.ParseDocxBlockContainer(b)
	}

	buf.WriteString(result)
	if len(b.CommentIds) > 0 && len(p.userComments) > 0 {
		for _, commentID := range b.CommentIds {
			for _, comment := range p.userComments {
				if *comment.CommentId == commentID {
					userCommentText := p.getAtAimeCommentText(comment)
					if userCommentText != "" {
						if blockType == lark.DocxBlockTypeTable || blockType == lark.DocxBlockTypeSheet || blockType == lark.DocxBlockTypeTableCell {
							buf.WriteString(fmt.Sprintf(" <!-- comment for cell %s：%s --> ", *comment.Quote, userCommentText))
						} else {
							buf.WriteString(fmt.Sprintf(" <!-- comment：%s --> ", userCommentText))
						}
					}
				}
			}
		}
	}
	// Restore previous state when leaving a table, grid or callout
	if blockType == lark.DocxBlockTypeTable {
		p.insideTable = wasInsideTable
	}
	if blockType == lark.DocxBlockTypeGrid {
		p.insideGrid = wasInsideGrid
	}
	if blockType == lark.DocxBlockTypeCallout {
		p.insideCallout = wasInsideCallout
	}

	return buf.String()
}

func (p *LarkParser) getAtAimeCommentText(comment *larkdrive.FileComment) string {
	atAime := ""
	for _, reply := range comment.ReplyList.Replies {
		for _, element := range reply.Content.Elements {
			if element.TextRun != nil && element.TextRun.Text != nil {
				text := *element.TextRun.Text
				if strings.Contains(strings.ToLower(text), "@aime") {
					if atAime == "" {
						atAime += html.UnescapeString(removeIgnoreCase(text, "@aime"))
					} else {
						atAime += "\n" + html.UnescapeString(removeIgnoreCase(text, "@aime"))
					}
				}
			}
		}
	}

	return strings.ReplaceAll(atAime, "\n", " ")
}

func removeIgnoreCase(s, target string) string {
	lowerS := strings.ToLower(s)
	lowerTarget := strings.ToLower(target)
	var result strings.Builder
	start := 0
	for {
		idx := strings.Index(lowerS[start:], lowerTarget)
		if idx == -1 {
			// 没找到，写入剩余部分
			result.WriteString(s[start:])
			break
		}
		// 写入匹配前的部分
		result.WriteString(s[start : start+idx])
		// 跳过匹配的子串
		start += idx + len(target)
	}
	return result.String()
}

func (p *LarkParser) ParseDocxBlockContainer(b *larkdocx.Block) string {
	buf := new(strings.Builder)

	// 处理全局评论 - 只在文档根节点处理
	if b.BlockType != nil && *b.BlockType == lark.DocxBlockTypePage && len(p.userComments) > 0 {
		// 查找全局评论（使用 IsWhole 字段判断）
		var globalComments []string
		for _, comment := range p.userComments {
			// 使用 IsWhole 字段判断是否为全局评论
			if comment.IsWhole != nil && *comment.IsWhole {
				globalCommentText := p.getAtAimeCommentText(comment)
				if globalCommentText != "" {
					globalComments = append(globalComments, globalCommentText)
				}
			}
		}

		// 如果有全局评论，添加到文档开头
		if len(globalComments) > 0 {
			buf.WriteString("<!-- 全局评论开始 -->\n")
			buf.WriteString("<!-- 注意：以下是针对整个文档的全局评论，请在理解和处理文档内容时重点参考这些评论 -->\n")
			for i, comment := range globalComments {
				buf.WriteString(fmt.Sprintf("<!-- 全局评论%d：%s -->\n", i+1, comment))
			}
			buf.WriteString("<!-- 全局评论结束 -->\n\n")
		}
	}

	for _, childID := range b.Children {
		childBlock := p.blockMap[childID]
		buf.WriteString(p.ParseDocxBlock(childBlock, 0))
		buf.WriteString("\n")
	}

	return buf.String()
}

func (p *LarkParser) ParseDocxBlockText(b *larkdocx.Text, blockType lark.DocxBlockType) string {
	buf := new(strings.Builder)
	numElem := len(b.Elements)
	for _, e := range b.Elements {
		inline := numElem > 1
		if p.config.enableComment && blockType == lark.DocxBlockTypeText &&
			p.currentSection != "" && !p.insideTable && !p.insideGrid {
			elementText := p.ParseDocxTextElement(e, inline)
			if elementText != "" && !strings.HasPrefix(elementText, "$$") && !strings.HasPrefix(elementText, "$") {
				p.sectionSentenceCount++
				buf.WriteString(fmt.Sprintf("<!-- 段落%s 句子%d -->", p.currentSection, p.sectionSentenceCount))
			}
		}
		buf.WriteString(p.ParseDocxTextElement(e, inline))
	}
	buf.WriteString("\n")
	return buf.String()
}

func (p *LarkParser) ParseDocxTextElement(e *larkdocx.TextElement, inline bool) string {
	buf := new(strings.Builder)
	if e.TextRun != nil {
		buf.WriteString(p.ParseDocxTextElementTextRun(e.TextRun))
	}
	if e.MentionUser != nil {
		buf.WriteString(*e.MentionUser.UserId)
	}
	if e.MentionDoc != nil && e.MentionDoc.Title != nil && e.MentionDoc.Url != nil {
		//if p.config.enableDownloadRefDoc {
		//	// 下载引用的文档并返回本地文件引用
		//	localFile := p.downloadReferencedDoc(context.Background(), *e.MentionDoc.Url, *e.MentionDoc.Title)
		//	if localFile != "" {
		//		buf.WriteString(fmt.Sprintf("[%s](%s)", *e.MentionDoc.Title, localFile))
		//	} else {
		//		// 下载失败，使用原始URL
		//		buf.WriteString(fmt.Sprintf("[%s](%s)", *e.MentionDoc.Title, util.UnescapeURL(*e.MentionDoc.Url)))
		//	}
		//} else {
		buf.WriteString(fmt.Sprintf("[%s](%s)", *e.MentionDoc.Title, util.UnescapeURL(*e.MentionDoc.Url)))
		//}
	}
	if e.Equation != nil && e.Equation.Content != nil {
		symbol := "$$"
		if inline {
			symbol = "$"
		}
		buf.WriteString(symbol + strings.TrimSuffix(*e.Equation.Content, "\n") + symbol)
	}
	return buf.String()
}

func (p *LarkParser) ParseDocxTextElementTextRun(tr *larkdocx.TextRun) string {
	buf := new(strings.Builder)
	postWrite := ""
	content := *tr.Content

	if style := tr.TextElementStyle; style != nil {
		// 对于粗体、斜体、删除线，如果content前后有空格，需要替换成&nbsp;
		needsSpaceReplacement := false

		if *style.Bold {
			buf.WriteString("**")
			postWrite = "**"
			needsSpaceReplacement = true
		} else if *style.Italic {
			buf.WriteString("***")
			postWrite = "***"
			needsSpaceReplacement = true
		} else if *style.Strikethrough {
			buf.WriteString("~~")
			postWrite = "~~"
			needsSpaceReplacement = true
		} else if *style.Underline {
		} else if *style.InlineCode {
			buf.WriteString("`")
			postWrite = "`"
		} else if link := style.Link; link != nil {
			buf.WriteString("[")
			postWrite = fmt.Sprintf("](%s)", util.UnescapeURL(*link.Url))
		}

		// 处理字体颜色
		if style.TextColor != nil && *style.TextColor > 0 {
			var colorName string
			switch *style.TextColor {
			case 1:
				colorName = "red"
			case 2:
				colorName = "orange"
			case 3:
				colorName = "yellow"
			case 4:
				colorName = "green"
			case 5:
				colorName = "blue"
			case 6:
				colorName = "purple"
			case 7:
				colorName = "gray"
			default:
				colorName = "red" // 默认颜色
			}
			buf.WriteString(fmt.Sprintf("<font color=\"%s\">", colorName))
			if postWrite != "" {
				postWrite = "</font>" + postWrite
			} else {
				postWrite = "</font>"
			}
		}

		// 对于粗体、斜体、删除线，替换前后空格为&nbsp;
		if needsSpaceReplacement {
			// 替换开头的多个空格
			leadingSpaces := 0
			for _, r := range content {
				if r == ' ' {
					leadingSpaces++
				} else {
					break
				}
			}
			if leadingSpaces > 0 {
				content = strings.Repeat("&nbsp;", leadingSpaces) + content[leadingSpaces:]
			}

			// 替换结尾的多个空格
			trailingSpaces := 0
			for i := len(content) - 1; i >= 0; i-- {
				if content[i] == ' ' {
					trailingSpaces++
				} else {
					break
				}
			}
			if trailingSpaces > 0 {
				content = content[:len(content)-trailingSpaces] + strings.Repeat("&nbsp;", trailingSpaces)
			}
		}
	}
	buf.WriteString(content)
	buf.WriteString(postWrite)
	return buf.String()
}

// isValidImage checks if the image file is valid and not corrupted
func (p *LarkParser) isValidImage(filePath string) bool {
	file, err := os.Open(filePath)
	if err != nil {
		logs.Error("failed to open image file %s: %v", filePath, err)
		return false
	}
	defer file.Close()

	// Try to decode the image config to check if it's valid
	config, format, err := image.DecodeConfig(file)
	if err != nil {
		logs.Error("failed to decode image config for file %s: %v", filePath, err)
		return false
	}

	// Check file size - if it's too small, it might be corrupted
	fileInfo, err := file.Stat()
	if err != nil {
		logs.Error("failed to get file info for %s: %v", filePath, err)
		return false
	}

	// If file size is less than 100 bytes, consider it corrupted
	if fileInfo.Size() < 100 {
		logs.Error("image file %s is too small (%d bytes), might be corrupted", filePath, fileInfo.Size())
		return false
	}

	// Additional validation: check if image dimensions are reasonable
	if config.Width <= 0 || config.Height <= 0 {
		logs.Error("image file %s has invalid dimensions: %dx%d", filePath, config.Width, config.Height)
		return false
	}
	logs.Info("image file %s is valid (format: %s, dimensions: %dx%d, size: %d bytes)",
		filePath, format, config.Width, config.Height, fileInfo.Size())
	return true
}

// downloadImageWithRetry downloads image with retry mechanism for corrupted images
func (p *LarkParser) downloadImageWithRetry(ctx context.Context, token, fileName string, maxRetries int) error {
	for attempt := 1; attempt <= maxRetries; attempt++ {
		logs.Info("downloading image attempt %d/%d for token %s", attempt, maxRetries, token)

		resp, err := p.larkCli.DownloadLarkMedia(ctx, token, p.larkOption.UserAccessToken)
		if err != nil {
			logs.Error("failed to download image on attempt %d: %v", attempt, err)
			if attempt == maxRetries {
				return fmt.Errorf("failed to download image after %d attempts: %v", maxRetries, err)
			}
			// Wait before retry
			time.Sleep(time.Duration(attempt) * time.Second)
			continue
		}

		// Save image to local file
		err = resp.WriteFile(fileName)
		if err != nil {
			logs.Error("failed to save image on attempt %d: %v", attempt, err)
			if attempt == maxRetries {
				return fmt.Errorf("failed to save image after %d attempts: %v", maxRetries, err)
			}
			// Wait before retry
			time.Sleep(time.Duration(attempt) * time.Second)
			continue
		}

		// Validate the downloaded image
		if p.isValidImage(fileName) {
			logs.Info("successfully downloaded and validated image %s on attempt %d", fileName, attempt)
			return nil
		}

		logs.Warn("downloaded image %s is corrupted on attempt %d, retrying...", fileName, attempt)

		// Remove the corrupted file
		if err := os.Remove(fileName); err != nil {
			logs.Error("failed to remove corrupted image file %s: %v", fileName, err)
		}

		if attempt < maxRetries {
			// Wait before retry with exponential backoff
			time.Sleep(time.Duration(attempt*2) * time.Second)
		}
	}

	return fmt.Errorf("failed to download valid image after %d attempts", maxRetries)
}

func (p *LarkParser) ParseDocxBlockImage(img *larkdocx.Image) string {
	buf := new(strings.Builder)
	ctx := context.Background()

	// Generate filename
	fileName := fmt.Sprintf("img_%s.png", *img.Token)

	// Download image with retry mechanism (max 3 attempts)
	err := p.downloadImageWithRetry(ctx, *img.Token, fileName, 3)
	if err != nil {
		logs.Error("failed to download image after retries: %v", err)
		return buf.String()
	}

	// Use markdown syntax to reference local image
	buf.WriteString(fmt.Sprintf("![图片](%s)", fileName))
	buf.WriteString("\n")
	p.ImgTokens = append(p.ImgTokens, *img.Token)
	return buf.String()
}

func (p *LarkParser) ParseDocxBlockBullet(b *larkdocx.Block, indentLevel int) string {
	buf := new(strings.Builder)

	buf.WriteString("- ")
	buf.WriteString(p.ParseDocxBlockText(b.Bullet, lark.DocxBlockTypeBullet))

	for _, childID := range b.Children {
		childBlock := p.blockMap[childID]
		buf.WriteString(p.ParseDocxBlock(childBlock, indentLevel+1))
	}

	return buf.String()
}

func (p *LarkParser) ParseDocxBlockOrdered(b *larkdocx.Block, indentLevel int) string {
	buf := new(strings.Builder)

	// calculate order and indent level
	parent := p.blockMap[*b.ParentId]
	order := 1
	for idx, child := range parent.Children {
		if child == *b.BlockId {
			for i := idx - 1; i >= 0; i-- {
				bType := p.blockMap[parent.Children[i]].BlockType
				if *bType == lark.DocxBlockTypeOrdered {
					order += 1
				} else {
					break
				}
			}
			break
		}
	}

	buf.WriteString(fmt.Sprintf("%d. ", order))
	buf.WriteString(p.ParseDocxBlockText(b.Ordered, lark.DocxBlockTypeOrdered))

	for _, childID := range b.Children {
		childBlock := p.blockMap[childID]
		buf.WriteString(p.ParseDocxBlock(childBlock, indentLevel+1))
	}

	return buf.String()
}

func (p *LarkParser) ParseDocxBlockTableCell(b *larkdocx.Block) string {
	buf := new(strings.Builder)

	// Set inside table flag to true when processing table cells
	wasInsideTable := p.insideTable
	p.insideTable = true

	for _, child := range b.Children {
		block := p.blockMap[child]
		content := p.ParseDocxBlock(block, 0)
		buf.WriteString(content)
	}

	// Restore previous state
	p.insideTable = wasInsideTable

	return buf.String()
}

func (p *LarkParser) ParseDocxBlockTable(t *larkdocx.Table) string {
	// - First row as header
	// - Ignore cell merging
	var rows [][]string
	for i, blockID := range t.Cells {
		block := p.blockMap[blockID]
		cellContent := p.ParseDocxBlock(block, 0)
		cellContent = strings.ReplaceAll(cellContent, "\n", "")
		rowIndex := i / *t.Property.ColumnSize
		if len(rows) < int(rowIndex)+1 {
			rows = append(rows, []string{})
		}
		rows[rowIndex] = append(rows[rowIndex], cellContent)
	}

	buf := new(strings.Builder)
	buf.WriteString(renderMarkdownTable(rows))
	buf.WriteString("\n")
	return buf.String()
}

func renderMarkdownTable(data [][]string) string {
	builder := &strings.Builder{}
	table := tablewriter.NewWriter(builder)
	table.SetCenterSeparator("|")
	table.SetAutoWrapText(false)
	table.SetAutoFormatHeaders(false)
	table.SetAutoMergeCells(false)
	table.SetBorders(tablewriter.Border{Left: true, Top: false, Right: true, Bottom: false})
	table.SetHeader(data[0])
	table.AppendBulk(data[1:])
	table.Render()
	return builder.String()
}

func (p *LarkParser) cleanBlockTable(table [][]string) [][]string {
	var tableNew [][]string

	for _, innerList := range table {
		emptyFlag := true         // Use this flag to check if the row is empty
		var innerListNew []string // This will store the new cleaned row
		for _, e := range innerList {
			eNew := e
			if eNew == "" {
				eNew = "" // In Go, there's no need to explicitly convert nil to "", but we need to maintain logic.
			} else {
				emptyFlag = false // The row is not empty since we have found a non-empty cell
			}
			innerListNew = append(innerListNew, eNew) // Add the cleaned cell to the new row
		}
		if !emptyFlag {
			// If the row is not empty, add it to the new table
			tableNew = append(tableNew, innerListNew)
		}
	}
	return tableNew
}

func (p *LarkParser) deduplicateBlockTable(table [][]string) [][]string {
	var tableNew [][]string
	existValueMap := make(map[string]bool)

	for _, colData := range table {
		colDataStr := strings.Join(colData, "|")

		if strings.TrimSpace(colDataStr) == "" { // Skip empty rows
			continue
		}
		if _, exists := existValueMap[colDataStr]; exists { // Skip duplicate rows
			continue
		}
		tableNew = append(tableNew, colData)
		existValueMap[colDataStr] = true
	}
	return tableNew
}

func (p *LarkParser) splitBlockTable(table [][]string, maxTokenNum int) string {
	table = p.deduplicateBlockTable(table)
	res := ""

	if len(table) == 0 {
		return res
	} else if len(table) == 1 {
		res = strings.Join(table[0], " ")
		return res
	}

	tableHead := table[0]
	for i := 1; i < len(table); i++ {
		colData := table[i]
		var colTextList []string
		for j, colValue := range colData {
			colValue = strings.TrimSpace(colValue)
			if colValue == "" || colValue == "-" {
				continue
			}
			colKey := strings.TrimSpace(tableHead[j])
			joinValue := colValue
			if colKey != "" && colKey != "-" {
				joinValue = colKey + "：" + colValue
			}

			if len(colTextList) == 0 || (len(colTextList) > 0 && joinValue != colTextList[len(colTextList)-1]) {
				colTextList = append(colTextList, joinValue)
			}
		}

		colText := strings.Join(colTextList, "；") + "。"
		//colText = strings.TrimSpace(regexp.MustCompile("；+").ReplaceAllString(colText, ""))
		//colText = strings.TrimSpace(regexp.MustCompile("。+").ReplaceAllString(colText, ""))

		//tokenCount := llm.CountSpecialSequence(res + colText)
		//
		//if tokenCount < maxTokenNum {
		//	res += colText + "\n"
		//} else {
		//	res = strings.TrimSpace(res)
		//	res += colText + "\n"
		//}
		res += colText + "\n"
	}

	res = strings.TrimSpace(res)
	return res
}

func (p *LarkParser) ParseDocxBlockTableV2(b *larkdocx.Block) string {
	// - First row as header
	// - Ignore cell merging
	var ColumnSize = *b.Table.Property.ColumnSize
	var RowSize = *b.Table.Property.RowSize

	// 回溯合并单元格
	tableMerge := make(map[string][2]int)
	for index, cell := range b.Table.Property.MergeInfo {
		rowIndex := index / ColumnSize
		columnIndex := index % ColumnSize
		realCell := [2]int{rowIndex, columnIndex}

		for rowIndex := 0; rowIndex < *cell.RowSpan; rowIndex++ {
			for colIndex := 0; colIndex < *cell.ColSpan; colIndex++ {
				if rowIndex == 0 && colIndex == 0 {
					continue
				}
				mergeKey := fmt.Sprintf("%d_%d", rowIndex+rowIndex, columnIndex+colIndex)
				tableMerge[mergeKey] = realCell
			}
		}
	}

	table := make([][]string, RowSize)
	for i := range table {
		table[i] = make([]string, ColumnSize)
	}
	index := 0
	for _, blockID := range b.Children {
		rowIndex := index / ColumnSize
		columnIndex := index % ColumnSize

		block := p.blockMap[blockID]
		cellContent := p.ParseDocxBlock(block, 0)
		cellContent = strings.ReplaceAll(cellContent, "\n", "<br>")
		table[rowIndex][columnIndex] = cellContent
		if cellContent == "" { // 解决单元格合并问题
			mergeKey := fmt.Sprintf("%d_%d", rowIndex, columnIndex)
			if realCell, exists := tableMerge[mergeKey]; exists {
				realRowIndex, realColumnIndex := realCell[0], realCell[1]
				table[rowIndex][columnIndex] = table[realRowIndex][realColumnIndex]
			}
		}
		index++
	}

	reNewline := regexp.MustCompile(`\n+`)
	for i, innerList := range table {
		for j, e := range innerList {
			if e == "" {
				continue
			}
			e = reNewline.ReplaceAllString(e, "\n")
			e = strings.TrimSpace(e)
			e = strings.Replace(e, "\n", " ", -1)
			table[i][j] = e
		}
	}

	table = p.cleanBlockTable(table)

	// Generate markdown table instead of text format
	buf := new(strings.Builder)

	if len(table) == 0 {
		return buf.String()
	}

	// Write table header
	for i, cell := range table[0] {
		if i == 0 {
			buf.WriteString("| ")
		}
		buf.WriteString(cell)
		buf.WriteString(" | ")
	}
	buf.WriteString("\n")

	// Write separator row
	for i := range table[0] {
		if i == 0 {
			buf.WriteString("| ")
		}
		buf.WriteString("--- | ")
	}
	buf.WriteString("\n")

	// Write table data
	for i := 1; i < len(table); i++ {
		for j, cell := range table[i] {
			if j == 0 {
				buf.WriteString("| ")
			}
			buf.WriteString(cell)
			buf.WriteString(" | ")
		}
		buf.WriteString("\n")
	}

	return buf.String()
}

func (p *LarkParser) ParseDocxBlockQuoteContainer(b *larkdocx.Block) string {
	buf := new(strings.Builder)

	for _, child := range b.Children {
		block := p.blockMap[child]
		buf.WriteString("> ")
		buf.WriteString(p.ParseDocxBlock(block, 0))
	}

	return buf.String()
}

// ParseDocxBlockCallout 解析高亮块为引用
func (p *LarkParser) ParseDocxBlockCallout(b *larkdocx.Block) string {
	buf := new(strings.Builder)

	// Format according to the specified callout structure
	buf.WriteString("```callout\n")

	// Add default background color (assuming 1 is default)
	bgColor := "1"
	if b.Callout != nil && b.Callout.BackgroundColor != nil {
		bgColor = fmt.Sprintf("%d", *b.Callout.BackgroundColor)
	}
	buf.WriteString(fmt.Sprintf("background_color: %s\n", bgColor))

	// Add default border color (assuming 1 is default)
	borderColor := "1"
	if b.Callout != nil && b.Callout.BorderColor != nil {
		borderColor = fmt.Sprintf("%d", *b.Callout.BorderColor)
	}
	buf.WriteString(fmt.Sprintf("border_color: %s\n", borderColor))

	// Add emoji if available
	if b.Callout != nil && b.Callout.EmojiId != nil {
		buf.WriteString(fmt.Sprintf("emoji_id: %s\n", *b.Callout.EmojiId))
	}

	// Add text content
	buf.WriteString("content:|\n")

	// Process each child block
	for _, childID := range b.Children {
		childBlock := p.blockMap[childID]
		content := p.ParseDocxBlock(childBlock, 0)
		// Indent each line with two spaces
		lines := strings.Split(content, "\n")
		for _, line := range lines {
			if line != "" {
				buf.WriteString("  " + line + "\n")
			}
		}
	}

	buf.WriteString("```\n")
	return buf.String()
}

// ParseDocxBlockBoard 解析白板为图片
func (p *LarkParser) ParseDocxBlockBoard(b *larkdocx.Block) string {
	buf := new(strings.Builder)

	if b.Board == nil || b.Board.Token == nil {
		logs.Error("board is nil or token is nil")
		return buf.String()
	}

	// Download whiteboard as image using larkCli
	ctx := context.Background()
	resp, err := p.larkCli.DownloadWhiteboardAsImage(ctx, *b.Board.Token, p.larkOption.UserAccessToken)
	if err != nil {
		logs.Error("failed to download whiteboard as image: %v", err)
		return buf.String()
	}

	// Save image to local file
	fileName := fmt.Sprintf("board_%s.png", *b.Board.Token)
	err = resp.WriteFile(fileName)
	if err != nil {
		logs.Error("failed to save whiteboard image: %v", err)
		return buf.String()
	}

	// Use markdown syntax to reference local image
	buf.WriteString(fmt.Sprintf("![board_%s](%s)", *b.Board.Token, fileName))
	buf.WriteString("\n")

	return buf.String()
}

// ParseDocxBlockFile 解析文件块为下载链接
func (p *LarkParser) ParseDocxBlockFile(b *larkdocx.Block) string {
	buf := new(strings.Builder)

	if b.File == nil || b.File.Token == nil {
		logs.Error("file is nil or token is nil")
		return buf.String()
	}

	// Download file using larkCli
	ctx := context.Background()
	resp, err := p.larkCli.DownloadLarkMedia(ctx, *b.File.Token, p.larkOption.UserAccessToken)
	if err != nil {
		logs.Error("failed to download file: %v", err)
		return buf.String()
	}

	// Save file to local
	fileName := fmt.Sprintf("file_%s", *b.File.Name)
	err = resp.WriteFile(fileName)
	if err != nil {
		logs.Error("failed to save file: %v", err)
		return buf.String()
	}

	// Use markdown syntax to reference local file
	buf.WriteString(fmt.Sprintf("![文件](%s)", fileName))
	buf.WriteString("\n")

	return buf.String()
}

// ParseDocxBlockGrid 解析Grid(分栏)块
func (p *LarkParser) ParseDocxBlockGrid(b *larkdocx.Block) string {
	buf := new(strings.Builder)

	// Format according to the specified grid structure
	buf.WriteString("```grid\ngrid_column:\n")

	// Set inside grid flag to true
	wasInsideGrid := p.insideGrid
	p.insideGrid = true

	// Process each column (child)
	for _, childID := range b.Children {
		childBlock := p.blockMap[childID]

		// Add column with width ratio
		buf.WriteString(fmt.Sprintf(" - width_ratio: %d\n", *childBlock.GridColumn.WidthRatio))
		buf.WriteString("   content:|\n")

		// Process content of this column
		content := p.ParseDocxBlock(childBlock, 0)
		// Indent each line with four spaces (two for list item, two for content indentation)
		lines := strings.Split(content, "\n")
		for _, line := range lines {
			if line != "" {
				buf.WriteString("     " + line + "\n")
			}
		}
	}

	// Restore previous state
	p.insideGrid = wasInsideGrid

	buf.WriteString("```\n")
	return buf.String()
}

func (p *LarkParser) ParseDocxBlockGridColumn(b *larkdocx.Block) string {
	buf := new(strings.Builder)

	// Keep inside grid flag
	wasInsideGrid := p.insideGrid
	p.insideGrid = true

	for _, childID := range b.Children {
		childBlock := p.blockMap[childID]
		buf.WriteString(p.ParseDocxBlock(childBlock, 0) + "\n")
	}

	// Restore previous state
	p.insideGrid = wasInsideGrid

	return buf.String()
}

func (p *LarkParser) ParseDocxBlockView(b *larkdocx.Block) string {
	buf := new(strings.Builder)
	for _, childID := range b.Children {
		childBlock := p.blockMap[childID]
		buf.WriteString(p.ParseDocxBlock(childBlock, 0) + "\n")
	}
	return buf.String()
}

/**
old doc parser
*/

// =============================================================
// Parse the old version of document (docs)
// =============================================================

// ParseDocxBlockSheet 解析电子表格为链接
func (p *LarkParser) ParseDocxBlockSheet(sheet *larkdocx.Sheet) string {
	buf := new(strings.Builder)

	if sheet == nil || sheet.Token == nil {
		logs.Error("sheet is nil or token is nil")
		return buf.String()
	}

	split := strings.Split(*sheet.Token, "_")
	if len(split) < 2 {
		p.c.GetLogger().Error("sheet token is invalid: %s", *sheet.Token)
		return buf.String()
	}
	filePath, err := download(context.Background(), p.larkCli, "xlsx", split[0], "sheet", split[1], p.larkOption.UserAccessToken)
	if err != nil {
		//下载失败，写成链接
		buf.WriteString(fmt.Sprintf("[](https://bytedance.larkoffice.com/sheets/%s?sheet=%s)", split[0], split[1]))
		return buf.String()
	}

	buf.WriteString(fmt.Sprintf("![preview](%s)", filePath))

	return buf.String()
}

func (p *LarkParser) ParseDocContent(ctx context.Context, docs *lark.DocContent) string {
	// Reset counters for each new document
	p.counters = make(map[string]int)
	p.headingLevel = make([]int, 10)
	p.currentParagraph = 0
	p.insideTable = false
	p.insideGrid = false
	p.insideCallout = false
	p.currentSection = ""
	p.sectionSentenceCount = 0

	buf := new(strings.Builder)
	buf.WriteString(p.ParseDocBody(docs.Body))
	return p.ParseUserOpenID(ctx, buf.String())
}

func (p *LarkParser) ParseDocParagraph(para *lark.DocParagraph) string {
	buf := new(strings.Builder)
	postWrite := ""
	if style := para.Style; style != nil {
		if style.HeadingLevel > 0 {
			buf.WriteString(strings.Repeat("#", int(style.HeadingLevel)))
			buf.WriteString(" ")
		} else if list := style.List; list != nil {
			indentLevel := 0
			if list.IndentLevel > 1 {
				indentLevel = list.IndentLevel - 1
			}
			buf.WriteString(strings.Repeat("  ", indentLevel))
			switch list.Type {
			case "number":
				buf.WriteString(strconv.Itoa(list.Number) + ".")
			case "bullet":
				buf.WriteString("-")
			case "checkBox":
				buf.WriteString("- [ ]")
			case "checkedBox":
				buf.WriteString("- [x]")
			}
			buf.WriteString(" ")
		} else if style.Quote {
			buf.WriteString("> ")
		} else {
			switch style.Align {
			case "right":
			case "center":
				buf.WriteString(fmt.Sprintf("<div style=\"text-align: %s\">", style.Align))
				postWrite += "</div>"
			default:
			}
		}
	}
	for _, e := range para.Elements {
		buf.WriteString(p.ParseDocParagraphElement(e))
	}
	buf.WriteString(postWrite)
	buf.WriteString("\n")
	return buf.String()
}

func (p *LarkParser) ParseDocBody(body *lark.DocBody) string {
	if body == nil {
		return ""
	}

	buf := new(strings.Builder)
	for _, b := range body.Blocks {
		buf.WriteString(p.ParseDocBlock(b))
		buf.WriteString("\n")
	}
	return buf.String()
}

func (p *LarkParser) ParseDocBlock(b *lark.DocBlock) string {
	switch b.Type {
	case lark.DocBlockTypeParagraph:
		return p.ParseDocParagraph(b.Paragraph)
	case lark.DocBlockTypeGallery:
		return p.ParseDocGallery(b.Gallery)
	case lark.DocBlockTypeCode:
		return p.ParseDocCode(b.Code)
	case lark.DocBlockTypeTable:
		return p.ParseDocTableV2(b)
	default:
		return ""
	}
}

func (p *LarkParser) ParseDocTableV2(b *lark.DocBlock) string {
	ColumnSize := b.Table.ColumnSize
	RowSize := b.Table.RowSize
	tableMerge := make(map[string][2]int)
	for _, cell := range b.Table.MergedCells {
		realCell := [2]int{cell.RowStartIndex, cell.ColumnStartIndex}
		for rowI := 0; rowI < cell.RowEndIndex; rowI++ {
			for colI := 0; colI < cell.ColumnEndIndex; colI++ {
				mergeKey := fmt.Sprintf("%d_%d", rowI, colI)
				tableMerge[mergeKey] = realCell
			}
		}
	}
	table := make([][]string, RowSize)
	for i := range table {
		table[i] = make([]string, ColumnSize)
	}
	rowIndex := 0
	for _, row := range b.Table.TableRows {
		columnIndex := 0
		for _, cell := range row.TableCells {
			tableText := ""
			bytes, err := json.Marshal(cell.Body)
			if err != nil {
				logs.Error("failed to marshal %v, err: %s\n", cell.Body, err)
				return ""
			}
			var body lark.DocBody
			err = json.Unmarshal(bytes, &body)
			if err != nil {
				logs.Error("failed to unmarshal '%s', err: %s\n", string(bytes), err)
				return ""
			}
			for _, block := range body.Blocks {
				if block.Type != "paragraph" {
					continue
				}
				r := block.Paragraph.Elements
				for _, element := range r {
					tableText += p.ParseDocParagraphElement(element)
				}
			}
			table[rowIndex][columnIndex] = tableText
			if tableText == "" {
				mergeKey := fmt.Sprintf("%d_%d", rowIndex, columnIndex)
				val, exist := tableMerge[mergeKey]
				if exist {
					table[rowIndex][columnIndex] = table[val[0]][val[1]]
				}
			}
			columnIndex += 1
		}
		rowIndex += 1
	}
	table = p.cleanBlockTable(table)
	tableString := p.splitBlockTable(table, 600)
	buf := new(strings.Builder)
	buf.WriteString(tableString)
	buf.WriteString("\n")
	return buf.String()
}

func (p *LarkParser) ParseDocParagraphElement(e *lark.DocParagraphElement) string {
	switch e.Type {
	case lark.DocParagraphElementTypeTextRun:
		return p.ParseDocTextRun(e.TextRun)
	case lark.DocParagraphElementTypeDocsLink:
		return p.ParseDocDocsLink(e.DocsLink)
	case lark.DocParagraphElementTypeEquation:
		return p.ParseDocEquation(e.Equation)
	default:
		return ""
	}
}

func (p *LarkParser) ParseDocTextRun(tr *lark.DocTextRun) string {
	buf := new(strings.Builder)
	postWrite := ""
	if style := tr.Style; style != nil {
		if style.Bold {
			buf.WriteString("<strong>")
			postWrite = "</strong>"
		} else if style.Italic {
			buf.WriteString("<em>")
			postWrite = "</em>"
		} else if style.StrikeThrough {
			buf.WriteString("<del>")
			postWrite = "</del>"
		} else if style.Underline {
			buf.WriteString("<u>")
			postWrite = "</u>"
		} else if style.CodeInline {
			buf.WriteString("`")
			postWrite = "`"
		} else if link := style.Link; link != nil {
			buf.WriteString("[")
			postWrite = fmt.Sprintf("](%s)", util.UnescapeURL(link.URL))
		}
	}
	buf.WriteString(tr.Text)
	buf.WriteString(postWrite)
	return buf.String()
}

func (p *LarkParser) ParseDocDocsLink(l *lark.DocDocsLink) string {
	buf := new(strings.Builder)
	buf.WriteString(fmt.Sprintf("[](%s)", util.UnescapeURL(l.URL)))
	return buf.String()
}

func (p *LarkParser) ParseDocEquation(eq *lark.DocEquation) string {
	buf := new(strings.Builder)
	buf.WriteString("$$" + eq.Equation + "$$")
	return buf.String()
}

func (p *LarkParser) ParseDocGallery(g *lark.DocGallery) string {
	buf := new(strings.Builder)
	for _, img := range g.ImageList {
		buf.WriteString(p.ParseDocImageItem(img))
	}
	return buf.String()
}

func (p *LarkParser) ParseDocImageItem(img *lark.DocImageItem) string {
	buf := new(strings.Builder)
	buf.WriteString(fmt.Sprintf("![](%s)", img.FileToken))
	buf.WriteString("\n")
	p.ImgTokens = append(p.ImgTokens, img.FileToken)
	return buf.String()
}

func (p *LarkParser) ParseDocCode(c *lark.DocCode) string {
	buf := new(strings.Builder)
	buf.WriteString("```")
	buf.WriteString(strings.ToLower(c.Language))
	buf.WriteString("\n")

	if c.Body != nil && c.Body.Blocks != nil {
		for _, b := range c.Body.Blocks {
			for _, elem := range b.Paragraph.Elements {
				switch elem.Type {
				case lark.DocParagraphElementTypeTextRun:
					buf.WriteString(elem.TextRun.Text)
				case lark.DocParagraphElementTypeDocsLink:
					buf.WriteString(elem.DocsLink.URL)
				}
			}
			buf.WriteString("\n")
		}
	}

	buf.WriteString("```\n")
	return buf.String()
}

func (p *LarkParser) ParseDocTableCell(cell *lark.DocTableCell) string {
	// DocTableCell {
	//     "columnIndex": int,
	//     "zoneId": string,
	//     "body": {object(Body)}
	// }
	// convert body(interface{}) to DocBody
	bytes, err := json.Marshal(cell.Body)
	if err != nil {
		logs.Error("failed to marshal %v, err: %s\n", cell.Body, err)
		return ""
	}
	var body lark.DocBody
	err = json.Unmarshal(bytes, &body)
	if err != nil {
		logs.Error("failed to unmarshal '%s', err: %s\n", string(bytes), err)
		return ""
	}

	// flatten contents to one line
	var contents string
	for _, block := range body.Blocks {
		content := p.ParseDocBlock(block)
		if content == "" {
			continue
		}
		contents += content
	}
	return contents
}

func (p *LarkParser) ParseDocTable(t *lark.DocTable) string {
	// - First row as header
	// - Ignore cell merging
	var rows [][]string

	// Set inside table flag
	wasInsideTable := p.insideTable
	p.insideTable = true

	for _, row := range t.TableRows {
		var cells []string
		for _, cell := range row.TableCells {
			cells = append(cells, p.ParseDocTableCell(cell))
		}
		rows = append(rows, cells)
	}

	// Restore previous state
	p.insideTable = wasInsideTable

	buf := new(strings.Builder)
	if p.config.enableComment && !p.insideTable && !p.insideGrid {
		p.counters["table"] = p.counters["table"] + 1
		buf.WriteString(fmt.Sprintf("<!-- 表格%d -->\n", p.counters["table"]))
	}
	buf.WriteString("\n")
	buf.WriteString(renderMarkdownTable(rows))
	buf.WriteString("\n")
	return buf.String()
}

// Add a new function for parsing heading text without sentence comments
func (p *LarkParser) ParseDocxBlockHeadingText(b *larkdocx.Text, level int) string {
	buf := new(strings.Builder)
	numElem := len(b.Elements)

	for _, e := range b.Elements {
		inline := numElem > 1
		buf.WriteString(p.ParseDocxTextElement(e, inline))
	}

	headingText := strings.TrimSpace(buf.String())

	// Check if the heading text is not empty and doesn't already have a number prefix
	// Only add number prefix when not inside table, grid, or callout
	if headingText != "" && !p.hasNumberPrefix(headingText) && !p.insideTable && !p.insideGrid && !p.insideCallout {
		// Generate heading number and prepend it
		headingNumber := p.getHeadingNumber(level)
		if headingNumber != "" {
			p.currentSection = headingNumber // Update current section
			headingText = headingNumber + " " + headingText
		}
	}

	return headingText
}

// downloadReferencedDoc 下载引用的文档并返回本地文件路径
func (p *LarkParser) downloadReferencedDoc(ctx context.Context, docURL, docTitle string) string {
	logs.CtxInfo(ctx, "downloading referenced doc: %s, of title: %s", docURL, docTitle)
	// 调用现有的下载逻辑
	result, err := downloadLarkDocument(p.c, p.larkCli, docURL, p.larkOption.UserAccessToken, false) // 不递归下载引用文档
	if err != nil || result == nil {
		return ""
	}

	// 提取文件路径
	if result.FilePath != "" {
		logs.CtxInfo(ctx, "downloaded file path: %s", result.FilePath)
		return result.FilePath
	}

	if len(result.FilePaths) > 0 {
		logs.CtxInfo(ctx, "downloaded file paths: %v", result.FilePaths)
		return result.FilePaths[0]
	}

	return ""
}

type AimeTask struct {
	Quote   string
	Content string
}

func (p *LarkParser) ParseAimeTasks(ctx context.Context, blocks []*larkdocx.Block) []AimeTask {
	if len(p.userComments) == 0 {
		return nil
	}
	res := make([]AimeTask, 0)

	currentLevel := 0
	currentTitle := ""
	currentBlockTasks := make([]string, 0)
	// 全局评论
	var globalComments []string
	for _, block := range blocks {
		blockType := *block.BlockType
		// todo 改成支持更多层级
		if blockType == lark.DocxBlockTypeHeading1 || blockType == lark.DocxBlockTypeHeading2 || blockType == lark.DocxBlockTypeHeading3 {
			if blockType <= currentLevel {
				// 说明是同级或者更大块
				if currentTitle != "" && len(currentBlockTasks) > 0 {
					res = append(res, AimeTask{
						Quote:   currentTitle,
						Content: strings.Join(currentBlockTasks, "\n"),
					})
				}
				currentTitle = ""
				currentBlockTasks = make([]string, 0)
			}
			currentLevel = blockType
			currentTitle = p.ParseDocxBlock(block, 0)
		}

		if len(block.CommentIds) > 0 {
			for _, commentID := range block.CommentIds {
				for _, comment := range p.userComments {
					if *comment.CommentId == commentID {
						userCommentText := p.getAtAimeCommentText(comment)
						if userCommentText != "" {
							if blockType == lark.DocxBlockTypeTable || blockType == lark.DocxBlockTypeSheet || blockType == lark.DocxBlockTypeTableCell {
								currentBlockTasks = append(currentBlockTasks, fmt.Sprintf("task for cell %s <-- %s", *comment.Quote, userCommentText))
							} else {
								currentBlockTasks = append(currentBlockTasks, fmt.Sprintf("%s <-- %s", *comment.Quote, userCommentText))
							}
						}
					}
				}
			}
		}

		if block.BlockType != nil && *block.BlockType == lark.DocxBlockTypePage {
			for _, comment := range p.userComments {
				if comment.IsWhole != nil && *comment.IsWhole {
					globalCommentText := p.getAtAimeCommentText(comment)
					if globalCommentText != "" {
						globalComments = append(globalComments, globalCommentText)
					}
				}
			}
		}
	}
	if currentTitle != "" && len(currentBlockTasks) > 0 {
		res = append(res, AimeTask{
			Quote:   currentTitle,
			Content: strings.Join(currentBlockTasks, "\n"),
		})
	}
	if len(globalComments) > 0 {
		buf := strings.Builder{}
		buf.WriteString("<!-- 全局评论开始 -->\n")
		buf.WriteString("<!-- 注意：以下是针对整个文档的全局评论，请在理解和处理文档内容时重点参考这些评论 -->\n")
		for i, comment := range globalComments {
			buf.WriteString(fmt.Sprintf("<!-- 全局评论%d：%s -->\n", i+1, comment))
		}
		buf.WriteString("<!-- 全局评论结束 -->\n\n")
		res = append(res, AimeTask{
			Quote:   "全局评论",
			Content: buf.String(),
		})
	}

	return res
}
