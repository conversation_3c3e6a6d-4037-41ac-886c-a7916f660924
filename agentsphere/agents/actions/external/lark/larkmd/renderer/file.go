package renderer

import (
	"encoding/json"
	"path/filepath"
	"strings"

	"github.com/samber/lo"
	"github.com/yuin/goldmark/ast"
	"github.com/yuin/goldmark/renderer"
	"github.com/yuin/goldmark/renderer/html"
	"github.com/yuin/goldmark/util"

	larkmdast "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/lark/larkmd/ast"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/lark/larkmd/parser"
	"code.byted.org/devgpt/kiwis/port/lark"
)

// FileRenderer is a renderer for file/image blocks
type FileRenderer struct{}

func NewFileRenderer(opts ...html.Option) renderer.NodeRenderer {
	r := &FileRenderer{}
	return r
}

// RegisterFuncs registers renderer functions for heading blocks
func (r *FileRenderer) RegisterFuncs(reg renderer.NodeRendererFuncRegisterer) {
	reg.Register(larkmdast.KindImage, r.renderImage)
	reg.Register(larkmdast.KindFilePreview, r.renderFilePreview)
	reg.Register(ast.KindImage, r.renderStandardImage)
}

func (r *FileRenderer) renderImage(w util.BufWriter, source []byte, node ast.Node, entering bool) (ast.WalkStatus, error) {
	if !entering {
		return ast.WalkContinue, nil
	}
	n := node.(*larkmdast.Image)
	blockID := generateBlockID()

	block := &lark.DocxBlock{
		BlockID:   blockID,
		BlockType: lark.DocxBlockTypeImage,
		Image: &lark.DocxBlockImage{
			Token: n.FilePath,
		},
	}

	// Serialize block to JSON and write to buffer
	blockBytes, err := json.Marshal(block)
	if err != nil {
		return ast.WalkStop, err
	}
	w.Write(blockBytes)
	node.SetAttributeString("block_id", blockID)
	w.WriteString(",")

	return ast.WalkSkipChildren, nil
}

func (r *FileRenderer) renderFilePreview(w util.BufWriter, source []byte, node ast.Node, entering bool) (ast.WalkStatus, error) {
	if !entering {
		return ast.WalkContinue, nil
	}
	n := node.(*larkmdast.FilePreview)

	// Check if file is xlsx and IsPreview is true
	ext := strings.ToLower(filepath.Ext(n.FilePath))
	if n.IsPreview && ext == ".xlsx" {
		// Create sheet type block
		blockID := generateBlockID()

		block := &lark.DocxBlock{
			BlockID:   blockID,
			BlockType: lark.DocxBlockTypeSheet,
			Sheet: &lark.DocxBlockSheet{
				Token: n.FilePath,
			},
		}

		// Serialize block to JSON and write to buffer
		blockBytes, err := json.Marshal(block)
		if err != nil {
			return ast.WalkStop, err
		}
		w.Write(blockBytes)
		w.WriteString(",")

		return ast.WalkSkipChildren, nil
	}

	// Original logic for non-xlsx files or non-preview mode
	var (
		viewBlockID = generateBlockID()
		fileBlockID = generateBlockID()
	)

	blocks := []*lark.DocxBlock{
		{
			BlockID:   viewBlockID,
			BlockType: lark.DocxBlockTypeView,
			View: &lark.DocxBlockView{
				ViewType: lark.DocxViewType(lo.Ternary(n.IsPreview, 2, 1)),
			},
			Children: []string{fileBlockID},
		},
		{
			BlockID:   fileBlockID,
			BlockType: lark.DocxBlockTypeFile,
			File: &lark.DocxBlockFile{
				Token: n.FilePath,
			},
		},
	}

	// Serialize blocks to JSON and write to buffer
	for _, block := range blocks {
		blockBytes, err := json.Marshal(block)
		if err != nil {
			return ast.WalkStop, err
		}
		w.Write(blockBytes)
		w.WriteString(",")
	}

	return ast.WalkSkipChildren, nil
}

func (r *FileRenderer) renderStandardImage(w util.BufWriter, source []byte, node ast.Node, entering bool) (ast.WalkStatus, error) {
	if !entering {
		return ast.WalkContinue, nil
	}
	n := node.(*ast.Image)
	blockID := generateBlockID()

	// Extract image URL from the embedded Link
	imageURL := string(n.Destination)
	// Handle URL links by downloading and processing them
	if strings.HasPrefix(imageURL, "http://") || strings.HasPrefix(imageURL, "https://") {
		localPath, isImage, err := parser.DownloadURLAndDetectType(imageURL)
		if err == nil && isImage {
			imageURL = localPath
		}
		// If download fails or it's not an image, keep the original URL
	}

	block := &lark.DocxBlock{
		BlockID:   blockID,
		BlockType: lark.DocxBlockTypeImage,
		Image: &lark.DocxBlockImage{
			Token: imageURL,
		},
	}

	// Serialize block to JSON and write to buffer
	blockBytes, err := json.Marshal(block)
	if err != nil {
		return ast.WalkStop, err
	}
	w.Write(blockBytes)
	node.SetAttributeString("block_id", blockID)
	w.WriteString(",")

	return ast.WalkSkipChildren, nil
}
