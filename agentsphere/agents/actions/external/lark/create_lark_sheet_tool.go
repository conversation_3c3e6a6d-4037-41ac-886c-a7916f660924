package lark

import (
	"context"
	_ "embed"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	larkdrive "github.com/larksuite/oapi-sdk-go/v3/service/drive/v1"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
)

// CreateLarkTableArgs defines the arguments for creating a Lark sheet or bitable
type CreateLarkTableArgs struct {
	FilePath  string `json:"file_path" mapstructure:"file_path" description:"必填，文件绝对路径，支持多种文件格式：1. xlsx、csv、xls文件转飞书表格；2. xlsx、csv文件转飞书多维表格。比如：/workspace/iris_e7c707a5-ae78-42d0-b045-1882a9f0a4d7/data.csv，注意：1. 禁止填url 2. 文件路径必须真实存在（提前使用命令行工具确认文件路径）"`
	Title     string `json:"title" mapstructure:"title" description:"必填，表格标题"`
	TableType string `json:"table_type" mapstructure:"table_type" description:"可选，指定转换的目标表格类型，可选值：sheets(电子表格)、base(多维表格)，默认根据文件类型自动选择"`
}

const (
	ToolCreateLarkTable            = "create_lark_table"
	ToolCreateLarkTableDescription = `将文件转换为飞书表格或多维表格。支持以下格式：(1. 飞书表格(sheets/table)：支持 xlsx、csv、xls 文件 (2. 飞书多维表格(Base)：支持 xlsx、csv 文件。
Convert files to Lark sheets/table or Lark Base. Supported formats: (1. Lark Sheets: supports xlsx, csv, xls files (2. Lark Base: supports xlsx, csv files`
)

// NewCreateLarkTable creates a new unified tool for generating Lark sheets and bitables
func NewCreateLarkTable() iris.Action {
	return actions.ToTool(ToolCreateLarkTable, ToolCreateLarkTableDescription, CreateLarkTable)
}

// determineTargetType determines the target document type based on file extension and user preference
func determineTargetType(fileExt, userTableType string) string {
	// If user specified a table type, use it
	if userTableType != "" {
		switch strings.ToLower(userTableType) {
		case "sheet", "sheets", "spreadsheet":
			return "sheet"
		case "bitable", "base":
			return "bitable"
		default:
			return "" // Invalid table type
		}
	}

	// Otherwise, determine based on file extension
	switch fileExt {
	case ".xlsx", ".csv", ".xls":
		// Default to sheet for these formats, but they can also be converted to bitable
		return "sheet"
	default:
		return "" // Unsupported file type
	}
}

// transferFileOwnership transfers the ownership of a file to the Lark app
func transferFileOwnership(run *iris.AgentRunContext, fileID string, fileType string) error {
	ctx := context.Background()

	// Determine the type for transfer ownership request
	var transferType string
	switch fileType {
	case "sheet":
		transferType = "sheet"
	case "bitable":
		transferType = "bitable"
	default:
		return fmt.Errorf("unsupported file type for ownership transfer: %s", fileType)
	}

	// Build the request to transfer ownership
	req := larkdrive.NewTransferOwnerPermissionMemberReqBuilder().
		Token(fileID).
		Type(transferType).
		NeedNotification(false).
		RemoveOldOwner(false).
		StayPut(true).
		Owner(larkdrive.NewOwnerBuilder().
			MemberType("email").
			MemberId(run.User.Username + "@bytedance.com").
			Build()).
		Build()

	// Call the API to transfer ownership
	_, err := larkClient(run).TransferOwnerPermissionMember(ctx, req, "")
	return err
}

// processOfficeFile handles the conversion of office files to Lark documents
func processOfficeFile(run *iris.AgentRunContext, filePath string, title string, docType string, folderToken string) (string, error) {
	ctx := context.Background()

	// Get file info for size
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		run.GetLogger().Errorf("Failed to get file info: %v", err)
		return "", err
	}

	// Open the file for reading
	file, err := os.Open(filePath)
	if err != nil {
		run.GetLogger().Errorf("Failed to open file: %v", err)
		return "", err
	}
	defer file.Close()

	// Upload the file to get a file token
	body := larkdrive.NewUploadAllMediaReqBodyBuilder().
		FileName(filepath.Base(filePath)).
		ParentType("ccm_import_open").
		Size(int(fileInfo.Size())).
		File(file).
		Extra(fmt.Sprintf(`{"obj_type": "%s","file_extension": "%s"}`, docType, strings.TrimPrefix(filepath.Ext(filePath), "."))).
		Build()

	token, err := larkClient(run).UploadLarkMedia(ctx, body, "")
	if err != nil {
		run.GetLogger().Errorf("Failed to upload file: %v", err)
		return "", err
	}
	if token == nil {
		return "", fmt.Errorf("received nil token for file upload")
	}

	// Create an import task for the file
	importTaskReq := larkdrive.NewCreateImportTaskReqBuilder().
		ImportTask(larkdrive.NewImportTaskBuilder().
			FileExtension(strings.TrimPrefix(filepath.Ext(filePath), ".")).
			FileName(title).
			FileToken(*token).
			Type(docType).
			Point(larkdrive.NewImportTaskMountPointBuilder().
				MountType(1).
				MountKey(folderToken).
				Build()).
			Build()).
		Build()

	ticket, err := larkClient(run).CreateImportTask(ctx, importTaskReq, "")
	if err != nil {
		run.GetLogger().Errorf("Failed to create import task: %v", err)
		return "", err
	}

	// Poll for task completion
	maxRetries := 10
	retryInterval := 2 * time.Second

	for i := 0; i < maxRetries; i++ {
		// Wait before checking status
		time.Sleep(retryInterval)

		// Check task status
		taskResult, err := larkClient(run).GetImportTask(ctx, *ticket, "")
		if err != nil {
			run.GetLogger().Errorf("Failed to get import task status: %v", err)
			continue
		}

		// Check if task is complete
		if *taskResult.Result.JobStatus == 0 { // 0 indicates success
			return *taskResult.Result.Token, nil
		} else if *taskResult.Result.JobStatus == 3 { // 3 indicates error
			return "", fmt.Errorf("import task failed: %s", *taskResult.Result.JobErrorMsg)
		}
	}

	return "", fmt.Errorf("timeout waiting for import task to complete")
}

// processFile processes a file to create a Lark document based on target type
func processFile(run *iris.AgentRunContext, filePath, title, targetType string) (map[string]any, error) {
	folderToken, err := getOrCreateDateFolder(run)
	if err != nil {
		run.GetLogger().Errorf("Failed to get or create date folder: %v", err)
	}
	// 使用 processOfficeFile 处理文件
	fileToken, err := processOfficeFile(run, filePath, title, targetType, folderToken)
	if err != nil {
		return nil, err
	}

	if err = addEditPermission(run, fileToken, targetType); err != nil {
		run.GetLogger().Errorf("Failed to addEditPermission to %s : %v", targetType, err)
		// Continue even if ownership transfer fails
	}

	// Generate appropriate URL and description based on target type
	var (
		url         string
		description string
	)

	switch targetType {
	case "sheet":
		url = fmt.Sprintf("https://bytedance.feishu.cn/sheets/%s", fileToken)
		description = fmt.Sprintf("飞书表格创建成功，请在%s查看", url)
	case "bitable":
		url = fmt.Sprintf("https://bytedance.feishu.cn/base/%s", fileToken)
		description = fmt.Sprintf("飞书多维表格创建成功，请在%s查看", url)
	}

	return map[string]any{
		"token":       fileToken,
		"url":         url,
		"description": description,
	}, nil
}

// CreateLarkTable creates a Lark sheet or bitable from various file formats
func CreateLarkTable(run *iris.AgentRunContext, args CreateLarkTableArgs) (map[string]any, error) {
	// Validate input parameters
	if args.FilePath == "" || args.Title == "" {
		return nil, fmt.Errorf("file_path and title are required")
	}

	// Check if file exists
	if _, err := os.Stat(args.FilePath); os.IsNotExist(err) {
		return nil, fmt.Errorf("file does not exist: %s", args.FilePath)
	}

	// Check the file extension to determine the processing method
	fileExt := strings.ToLower(filepath.Ext(args.FilePath))

	// Determine the target document type based on file extension and user preference
	targetType := determineTargetType(fileExt, args.TableType)
	if targetType == "" {
		return nil, fmt.Errorf("unsupported file type: %s or invalid target table type: %s", fileExt, args.TableType)
	}

	return processFile(run, args.FilePath, args.Title, targetType)
}
