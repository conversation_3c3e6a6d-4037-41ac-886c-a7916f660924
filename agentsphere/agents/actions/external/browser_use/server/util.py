import re
import urllib.parse

def extract_result(text: str) -> str:
    start_tag = "<result>"
    end_tag = "</result>"
    start = text.find(start_tag)
    if start == -1:
        return "None"
    start += len(start_tag)
    end = text.find(end_tag, start)
    return text[start:end].strip() if end != -1 else "None"


limited_hosts = ["byted.org", "bytedance.org", "bytedance.net", "bytedance.larkoffice.com"]

white_hosts = ["bitsai.bytedance.net",
               "libra-staging.maat.bytedance.net", "test-union.bytedance.net",
               "aime.bytedance.net", "aime-boe.bytedance.net", "rdk.bytedance.net",
               "voc.bytedance.net"]

white_urls = ["https://cloud.bytedance.net/docs"]

white_url_patterns = ["https://meego.larkoffice.com/.+?/story/detail", "https://meego.larkoffice.com/.+?/issue/detail"]

white_wildcard_hosts = ["aime-app.bytedance.net"]


def is_limited_host(current_host: str | None, current_url: str | None):
    scheme = urllib.parse.urlparse(current_url).scheme
    if scheme == "file":
        return False

    for wildcard_host in white_wildcard_hosts:

        if wildcard_host in current_host:
            return False

    if current_host in white_hosts:
        return False

    for url in white_urls:

        if current_url.startswith(url):
            return False

    for url_re in white_url_patterns:
        if len(re.compile(url_re).findall(current_url)) != 0:
            return False

    for limited_host in limited_hosts:

        if limited_host in current_host:
            return True

    return False
