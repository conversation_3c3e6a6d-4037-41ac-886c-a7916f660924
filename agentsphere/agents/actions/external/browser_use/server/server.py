from log import logger

import asyncio
import base64
import io
import json
import os
import re
import time
import traceback
import uuid
from datetime import datetime
from pathlib import Path
from urllib.parse import parse_qs
from urllib.parse import urlparse
import requests
import pandas as pd
import glob

import anyio
import click
import mcp.types as types
import wget
from PIL import Image
from browser_use import Agent, DomService
from browser_use.browser.views import BrowserStateSummary
from dotenv import load_dotenv
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.prompts import PromptTemplate
from mcp.server.lowlevel import Server
from playwright.async_api import TimeoutError, Route, FileChooser, Page

from basic.tool import browser_hover, scroll, browser_markdownify_content
from basic.specific_tool import get_libra_screenshot_and_data
from global_vars import get_browser_session, find_element_by_text_prompt, llm, find_file_name_prompt, \
    find_element_by_sample_prompt, gif_dir, doubao_cookies, get_browser_controller, network_data, reset_browser_session, \
    task_store, screenshot_dirs, reviewer_prompt, vlm_reviewer, llm_high, download_dom_dirs, aeolus_debug_dir, \
    set_browser_context_healthy, check_browser_health, get_browser_context_healthy, \
    save_cookies
from util import *
from vlm.vlm import vlm_execute_fast

print(os.environ)

load_dotenv()


async def find_element(query: str) -> (str, str, str):
    try:
        # try to find element by elements_text first
        browser_state_summary: BrowserStateSummary = await get_browser_session().get_state_summary(
            cache_clickable_elements_hashes=True)
        elements_text = browser_state_summary.element_tree.clickable_elements_to_string(
            include_attributes=["aria-label", "data-testid"])
        screenshot = browser_state_summary.screenshot

        page = await get_browser_session().get_current_page()
        await get_browser_session().remove_highlights()
        clean_screenshot_bytes = await page.screenshot(full_page=False, type="png")
        clean_screenshot_base64 = base64.b64encode(clean_screenshot_bytes).decode("utf-8")

        message_content = [
            {
                "type": "text",
                "text": f"{find_element_by_text_prompt}"
            },
            {
                "type": "text",
                "text": f"Here is the index and text of each element:\n{elements_text}",
            },
            {
                "type": "text",
                "text": f"Here is the user query: {query}"
            }
        ]
        message = HumanMessage(
            content=message_content
        )
        resp = await llm.ainvoke([message])
        logger.info(f"find by element text result: {resp.content}")
        index = extract_result(resp.content)
        if index != "None":
            return index, screenshot, clean_screenshot_base64
        # cannot find element by text, try to find element by screenshot

        url = page.url
        parsed_url = urlparse(url)
        domain = parsed_url.netloc
        dir_name = domain.replace(".", "_")
        dir_path = Path(f"/opt/browser-use/icons/{dir_name}")
        if not dir_path.exists():
            return "None", screenshot, clean_screenshot_base64
        files = [file.name for file in dir_path.iterdir() if file.is_file()]
        logger.info(files)
        message_content = [
            {
                "type": "text",
                "text": f"{find_file_name_prompt}\nFile names: {files}\nUser query: {query}"
            }
        ]
        message = HumanMessage(content=message_content)
        resp = await llm.ainvoke([message])
        logger.info(f"find by file name result: {resp.content}")
        file_name = extract_result(resp.content)
        if file_name == "None":
            return "None", screenshot, clean_screenshot_base64
        file_path = dir_path / file_name
        if not file_path.exists():
            logger.info(f"File {file_path} does not exist")
            return "None", screenshot, clean_screenshot_base64
        with open(file_path, "rb") as f:
            screenshot_sample_bytes = f.read()
        screenshot_sample_base64 = base64.b64encode(screenshot_sample_bytes).decode("utf-8")
        message_content = [
            {
                "type": "text",
                "text": find_element_by_sample_prompt,
            },
            {
                "type": "text",
                "text": "This is image 1, the image you need to locate in the screenshot\n"
            },
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/png;base64,{screenshot_sample_base64}",
                }
            },
            {
                "type": "text",
                "text": "This is image 2, a clean screenshot. You need to find the location of image 1 in it.\n"
            },
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/png;base64,{clean_screenshot_base64}",
                },
            },
            {
                "type": "text",
                "text": "This is image 3, the same screenshot as image 2 but with colored rectangles numbered by element index with the same color.\n"
            },
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/png;base64,{screenshot}",
                },
            },
        ]
        message = HumanMessage(
            content=message_content
        )
        resp = await llm.ainvoke([message])
        logger.info(f"find by screenshot result: {resp.content}")
        index = extract_result(resp.content)
        return index, screenshot, clean_screenshot_base64

    except Exception as e:
        logger.error(f"Error executing find_element: {str(e)}")
        return json.dumps(
            {"error": f"Failed to find_element: {str(e)}"}, indent=2
        ), "", ""


async def screenshot_list_to_gif(screenshot_list: list) -> str:
    img_list = []
    for ss in screenshot_list:
        img = Image.open(io.BytesIO(ss))
        img_list.append(img)
    if img_list:
        gif_id = uuid.uuid4()
        gif_path = os.path.join(f'{gif_dir}/gif-{gif_id}.gif')
        logger.info(
            f"start to save gif at {gif_path}, composed by {len(img_list)} images")
        img_list[0].save(
            gif_path,
            save_all=True,
            append_images=img_list[1:],
            duration=1500,
            loop=1,
            optimize=False,
        )
        return gif_path
    else:
        return ""


async def execute_playwright_script(arguments):
    try:
        # Execute the playwright_script action
        script = arguments["script"]
        pattern = r'https?://[^\s]+'
        links = re.findall(pattern, script)
        page = await get_browser_session().get_current_page()
        if page is None:
            page = await get_browser_session().browser_context.new_page()
        links.extend(page.url)

        white_url_list = [
            "https://data.bytedance.net/aeolus",
            "https://meego.larkoffice.com/",
        ]
        for link in links:
            whited_url = False
            for white_url in white_url_list:
                if white_url in page.url:
                    whited_url = True
                    break
            if not whited_url:
                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {
                                "error": f"Failed to execute script",
                                "message": "Contains url is not in whitelist, for security reasons will not execute",
                            },
                            indent=2
                        ),
                    )
                ]

        # 如果脚本没有明确的返回值，添加一个默认返回
        if "return" not in script:
            script += "    return None\n"

        debug_info = {"generated_code": script}
        namespace = {"page": page}
        exec(script, globals(), namespace)
        result = await namespace["run"](page)
        return [
            types.TextContent(
                type="text",
                text=json.dumps(
                    {
                        "success": True,
                        "message": "Script executed successfully",
                        "result": result,
                    },
                    indent=2
                )
            )
        ]
    except Exception as e:
        logger.error(f"Error executing browser_execute_script: {str(e)}")
        error_details = {
            "error_type": str(type(e)),
            "error_message": str(e),
            "traceback": traceback.format_exc(),
            "debug_info": debug_info if 'debug_info' in locals() else {}
        }
        return [
            types.TextContent(
                type="text",
                text=json.dumps(
                    {"error": f"Failed to execute browser_execute_script: {str(error_details)}"}, indent=2
                ),
            )
        ]


async def add_lane_headers(route: Route, headers: dict):
    headers_copy = route.request.headers.copy()
    # Add headers to the request
    for key, value in headers.items():
        headers_copy[key] = value

    # Continue with the request
    await route.continue_(headers=headers_copy)


async def handle_filechooser(file_chooser: FileChooser, page: Page, file_path_list: list[str]):
    logger.info(f"start to handle filechooser, file_path: {file_path_list}")
    await file_chooser.set_files(file_path_list)
    if "doubao.com" in page.url:
        alert_list = await page.query_selector_all('[role=alert]')
        logger.info(f"alert list: {alert_list}")
        if len(alert_list) > 0:
            alert_content = await alert_list[0].text_content()
            logger.info(f"alert content: {alert_content}")


last_file_chooser = None


async def register_filechooser(file_path_list: list[str]):
    try:
        logger.info(
            f"start to register filechooser, file_path: {file_path_list}")
        iris_workspace = os.environ.get("IRIS_WORKSPACE_PATH")
        absolute_path_list = []
        for p in file_path_list:
            file_path = os.path.join(iris_workspace, p)
            if not os.path.exists(file_path):
                logger.error(f"File path {file_path} does not exist")
                raise FileNotFoundError(
                    f"File path {file_path} does not exist")
            absolute_path_list.append(file_path)
        page = await get_browser_session().get_current_page()
        logger.info(f"page url: {page.url}")
        global last_file_chooser
        if last_file_chooser is not None:
            page.remove_listener("filechooser", last_file_chooser)
        last_file_chooser = lambda file_chooser: handle_filechooser(file_chooser, page, absolute_path_list)
        page.on("filechooser", last_file_chooser)

        return [
            types.TextContent(
                type="text",
                text=json.dumps(
                    {"success": True, "message": f"File chooser registered successfully"}, indent=2
                )
            )
        ]
    except Exception as e:
        logger.error(f"Error executing register_filechooser: {str(e)}")
        return [
            types.TextContent(
                type="text",
                text=json.dumps(
                    {"error": f"Failed to register_filechooser: {str(e)}"}, indent=2
                )
            )
        ]


async def browser_ask_doubao_and_fetch_answer(question: str, headers: dict) -> list[
    types.TextContent | types.ImageContent | types.EmbeddedResource]:
    if question == "":
        question = "你能做什么工作，详细介绍"
    try:
        screenshot_list = []
        # go to doubao.com
        page = await get_browser_session().get_current_page()
        if not "about:blank" == page.url and not page.url.startswith(
                "chrome://new-tab-page") and "doubao.com" not in page.url:
            return [
                types.TextContent(
                    type="text",
                    text=json.dumps(
                        {"error": "The use of doubao is forbidden"}, indent=2
                    )
                )
            ]
        if doubao_cookies:
            await get_browser_session().browser_context.add_cookies(doubao_cookies)
        if headers:
            await page.route("**/*", lambda route: add_lane_headers(route, headers))
        await page.goto("https://www.doubao.com")
        await page.wait_for_load_state()
        await asyncio.sleep(3)
        screenshot_list.append(await page.screenshot(full_page=False, type="png"))
        logger.info(f"Page loaded: {page.url}")
        # find input area
        input_area_index, _, _ = await find_element("find the input text area with name chat_input_input")
        if input_area_index == "None":
            return [
                types.TextContent(
                    type="text",
                    text=json.dumps(
                        {"error": "Failed to find input area"}, indent=2
                    ),
                )
            ]
        # input question into input area
        params = {"index": input_area_index, "text": question}
        result = await get_browser_controller().registry.execute_action(
            "input_text", params, browser_session=get_browser_session(), page_extraction_llm=llm
        )
        screenshot_list.append(await page.screenshot(full_page=False, type="png"))
        logger.info(f"Input text result: {result}")
        await asyncio.sleep(1)
        # send Enter key
        params = {"keys": "Enter"}
        result = await get_browser_controller().registry.execute_action(
            "send_keys", params, browser_session=get_browser_session(), page_extraction_llm=llm
        )
        logger.info(f"Send keys result: {result}")
        # wait for the answer
        logger.info("Waiting for answer...")
        await asyncio.sleep(3)
        screenshot_list.append(await page.screenshot(full_page=False, type="png"))

        selector = '[data-testid="message_text_content"]'
        js_check = """
            () => {
                const elementList = document.querySelectorAll('[data-testid="message_text_content"]');
                if (elementList.length < 2) return false;
                const element = elementList[1];
                if (!element) return false;

                // 排除中间状态关键词
                if (element.textContent.includes('正在搜索')) return false;

                // 使用 dataset 保持状态
                const now = Date.now();
                const currentText = element.textContent;

                if (!element.dataset.lastStable) {
                    // 初始化状态
                    element.dataset.lastStable = currentText;
                    element.dataset.lastChange = now;
                    return false;
                }

                if (currentText !== element.dataset.lastStable) {
                    // 检测到内容变化
                    console.log(currentText)
                    element.dataset.lastStable = currentText;
                    element.dataset.lastChange = now;
                    return false;
                }

                console.log(element.dataset.lastChange)

                // 计算稳定时长
                const stableDuration = now - parseInt(element.dataset.lastChange);
                return stableDuration > 5000; // 5秒无变化
            }
            """
        try:
            await page.wait_for_function(js_check, timeout=120000)
        except TimeoutError as e:
            logger.warning(f"Timeout waiting for answer: {str(e)}")
            pass
        logger.info("Answer is stable")
        elements = await page.query_selector_all(selector)
        if len(elements) > 1:
            second_element = elements[1]
            content = await second_element.text_content()
            logger.info(f"Answer content: {content}")
            screenshot_list.append(await page.screenshot(full_page=False, type="png"))
            gif_path = await screenshot_list_to_gif(screenshot_list)
            result = [
                types.TextContent(
                    type="text",
                    text=json.dumps(
                        {"success": True, "message": f"Answer: {content}"}, indent=2
                    ),
                )
            ]
            if gif_path:
                result.append(types.ImageContent(
                    type="image",
                    mimeType="image/gif",
                    data=gif_path,
                    alt_text=f"Gif of the answer process",
                ))
            return result
        else:
            return [
                types.TextContent(
                    type="text",
                    text=json.dumps(
                        {"error": "Failed to find answer"}, indent=2
                    ),
                )
            ]

    except Exception as e:
        logger.error(f"Error executing browser_ask_doubao_and_fetch_answer: {str(e)}")
        return [
            types.TextContent(
                type="text",
                text=json.dumps(
                    {"error": f"Failed to browser_ask_doubao_and_fetch_answer: {str(e)}"}, indent=2
                ),
            )
        ]


async def on_request(request):
    try:
        page = await get_browser_session().get_current_page()

        url = page.url

        if network_data.get(url) is None:
            network_data[url] = ""

        referer = await request.header_value('referer')
        if referer is None:
            logger.info("referer is None")
            return
        response = await request.response()
        text = await response.text()
        status = response.status
        request_host = urllib.parse.urlparse(url).hostname
        if request_host in request.url and url != request.url and is_json(text) and status >= 200 and status < 300:
            # 不要html格式的内容
            # logger.info(f"network load: {text}")
            network_data[
                url] += f"{urllib.parse.urlparse(url).hostname}/{urllib.parse.urlparse(url).path}: " + text + "\n"
    except:
        # 忽略非文本
        pass

page_lock = asyncio.Lock()
total_page = 10


suggested_login_host = ["sso.bytedance.com", "accounts.feishu.cn", "www.xiaohongshu.com"]


async def hardcode_for_sso(page: Page):
    title = await page.title()

    if title == "ByteDance SSO":

        locator_for_sso = page.locator('css=[id="sso-idp__lark"]')

        if await locator_for_sso.count() != 0:

            await locator_for_sso.click(timeout=2000)


def is_timeout(e):
    return "Timeout" in str(e)


def is_json(j):
    try:
        json.loads(j)
    except ValueError:
        return False
    return True


async def run_browser_task_async(task_id, url, action):
    """Run a browser task asynchronously and store the result."""
    try:
        # Update task status to running
        task_store[task_id]["status"] = "running"
        task_store[task_id]["start_time"] = datetime.now().isoformat()
        task_store[task_id]["progress"] = {
            "current_step": 0,
            "total_steps": 0,
            "steps": [],
        }

        # Reset browser context to ensure a clean state
        await reset_browser_session()

        # Check browser health
        if not await check_browser_health():
            task_store[task_id]["status"] = "failed"
            task_store[task_id]["end_time"] = datetime.now().isoformat()
            task_store[task_id]["error"] = (
                "Browser context is unhealthy and could not be reset"
            )
            return

        # Define step callback function with the correct signature
        async def step_callback(browser_state, agent_output, step_number):
            # Update progress in task store
            task_store[task_id]["progress"]["current_step"] = step_number
            task_store[task_id]["progress"]["total_steps"] = max(
                task_store[task_id]["progress"]["total_steps"], step_number
            )

            # Add step info with minimal details
            step_info = {"step": step_number,
                         "time": datetime.now().isoformat()}

            # Add goal if available
            if agent_output and hasattr(agent_output, "current_state"):
                if hasattr(agent_output.current_state, "next_goal"):
                    step_info["goal"] = agent_output.current_state.next_goal

            # Add to progress steps
            task_store[task_id]["progress"]["steps"].append(step_info)

            # Log progress
            logger.info(f"Task {task_id}: Step {step_number} completed")

        # Define done callback function with the correct signature
        async def done_callback(history):
            # Log completion
            logger.info(
                f"Task {task_id}: Completed with {len(history.history)} steps")

            # Add final step
            current_step = task_store[task_id]["progress"]["current_step"] + 1
            task_store[task_id]["progress"]["steps"].append(
                {
                    "step": current_step,
                    "time": datetime.now().isoformat(),
                    "status": "completed",
                }
            )

        # Use the existing browser context with callbacks
        agent = Agent(
            task=f"First, navigate to {url}. Then, {action}",
            llm=llm,
            browser_session=get_browser_session(),
            register_new_step_callback=step_callback,
            register_done_callback=done_callback,
        )

        # Run the agent
        ret = await agent.run(max_steps=10)

        # Get the final result
        final_result = ret.final_result()

        # Check if we have a valid result
        if final_result and hasattr(final_result, "raise_for_status"):
            final_result.raise_for_status()
            result_text = str(final_result.text)
        else:
            result_text = (
                str(final_result) if final_result else "No final result available"
            )

        # Gather essential information from the agent history
        is_successful = ret.is_successful()
        has_errors = ret.has_errors()
        errors = ret.errors()
        urls_visited = ret.urls()
        action_names = ret.action_names()
        extracted_content = ret.extracted_content()
        steps_taken = ret.number_of_steps()

        # Create a focused response with the most relevant information for an LLM
        response_data = {
            "final_result": result_text,
            "success": is_successful,
            "has_errors": has_errors,
            "errors": [str(err) for err in errors if err],
            "urls_visited": [str(url) for url in urls_visited if url],
            "actions_performed": action_names,
            "extracted_content": extracted_content,
            "steps_taken": steps_taken,
        }

        # Store the result
        task_store[task_id]["status"] = "completed"
        task_store[task_id]["end_time"] = datetime.now().isoformat()
        task_store[task_id]["result"] = response_data

    except Exception as e:
        logger.error(f"Error in async browser task: {str(e)}")

        tb = traceback.format_exc()

        # Mark the browser context as unhealthy
        set_browser_context_healthy(False)

        # Store the error
        task_store[task_id]["status"] = "failed"
        task_store[task_id]["end_time"] = datetime.now().isoformat()
        task_store[task_id]["error"] = str(e)
        task_store[task_id]["traceback"] = tb

    finally:
        # Always try to reset the browser context to a clean state after use
        try:
            current_page = await get_browser_session().get_current_page()
            await current_page.goto("about:blank")
        except Exception as e:
            logger.warning(f"Error resetting page state: {str(e)}")
            set_browser_context_healthy(False)


@click.command()
@click.option("--port", default=8000, help="Port to listen on for SSE")
@click.option(
    "--transport",
    type=click.Choice(["stdio", "sse", "http"]),
    default="stdio",
    help="Transport type",
)
def main(port: int, transport: str) -> int:
    app = Server("browser_use")

    @app.call_tool()
    async def call_tool(
            name: str, arguments: dict
    ) -> list[types.TextContent | types.ImageContent | types.EmbeddedResource]:
        import json  # imports at the module level are not automatically available in nested functions

        # Check browser health for direct browser operations
        if name.startswith("browser_") and name not in ["browser_use", "browser_get_result"]:
            if not get_browser_context_healthy():
                await reset_browser_session()
                if not get_browser_context_healthy():
                    return [
                        types.TextContent(
                            type="text",
                            text=json.dumps(
                                {
                                    "error": "Browser context is not healthy",
                                    "message": "Failed to reset browser context. Please try again later.",
                                },
                                indent=2,
                            ),
                        )
                    ]

        # Handle different tool types
        if name == "browser_use":
            # Check required arguments
            if "url" not in arguments:
                raise ValueError("Missing required argument 'url'")
            if "action" not in arguments:
                raise ValueError("Missing required argument 'action'")

            # Generate a task ID
            task_id = str(uuid.uuid4())

            # Initialize task in store
            task_store[task_id] = {
                "id": task_id,
                "status": "pending",
                "url": arguments["url"],
                "action": arguments["action"],
                "created_at": datetime.now().isoformat(),
            }

            # Start task in background
            asyncio.create_task(
                run_browser_task_async(
                    task_id, arguments["url"], arguments["action"])
            )

            # Estimate task duration
            estimated_seconds = 60  # Default estimate of 60 seconds

            # Return task ID immediately with explicit sleep instruction
            return [
                types.TextContent(
                    type="text",
                    text=json.dumps(
                        {
                            "task_id": task_id,
                            "status": "pending",
                            "message": f"Browser task started. Please wait for {estimated_seconds} seconds, then check the result using browser_get_result or the resource URI. Always wait exactly 5 seconds between status checks.",
                            "estimated_time": f"{estimated_seconds} seconds",
                            "resource_uri": f"resource://browser_task/{task_id}",
                            "sleep_command": "sleep 5",
                            "instruction": "Use the terminal command 'sleep 5' to wait 5 seconds between status checks. IMPORTANT: Always use exactly 5 seconds, no more and no less.",
                        },
                        indent=2,
                    ),
                )
            ]

        elif name == "browser_get_result":
            # Get result of async task
            if "task_id" not in arguments:
                raise ValueError("Missing required argument 'task_id'")

            task_id = arguments["task_id"]

            if task_id not in task_store:
                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {"error": "Task not found", "task_id": task_id}, indent=2
                        ),
                    )
                ]

            # Get the current task data
            task_data = task_store[task_id].copy()

            # If task is still running, add simple guidance
            if task_data["status"] == "running":
                # Add a simple next check suggestion
                progress = task_data.get("progress", {})
                current_step = progress.get("current_step", 0)

                if current_step > 0:
                    # Simple message based on current step
                    task_data["message"] = (
                        f"Task is running (step {current_step}). Wait 5 seconds before checking again."
                    )
                    task_data["sleep_command"] = "sleep 5"
                    task_data["instruction"] = (
                        "Use the terminal command 'sleep 5' to wait 5 seconds before checking again. IMPORTANT: Always use exactly 5 seconds, no more and no less."
                    )
                else:
                    task_data["message"] = (
                        "Task is starting. Wait 5 seconds before checking again."
                    )
                    task_data["sleep_command"] = "sleep 5"
                    task_data["instruction"] = (
                        "Use the terminal command 'sleep 5' to wait 5 seconds before checking again. IMPORTANT: Always use exactly 5 seconds, no more and no less."
                    )

            # Return current task status and result if available
            return [
                types.TextContent(
                    type="text", text=json.dumps(task_data, indent=2))
            ]

        if name == "browser_get_state":

            logger.info(f"getting browser state {get_browser_context_healthy()}")
            # 检查浏览器健康状态
            if not get_browser_context_healthy():
                await reset_browser_session()
                if not get_browser_context_healthy():
                    return [
                        types.TextContent(
                            type="text",
                            text=json.dumps(
                                {
                                    "error": "Browser context is not healthy",
                                    "message": "Failed to reset browser context. Please try again later.",
                                },
                                indent=2,
                            ),
                        )
                    ]

            try:
                # 获取当前浏览器状态
                page = await get_browser_session().get_current_page()

                logger.info("test console healthy")

                await page.evaluate('1')

                logger.info("console is healthy")
                # 获取当前浏览器状态

                await get_browser_session().remove_highlights()
                dom_service = DomService(page)
                pixels_above, pixels_below = await get_browser_session().get_scroll_info(page)
                for i in range(3):
                    try:
                        content = await dom_service.get_clickable_elements(
                            focus_element=-1,
                            viewport_expansion=get_browser_session().browser_profile.viewport_expansion,
                            highlight_elements=get_browser_session().browser_profile.highlight_elements,
                        )
                        get_browser_session().browser_state_summary = BrowserStateSummary(
                            element_tree=content.element_tree,
                            selector_map=content.selector_map,
                            url=page.url,
                            title=await page.title(),
                            tabs=await get_browser_session().get_tabs_info(),
                            pixels_above=pixels_above,
                            pixels_below=pixels_below,
                        )
                        break
                    except Exception as state_e:
                        tb = traceback.format_exc()
                        logger.info(
                            f"failied to contruct dom but ingore {state_e}. \n{tb}")
                        logger.info(f"refresh page... ")
                        try:
                            await page.reload(timeout=10000)
                        except Exception as reload_e:
                            if i == 2:
                                raise reload_e
                            else:
                                continue
                        continue

                if get_browser_session().browser_profile.cookies_file:
                    asyncio.create_task(get_browser_session().save_cookies())

                get_browser_session()._cached_browser_state_summary = get_browser_session().browser_state_summary

                # 保存cookies到本地文件
                try:
                    sso_dir = "/tmp/sso"
                    if not os.path.exists(sso_dir):
                        os.makedirs(sso_dir)

                    # 获取当前页面的cookies
                    cookies = await page.context.cookies()

                    # 生成文件名（基于当前URL的域名）
                    from urllib.parse import urlparse
                    parsed_url = urlparse(page.url)
                    domain = parsed_url.netloc.replace(".", "_")
                    cookie_file = os.path.join(sso_dir, f"cookies_{domain}.json")

                    # 保存cookies到文件
                    import json
                    with open(cookie_file, 'w', encoding='utf-8') as f:
                        json.dump(cookies, f, indent=2, ensure_ascii=False)

                    logger.info(f"Cookies saved to: {cookie_file}")
                except Exception as e:
                    logger.warning(f"Failed to save cookies to /tmp/sso: {str(e)}")

                has_content_above = (
                    get_browser_session().browser_state_summary.pixels_above or 0) > 0
                has_content_below = (
                    get_browser_session().browser_state_summary.pixels_below or 0) > 0

                elements_text = get_browser_session().browser_state_summary.element_tree.clickable_elements_to_string(
                    include_attributes=["aria-label", "data-testid"])

                if elements_text != '':
                    if has_content_above:
                        elements_text = (
                            f'... {get_browser_session().browser_state_summary.pixels_above} pixels above - scroll or extract content to see more ...\n{elements_text}'
                        )
                    else:
                        elements_text = f'[Start of page]\n{elements_text}'
                    if has_content_below:
                        elements_text = (
                            f'{elements_text}\n... {get_browser_session().browser_state_summary.pixels_below} pixels below - scroll or extract content to see more ...'
                        )
                    else:
                        elements_text = f'{elements_text}\n[End of page]'

                state_data = {
                    "url": get_browser_session().browser_state_summary.url,
                    "title": get_browser_session().browser_state_summary.title,
                    "tabs": [tab.model_dump() for tab in get_browser_session().browser_state_summary.tabs],
                    "pixels_above": get_browser_session().browser_state_summary.pixels_above,
                    "pixels_below": get_browser_session().browser_state_summary.pixels_below,
                    "elements_text": elements_text,
                }
                logger.info("finish get browser state")
                # state_data["has_screenshot"] = False
                # 返回不包含截图的响应
                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(state_data, indent=2)
                    )
                ]
            except Exception as e:

                tb = traceback.format_exc()

                logger.error(
                    f"Error getting browser state: {str(e)}. stack: {tb}")
                browser_context_healthy = False
                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {
                                "error": "Failed to get browser state",
                                "message": str(e)
                            },
                            indent=2
                        )
                    )
                ]
        # New direct browser action implementations
        elif name == "browser_search_google":
            if "query" not in arguments:
                raise ValueError("Missing required argument 'query'")

            try:
                # Execute the search_google action
                params = {"query": arguments["query"]}
                result = await get_browser_controller().registry.execute_action(
                    "search_google", params, browser_session=get_browser_session(), page_extraction_llm=llm
                )

                await asyncio.sleep(3)

                page = await get_browser_session().get_current_page()

                logger.info(f"[google] page url {page.url}")

                if page.url.startswith("https://www.google.com/sorry/index"):
                    return [
                        types.TextContent(
                            type="text",
                            text=json.dumps(
                                {
                                    "success": False,
                                    "message": f"Failed to Searched Google for: {arguments['query']} due to abnormal traffic or recaptcha",
                                    "result": result.extracted_content if hasattr(result, "extracted_content") else str(result),
                                },
                                indent=2
                            )
                        )
                    ]

                # Return the result
                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {
                                "success": True,
                                "message": f"Searched Google for: {arguments['query']}",
                                "result": result.extracted_content if hasattr(result, "extracted_content") else str(result),
                            },
                            indent=2
                        )
                    )
                ]
            except Exception as e:
                logger.error(f"Error executing search_google: {str(e)}")
                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {"error": f"Failed to search Google: {str(e)}"}, indent=2
                        ),
                    )
                ]
        elif name == "browser_goto_pdf":
            if "url" not in arguments:
                raise ValueError("Missing required argument 'url'")

            try:
                # Execute the go_to_url action
                params = {"url": arguments["url"]}
                result = await get_browser_controller().registry.execute_action(
                    "go_to_url", params, browser_session=get_browser_session(), page_extraction_llm=llm
                )
                params = {"seconds": 3}
                await get_browser_controller().registry.execute_action(
                    "wait", params, browser_session=get_browser_session(), page_extraction_llm=llm
                )

                # Return the result
                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {
                                "success": True,
                                "message": f"Navigated to the URL of PDF: {arguments['url']}",
                                "result": result.extracted_content if hasattr(result, "extracted_content") else str(
                                    result),
                            },
                            indent=2
                        )
                    )
                ]
            except Exception as e:
                logger.error(f"Error executing go_to_url: {str(e)}")
                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {"error": f"Failed to navigate the URL of PDF: {str(e)}"}, indent=2
                        ),
                    )
                ]

        elif name == "browser_goto":
            if "url" not in arguments:
                raise ValueError("Missing required argument 'url'")

            try:
                url = arguments["url"]
                headers = arguments.get("headers", {})

                page = await get_browser_session().get_current_page()
                if "doubao.com" in url and doubao_cookies:
                    await get_browser_session().browser_context.add_cookies(doubao_cookies)
                if headers:
                    await page.route("**/*", lambda route: add_lane_headers(route, headers))

                page.on("request", on_request)

                page = await get_browser_session().get_current_page()
                if page:
                    await page.goto(url)
                else:
                    page = await get_browser_session().create_new_tab(url)

                try:
                    await page.wait_for_load_state("networkidle", timeout=30000)
                except:
                    pass

                await hardcode_for_sso(page)

                if urllib.parse.urlparse(page.url).hostname in suggested_login_host:
                    return [
                        types.TextContent(
                            type="text",
                            text=json.dumps(
                                {
                                    "success": True,
                                    "message": f"Navigated to: {page.url}. Use browser_takeover to ask human login",
                                    "result": f"Navigated to: {page.url}. Use browser_takeover to ask human login",
                                },
                                indent=2
                            )
                        )
                    ]

                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {
                                "success": True,
                                "message": f"Navigated to: {arguments['url']}.",
                                "result": f"Navigated to: {arguments['url']}.",
                            },
                            indent=2
                        )
                    )
                ]

            except Exception as e:
                if is_timeout(e):
                    try:
                        return [
                            types.TextContent(
                                type="text",
                                text=json.dumps(
                                    {
                                        "success": True,
                                        "message": f"Navigated to: {arguments['url']}",
                                        "result": f"Navigated to: {arguments['url']}",
                                    },
                                    indent=2
                                )
                            )
                        ]
                    except:
                        pass

                logger.error(f"Error executing go_to_url: {str(e)}")
                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {"error": f"Failed to navigate to URL: {str(e)}"}, indent=2
                        ),
                    )
                ]

        elif name == "browser_click":
            if "index" not in arguments:
                raise ValueError("Missing required argument 'index'")
            if "goal" not in arguments:
                raise ValueError("Missing required argument 'goal'")
            if "expectation" not in arguments:
                raise ValueError("Missing required argument 'expectation'")
            goal = arguments["goal"]
            expectation = arguments["expectation"]

            try:
                page = await get_browser_session().get_current_page()

                current_host = urllib.parse.urlparse(page.url).hostname

                if is_limited_host(current_host, page.url):
                    return [
                        types.TextContent(
                            type="text",
                            text=json.dumps(
                                {
                                    "success": False,
                                    "message": f"You are Forbidden to click element with index: {arguments['index']} in {current_host} as an agent. Use browser_markdownify_content to extract this page",
                                    "result": f"You are Forbidden to click element with index: {arguments['index']} in {current_host} as an agent. Use browser_markdownify_content to extract this page",
                                },
                                indent=2
                            )
                        )
                    ]

                page = await get_browser_session().get_current_page()
                page.on("request", on_request)

                image_id = uuid.uuid4()
                image_path = os.path.join(f'{screenshot_dirs}/screenshot-before-{image_id}.png')
                await get_browser_session().remove_highlights()
                screenshot_before_action_bytes = await page.screenshot(
                    full_page=False, type="png", path=image_path
                )
                screenshot_before_action_base64 = base64.b64encode(
                    screenshot_before_action_bytes
                ).decode("utf-8")

                # Execute the click_element_by_index(name updated in v2) action
                params = {"index": arguments["index"]}

                initial_pages = len(get_browser_session().tabs)

                result = await get_browser_controller().registry.execute_action(
                    "click_element_by_index", params, browser_session=get_browser_session(), page_extraction_llm=llm
                )

                await asyncio.sleep(3)

                if len(get_browser_session().tabs) > initial_pages:
                    new_tab_msg = 'New tab opened - switching to it'
                    logger.info(new_tab_msg)
                    await get_browser_session().switch_to_tab(-1)

                page = await get_browser_session().get_current_page()

                await page.wait_for_load_state()

                image_path = os.path.join(f'{screenshot_dirs}/screenshot-after-{image_id}.png')
                await get_browser_session().remove_highlights()
                screenshot_after_action_bytes = await page.screenshot(
                    full_page=False, type="png", path=image_path
                )
                screenshot_after_action_base64 = base64.b64encode(
                    screenshot_after_action_bytes
                ).decode("utf-8")

                # review this operation
                review_message_content = [
                    {
                        "type": "text",
                        "text": "This is screenshot 1, the screenshot before the click operation"
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{screenshot_before_action_base64}"
                        }
                    },
                    {
                        "type": "text",
                        "text": "This is screenshot 2, the screenshot after the click operation"
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{screenshot_after_action_base64}"
                        }
                    },
                    {
                        "type": "text",
                        "text": f"The task is:\n {goal}\n\n The expectation is:\n{expectation}"
                    }
                ]

                system_message = SystemMessage(
                    content=reviewer_prompt
                )

                message = HumanMessage(
                    content=review_message_content
                )
                start_review_time = datetime.now()
                resp = await vlm_reviewer.ainvoke([system_message, message])
                end_review_time = datetime.now()
                review_duration = (end_review_time - start_review_time).total_seconds()
                logger.info(f"review duration: {review_duration} seconds")
                logger.info(f"review result full: {resp}")
                review_result = extract_result(resp.content)
                logger.info(f"review result: {review_result}")

                if review_result != "success":
                    # await click_by_uitars(arguments["goal"], arguments["expectation"], image_path)
                    return [
                        types.TextContent(
                            type="text",
                            text=json.dumps(
                                {
                                    "success": True,
                                    "message": f"Clicked element with index: {arguments['index']}.",
                                    "result": f"The click operation fails. review result: {resp.content}.",
                                },
                                indent=2
                            )
                        )
                    ]
                    # Return the result
                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {
                                "success": True,
                                "message": f"Clicked element with index: {arguments['index']}.",
                                "result": result.extracted_content if hasattr(result, "extracted_content") else str(result),
                            },
                            indent=2
                        )
                    )
                ]
            except Exception as e:
                if is_timeout(e):
                    try:

                        return [
                            types.TextContent(
                                type="text",
                                text=json.dumps(
                                    {
                                        "success": True,
                                        "message": f"Navigated to: {arguments['url']}",
                                        "result": f"Navigated to: {arguments['url']}",
                                    },
                                    indent=2
                                )
                            )
                        ]
                    except Exception as e2:
                        logger.error(f"failed to handle click timeout {e2}")

                logger.error(f"Error executing click_element: {str(e)}")
                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {"error": f"Failed to click element: {str(e)}"}, indent=2
                        ),
                    )
                ]

        elif name == "browser_input":
            if "index" not in arguments:
                raise ValueError("Missing required argument 'index'")
            if "text" not in arguments:
                raise ValueError("Missing required argument 'text'")
            if "goal" not in arguments:
                raise ValueError("Missing required argument 'goal'")
            if "expectation" not in arguments:
                raise ValueError("Missing required argument 'expectation'")
            goal = arguments["goal"]
            expectation = arguments["expectation"]

            try:
                page = await get_browser_session().get_current_page()

                current_host = urllib.parse.urlparse(page.url).hostname

                if is_limited_host(current_host, page.url):
                    return [
                        types.TextContent(
                            type="text",
                            text=json.dumps(
                                {
                                    "success": False,
                                    "message": f"You are Forbidden to input in {current_host} as an agent. Use browser_markdownify_content to extract this page",
                                    "result": f"You are Forbidden to input in {current_host} as an agent. Use browser_markdownify_content to extract this page",
                                },
                                indent=2
                            )
                        )
                    ]

                image_id = uuid.uuid4()
                image_path = os.path.join(f'{screenshot_dirs}/screenshot-before-{image_id}.png')
                await get_browser_session().remove_highlights()
                screenshot_before_action_bytes = await page.screenshot(
                    full_page=False, type="png", path=image_path
                )
                screenshot_before_action_base64 = base64.b64encode(
                    screenshot_before_action_bytes
                ).decode("utf-8")

                # Execute the input_text action
                params = {"index": arguments["index"],
                          "text": arguments["text"]}
                result = await get_browser_controller().registry.execute_action(
                    "input_text", params, browser_session=get_browser_session(), page_extraction_llm=llm
                )

                image_path = os.path.join(f'{screenshot_dirs}/screenshot-after-{image_id}.png')
                await get_browser_session().remove_highlights()
                screenshot_after_action_bytes = await page.screenshot(
                    full_page=False, type="png", path=image_path
                )
                screenshot_after_action_base64 = base64.b64encode(
                    screenshot_after_action_bytes
                ).decode("utf-8")

                # review this operation
                review_message_content = [
                    {
                        "type": "text",
                        "text": "This is screenshot 1, the screenshot before the input operation"
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{screenshot_before_action_base64}"
                        }
                    },
                    {
                        "type": "text",
                        "text": "This is screenshot 2, the screenshot after the input operation"
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{screenshot_after_action_base64}"
                        }
                    },
                    {
                        "type": "text",
                        "text": f"The task is:\n {goal}\n\n The expectation is:\n{expectation}"
                    }
                ]

                system_message = SystemMessage(
                    content=reviewer_prompt
                )

                message = HumanMessage(
                    content=review_message_content
                )
                start_review_time = datetime.now()
                resp = await vlm_reviewer.ainvoke([system_message, message])
                end_review_time = datetime.now()
                review_duration = (end_review_time - start_review_time).total_seconds()
                logger.info(f"review duration: {review_duration} seconds")
                logger.info(f"review result full: {resp}")
                review_result = extract_result(resp.content)
                logger.info(f"review result: {review_result}")

                if review_result != "success":
                    return [
                        types.TextContent(
                            type="text",
                            text=json.dumps(
                                {
                                    "success": True,
                                    "message": f"Input text into element with index: {arguments['index']}.",
                                    "result": f"The input operation fails. review result: {resp.content}.",
                                },
                                indent=2
                            )
                        )
                    ]

                # Return the result
                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {
                                "success": True,
                                "message": f"Input text into element with index: {arguments['index']}",
                                "result": result.extracted_content if hasattr(result, "extracted_content") else str(
                                    result),
                            },
                            indent=2
                        )
                    )
                ]
            except Exception as e:
                logger.error(f"Error executing input_text: {str(e)}")
                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {"error": f"Failed to input text: {str(e)}"}, indent=2
                        ),
                    )
                ]

        elif name == "browser_scroll_down":
            if "amount" not in arguments:
                raise ValueError("Missing required argument 'amount'")
            if "goal" not in arguments:
                raise ValueError("Missing required argument 'goal'")
            if "expectation" not in arguments:
                raise ValueError("Missing required argument 'expectation'")

            return await scroll("down", arguments.get("amount"), arguments.get("goal"), arguments.get("expectation"))

        elif name == "browser_scroll_up":
            if "amount" not in arguments:
                raise ValueError("Missing required argument 'amount'")
            if "goal" not in arguments:
                raise ValueError("Missing required argument 'goal'")
            if "expectation" not in arguments:
                raise ValueError("Missing required argument 'expectation'")

            return await scroll("up", arguments.get("amount"), arguments.get("goal"), arguments.get("expectation"))


        elif name == "browser_markdownify_content":
            if "goal" not in arguments:
                arguments["goal"] = "extract the content of the page"
            if "language" not in arguments:
                arguments["language"] = "English"

            try:
                goal = arguments["goal"]
                language = arguments["language"]
                return await browser_markdownify_content(goal, language)

            except Exception as e:
                logger.error(
                    f"Error executing extract_content: {str(e)}. trace: {traceback.format_exc()}")
                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {"error": f"Failed to extract content: {str(e)}"}, indent=2
                        ),
                    )
                ]

        elif name == "browser_download":
            urls = {}
            try:
                if "url" in arguments:
                    if "file_name" not in arguments:
                        raise ValueError("Missing required argument 'file_name' when URL is provided")

                    urls[arguments["url"]] = arguments["file_name"]

                # find urls to downloads based on user goal
                elif "goal" in arguments:
                    goal = f"Find file download links to meet user goal: {arguments['goal']}. Place the link in the <reference></reference> part. DO NOT add links not relevant to the goal anywhere in the markdown. Please make your link description within [] brief but not repetitive with other links, and the link itself within () is the actual link to download the file. If you cannot find any file download links, return an empty markdown."
                    markdownify_result = await browser_markdownify_content(goal, language="English")
                    result = markdownify_result[0].text

                    # read (url, description) pairs from markdownify result
                    for match in re.findall(r'- \[(.*?)\]\((.*?)\)', result):
                        description, url = match
                        # some urls are not file urls, like https://www.example.com/
                        if url.split("/")[-1]=='':
                            continue
                        urls[url] = description.replace(" ", "_").encode(
                            # Ensure the filename supports Chinese characters by decoding unicode escapes if present
                            'utf-8').decode('unicode_escape')  # if url repeats, later description overwrites
                else:
                    raise ValueError("Missing required argument 'url' or 'goal'")

                def get_filetype(filename):
                    import filetype
                    kind = filetype.guess(filename)
                    if kind is None:
                        return None
                    return kind.extension

                success_urls, failed_urls = [], []

                for url, file_name in urls.items():
                    workspace = os.getenv("IRIS_WORKSPACE_PATH")

                    # Truncate path to avoid path too long error
                    path = os.path.join(workspace, file_name)[0:256]

                    logger.info(f"start to download {file_name} from {url} to {path}")

                    os.environ['http_proxy'] = 'http://sys-proxy-rd-relay.byted.org:3128'
                    os.environ['https_proxy'] = 'https://sys-proxy-rd-relay.byted.org:3128'

                    try:
                        wget.download(url, out=path)
                    except Exception as e:
                        failed_urls.append(url)

                    os.unsetenv('http_proxy')
                    os.unsetenv('https_proxy')
                    success_urls.append({url: file_name})

                    ext = get_filetype(path)
                    logger.info(f"file type of {path} is {ext}")
                    if ext is not None and ext != os.path.splitext(path)[-1]:
                        import shutil
                        shutil.move(path, path + f".{ext}")
                        path = path + f".{ext}"

                if "url" in arguments:
                    return [
                        types.TextContent(
                            type="text",
                            text=json.dumps(
                                {
                                    "success": True,
                                    "message": f"Successfully download file from: {arguments['url']}",
                                    "result": f"Successfully download file from {arguments['url']} at {path} and do not repeat to download",
                                },
                                indent=2
                            )
                        )
                    ]
                else:
                    return [
                        types.TextContent(
                            type="text",
                            text=json.dumps(
                                {
                                    "success": True,
                                    "message": f"Successfully download files according to user goal: {arguments['goal']}. Downloaded files are {urls}" + (f", failed to download files are {failed_urls}" if failed_urls else ""),
                                    "result": f"Successfully download files according to user goal: {arguments['goal']}. Downloaded files are {urls}" + (f", failed to download files are {failed_urls}" if failed_urls else "") + ". Do not repeat to download the successfully downloaded files.",
                                },
                                indent=2
                            )
                        )
                    ]

            except Exception as e:
                logger.error(f"Error executing download: {str(e)}")
                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {"error": f"Failed to download: {str(e)}"}, indent=2
                        ),
                    )
                ]

        elif name == "browser_send_keys":
            if "keys" not in arguments:
                raise ValueError("Missing required argument 'keys'")

            try:
                page = await get_browser_session().get_current_page()

                current_host = urllib.parse.urlparse(page.url).hostname

                if is_limited_host(current_host, page.url):
                    return [
                        types.TextContent(
                            type="text",
                            text=json.dumps(
                                {
                                    "success": False,
                                    "message": f"You are Forbidden to send keys in {current_host} as an agent. Use browser_markdownify_content to extract this page",
                                    "result": f"You are Forbidden to send keys in {current_host} as an agent. Use browser_markdownify_content to extract this page",
                                },
                                indent=2
                            )
                        )
                    ]

                # Execute the send_keys action
                params = {"keys": arguments["keys"]}
                initial_pages = len(get_browser_session().tabs)

                result = await get_browser_controller().registry.execute_action(
                    "send_keys", params, browser_session=get_browser_session(), page_extraction_llm=llm
                )

                await asyncio.sleep(3)

                if len(get_browser_session().tabs) > initial_pages:
                    new_tab_msg = 'New tab opened - switching to it'
                    logger.info(new_tab_msg)
                    await get_browser_session().switch_to_tab(-1)

                page = await get_browser_session().get_current_page()

                await page.wait_for_load_state()

                # Return the result
                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {
                                "success": True,
                                "message": f"Sent keys: {arguments['keys']}",
                                "result": f"Sent keys: {arguments['keys']}",
                            },
                            indent=2
                        )
                    )
                ]
            except Exception as e:
                logger.error(f"Error executing send_keys: {str(e)}")
                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {"error": f"Failed to send keys: {str(e)}"}, indent=2
                        ),
                    )
                ]

        elif name == "browser_wait":
            try:
                # Execute the wait action
                seconds = arguments.get("seconds", 3)
                params = {"seconds": seconds}
                result = await get_browser_controller().registry.execute_action(
                    "wait", params, browser_session=get_browser_session(), page_extraction_llm=llm
                )

                # Return the result
                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {
                                "success": True,
                                "message": f"Waited for {seconds} seconds",
                                "result": result.extracted_content if hasattr(result, "extracted_content") else str(
                                    result),
                            },
                            indent=2
                        )
                    )
                ]
            except Exception as e:
                logger.error(f"Error executing wait: {str(e)}")
                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {"error": f"Failed to wait: {str(e)}"}, indent=2
                        ),
                    )
                ]
        elif name == "browser_clear_screenshot":
            try:
                full_page = arguments.get("full_page", True)

                page = await get_browser_session().get_current_page()

                total_height = await page.evaluate("document.body.scrollHeight")
                total_width = await page.evaluate("document.body.scrollWidth")

                # 移除高亮框
                await get_browser_session().remove_highlights()

                image_id = uuid.uuid4()

                image_path = os.path.join(
                    f'{screenshot_dirs}/screenshot-clear-{image_id}.jpeg')

                logger.info(f"start to save image data in {image_path}")

                await page.screenshot(full_page=full_page, type="jpeg", quality=95, path=image_path, clip={"x": 0, "y": 0, "width": total_width, "height": min(total_height, 16384)})

                logger.info(f"saved image data in {image_path}")

                # Get page title and URL for context
                page_title = await page.title()

                page_url = page.url

                return [
                    types.ImageContent(
                        type="image",
                        mimeType="image/jpeg",
                        data=image_path,
                        alt_text=f"Screenshot of {page_title} at {page_url}"
                    ),
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {
                                "success": True,
                                "message": f"Screenshot taken of {'full page' if full_page else 'viewport'}",
                                "url": page_url,
                                "title": page_title,
                            },
                            indent=2
                        )
                    )
                ]
            except Exception as e:
                logger.error(f"Error taking screenshot and retry: {str(e)}")
                try:

                    page = await get_browser_session().get_current_page()

                    await get_browser_session().remove_highlights()

                    image_id = uuid.uuid4()

                    image_path = os.path.join(
                        f'{screenshot_dirs}/screenshot-clear-{image_id}.jpeg')

                    logger.info(f"start to save image data in {image_path}")

                    await page.screenshot(full_page=False, type="jpeg", quality=95, path=image_path)

                    logger.info(f"saved image data in {image_path}")

                    # Get page title and URL for context
                    page_title = await page.title()
                    page_url = page.url
                    return [
                        types.ImageContent(
                            type="image",
                            mimeType="image/jpeg",
                            data=image_path,
                            alt_text=f"Screenshot of {page_title} at {page_url}"
                        ),
                        types.TextContent(
                            type="text",
                            text=json.dumps(
                                {
                                    "success": True,
                                    "message": f"Screenshot taken of {'full page' if full_page else 'viewport'}",
                                    "url": page_url,
                                    "title": page_title,
                                },
                                indent=2
                            )
                        )
                    ]
                except Exception as e2:
                    logger.error(f"Error taking screenshot: {str(e2)}")
                    return [
                        types.TextContent(
                            type="text",
                            text=json.dumps(
                                {"error": f"Failed to take screenshot: {str(e2)}"}, indent=2
                            ),
                        )
                    ]

        elif name == "browser_get_dom":
            if "url" not in arguments:
                raise ValueError("Missing required argument 'url'")
            try:
                url = arguments["url"]

                new_page = await get_browser_session().browser_context.new_page()

                await new_page.goto(url)

                content = await new_page.content()

                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {
                                "success": True,
                                "message": f"Success to get dom: {arguments['url']}",
                                "result": content,
                            },
                            indent=2
                        )
                    )
                ]
            except Exception as e:
                logger.error(f"Error executing get_dom: {str(e)}")
                try:
                    url = arguments["url"]
                    file_name = str(uuid.uuid4())
                    path = os.path.join(download_dom_dirs, file_name)
                    logger.info(f"start to download {url} to {path}")
                    os.environ['http_proxy'] = 'http://sys-proxy-rd-relay.byted.org:3128'
                    os.environ['https_proxy'] = 'https://sys-proxy-rd-relay.byted.org:3128'
                    wget.download(url, out=path)
                    os.unsetenv('http_proxy')
                    os.unsetenv('https_proxy')
                    with open(path, 'r') as f:
                        content = f.read()

                    return [
                        types.TextContent(
                            type="text",
                            text=json.dumps(
                                {
                                    "success": True,
                                    "message": f"Success to get dom: {arguments['url']}",
                                    "result": content,
                                },
                                indent=2
                            )
                        )
                    ]
                except Exception as e:
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {"error": f"Failed to get dom: {str(e)}"}, indent=2
                        ),
                    )

                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {"error": f"Failed to get dom: {str(e)}"}, indent=2
                        ),
                    )
                ]
        elif name == "browser_screenshot":
            try:
                # Get parameter
                full_page = arguments.get("full_page", False)

                # Get current page
                page = await get_browser_session().get_current_page()

                await get_browser_session().remove_highlights()

                dom = DomService(page)

                await dom._build_dom_tree(True, -1, 0)

                image_id = uuid.uuid4()

                image_path = os.path.join(
                    f'{screenshot_dirs}/screenshot-{image_id}.jpeg')

                await page.screenshot(full_page=full_page, type="jpeg", quality=95, path=image_path)

                page_title = await page.title()

                page_url = page.url

                return [
                    types.ImageContent(
                        type="image",
                        mimeType="jpeg",
                        data=image_path,
                        alt_text=f"Screenshot of {page_title} at {page_url}"
                    ),
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {
                                "success": True,
                                "message": f"Screenshot taken of {'full page' if full_page else 'viewport'}",
                                "url": page_url,
                                "title": page_title,
                            },
                            indent=2
                        )
                    )
                ]
            except Exception as e:
                logger.error(f"Error taking screenshot: {str(e)}")
                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {"error": f"Failed to take screenshot: {str(e)}"}, indent=2
                        ),
                    )
                ]
        elif name == "browser_get_dropdown_options":
            if "index" not in arguments:
                raise ValueError("Missing required argument 'index'")

            try:
                # Execute the get_dropdown_options action
                params = {"index": arguments["index"]}
                result = await get_browser_controller().registry.execute_action(
                    "get_dropdown_options", params, browser_session=get_browser_session(), page_extraction_llm=llm
                )

                # Parse the result
                if hasattr(result, "extracted_content") and result.extracted_content:
                    try:
                        # Try to parse as JSON if it's already JSON formatted
                        options_data = json.loads(result.extracted_content)
                    except json.JSONDecodeError:
                        # If not JSON, use the text as is
                        options_data = result.extracted_content
                else:
                    options_data = "No options found or empty dropdown"

                # Return the result
                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {
                                "success": True,
                                "message": f"Retrieved options for dropdown element with index {arguments['index']}",
                                "options": options_data,
                            },
                            indent=2
                        )
                    )
                ]
            except Exception as e:
                logger.error(f"Error getting dropdown options: {str(e)}")
                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {"error": f"Failed to get dropdown options: {str(e)}"}, indent=2
                        ),
                    )
                ]

        elif name == "browser_remove_highlights":
            try:
                await get_browser_session().remove_highlights()

                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {
                                "success": True,
                                "message": "success to remove highlights",
                            },
                            indent=2
                        )
                    )
                ]
            except Exception as e:
                logger.error(
                    f"Error execute browser_remove_highlights: {str(e)}")
                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {"error": f"Failed to execute browser_remove_highlights: {str(e)}"}, indent=2
                        ),
                    )
                ]
        elif name == "browser_takeover":
            if "reason" not in arguments:
                raise ValueError("Missing required argument 'reason'")

            reason = arguments.get("reason")

            if not reason.endswith("。"):
                reason = f"{reason}。"

            page = await get_browser_session().get_current_page()

            host = urllib.parse.urlparse(page.url).hostname

            ask_save_cookies = (host == "sso.bytedance.com" or host == "accounts.feishu.cn")

            return [
                types.TextContent(
                    type="text",
                    text=json.dumps(
                        {
                            "success": True,
                            "message": f"{reason}",
                            "result": f"Take control of browser by human due to {reason}",
                            "ask_save_cookies": ask_save_cookies,
                        },
                        indent=2
                    )
                )
            ]

        elif name == "browser_goback":
            try:
                old_page = await get_browser_session().get_current_page()

                resp = await old_page.go_back(timeout=5000)

                if resp is None:
                    return [
                        types.TextContent(
                            type="text",
                            text=json.dumps(
                                {
                                    "success": False,
                                    "message": f"{old_page.url} cannot go back",
                                    "result": f"{old_page.url} cannot go back",
                                },
                                indent=2
                            )
                        )
                    ]

                # Return the result
                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {
                                "success": True,
                                "message": f"Website goback from {old_page.url}.",
                                "result": f"Website goback from {old_page.url}.",
                            },
                            indent=2
                        )
                    )
                ]
            except Exception as e:
                if is_timeout(e):
                    logger.warning("timeout to go back")
                    try:

                        return [
                            types.TextContent(
                                type="text",
                                text=json.dumps(
                                    {
                                        "success": True,
                                        "message": f"Website goback.",
                                        "result": f"Website goback.",
                                    },
                                    indent=2
                                )
                            )
                        ]
                    except:
                        pass

                logger.error(f"Error executing goback: {str(e)}")
                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {"error": f"Failed to goback: {str(e)}"}, indent=2
                        ),
                    )
                ]

        elif name == "browser_select_dropdown_option":
            if "index" not in arguments:
                raise ValueError("Missing required argument 'index'")
            if "option_text" not in arguments:
                raise ValueError("Missing required argument 'option_text'")

            try:
                # Execute the select_dropdown_option action
                params = {
                    "index": arguments["index"],
                    "text": arguments["option_text"]
                }
                result = await get_browser_controller().registry.execute_action(
                    "select_dropdown_option", params, browser_session=get_browser_session(), page_extraction_llm=llm
                )

                # Return the result
                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {
                                "success": True,
                                "message": f"Selected option '{arguments['option_text']}' from dropdown with index {arguments['index']}",
                                "result": result.extracted_content if hasattr(result, "extracted_content") else str(
                                    result),
                            },
                            indent=2
                        )
                    )
                ]
            except Exception as e:
                logger.error(f"Error selecting dropdown option: {str(e)}")
                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {"error": f"Failed to select dropdown option: {str(e)}"}, indent=2
                        ),
                    )
                ]
        elif name == "browser_ask_doubao_and_fetch_answer":
            if "question" not in arguments:
                raise ValueError("Missing required argument 'question'")
            headers = arguments.get("headers", {})
            return await browser_ask_doubao_and_fetch_answer(arguments["question"], headers)
        elif name == "browser_execute_script":
            return await execute_playwright_script(arguments)
        elif name == "browser_register_filechooser":
            if "file_path_list" not in arguments:
                raise ValueError("Missing required argument 'file_path_list'")
            return await register_filechooser(arguments["file_path_list"])
        elif name == "browser_handover_to_vlm":
            if "goal" not in arguments:
                raise ValueError("Missing required argument 'goal'")
            if "expectation" not in arguments:
                raise ValueError("Missing required argument 'expectation'")

            try:
                success, result, gif_path = await vlm_execute_fast(arguments["goal"], arguments["expectation"])
                result_list = [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {
                                "success": success,
                                "message": result,
                                "result": result,
                            },
                            indent=2
                        )
                    )
                ]
                if gif_path:
                    result_list.append(
                        types.ImageContent(
                            type="image",
                            mimeType="image/gif",
                            data=gif_path,
                            alt_text="Gif of VLM operations"
                        )
                    )
                return result_list
            except Exception as e:
                logger.error(f"Error executing browser_handover_to_vlm: {str(e)}")
                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {"error": f"Failed to handover to VLM: {str(e)}"}, indent=2
                        ),
                    )
                ]
        elif name == "browser_hover":
            if "index" not in arguments:
                raise ValueError("Missing required argument 'index'")
            if "goal" not in arguments:
                raise ValueError("Missing required argument 'goal'")
            if "expectation" not in arguments:
                raise ValueError("Missing required argument 'expectation'")
            return await browser_hover(arguments["index"], arguments["goal"], arguments["expectation"])

        elif name == "browser_drag_drop":
            if "goal" not in arguments:
                raise ValueError("Missing required argument 'goal'")
            if "expectation" not in arguments:
                raise ValueError("Missing required argument 'expectation'")

            try:
                success, result, gif_path = await vlm_execute_fast(arguments["goal"], arguments["expectation"])
                result_list = [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {
                                "success": success,
                                "message": result,
                                "result": result,
                            },
                            indent=2
                        )
                    )
                ]
                if gif_path:
                    result_list.append(
                        types.ImageContent(
                            type="image",
                            mimeType="image/gif",
                            data=gif_path,
                            alt_text="Gif of VLM operations"
                        )
                    )
                return result_list
            except Exception as e:
                logger.error(f"Error executing browser_drag_drop: {str(e)}")
                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {"error": f"Failed to handover to VLM for drag_drop: {str(e)}"}, indent=2
                        ),
                    )
                ]

        elif name == "browser_get_libra_screenshot_and_data":
            template_path = arguments.get("template_path", "")
            return await get_libra_screenshot_and_data(template_path)
        
        elif name == "browser_get_aeolus_screenshot_and_data":
            page = await get_browser_session().get_current_page()
            language = arguments.get("language", None)
            try:
                result = await monitor_aeolus_dashboard_and_download_reports(page, language)
                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {
                                "success": True,
                                "message": "Success to get aeolus screenshot and data",
                                "result": result,
                            },
                            indent=2
                        )
                    )
                ]
            except Exception as e:
                logger.error(f"Error executing browser_get_aeolus_screenshot_and_data: {str(e)}")
                error_details = {
                    "error_type": str(type(e)),
                    "error_message": str(e),
                    "traceback": traceback.format_exc(),
                    "debug_info": debug_info if 'debug_info' in locals() else {}
                }
                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {"error": f"Failed to get aeolus screenshot and data: {str(error_details)}"}, indent=2
                        ),
                    )
                ] 
        
        elif name == "browser_get_console":
            try:
                page = await get_browser_session().get_current_page()

                console_logs = []

                page.on("console", lambda msg: console_logs.append({
                    "type": msg.type,
                    "text": msg.text,
                }))

                page.on("pageerror", lambda msg: console_logs.append({
                    "type": "pageerror",
                    "text": msg.message,
                    "stack": msg.stack
                }))

                page.on("requestfailed", lambda msg: console_logs.append({
                    "type": "requestfailed",
                    "text": msg.url,
                    "failure": msg.failure if msg.failure else "No failure info available"
                }))

                # 刷新页面（会丢失当前历史，但后续日志可捕获）
                await page.reload()
                await page.wait_for_load_state("networkidle")

                for log in console_logs:
                    logger.debug(f"Get console - {log['type']}: {log['text']}")

                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {"console": console_logs}, indent=2
                        ),
                    )
                ]
            except Exception as e:
                logger.error(f"Error getting console logs: {str(e)}")
                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {"error": f"Failed to get console logs: {str(e)}"}, indent=2
                        ),
                    )
                ]
        elif name == "browser_save_cookies":
            try:
                await save_cookies()
                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {
                                "success": True,
                                "message": "success to save cookies",
                            },
                            indent=2
                        )
                    )
                ]
            except Exception as e:
                logger.error(f"Error in save cookies: {str(e)}")
                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {
                                "success": False,
                                "message": f"Failed to save cookies: {str(e)}",
                            },
                            indent=2
                        )
                    )
                ]
        else:
            raise ValueError(f"Unknown tool: {name}")

    @app.list_tools()
    async def list_tools() -> list[types.Tool]:

        # Basic tools
        tools = [
            # types.Tool(
            #     name="browser_use",
            #     description="Performs a browser action and returns a task ID for async execution",
            #     inputSchema={
            #         "type": "object",
            #         "required": ["url", "action"],
            #         "properties": {
            #             "url": {
            #                 "type": "string",
            #                 "description": "URL to navigate to",
            #             },
            #             "action": {
            #                 "type": "string",
            #                 "description": "Action to perform in the browser",
            #             },
            #         },
            #     },
            # ),
            # types.Tool(
            #     name="browser_get_result",
            #     description="Gets the result of an asynchronous browser task",
            #     inputSchema={
            #         "type": "object",
            #         "required": ["task_id"],
            #         "properties": {
            #             "task_id": {
            #                 "type": "string",
            #                 "description": "ID of the task to get results for",
            #             }
            #         },
            #     },
            # ),
            types.Tool(
                name="browser_screenshot",
                description="Take a screenshot of the current browser page",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "full_page": {
                            "type": "boolean",
                            "description": "Whether to capture the full page or just the viewport (default: false)",
                        }
                    },
                },
            ),
            types.Tool(
                name="browser_clear_screenshot",
                description="Take a clear screenshot of the current browser page without highlights",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "full_page": {
                            "type": "boolean",
                            "description": "Whether to capture the full page or just the viewport (default: false)",
                        }
                    },

                }
            ),
        ]

        # Add direct browser action tools if browser is healthy
        if get_browser_context_healthy():
            # Add search_google tool
            tools.append(
                types.Tool(
                    name="browser_search_google",
                    description="Search Google in the current browser tab",
                    inputSchema={
                        "type": "object",
                        "required": ["query"],
                        "properties": {
                            "query": {
                                "type": "string",
                                "description": "Search query for Google",
                            }
                        },
                    },
                )
            )

            # Add go_to_url tool
            tools.append(
                types.Tool(
                    name="browser_goto_pdf",
                    description="Navigate to a URL of pdf in the current browser tab",
                    inputSchema={
                        "type": "object",
                        "required": ["url"],
                        "properties": {
                            "url": {
                                "type": "string",
                                "description": "PDF URL to navigate to",
                            }
                        },
                    },
                )
            )

            # Add download tool
            tools.append(
                types.Tool(
                    name="browser_download",
                    description="Download resource by URL or user instruction. When URL is not specified, the agent will try to find relevant link on the page to download the file based on the goal. You MUST use this tool if user asks you to DOWNLOAD something from the current webpage",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "url": {
                                "type": "string",
                                "description": "URL to download the file from.",
                            },
                            "file_name":{
                                "type": "string",
                                "description": "The file name to save the downloaded file as. You must specify the file name if downloading the file from a URL.",
                            },
                            "goal":{
                                "type": "string",
                                "description": "The goal of this download operation. Describe what you want to download. Example: I want to download all PDF in this page.",
                            },
                            "max_download_files": {
                                "type": "integer",
                                "description": "The maximum number of files to download",
                            }
                        },
                    },
                )
            )

            # Add go_to_url tool
            tools.append(
                types.Tool(
                    name="browser_goto",
                    description="Navigate to a URL in the current browser tab",
                    inputSchema={
                        "type": "object",
                        "required": ["url"],
                        "properties": {
                            "url": {
                                "type": "string",
                                "description": "URL to navigate to",
                            },
                            "headers": {
                                "type": "dict",
                                "description": "Headers to set for the request (optional)",
                            },
                        },
                    },
                )
            )

            # Add click_element tool
            tools.append(
                types.Tool(
                    name="browser_click",
                    description="Click an element on the page by index. DO NOT use browser_click if you want to DOWNLOAD files from current webpage.",
                    inputSchema={
                        "type": "object",
                        "required": ["index", "goal", "expectation"],
                        "properties": {
                            "index": {
                                "type": "integer",
                                "description": "Index of the element to click",
                            },
                            "goal": {
                                "type": "string",
                                "description": "The goal of this click operation. DO NOT use any element index here. Describe what you want to click. Example: I want to click the element which shows time range. ",
                            },
                            "expectation": {
                                "type": "string",
                                "description": "What to expect after the click operation. Example: The click operation will open a dropdown menu which contains the time range options to select.",
                            }
                        },
                    },
                )
            )

            # Add input_text tool
            tools.append(
                types.Tool(
                    name="browser_input",
                    description="Input text into an element on the page",
                    inputSchema={
                        "type": "object",
                        "required": ["index", "text", "goal", "expectation"],
                        "properties": {
                            "index": {
                                "type": "integer",
                                "description": "Index of the element to input text into",
                            },
                            "text": {
                                "type": "string",
                                "description": "Text to input into the element",
                            },
                            "goal": {
                                "type": "string",
                                "description": "The goal of this input operation. DO NOT use any element index here. Describe where you want to input. Example: I want to input the text into the search box.",
                            },
                            "expectation": {
                                "type": "string",
                                "description": "What to expect after the input operation. Example: The text should be inputted into the search box and the search results should be updated.",
                            }
                        },
                    },
                )
            )

            # Add scroll_down tool
            tools.append(
                types.Tool(
                    name="browser_scroll_down",
                    description="Scroll down the page",
                    inputSchema={
                        "type": "object",
                        "required": ["amount","goal","expectation"],
                        "properties": {
                            "amount": {
                                "type": "integer",
                                "description": "Amount of pixels to scroll down",
                            },
                            "goal": {
                                "type": "string",
                                "description": "The goal of this scroll down operation. Describe where you want to scroll dowon and how many pixels. Example: I want to scroll down in xxx region.",
                            },
                            "expectation": {
                                "type": "string",
                                "description": "What to expect after the scroll down operation. Example: The viewpoint should scroll down by specified pixels",
                            }
                        },
                    },
                )
            )

            # Add goback tool
            tools.append(
                types.Tool(
                    name="browser_get_state",
                    description="get browser state",
                    inputSchema={
                        "type": "object",
                    },
                )
            )

            # Add scroll_up tool
            tools.append(
                types.Tool(
                    name="browser_scroll_up",
                    description="Scroll up the page",
                    inputSchema={
                        "type": "object",
                        "required": ["amount", "goal", "expectation"],
                        "properties": {
                            "amount": {
                                "type": "integer",
                                "description": "Amount of pixels to scroll down",
                            },
                            "goal": {
                                "type": "string",
                                "description": "The goal of this scroll up operation. Describe where you want to scroll up and how many pixels. Example: I want to scroll up in xxx region.",
                            },
                            "expectation": {
                                "type": "string",
                                "description": "What to expect after the scroll up operation. Example: The viewpoint should scroll up by specified pixels",
                            }
                        },
                    }
                )
            )

            tools.append(
                types.Tool(
                    name="browser_goback",
                    description="Goback from current page",
                    inputSchema={
                        "type": "object",
                    },
                )
            )

            # Add extract_content tool
            tools.append(
                types.Tool(
                    name="browser_markdownify_content",
                    description="Convert HTML on the current page into an markdown using a JavaScript DOM parser to finish extraction and data annotation. Scroll is not required. You MUST use this tool if user expects you to extract information from a page with lots of words.",
                    inputSchema={
                        "type": "object",
                        "required": [],
                        "properties": {
                            "goal": {
                                "type": "string",
                                "description": "Description of the content to extract",
                            },
                            "language": {
                                "type": "string",
                                "description": "Extraction should use this language",
                            }
                        },
                    },
                )
            )

            tools.append(
                types.Tool(
                    name="browser_remove_highlights",
                    description="Remove highlights",
                    inputSchema={
                        "type": "object",
                        "required": [],
                        "properties": {},
                    },
                )
            )

            # Add send_keys tool
            tools.append(
                types.Tool(
                    name="browser_send_keys",
                    description="Send keyboard keys to the browser",
                    inputSchema={
                        "type": "object",
                        "required": ["keys"],
                        "properties": {
                            "keys": {
                                "type": "string",
                                "description": "Keys to send, e.g. 'Enter', 'Escape', 'Control+v'",
                            }
                        },
                    },
                )
            )

            # Add get_dom tool
            tools.append(
                types.Tool(
                    name="browser_get_dom",
                    description="Get a dom info by url",
                    inputSchema={
                        "type": "object",
                        "required": ["url"],
                        "properties": {
                            "url": {
                                "type": "string",
                                "description": "URL should be extracted dom",
                            }
                        },
                    },
                )
            )

            # Add get_dom tool
            tools.append(
                types.Tool(
                    name="browser_takeover",
                    description="Ask human to take control browser to help you finish task (e.g. login and reCAPTCHA)",
                    inputSchema={
                        "type": "object",
                        "required": ["reason"],
                        "properties": {
                            "reason": {
                                "type": "string",
                                "description": "The brief reason (no more than 10 characters) to take control browser by human in Chinese",
                            }
                        },
                    },
                )
            )

            # Add wait tool
            tools.append(
                types.Tool(
                    name="browser_wait",
                    description="If page presents loading... or page load partially, wait for a specified number of seconds",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "seconds": {
                                "type": "integer",
                                "description": "Number of seconds to wait (default: 3)",
                            }
                        },
                    },
                )
            )

            # Add get_dropdown_options tool
            tools.append(
                types.Tool(
                    name="browser_get_dropdown_options",
                    description="Get all available options in a dropdown menu identified with a <select > tag",
                    inputSchema={
                        "type": "object",
                        "required": ["index"],
                        "properties": {
                            "index": {
                                "type": "integer",
                                "description": "Index of the dropdown element",
                            }
                        },
                    },
                )
            )

            # Add select_dropdown_option tool
            tools.append(
                types.Tool(
                    name="browser_select_dropdown_option",
                    description="Select an option from a dropdown menu identified with a <select > tag",
                    inputSchema={
                        "type": "object",
                        "required": ["index", "option_text"],
                        "properties": {
                            "index": {
                                "type": "integer",
                                "description": "Index of the dropdown element",
                            },
                            "option_text": {
                                "type": "string",
                                "description": "Text of the option to select",
                            }
                        },
                    },
                )
            )

            tools.append(
                types.Tool(
                    name="browser_get_libra_screenshot_and_data",
                    description="Get libra data and screenshots from libra websites (https://data.bytedance.net/libra/). If a template markdown file is provided, also generate a report for later reference based on the user's template, with prefix 'generated_report_'. You MUST refer to the generated report in your later analysis.",
                    inputSchema={
                        "type": "object",
                        "required": [],
                        "properties": {
                            "template_path": {
                                "type": "string",
                                "description": "The file path of the markdown template to use for the report. You MUST provide a template if you have one.",
                            }
                        },
                    },

                )
            )

            tools.append(
                types.Tool(
                    name="browser_get_aeolus_screenshot_and_data",
                    description="Get aeolus(风神) data and screenshots from aeolus platform (https://data.bytedance.net/aeolus). This tool is specifically designed for extracting dashboard, visualized query data from aeolus platform. Supports both Chinese (default) and English interfaces.",
                    inputSchema={
                        "type": "object",
                        "required": [],
                        "properties": {
                            "language": {
                                "type": "string",
                                "description": "Optional language parameter. Use 'en' for English interface, leave empty for default Chinese interface.",
                            }
                        },
                    },

                )
            )

            tools.append(
                types.Tool(
                    name="browser_ask_doubao_and_fetch_answer",
                    description="Ask Doubao and fetch the answer",
                    inputSchema={
                        "type": "object",
                        "required": ["question"],
                        "properties": {
                            "question": {
                                "type": "string",
                                "description": "Question to ask Doubao",
                            },
                            "headers": {
                                "type": "dict",
                                "description": "Headers to set for the request (optional)",
                            }
                        },
                    },

                )
            )

            tools.append(
                types.Tool(
                    name="browser_execute_script",
                    description="Execute a playwright emulate script in the browser, only support async function and python version that in knowledge, `page` variable is already available in context, WARNING: can only execute python scripts provided by knowledge",
                    inputSchema={
                        "type": "object",
                        "required": ["script"],
                        "properties": {
                            "script": {
                                "type": "string",
                                "description": "The script to execute",
                            }
                        },
                    },
                )
            )

            tools.append(
                types.Tool(
                    name="browser_register_filechooser",
                    description="Before clicking the file input to upload one or more files, register the filechooser event so that the files can be automatically selected",
                    inputSchema={
                        "type": "object",
                        "required": ["file_path_list"],
                        "properties": {
                            "file_path_list": {
                                "type": "array",
                                "description": "The path list of the files to upload",
                            }
                        },
                    }
                )
            )

            tools.append(
                types.Tool(
                    name="browser_handover_to_vlm",
                    description="When some click or input action is not working, you can use this tool to handover the control to VLM. The VLM will take over the browser and do whatever it wants.",
                    inputSchema={
                        "type": "object",
                        "required": ["goal", "expectation"],
                        "properties": {
                            "goal": {
                                "type": "string",
                                "description": "The goal of this action. Describe what you want to do.",
                            },
                            "expectation": {
                                "type": "string",
                                "description": "What to expect after this action.",
                            }
                        },
                    }
                )
            )

            tools.append(
                types.Tool(
                    name="browser_hover",
                    description="Hover over an element on the page by index",
                    inputSchema={
                        "type": "object",
                        "required": ["index", "goal", "expectation"],
                        "properties": {
                            "index": {
                                "type": "integer",
                                "description": "Index of the element to hover over",
                            },
                            "goal": {
                                "type": "string",
                                "description": "The goal of this hover operation. DO NOT use any element index here. Describe what you want to hover over. Example: I want to hover over the element which shows the menu.",
                            },
                            "expectation": {
                                "type": "string",
                                "description": "What to expect after the hover operation. Example: The hover operation will show a tooltip or a dropdown menu.",
                            }
                        },
                    },
                )
            )

            tools.append(
                types.Tool(
                    name="browser_drag_drop",
                    description="Invoke VLM helper to drag and drop, useful for slidebar, price range selection, etc.",
                    inputSchema={
                        "type": "object",
                        "required": ["goal", "expectation"],
                        "properties": {
                            "goal": {
                                "type": "string",
                                "description": "The goal of this drag_drop operation. DO NOT use any element index here. Describe what you want to drag and drop. Example: I want to set filter price range to $100-$200.",
                            },
                            "expectation": {
                                "type": "string",
                                "description": "What to expect after the drag_drop operation. Example: The drag_drop operation will drag the left end of price slidebar to $100 and the right end to $200.",
                            }
                        },
                    },
                )
            )

            tools.append(
                types.Tool(
                    name="browser_get_console",
                    description="Get the console/pageerror/requestfailed output from the browser. You MUST use this tool when validating deployed links or debugging browser issues.",
                    inputSchema={
                        "type": "object",
                    },
                )
            )

            tools.append(
                types.Tool(
                    name="browser_save_cookies",
                    description="Save sso and feishu cookies",
                    inputSchema={
                        "type": "object",
                        "required": [],
                        "properties": {},
                    },
                )
            )

        return tools

    @app.list_resources()
    async def list_resources() -> list[types.Resource]:
        # List all completed tasks as resources
        resources = []
        for task_id, task_data in task_store.items():
            if task_data["status"] in ["completed", "failed"]:
                resources.append(
                    types.Resource(
                        uri=f"resource://browser_task/{task_id}",
                        title=f"Browser Task Result: {task_id[:8]}",
                        description=f"Result of browser task for URL: {task_data.get('url', 'unknown')}",
                    )
                )
        return resources

    @app.read_resource()
    async def read_resource(uri: str) -> list[types.ResourceContents]:
        # Extract task ID from URI
        if not uri.startswith("resource://browser_task/"):
            return [
                types.ResourceContents(
                    type="text",
                    text=json.dumps(
                        {"error": f"Invalid resource URI: {uri}"}, indent=2
                    ),
                )
            ]

        task_id = uri.replace("resource://browser_task/", "")
        if task_id not in task_store:
            return [
                types.ResourceContents(
                    type="text",
                    text=json.dumps(
                        {"error": f"Task not found: {task_id}"}, indent=2),
                )
            ]

        # Return task data
        return [
            types.ResourceContents(
                type="text", text=json.dumps(task_store[task_id], indent=2)
            )
        ]

    if transport == "sse":
        from mcp.server.sse import SseServerTransport
        from starlette.applications import Starlette
        from starlette.routing import Mount, Route

        sse = SseServerTransport("/messages/")

        async def handle_sse(request):
            try:
                async with sse.connect_sse(
                    request.scope, request.receive, request._send
                ) as streams:
                    await app.run(
                        streams[0], streams[1], app.create_initialization_options()
                    )
            except Exception as e:
                logger.error(f"Error in handle_sse: {str(e)}")
                # Ensure browser context is reset if there's an error
                asyncio.create_task(reset_browser_session())
                raise

        starlette_app = Starlette(
            debug=True,
            routes=[
                Route("/sse", endpoint=handle_sse),
                Mount("/messages/", app=sse.handle_post_message),
            ],
        )

        import uvicorn

        # Add a startup event to initialize the browser
        @starlette_app.on_event("startup")
        async def startup_event():
            logger.info("Starting browser context...")
            await reset_browser_session()
            logger.info("Browser context started")

            # Start background task cleanup
            asyncio.create_task(cleanup_old_tasks())

        @starlette_app.on_event("shutdown")
        async def shutdown_event():
            logger.info("Shutting down browser context...")
            await get_browser_session().close()
            logger.info("Browser context closed")

        async def cleanup_old_tasks():
            """Periodically clean up old completed tasks to prevent memory leaks."""
            while True:
                try:
                    # Sleep first to avoid cleaning up tasks too early
                    await asyncio.sleep(3600)  # Run cleanup every hour

                    current_time = datetime.now()
                    tasks_to_remove = []

                    # Find completed tasks older than 1 hour
                    for task_id, task_data in task_store.items():
                        if (
                                task_data["status"] in ["completed", "failed"]
                                and "end_time" in task_data
                        ):
                            end_time = datetime.fromisoformat(task_data["end_time"])
                            hours_elapsed = (current_time - end_time).total_seconds() / 3600

                            if hours_elapsed > 1:  # Remove tasks older than 1 hour
                                tasks_to_remove.append(task_id)

                    # Remove old tasks
                    for task_id in tasks_to_remove:
                        del task_store[task_id]

                    if tasks_to_remove:
                        logger.info(
                            f"Cleaned up {len(tasks_to_remove)} old tasks")

                except Exception as e:
                    logger.error(f"Error in task cleanup: {str(e)}")

        uvicorn.run(starlette_app, host="0.0.0.0", port=port)
    elif transport == "stdio":
        from mcp.server.stdio import stdio_server

        async def arun():
            try:
                # Ensure browser context is healthy before starting
                await check_browser_health()

                async with stdio_server() as streams:
                    await app.run(
                        streams[0], streams[1], app.create_initialization_options()
                    )
            except Exception as e:
                logger.error(f"Error in arun: {str(e)}")
                # Ensure browser context is reset if there's an error
                await reset_browser_session()
            finally:
                # Clean up resources
                try:
                    await get_browser_session().close()
                except Exception as e:
                    logger.error(f"Error cleaning up resources: {str(e)}")

        anyio.run(arun)

    else:
        from aiohttp import web

        async def handle(request):
            global total_page
            try:
                while True:
                    await page_lock.acquire()
                    logger.info(f"has open {10 - total_page} tabs")

                    if total_page > 0:
                        total_page -= 1
                        page_lock.release()
                        logger.info(
                            f"has open {10 - total_page} tabs. continuing...")
                        break
                    logger.info(f"has open {10 - total_page} tabs. waiting...")
                    page_lock.release()
                    await asyncio.sleep(0.5)

                params = request.rel_url.query
                url = params.get("url")
                if url is None:
                    return web.Response(body=json.dumps({
                        "error": "no valid url"
                    }).encode())

                new_page = await get_browser_session().get_current_page()
                if new_page:
                    await new_page.goto(url)
                else:
                    new_page = await get_browser_session().create_new_tab(url)

                await new_page.goto(url, wait_until="commit")

                content = await new_page.content()

                logger.info(f"close {url} === {await new_page.title()} ...")

                await new_page.close()

                return web.Response(body=json.dumps({
                    "content": str(content)
                }).encode())
            except Exception as e:
                tb = traceback.format_exc()
                logger.error(f"failed to execute get dom: {e}: {tb}")
                if "Unable to retrieve content because the page is navigating and changing the content" in str(e):
                    while True:
                        try:
                            page = await get_browser_session().get_current_page()

                            content = await page.content()

                            logger.info(f"close {page.url} === {await page.title()} ...")

                            await page.close()

                            return web.Response(body=json.dumps({
                                "content": str(content)
                            }).encode())

                        except Exception as e:
                            if "Unable to retrieve content because the page is navigating and changing the content" in str(e):
                                await asyncio.sleep(1)
                                continue
                            else:
                                return web.Response(body=json.dumps({
                                    "error": e.__str__()
                                }).encode())
                else:
                    return web.Response(body=json.dumps({
                        "error": e.__str__()
                    }).encode())
            finally:

                await page_lock.acquire()

                total_page += 1

                page_lock.release()

        async def run_server():
            app = web.Application()

            asyncio.create_task(reset_browser_session())

            app.add_routes([web.get('/', handle)])
            runner = web.AppRunner(app)
            await runner.setup()
            site = web.TCPSite(runner, '0.0.0.0', port)
            await site.start()

        async def run_other():
            while True:
                await asyncio.sleep(1)

        loop = asyncio.get_event_loop()
        loop.create_task(run_server())
        loop.run_until_complete(run_other())
        loop.run_forever()
    return 0

# Default configuration constants for report downloading
DEFAULT_BASE_URL = 'https://data.bytedance.net'


class ReportDownloader:
    def __init__(self, titan_token=None, url=None, language=None):
        """
        初始化下载器

        Args:
            titan_token (str): x-titan-token认证令牌
            url (str, optional): 数据平台URL，用于自动解析类型和参数
            language (str, optional): 界面语言，"en"为英文，默认为中文
        """
        self.titan_token = titan_token

        # 从URL中提取base_url，如果没有URL或提取失败则使用默认值
        self.base_url = DEFAULT_BASE_URL
        if url:
            try:
                parsed_url = urlparse(url)
                if parsed_url.scheme and parsed_url.netloc:
                    self.base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
                    logger.info(f"从URL提取base_url: {self.base_url}")
                else:
                    logger.warning(f"无法从URL提取有效的host，使用默认值: {self.base_url}")
            except Exception as e:
                logger.warning(f"解析URL失败，使用默认base_url: {e}")

        self.base_headers = {
            'x-titan-token': self.titan_token,
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,zh-TW;q=0.8,en;q=0.7',
            'content-type': 'application/json'
        }

        if language == "en":
            self.base_headers['content-language'] = 'en-US'
            self.base_headers['project-language'] = 'en_US'
            self.language = 'en'
       

        # 内部状态存储
        self.query_type = None  # 'dashboard' 或 'query'
        self.dashboard_id = None
        self.sheet_id = None
        self.report_id = None  # 用于query类型
        self.history_id = None  # 用于query类型的历史查询

        # 报表信息缓存
        self.sheet_data = None
        self.sheet_filters = {}
        self.report_names = {}  # report_id -> name 映射
        self.report_details = {}  # report_id -> details 映射

        # 文件映射存储 - 用于生成汇总CSV
        self.report_csv_mapping = {}  # report_id -> csv_filename 映射
        self.report_screenshot_mapping = {}  # report_id -> screenshot_filename 映射

        # 如果提供了URL，自动解析
        if url:
            self.parse_and_set_url(url)

    def parse_and_set_url(self, url):
        """
        解析URL并设置内部状态

        Args:
            url (str): 数据平台URL
        """
        logger.info(f"解析并设置URL: {url}")

        if '/dashboard/' in url:
            self.query_type = 'dashboard'
            self.dashboard_id, self.sheet_id = self._parse_dashboard_url(url)
            logger.info(
                f"设置为dashboard类型 - dashboardID: {self.dashboard_id}, sheetID: {self.sheet_id}")
        elif '/dataQuery' in url:
            self.query_type = 'query'
            self._parse_query_url(url)
            logger.info(
                f"设置为query类型 - reportID: {self.report_id}, historyID: {self.history_id}")
        else:
            raise ValueError(f"不支持的URL类型: {url}")

    def _parse_dashboard_url(self, url):
        """
        解析dashboard URL获取dashboardID和sheetID

        Args:
            url (str): dashboard URL

        Returns:
            tuple: (dashboard_id, sheet_id)
        """
        # 从URL路径中提取dashboardID
        path_match = re.search(r'/dashboard/(\d+)', url)
        if not path_match:
            raise ValueError("无法从URL中提取dashboardID")

        dashboard_id = path_match.group(1)

        # 从URL查询参数中提取sheetID
        parsed_url = urlparse(url)
        query_params = parse_qs(parsed_url.query)

        if 'sheetId' not in query_params:
            raise ValueError("无法从URL中提取sheetID")

        sheet_id = query_params['sheetId'][0]
        return dashboard_id, sheet_id

    def _parse_query_url(self, url):
        """
        解析query URL获取相关参数

        Args:
            url (str): query URL
        """
        parsed_url = urlparse(url)
        query_params = parse_qs(parsed_url.query)

        # 优先使用historyId
        if 'id' in query_params:
            self.history_id = query_params['id'][0]
        elif 'rid' in query_params:
            self.report_id = query_params['rid'][0]
        else:
            raise ValueError("URL中未找到rid或id参数")

    def initialize_dashboard_data(self):
        """
        初始化dashboard相关数据（sheet信息、filters等）
        """
        if self.query_type != 'dashboard':
            raise ValueError("只有dashboard类型才能初始化dashboard数据")

        if not self.sheet_data:
            logger.info("初始化dashboard数据...")
            self.sheet_data = self.get_sheet_info()
            self.sheet_filters = self.extract_filters_from_sheet()

            # 预加载所有报表名称
            report_id_list = self.sheet_data.get('reportIdList', [])
            if report_id_list:
                self.report_names = self.get_report_names_by_ids(
                    report_id_list)

            logger.info(f"Dashboard数据初始化完成 - {len(report_id_list)}个报表")

    def get_sheet_info(self):
        """
        获取sheet的基本信息（使用内部状态）

        Returns:
            dict: sheet信息
        """
        if self.query_type != 'dashboard':
            raise ValueError("只有dashboard类型才能获取sheet信息")

        logger.info(
            f"获取Sheet信息 - sheetID: {self.sheet_id}, dashboardID: {self.dashboard_id}")

        url = f'{self.base_url}/aeolus/api/v3/sheet/simpleSheet?sheetId={self.sheet_id}&dashboardId={self.dashboard_id}&isEdit=0'

        session = requests.Session()
        session.trust_env = False
        response = session.get(url, headers=self.base_headers)

        if response.status_code != 200:
            raise Exception(f"获取Sheet信息失败，状态码: {response.status_code}")

        data = response.json()

        if data.get('code') != 'aeolus/ok':
            raise Exception(f"API返回错误: {data.get('msg')}")

        logger.info(f"成功获取Sheet信息: {data['data']['name']}")
        return data['data']

    def get_report_details(self, report_id):
        """
        获取单个报表的详细信息（带缓存）

        Args:
            report_id (int): 报表ID

        Returns:
            dict: 报表详细信息
        """
        # 检查缓存
        if report_id in self.report_details:
            return self.report_details[report_id]

        logger.info(f"获取报表详情 - reportID: {report_id}")

        # 根据查询类型选择不同的接口
        if self.query_type == 'dashboard' and self.dashboard_id is not None:
            # dashboard场景使用sheetReport接口
            url = f'{self.base_url}/aeolus/api/v3/sheet/sheetReport?reportId={report_id}&dashboardId={self.dashboard_id}&isEdit='
            logger.info(f"使用dashboard接口: {url}")
        else:
            # query场景或其他情况使用dataMart接口，这个接口更通用
            url = f'{self.base_url}/aeolus/api/v3/dataMart/report?reportId={report_id}'
            logger.info(f"使用dataMart接口: {url}")

        session = requests.Session()
        session.trust_env = False
        response = session.get(url, headers=self.base_headers)

        if response.status_code != 200:
            raise Exception(f"获取报表详情失败，状态码: {response.status_code}")

        data = response.json()

        # 支持多种成功状态码
        success_codes = [0, 'OK', 'aeolus/ok']
        if data.get('code') not in success_codes:
            raise Exception(
                f"API返回错误: {data.get('msg', data.get('code', 'Unknown error'))}")

        # 缓存结果
        self.report_details[report_id] = data['data']

        logger.info(f"成功获取报表详情: {data['data'].get('name', 'N/A')}")
        return data['data']

    def extract_filters_from_sheet(self):
        """
        从sheet信息中提取filter配置（使用内部状态）

        Returns:
            dict: 按reportID分组的filter信息
        """
        if not self.sheet_data:
            raise ValueError("需要先获取sheet数据")

        logger.info("提取filter信息...")

        filters_by_report = {}

        # 查找componentTree中的过滤器
        component_tree = self.sheet_data.get(
            'content', {}).get('componentTree', {})

        def extract_filters_recursive(component):
            """递归提取过滤器"""
            if isinstance(component, dict):
                # 递归处理子组件
                if 'children' in component:
                    for child in component['children']:
                        extract_filters_recursive(child)

                # 查找queryContainer组件（这是包含过滤器的正确组件类型）
                elif component.get('componentName') == 'queryContainer':
                    props = component.get('props', {})
                    filters = props.get('filters', [])

                    # 处理每个filter
                    for filter_item in filters:
                        filter_props = filter_item.get('props', {})
                        chart_ids = filter_props.get('chartIDs', [])
                        filter_config = filter_props.get('filter', {})
                        name = filter_props.get('name', '')
                        fields = filter_props.get('fields', [])

                        # 只处理有值的过滤器
                        if filter_config.get('val') == []:
                            continue

                        # 为每个关联的chart记录filter
                        for chart_id in chart_ids:
                            if chart_id not in filters_by_report:
                                filters_by_report[chart_id] = []

                            # 为每个field创建一个过滤器项
                            for field in fields:
                                dim_met_id = field.get('dimMetId')
                                data_set_id = field.get('dataSetId')

                                if dim_met_id:  # 只有当dimMetId存在时才添加
                                    filters_by_report[chart_id].append({
                                        'name': name,
                                        'dimMetId': dim_met_id,
                                        'dataSetId': data_set_id,
                                        'filter': filter_config,
                                        'field': field
                                    })

        extract_filters_recursive(component_tree)

        logger.info(f"提取到 {len(filters_by_report)} 个报表的filter信息")
        for report_id, filters in filters_by_report.items():
            logger.info(f"  报表 {report_id}: {len(filters)} 个过滤器")
            for f in filters:
                logger.info(
                    f"    - {f['name']} (dimMetId: {f.get('dimMetId', 'N/A')})")

        return filters_by_report

    def merge_filters_with_report(self, report_data):
        """
        合并sheet level的filter和report level的whereList（使用内部状态）

        Args:
            report_data (dict): 报表数据

        Returns:
            tuple: (合并后的query whereList, 合并后的schema whereList)
        """
        report_id = report_data['id']
        req_json = report_data.get('reqJson', {})

        # 获取现有的whereList
        query_where_list = req_json.get('query', {}).get('whereList', [])
        schema_where_list = req_json.get('schema', {}).get('whereList', [])

        # 复制现有的whereList
        merged_query_where_list = query_where_list.copy()
        merged_schema_where_list = schema_where_list.copy()

        # 添加sheet级别的filters
        if report_id in self.sheet_filters:
            for filter_info in self.sheet_filters[report_id]:
                filter_config = filter_info['filter']
                dim_met_id = filter_info['dimMetId']
                data_set_id = filter_info['dataSetId']
                current_time = int(time.time() * 1000)

                # 检查当前报表的dataSetId是否匹配
                report_data_set_id = req_json.get('query', {}).get('dataSetId')
                if data_set_id != report_data_set_id:
                    logger.info(
                        f"    跳过不匹配的数据集filter: {filter_info['name']} (dataSetId: {data_set_id} != {report_data_set_id})")
                    continue

                # 检查query的whereList中是否已存在相同id的filter
                existing_query_filter_index = None
                for i, existing_filter in enumerate(merged_query_where_list):
                    if existing_filter.get('id') == str(dim_met_id):
                        existing_query_filter_index = i
                        break

                if existing_query_filter_index is not None:
                    # 如果存在相同id的filter，保留原名字，替换其他配置
                    original_name = merged_query_where_list[existing_query_filter_index].get('name', filter_info['name'])
                    query_where_item = {
                        'name': original_name,  # 保持原名字不变
                        'id': str(dim_met_id),
                        'preRelation': 'and',
                        'uniqueId': current_time,
                        **filter_config  # 使用新的filter配置
                    }
                    merged_query_where_list[existing_query_filter_index] = query_where_item
                    logger.info(
                        f"    替换query中的filter: {original_name} (dimMetId: {dim_met_id})")
                else:
                    # 如果不存在相同id的filter，添加新的filter
                    query_where_item = {
                        'name': filter_info['name'],
                        'id': str(dim_met_id),  # 使用dimMetId作为id
                        'preRelation': 'and',
                        'uniqueId': current_time,
                        **filter_config  # 直接使用完整的filter配置
                    }
                    merged_query_where_list.append(query_where_item)
                    logger.info(
                        f"    添加filter到query: {filter_info['name']} (dimMetId: {dim_met_id})")

                # 检查schema的whereList中是否已存在相同id的filter
                existing_schema_filter_index = None
                for i, existing_filter in enumerate(merged_schema_where_list):
                    if existing_filter.get('id') == str(dim_met_id) or existing_filter.get('dimMetId') == dim_met_id:
                        existing_schema_filter_index = i
                        break

                if existing_schema_filter_index is not None:
                    # 如果存在相同id的filter，保留原名字，替换其他配置
                    original_name = merged_schema_where_list[existing_schema_filter_index].get('name', filter_info['name'])
                    schema_where_item = {
                        'uniqueId': current_time + 1,  # 确保ID不重复
                        'id': str(dim_met_id),  # 使用dimMetId作为id
                        'dimMetId': dim_met_id,
                        'originId': str(dim_met_id),
                        'preRelation': 'and',
                        'filter': filter_config,  # 完整的filter配置
                        'name': original_name,  # 保持原名字不变
                        'dataSetId': data_set_id,
                        'subFilterIds': [],
                        'index': existing_schema_filter_index,  # 保持原索引位置
                    }
                    merged_schema_where_list[existing_schema_filter_index] = schema_where_item
                    logger.info(
                        f"    替换schema中的filter: {original_name} (dimMetId: {dim_met_id})")
                else:
                    # 如果不存在相同id的filter，添加新的filter
                    schema_where_item = {
                        'uniqueId': current_time + 1,  # 确保ID不重复
                        'id': str(dim_met_id),  # 使用dimMetId作为id
                        'dimMetId': dim_met_id,
                        'originId': str(dim_met_id),
                        'preRelation': 'and',
                        'filter': filter_config,  # 完整的filter配置
                        'name': filter_info['name'],
                        'dataSetId': data_set_id,
                        'subFilterIds': [],
                        'index': len(merged_schema_where_list),
                    }
                    merged_schema_where_list.append(schema_where_item)
                    logger.info(
                        f"    添加filter到schema: {filter_info['name']} (dimMetId: {dim_met_id})")

        return merged_query_where_list, merged_schema_where_list

    def download_report(self, report_data, output_dir='.'):
        """
        下载报表数据（使用内部状态）

        Args:
            report_data (dict): 报表详细信息
            output_dir (str): 输出目录

        Returns:
            str: 下载的文件路径
        """
        report_name = report_data['name']
        report_id = report_data['id']

        logger.info(f"开始下载报表: {report_name}")

        # 获取报表的reqJson配置
        req_json = report_data.get('reqJson', {})

        # 合并filter到whereList (包括query和schema)
        merged_query_where_list, merged_schema_where_list = self.merge_filters_with_report(
            report_data)

        # 获取原始schema并更新whereList
        original_schema = req_json.get('schema', {})
        updated_schema = {
            **original_schema,
            'whereList': merged_schema_where_list
        }

        # 组装完整的query和schema用于调试
        assembled_query = {
            **req_json.get('query', {}),
            "whereList": merged_query_where_list,
            "limit": 100000
        }
        
        # 过滤掉sort中depend为空的项
        if 'sort' in assembled_query and assembled_query['sort']:
            sort_config = assembled_query['sort']
            
            # 过滤orderByList中depend为空的项
            if 'orderByList' in sort_config and sort_config['orderByList']:
                filtered_order_by_list = []
                for order_item in sort_config['orderByList']:
                    # 如果有depend字段但值为空（空字符串或None），则过滤掉
                    if 'depend' in order_item and (order_item['depend'] == "" or order_item['depend'] is None):
                        continue
                    filtered_order_by_list.append(order_item)
                sort_config['orderByList'] = filtered_order_by_list
            
            # 过滤orderByListState中depend为空的项
            if 'orderByListState' in sort_config and sort_config['orderByListState']:
                filtered_order_by_list_state = []
                for order_item in sort_config['orderByListState']:
                    # 如果有depend字段但值为空（空字符串或None），则过滤掉
                    if 'depend' in order_item and (order_item['depend'] == "" or order_item['depend'] is None):
                        continue
                    filtered_order_by_list_state.append(order_item)
                sort_config['orderByListState'] = filtered_order_by_list_state
            
            assembled_query['sort'] = sort_config

        # 构建下载请求体
        download_payload = {
            "downloadConfig": {
                "limit": 1000000,
                "type": "all",
                "fileFormat": 'csv',
                "encoding": "UTF-8",
                "disableScientificNotation": True,
                "reportName": f"{report_name}.csv",
                "downloadType": "default",
                "enableHiddenFields": True
            },
            "version": 4,
            "metaData": req_json.get('metaData', {}),
            "reportId": report_id,
            "dataSourceId": req_json.get('dataSourceId'),
            "query": assembled_query,
            "schema": updated_schema,
            "display": req_json.get('display', {})
        }

        # 只有当dashboard_id不为None时才添加dashboardId字段
        if self.dashboard_id is not None:
            download_payload["dashboardId"] = int(self.dashboard_id)

        # 保存debug信息到文件
        self._save_debug_info(report_id, report_name, assembled_query,
                              updated_schema, download_payload, output_dir)

        # 设置下载请求头
        download_headers = {
            **self.base_headers,
        }

        url = f'{self.base_url}/aeolus/vqs/api/v2/vizQuery/download'

        session = requests.Session()
        session.trust_env = False
        response = session.post(
            url, headers=download_headers, json=download_payload)

        if response.status_code != 200:
            # 下载失败时也保存响应信息用于调试
            self._save_debug_info(
                report_id, response=response, output_dir=output_dir, error_type='error')
            raise Exception(f"下载请求失败，状态码: {response.status_code}")

        # 保存文件
        filename = f"{report_name}.csv"
        # 清理文件名中的特殊字符
        filename = re.sub(r'[<>:"/\\|?*\s]', '_', filename)

        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        filepath = os.path.join(output_dir, filename)

        with open(filepath, 'wb') as f:
            f.write(response.content)

        logger.info(f"报表下载完成: {filepath}")

        # 记录CSV文件映射关系
        self.report_csv_mapping[report_id] = filename

        return filepath

    def _save_debug_info(self, report_id, report_name=None, query=None, schema=None, payload=None, output_dir='.', response=None, error_type='debug'):
        """
        保存调试信息到文件（统一处理成功和错误情况）

        Args:
            report_id (int): 报表ID
            report_name (str, optional): 报表名称
            query (dict, optional): 组装的query对象
            schema (dict, optional): 组装的schema对象
            payload (dict, optional): 完整的下载请求体
            output_dir (str): 输出目录
            response (object, optional): HTTP响应对象（错误情况下使用）
            error_type (str): 调试类型，'debug' 或 'error'
        """
        try:
            # 确保debug目录存在
            debug_dir = aeolus_debug_dir
            os.makedirs(debug_dir, exist_ok=True)

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

            if error_type == 'error' and response is not None:
                # 错误情况：保存错误信息
                error_file = os.path.join(
                    debug_dir, f'error_{report_id}_{timestamp}.txt')

                with open(error_file, 'w', encoding='utf-8') as f:
                    f.write(f"下载错误信息\n")
                    f.write(f"=" * 50 + "\n")
                    f.write(f"报表ID: {report_id}\n")
                    f.write(f"错误时间: {datetime.now()}\n")
                    f.write(f"状态码: {response.status_code}\n")
                    f.write(f"响应头: {dict(response.headers)}\n")
                    f.write(f"响应内容: {response.text}\n")

                logger.info(f"    错误调试信息已保存到: {os.path.basename(error_file)}")

            else:
                # 成功情况：保存完整调试信息
                if not report_name:
                    report_name = self.report_names.get(
                        report_id, f'Report_{report_id}')

                # 清理文件名
                safe_report_name = re.sub(r'[<>:"/\\|?*\s]', '_', report_name)

                saved_files = []

                # 保存完整的请求体（如果提供）
                if payload:
                    payload_file = os.path.join(
                        debug_dir, f'payload_{report_id}_{safe_report_name}_{timestamp}.json')
                    with open(payload_file, 'w', encoding='utf-8') as f:
                        json.dump(payload, f, indent=2, ensure_ascii=False)
                    saved_files.append(('请求体', os.path.basename(payload_file)))

                # 保存简要信息
                summary_file = os.path.join(
                    debug_dir, f'summary_{report_id}_{safe_report_name}_{timestamp}.txt')
                with open(summary_file, 'w', encoding='utf-8') as f:
                    f.write(f"报表调试信息\n")
                    f.write(f"=" * 50 + "\n")
                    f.write(f"报表ID: {report_id}\n")
                    f.write(f"报表名称: {report_name}\n")
                    f.write(f"查询类型: {self.query_type}\n")
                    f.write(f"Dashboard ID: {self.dashboard_id}\n")
                    f.write(f"Sheet ID: {self.sheet_id}\n")
                    f.write(f"生成时间: {datetime.now()}\n")

                    # 添加保存文件清单
                    if saved_files:
                        f.write(f"\n保存的调试文件:\n")
                        for file_type, filename in saved_files:
                            f.write(f"  - {file_type}: {filename}\n")

                    saved_files.append(('摘要', os.path.basename(summary_file)))

                # 打印保存的文件信息
                if saved_files:
                    logger.info(f"    调试信息已保存到: {debug_dir}")
                    for file_type, filename in saved_files:
                        logger.info(f"      - {file_type}: {filename}")

        except Exception as e:
            logger.warning(f"    保存调试信息失败: {str(e)}")

    def _save_execution_summary(self, output_dir, downloaded_files, execution_time, total_reports=0):
        """
        保存整个脚本执行的摘要（使用内部状态）

        Args:
            output_dir (str): 输出目录
            downloaded_files (list): 下载的文件列表
            execution_time (float): 执行时间(秒)
            total_reports (int): 总报表数量
        """

        try:
            # 确保debug目录存在
            debug_dir = aeolus_debug_dir
            os.makedirs(debug_dir, exist_ok=True)

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            summary_file = os.path.join(
                debug_dir, f'execution_summary_{timestamp}.txt')

            with open(summary_file, 'w', encoding='utf-8') as f:
                f.write(f"脚本执行摘要\n")
                f.write(f"=" * 50 + "\n")
                f.write(f"执行时间: {datetime.now()}\n")
                f.write(f"查询类型: {self.query_type}\n")
                f.write(f"Dashboard ID: {self.dashboard_id}\n")
                f.write(f"Sheet ID: {self.sheet_id}\n")
                f.write(f"Report ID: {self.report_id}\n")
                f.write(f"History ID: {self.history_id}\n")
                f.write(f"输出目录: {output_dir}\n")
                f.write(f"执行耗时: {execution_time:.2f} 秒\n")
                f.write(f"总报表数量: {total_reports}\n")
                f.write(f"成功下载: {len(downloaded_files)} 个文件\n")
                f.write(
                    f"成功率: {len(downloaded_files)/total_reports*100:.1f}%\n" if total_reports > 0 else "成功率: N/A\n")

                f.write(f"\n下载的文件列表:\n")
                for i, filepath in enumerate(downloaded_files, 1):
                    filename = os.path.basename(filepath)
                    f.write(f"  {i}. {filename}\n")

                if not downloaded_files:
                    f.write("  (无文件下载)\n")

            logger.info(f"执行摘要已保存到: {os.path.basename(summary_file)}")

        except Exception as e:
            logger.warning(f"保存执行摘要失败: {str(e)}")

    async def download_dashboard_reports(self, output_dir='.'):
        """
        完整的dashboard下载流程（使用内部状态）

        Args:
            output_dir (str): 输出目录

        Returns:
            list: 下载的文件列表
        """
        if self.query_type != 'dashboard':
            raise ValueError("只有dashboard类型才能使用此方法")

        start_time = time.time()

        try:
            # 初始化dashboard数据
            self.initialize_dashboard_data()

            # 获取reportIdList
            report_id_list = self.sheet_data.get('reportIdList', [])
            logger.info(f"找到 {len(report_id_list)} 个报表")

            downloaded_files = []

            # 对每个reportID获取详情并下载 - 改为并发执行
            async def download_single_report(report_id):
                """下载单个报表的异步函数"""
                try:
                    # 获取报表详情
                    report_data = await asyncio.get_event_loop().run_in_executor(
                        None, self.get_report_details, report_id
                    )

                    # 下载报表
                    filepath = await asyncio.get_event_loop().run_in_executor(
                        None, self.download_report, report_data, output_dir
                    )

                    logger.info(f"成功下载报表 {report_id}: {os.path.basename(filepath)}")
                    return filepath

                except Exception as e:
                    logger.error(f"处理报表 {report_id} 时发生错误: {str(e)}")
                    return None

            # 创建并发任务列表
            tasks = [download_single_report(report_id) for report_id in report_id_list]

            # 并发执行所有下载任务
            logger.info(f"开始并发下载 {len(tasks)} 个报表...")
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 处理结果，过滤掉失败的下载
            for result in results:
                if result and not isinstance(result, Exception):
                    downloaded_files.append(result)

            execution_time = time.time() - start_time
            logger.info(f"下载完成！共下载 {len(downloaded_files)} 个文件")

            # 保存执行摘要
            self._save_execution_summary(
                output_dir, downloaded_files, execution_time, len(report_id_list))

            return downloaded_files

        except Exception as e:
            execution_time = time.time() - start_time
            # 即使出错也保存摘要
            self._save_execution_summary(output_dir, [], execution_time, 0)
            logger.error(f"下载过程中发生错误: {str(e)}")
            raise

    def download_query_report(self, output_dir='.'):
        """
        下载dataQuery页面的报表（使用内部状态）

        Args:
            output_dir (str): 输出目录

        Returns:
            list: 下载的文件列表
        """
        if self.query_type != 'query':
            raise ValueError("只有query类型才能使用此方法")

        start_time = time.time()
        logger.info(
            f"解析dataQuery - reportID: {self.report_id}, historyID: {self.history_id}")

        downloaded_files = []
        total_reports = 1  # dataQuery通常只有一个报表

        try:
            # 情况1: 如果有report_id参数
            if self.report_id:
                logger.info(f"处理reportID: {self.report_id}")

                try:
                    # 调用dataMart API获取报表信息
                    report_data = self.get_report_details(self.report_id)

                    # 使用现有的下载逻辑，不需要sheet级别的filters
                    filepath = self.download_report(report_data, output_dir)
                    downloaded_files.append(filepath)

                except Exception as e:
                    logger.error(
                        f"处理reportID {self.report_id} 时发生错误: {str(e)}")
                    raise

            # 情况2: 如果有history_id参数
            elif self.history_id:
                logger.info(f"处理historyID: {self.history_id}")

                try:
                    # 调用queryEngine API获取历史查询信息
                    history_data = self.get_query_history(self.history_id)

                    # 构造报表数据结构用于下载
                    report_data = self.convert_history_to_report(
                        history_data, self.history_id)

                    # 使用现有的下载逻辑
                    filepath = self.download_report(report_data, output_dir)
                    downloaded_files.append(filepath)

                except Exception as e:
                    logger.error(
                        f"处理historyID {self.history_id} 时发生错误: {str(e)}")
                    raise

            else:
                raise ValueError("未找到有效的reportID或historyID")

            execution_time = time.time() - start_time
            logger.info(f"dataQuery下载完成！共下载 {len(downloaded_files)} 个文件")

            # 保存执行摘要
            self._save_execution_summary(
                output_dir, downloaded_files, execution_time, total_reports)

            return downloaded_files

        except Exception as e:
            execution_time = time.time() - start_time
            # 即使出错也保存摘要
            self._save_execution_summary(
                output_dir, downloaded_files, execution_time, total_reports)
            raise

    def get_query_history(self, history_id):
        """
        获取查询历史信息

        Args:
            history_id (str): 历史查询ID

        Returns:
            dict: 查询历史信息
        """
        logger.info(f"获取查询历史信息 - historyID: {history_id}")

        url = f'{self.base_url}/aeolus/api/v3/queryEngine/history?historyId={history_id}'

        session = requests.Session()
        session.trust_env = False
        response = session.get(url, headers=self.base_headers)

        if response.status_code != 200:
            raise Exception(f"获取查询历史信息失败，状态码: {response.status_code}")

        data = response.json()

        # 支持多种成功状态码
        success_codes = [0, 'OK', 'aeolus/ok']
        if data.get('code') not in success_codes:
            raise Exception(
                f"API返回错误: {data.get('msg', data.get('code', 'Unknown error'))}")

        logger.info(f"成功获取查询历史信息")
        return data['data']

    def convert_history_to_report(self, history_data, history_id):
        """
        将查询历史数据转换为报表数据结构

        Args:
            history_data (dict): 查询历史数据
            history_id (str): 历史查询ID

        Returns:
            dict: 转换后的报表数据结构
        """
        # 构造与get_report_details返回结果兼容的数据结构
        report_data = {
            'id': history_id,
            'name': f'未命名查询_{history_id}',
            'reqJson': history_data.get('reqJson', {})
        }

        logger.info(f"转换查询历史为报表结构: {report_data['name']}")
        return report_data

    def get_report_names_by_ids(self, report_ids):
        """
        获取多个报表的名称（使用内部状态）

        Args:
            report_ids (list): 报表ID列表

        Returns:
            dict: 报表ID到名称的映射
        """
        report_names = {}

        for report_id in report_ids:
            try:
                report_data = self.get_report_details(report_id)
                report_names[report_id] = report_data.get(
                    'name', f'Report_{report_id}')
            except Exception as e:
                logger.error(f"获取报表 {report_id} 名称失败: {str(e)}")
                report_names[report_id] = f'Report_{report_id}'

        return report_names

    async def download_with_page_interaction(self, page, output_dir='.'):
        """
        根据URL类型执行相应的下载和页面交互逻辑

        Args:
            page: Playwright page object
            output_dir (str): 输出目录

        Returns:
            dict: 包含下载文件列表和统计信息的字典
        """
        current_url = page.url
        logger.info(
            f"Starting download with page interaction for URL: {current_url}")

        try:
            if self.query_type == 'dashboard':
                logger.info(
                    "Dashboard URL detected, executing dashboard download workflow")
                return await self._handle_dashboard_workflow(page, output_dir)
            elif self.query_type == 'query':
                logger.info(
                    "DataQuery URL detected, executing dataQuery download workflow")
                return await self._handle_dataquery_workflow(page, output_dir)
            else:
                logger.warning(f"Unknown URL pattern: {current_url}")
                raise Exception(f"Unknown URL pattern: {current_url}")
        except Exception as e:
            logger.error(f"Error in download_with_page_interaction: {e}")
            raise

    async def _handle_dashboard_workflow(self, page, output_dir):
        """
        处理dashboard页面的完整工作流程

        Args:
            page: Playwright page object
            output_dir (str): 输出目录

        Returns:
            dict: 包含下载文件列表和统计信息的字典
        """
        logger.info("Executing dashboard workflow")

        try:
            # 1. 执行滚动脚本
            await self._execute_scroll_script(page)

            # 2. 初始化dashboard数据
            self.initialize_dashboard_data()

            # 3. 提取组件-报表关系
            component_report_map = self._extract_component_report_relationships()
            logger.info(
                f"Found {len(component_report_map)} component-report relationships")

            # 4&5. 并发执行截图和下载
            async def screenshot_task():
                return await self._screenshot_dashboard_components_by_report(page, output_dir)

            async def download_task():
                # 直接调用异步方法
                return await self.download_dashboard_reports(output_dir)

            # 并发执行截图和下载任务
            logger.info("Starting concurrent screenshot and download tasks...")
            (screenshot_count, screenshot_files), downloaded_files = await asyncio.gather(
                screenshot_task(),
                download_task()
            )

            # 6. 生成报表汇总CSV
            summary_filename = self._generate_report_summary_csv(output_dir)

            logger.info(
                f"Dashboard workflow completed: {len(downloaded_files)} reports downloaded, {screenshot_count} screenshots taken")

            return {
                "downloaded_files": downloaded_files,
                "report_count": len(downloaded_files),
                "screenshot_count": screenshot_count,
                "screenshot_files": screenshot_files,
                "workflow_type": "dashboard",
                "report_summary_info": f"报表相关信息都保存在{summary_filename}文件中，包括每个report的数据文件名，截图文件名，和风神嵌出图表链接" if summary_filename else "报表汇总文件生成失败"
            }

        except Exception as e:
            logger.error(f"Error in dashboard workflow: {e}")
            raise

    def _generate_report_summary_csv(self, output_dir):
        """
        生成包含所有报表信息的汇总CSV文件

        Args:
            output_dir (str): 输出目录

        Returns:
            str: 生成的CSV文件名，如果失败则返回None
        """
        logger.info("Generating report summary CSV...")

        try:
            import csv

            # 创建CSV文件
            sheet_name = self.sheet_data.get('name', 'Unknown_Sheet')
            safe_sheet_name = re.sub(r'[<>:"/\\|?*\s]', '_', sheet_name)
            summary_filename = f"report_summary_{safe_sheet_name}.csv"
            summary_filepath = os.path.join(output_dir, summary_filename)

            # 获取报表ID列表
            report_id_list = self.sheet_data.get('reportIdList', [])

            # 创建报表名称映射
            report_names = {}
            if hasattr(self, 'report_names') and self.report_names:
                report_names = self.report_names
            else:
                try:
                    report_names = self.get_report_names_by_ids(report_id_list)
                except Exception as e:
                    logger.warning(f"Failed to get report names: {e}")

            # 写入CSV文件
            with open(summary_filepath, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['报表名称', 'CSV文件名', '截图文件名', '风神嵌出图表链接']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                # 写入标题行
                writer.writeheader()

                # 为每个报表写入一行
                for report_id in report_id_list:
                    report_name = report_names.get(report_id, f'Report_{report_id}')

                    # 使用存储的映射关系获取CSV文件名和截图文件名
                    csv_filename = self.report_csv_mapping.get(report_id, '未找到')
                    screenshot_filename = self.report_screenshot_mapping.get(report_id, '未找到')

                    # 生成嵌出图表链接 - 严格按照用户提供的格式
                    feature_json = '{"dashboard":{"report":{"showTitle":true,"showTips":false,"showToolbar":false,"showHeader":true,"enableEnterVizQuery":true,"actions":["excelExport","FullScreen","imgExport","copyToDashboard","embed","comment","viewReport","createMonitor","UserContract","CustomFields","Refresh"]},"enableComment":false}}'
                    feature_json_encoded = urllib.parse.quote(feature_json)
                    embed_url = f"https://data.bytedance.net/aeolus/pages/external/dashboard/{self.dashboard_id}/{report_id}?appId=1002611&feature={feature_json_encoded}&inline=true&sheetId={self.sheet_id}"

                    # 写入行数据
                    writer.writerow({
                        '报表名称': report_name,
                        'CSV文件名': csv_filename,
                        '截图文件名': screenshot_filename,
                        '风神嵌出图表链接': embed_url
                    })

            logger.info(f"Report summary CSV generated: {summary_filepath}")
            logger.info(f"Summary contains {len(report_id_list)} reports")
            return summary_filename

        except Exception as e:
            logger.error(f"Error generating report summary CSV: {e}")
            # 不抛出异常，避免影响主流程
            return None

    async def _handle_dataquery_workflow(self, page, output_dir):
        """
        处理dataQuery页面的完整工作流程

        Args:
            page: Playwright page object
            output_dir (str): 输出目录

        Returns:
            dict: 包含下载文件列表和统计信息的字典
        """
        logger.info("Executing dataQuery workflow")

        try:
            # 1. 下载报表数据
            downloaded_files = self.download_query_report(output_dir)

            # 2. 截图图表元素
            screenshot_count, screenshot_files = await self._screenshot_chart_elements(page, output_dir)

            # 3. 获取当前页面URL并生成报表汇总CSV（针对dataQuery）
            current_url = page.url
            summary_filename = self._generate_dataquery_summary_csv(downloaded_files, screenshot_files, output_dir, current_url)

            logger.info(
                f"DataQuery workflow completed: {len(downloaded_files)} reports downloaded, {screenshot_count} screenshots taken")

            return {
                "downloaded_files": downloaded_files,
                "report_count": len(downloaded_files),
                "screenshot_count": screenshot_count,
                "screenshot_files": screenshot_files,
                "workflow_type": "dataQuery",
                "report_summary_info": f"报表相关信息都保存在{summary_filename}文件中，包括每个report的数据文件名，截图文件名，和风神嵌出图表链接" if summary_filename else "报表汇总文件生成失败"
            }

        except Exception as e:
            logger.error(f"Error in dataQuery workflow: {e}")
            raise

    def _generate_dataquery_summary_csv(self, downloaded_files, screenshot_files, output_dir, current_url):
        """
        生成dataQuery页面的报表汇总CSV文件

        Args:
            downloaded_files (list): 下载的CSV文件路径列表
            screenshot_files (list): 截图文件名列表
            output_dir (str): 输出目录
            current_url (str): 当前页面URL

        Returns:
            str: 生成的CSV文件名，如果失败则返回None
        """
        logger.info("Generating dataQuery report summary CSV...")

        try:
            import csv

            # 创建CSV文件
            # 为dataQuery生成有意义的文件名标识符
            if self.report_id:
                file_identifier = f"report_{self.report_id}"
            elif self.history_id:
                file_identifier = f"history_{self.history_id}"
            else:
                file_identifier = "dataquery"

            summary_filename = f"report_summary_{file_identifier}.csv"
            summary_filepath = os.path.join(output_dir, summary_filename)

            # 写入CSV文件
            with open(summary_filepath, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['报表名称', 'CSV文件名', '截图文件名', '风神嵌出图表链接']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                # 写入标题行
                writer.writeheader()

                # 对于dataQuery，通常只有一个报表
                if downloaded_files:
                    csv_file = downloaded_files[0]
                    csv_filename = os.path.basename(csv_file)

                    # 从文件名中提取报表名称
                    report_name = csv_filename.replace('.csv', '').replace('_', ' ')

                    # 使用存储的映射关系获取截图文件名
                    key = self.report_id if self.report_id else self.history_id
                    screenshot_filename = self.report_screenshot_mapping.get(key, '未找到') if key else '未找到'

                    # 生成嵌出图表链接 - 使用当前页面URL并添加嵌入参数
                    embed_url = f"{current_url}&embeddingMode=larkDocCard&originalUrl={current_url}"

                    # 写入行数据
                    writer.writerow({
                        '报表名称': report_name,
                        'CSV文件名': csv_filename,
                        '截图文件名': screenshot_filename,
                        '风神嵌出图表链接': embed_url
                    })

            logger.info(f"DataQuery report summary CSV generated: {summary_filepath}")
            return summary_filename

        except Exception as e:
            logger.error(f"Error generating dataQuery report summary CSV: {e}")
            # 不抛出异常，避免影响主流程
            return None

    async def _close_all_alerts(self, page):
        try:
            # 直接删除所有弹窗，不需要等待
            await page.evaluate("""
                () => {
                    try {
                        document.querySelectorAll('.slideNoticeRight-enter-done').forEach(popup => popup.remove());
                        // 兜底：同时处理可能的其他alert元素
                        document.querySelectorAll('[role="alert"]').forEach(alert => alert.remove());
                    } catch (e) {
                        console.log('关闭弹窗时出现错误:', e);
                    }
                }
            """)
        except Exception as e:
            # 兜底处理，确保方法不会因为任何原因报错
            logger.error(f"关闭弹窗时出现错误: {e}")
            pass

    async def _execute_scroll_script(self, page):
        """
        执行滚动脚本用于dashboard页面

        Args:
            page: Playwright page object
        """
        logger.info('Executing scroll script for dashboard...')

        scroll_script = """
        (async function() {
            // Find all potentially scrollable containers
            const scrollContainers = [];

            // Look for specific container IDs that match dashboard patterns
            const dashboardContainers = document.querySelectorAll('[id$="-scroll-container"]');
            if (dashboardContainers.length > 0) {
                for (const container of dashboardContainers) {
                    scrollContainers.push(container);
                }
            }

            // If no specific containers found, find any scrollable container
            if (scrollContainers.length === 0) {
                const allElements = document.querySelectorAll('*');
                for (const el of allElements) {
                    const style = window.getComputedStyle(el);
                    const hasOverflow =
                        style.overflowY === 'scroll' ||
                        style.overflowY === 'auto' ||
                        style.overflow === 'scroll' ||
                        style.overflow === 'auto';

                    const isScrollable =
                        hasOverflow &&
                        el.scrollHeight > el.clientHeight;

                    if (isScrollable) {
                        scrollContainers.push(el);
                    }
                }
            }

            // Smooth scrolling function with enhanced easing
            const smoothScroll = async (container, start, end, duration) => {
                // 使用更平滑的缓动函数
                const easeInOutCubic = t => t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
                const startTime = performance.now();

                return new Promise(resolve => {
                    const animateScroll = (currentTime) => {
                        const elapsedTime = currentTime - startTime;

                        if (elapsedTime >= duration) {
                            container.scrollTop = end;
                            resolve();
                            return;
                        }

                        const progress = elapsedTime / duration;
                        const easedProgress = easeInOutCubic(progress);
                        const scrollPosition = start + (end - start) * easedProgress;

                        container.scrollTop = scrollPosition;
                        requestAnimationFrame(animateScroll);
                    };

                    requestAnimationFrame(animateScroll);
                });
            };

            // Scroll each container to bottom
            let scrolledAny = false;
            for (const container of scrollContainers) {
                const initialScrollTop = container.scrollTop;
                const maxScroll = container.scrollHeight - container.clientHeight;

                if (maxScroll > 0) {
                    // 增加步数，让滚动更细分更平滑
                    const steps = 40;
                    const stepSize = Math.max(1, Math.floor(maxScroll / steps));

                    for (let i = 1; i <= steps; i++) {
                        const targetPosition = Math.min(initialScrollTop + i * stepSize, maxScroll);

                        // 增加动画时长，让滚动更平滑
                        await smoothScroll(container, container.scrollTop, targetPosition, 600);
                        // 增加步间等待时间，让滚动更慢
                        await new Promise(resolve => setTimeout(resolve, 400));

                        // 更频繁的暂停，让滚动更从容
                        if (i % 3 === 0) {
                            await new Promise(resolve => setTimeout(resolve, 800));
                        }
                    }

                    // 最终滚动到底部，使用更长的动画时间
                    await smoothScroll(container, container.scrollTop, maxScroll, 800);

                    if (container.scrollTop > initialScrollTop) {
                        scrolledAny = true;
                    }
                }
            }

            // Also perform the default window scroll as a fallback
            if (!scrolledAny) {
                const beforeScrollY = window.scrollY;
                const documentHeight = document.body.scrollHeight;
                const targetY = documentHeight - window.innerHeight;

                // 增加窗口滚动的段数，让滚动更平滑
                const sections = 20;
                const sectionHeight = (targetY - beforeScrollY) / sections;

                for (let i = 1; i <= sections; i++) {
                    const targetPosition = beforeScrollY + sectionHeight * i;

                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });

                    // 增加窗口滚动的等待时间
                    await new Promise(resolve => setTimeout(resolve, 600));
                }
            }

            // 最终等待时间也增加
            await new Promise(resolve => setTimeout(resolve, 1000));
            return scrolledAny;
        })();
        """

        # Execute the scroll script
        scroll_result = await page.evaluate(scroll_script)

        if scroll_result:
            logger.info('Scrolled dashboard containers to bottom.')
        else:
            logger.info('Scrolled to bottom of page using standard method.')

        # Wait for the application to load
        logger.info('Waiting for application to load...')
        await page.wait_for_timeout(5000)

    def _extract_component_report_relationships(self):
        """
        从sheet数据中提取组件和报表关系

        Returns:
            dict: 组件ID到报表信息的映射
        """
        if not self.sheet_data:
            raise ValueError("需要先初始化sheet数据")

        relationships = {}

        def traverse_components(component, parent_id=None):
            if isinstance(component, dict):
                component_id = component.get('componentId')
                component_name = component.get('componentName')

                # Check if this component has props with reportId
                if component_name and component_id:
                    props = component.get('props', {})
                    report_id = props.get('reportId')

                    if report_id:
                        # Get report name from reportIdList if available
                        report_name = f'Report_{report_id}'

                        relationships[component_id] = {
                            'reportId': report_id,
                            'reportName': report_name,
                            'componentName': component_name,
                            'parentId': parent_id
                        }
                        logger.info(
                            f"Found component-report relationship: {component_id} -> {report_id} ({component_name})")

                # Recursively process children
                children = component.get('children', [])
                for child in children:
                    traverse_components(child, component_id)

        # Start traversal from component tree
        component_tree = self.sheet_data.get(
            'content', {}).get('componentTree', {})
        traverse_components(component_tree)

        logger.info(
            f"Extracted {len(relationships)} component-report relationships")
        return relationships

    async def _screenshot_dashboard_components_by_report(self, page, output_dir):
        """
        基于报表关系截图dashboard组件

        Args:
            page: Playwright page object
            output_dir: 截图输出目录

        Returns:
            tuple: (成功截图的数量, 截图文件名列表)
        """
        logger.info("Taking screenshots of dashboard components by report")

        # 关闭所有alert
        await self._close_all_alerts(page)

        screenshot_count = 0
        screenshot_files = []

        try:
            # Extract component-report relationships from sheet data
            relationships = self._extract_component_report_relationships()

            if not relationships:
                logger.warning("No component-report relationships found")
                return screenshot_count, screenshot_files

            logger.info(f"Found {len(relationships)} components with reports")

            # Get report details for naming using existing methods
            report_names = {}
            try:
                report_ids = [info['reportId']
                              for info in relationships.values()]
                # Use existing report_names cache or get new ones
                if hasattr(self, 'report_names') and self.report_names:
                    report_names = self.report_names
                else:
                    report_names = self.get_report_names_by_ids(report_ids)
                logger.info(f"Retrieved {len(report_names)} report names")
            except Exception as e:
                logger.warning(f"Failed to get report names: {e}")

            # Fallback to default names if needed
            for component_id, info in relationships.items():
                report_id = info['reportId']
                if report_id not in report_names:
                    report_names[report_id] = info['reportName']

            # Take screenshot of each component with enhanced waiting
            for component_id, info in relationships.items():
                try:
                    report_id = info['reportId']
                    report_name = report_names.get(
                        report_id, f'Report_{report_id}')
                    component_name = info['componentName']

                    logger.info(f"Processing component {component_id} - {report_name} ({component_name})")

                    # Find element by data-component-id attribute
                    selector = f'[data-component-id="{component_id}"]'
                    element = await page.query_selector(selector)

                    if element:
                        # Wait for chart to be fully loaded and ready
                        is_ready = await self._wait_for_chart_ready(page, element, component_id, component_name, max_wait=30)

                        if is_ready:
                            # Generate filename with report name
                            safe_report_name = re.sub(
                                r'[<>:"/\\|?*\s()]', '_', report_name)
                            filename = f"{safe_report_name}-chart.png"
                            filepath = os.path.join(output_dir, filename)

                            # Take screenshot of the element
                            await element.screenshot(path=filepath)
                            logger.info(f"Screenshot saved: {filepath}")
                            screenshot_count += 1
                            screenshot_files.append(filename)

                            # 记录截图文件映射关系
                            self.report_screenshot_mapping[report_id] = filename
                        else:
                            logger.warning(f"Chart {component_id} ({report_name}) not ready for screenshot after waiting")

                    else:
                        logger.warning(
                            f"Element not found for component ID: {component_id}")

                except Exception as e:
                    logger.error(
                        f"Error taking screenshot of component {component_id}: {e}")

        except Exception as e:
            logger.error(
                f"Error in _screenshot_dashboard_components_by_report: {e}")

        logger.info(
            f"Dashboard screenshots completed: {screenshot_count} screenshots taken")
        return screenshot_count, screenshot_files

    async def _wait_for_chart_ready(self, page, element, component_id, component_name, max_wait=30):
        """
        等待图表完全加载并准备好截图

        Args:
            page: Playwright page object
            element: 图表元素
            component_id: 组件ID
            component_name: 组件名称
            max_wait: 最大等待时间(秒)

        Returns:
            bool: 图表是否准备就绪
        """
        logger.info(f"Waiting for chart {component_id} ({component_name}) to be ready...")

        start_time = time.time()

        try:
            # 1. 首先确保元素可见
            await element.wait_for_element_state('visible', timeout=5000)
            logger.info(f"Chart {component_id}: Element is visible")

            # 2. 检查元素尺寸是否合理
            bounding_box = await element.bounding_box()
            if not bounding_box or bounding_box['width'] < 50 or bounding_box['height'] < 50:
                logger.warning(f"Chart {component_id}: Invalid size {bounding_box}")
                await page.wait_for_timeout(2000)  # 等待2秒后重新检查
                bounding_box = await element.bounding_box()
                if not bounding_box or bounding_box['width'] < 50 or bounding_box['height'] < 50:
                    logger.warning(f"Chart {component_id}: Still invalid size after waiting")
                    return False

            logger.info(f"Chart {component_id}: Size is valid {bounding_box['width']}x{bounding_box['height']}")

            # 3. 对于 measure_card、pivot_table、table 组件，不需要检查Canvas
            if component_name in ['measure_card', 'pivot_table', 'table']:
                logger.info(f"Chart {component_id}: Component type {component_name} does not require canvas check")
                # 等待一段时间让内容加载，然后直接返回准备就绪
                await page.wait_for_timeout(2000)
                return True

            # 4. 对于其他组件，检查Canvas是否有内容
            check_script = f"""
            async () => {{
                const element = document.querySelector('[data-component-id="{component_id}"]');
                if (!element) return {{ ready: false, reason: 'Element not found' }};
                
                // 查找Canvas元素
                const canvasElements = element.querySelectorAll('canvas');
                if (canvasElements.length === 0) {{
                    return {{ ready: false, reason: 'No canvas elements found' }};
                }}
                
                // 检查Canvas是否有内容
                let hasContent = false;
                for (const canvas of canvasElements) {{
                    if (canvas.width > 0 && canvas.height > 0) {{
                        // 检查canvas是否不是完全空白
                        const ctx = canvas.getContext('2d');
                        if (ctx) {{
                            const imageData = ctx.getImageData(0, 0, Math.min(canvas.width, 100), Math.min(canvas.height, 100));
                            const data = imageData.data;
                            // 检查是否有非透明像素
                            for (let i = 3; i < data.length; i += 4) {{
                                if (data[i] > 0) {{ // alpha channel
                                    hasContent = true;
                                    break;
                                }}
                            }}
                        }}
                    }}
                    if (hasContent) break;
                }}
                
                if (!hasContent) {{
                    return {{ ready: false, reason: 'Canvas content not rendered yet' }};
                }}
                
                return {{ ready: true, reason: 'Canvas is ready' }};
            }}
            """

            # 循环检查直到图表准备就绪或超时
            while time.time() - start_time < max_wait:
                try:
                    result = await page.evaluate(check_script)
                    logger.info(f"Chart {component_id}: Check result - {result['reason']}")

                    if result['ready']:
                        logger.info(f"Chart {component_id}: Canvas ready for screenshot")
                        return True

                    await page.wait_for_timeout(1000)

                except Exception as e:
                    logger.warning(f"Chart {component_id}: Error during canvas check: {e}")
                    await page.wait_for_timeout(1000)

            logger.warning(f"Chart {component_id}: Timeout waiting for canvas content")
            return True

        except Exception as e:
            logger.error(f"Chart {component_id}: Error in _wait_for_chart_ready: {e}")
            return True

    async def _screenshot_chart_elements(self, page, output_dir):
        """
        截图dataQuery页面的图表元素

        Args:
            page: Playwright page object
            output_dir: 截图输出目录

        Returns:
            tuple: (成功截图的数量, 截图文件名列表)
        """
        logger.info("Taking screenshots of chart elements")

        screenshot_count = 0
        screenshot_files = []

        try:
            # 等待页面加载完成
            logger.info("Waiting for page to load completely...")
            await page.wait_for_timeout(3000)  # 等待3秒让页面完全加载

            # 尝试多种可能的图表元素选择器，基于用户提供的实际selector
            selectors = [
                '#viz-query-result .ui-test-report-chart.viz-report-chart',  # 在viz-query-result容器内查找
                'div.ui-test-report-chart.viz-report-chart',  # 原始选择器
                '#viz-query-result .ui-test-report-chart',  # 在容器内只使用第一个class
                '#viz-query-result .viz-report-chart',  # 在容器内只使用第二个class
                'div.ui-test-report-chart',  # 只使用第一个class
                'div.viz-report-chart',  # 只使用第二个class
            ]

            chart_elements = []
            used_selector = None

            # 逐个尝试选择器，直到找到元素
            for selector in selectors:
                logger.info(f"Trying selector: {selector}")
                elements = await page.query_selector_all(selector)
                if elements:
                    chart_elements = elements
                    used_selector = selector
                    logger.info(
                        f"Found {len(elements)} elements with selector: {selector}")
                    break
                else:
                    logger.info(f"No elements found with selector: {selector}")

            if not chart_elements:
                logger.warning(
                    "No chart elements found with class 'ui-test-report-chart viz-report-chart'")
                return screenshot_count, screenshot_files

            logger.info(
                f"Using selector '{used_selector}' - found {len(chart_elements)} chart elements")

            # Take screenshot of each chart element
            for i, element in enumerate(chart_elements):
                try:
                    # 检查元素是否可见
                    is_visible = await element.is_visible()
                    if not is_visible:
                        logger.warning(
                            f"Chart element {i+1} is not visible, skipping")
                        continue

                    # 获取元素的边界框信息
                    bounding_box = await element.bounding_box()
                    if not bounding_box or bounding_box['width'] == 0 or bounding_box['height'] == 0:
                        logger.warning(
                            f"Chart element {i+1} has invalid bounding box, skipping")
                        continue

                    logger.info(f"Chart element {i + 1} - size: {bounding_box['width']}x{bounding_box['height']}")

                    # Generate filename
                    timestamp = datetime.now().isoformat().replace(':', '-').replace('.', '-')
                    filename = f"chart-{i + 1}-{timestamp}.png"
                    filepath = os.path.join(output_dir, filename)

                    # Take screenshot of the element
                    await element.screenshot(path=filepath)
                    logger.info(f"Screenshot saved: {filepath}")
                    screenshot_count += 1
                    screenshot_files.append(filename)

                    # 记录截图文件映射关系（对于dataQuery，使用report_id或history_id作为键）
                    key = self.report_id if self.report_id else self.history_id
                    if key:
                        self.report_screenshot_mapping[key] = filename

                except Exception as e:
                    logger.error(
                        f"Error taking screenshot of chart element {i+1}: {e}")

        except Exception as e:
            logger.error(f"Error in _screenshot_chart_elements: {e}")

        logger.info(
            f"Chart screenshots completed: {screenshot_count} screenshots taken")
        return screenshot_count, screenshot_files

async def monitor_aeolus_dashboard_and_download_reports(page, language=None):
    """
    监控风神(Aeolus)仪表盘并下载报告
    
    Args:
        page: Playwright页面对象
        language (str, optional): 界面语言，"en"为英文，默认为中文
    
    Returns:
        dict: 包含下载结果的字典
    """
    # Get workspace directory from environment variable
    workspace = os.getenv("IRIS_WORKSPACE_PATH")
    if not workspace:
        workspace = os.getcwd()
        logger.info(
            f"IRIS_WORKSPACE_PATH not set, using current directory: {workspace}")

    # Setup download and debug directories inside workspace
    download_dir = os.path.join(workspace, "aeolus_download")
    debug_dir = aeolus_debug_dir

    # Ensure directories exist
    if not os.path.exists(download_dir):
        os.makedirs(download_dir, exist_ok=True)
        logger.info(f"Created download directory: {download_dir}")

    if not os.path.exists(debug_dir):
        os.makedirs(debug_dir, exist_ok=True)
        logger.info(f"Created debug directory: {debug_dir}")

    # Current page URL
    current_url = page.url
    logger.info(f"Starting monitoring on page: {current_url}")

    # Store token and request info
    titan_token = None
    found_token = False
    download_result = None
    download_completed = False
    await get_browser_session().remove_highlights()

    # Function to monitor requests for x-titan-token
    async def monitor_requests(route, request):
        nonlocal titan_token, found_token, download_result, download_completed

        # Check if request has x-titan-token header
        headers = request.headers
        token = headers.get('x-titan-token')
        logger.info(f"Try to find x-titan-token in request: {request.url}")

        if token and not found_token:
            logger.info(f"Found x-titan-token in request: {request.url}")
            titan_token = token
            found_token = True

            # Continue with the request
            await route.continue_()

            # Stop monitoring by removing the route handler
            await page.unroute('**/*')

            # Now call the appropriate download method based on URL
            try:
                logger.info("Starting download process...")
                # Create ReportDownloader instance with URL for auto-parsing
                downloader = ReportDownloader(
                    titan_token=titan_token, url=current_url, language=language)

                # Use the unified download method that handles both dashboard and dataQuery URLs
                download_result = await downloader.download_with_page_interaction(page, download_dir)
                logger.info(f"Download process completed: {download_result}")
                download_completed = True
            except Exception as e:
                logger.error(f"Error in download process: {e}")
                download_result = {
                    "downloaded_files": [],
                    "report_count": 0,
                    "screenshot_count": 0,
                    "screenshot_files": [],
                    "workflow_type": "unknown",
                    "error": str(e)
                }
                download_completed = True
        else:
            # Continue with the request if no token found
            await route.continue_()
    

    # If language is English, set localeId cookie and refresh page
    if language == "en":
        logger.info("Setting localeId cookie to en_US for English interface")
        await page.evaluate("""
            document.cookie = 'localeId=en_US; path=/';
        """)
        await page.reload()
        logger.info("Page reloaded with English locale")

    # Set up request monitoring
    await page.route('**/*', monitor_requests)

    if '/dataQuery' in current_url:
        # Check if it's a dataquery page and refresh if needed
        logger.info("Detected dataquery page, refreshing...")
        await page.reload()
        logger.info("Dataquery page refresh initiated")

    # Wait for token to be found (with timeout)
    max_wait_time = 60  # Maximum wait time in seconds
    start_time = time.time()

    while not found_token and time.time() - start_time < max_wait_time:
        logger.info("Waiting for request with x-titan-token...")
        await page.wait_for_timeout(3000)  # Check every 3 seconds

    if not found_token:
        logger.warning("No x-titan-token found within timeout period")
        return {
            "success": False,
            "message": "No x-titan-token found within timeout period",
            "report_count": 0,
            "screenshot_count": 0,
            "screenshot_files": []
        }

    # Wait for download process to complete (with timeout)
    logger.info("Token found, waiting for download process to complete...")
    download_wait_start = time.time()
    # Maximum wait time for download process (5 minutes)
    max_download_wait = 300

    while not download_completed and time.time() - download_wait_start < max_download_wait:
        await page.wait_for_timeout(2000)  # Check every 2 second

    if not download_completed:
        logger.error("Download process did not complete within timeout period")
        return {
            "success": False,
            "message": "Download process did not complete within timeout period",
            "report_count": 0,
            "screenshot_count": 0,
            "screenshot_files": []
        }

    logger.info("Monitoring completed successfully")

    # Prepare final result
    result = {
        "success": True,
        "message": "Token found and download process completed",
        "report_count": 0,
        "screenshot_count": 0,
        "workflow_type": "unknown"
    }

    # Add download statistics if available
    if download_result:
        result.update({
            "report_count": download_result.get("report_count", 0),
            "screenshot_count": download_result.get("screenshot_count", 0),
            "workflow_type": download_result.get("workflow_type", "unknown"),
            "downloaded_files": [os.path.basename(f) for f in download_result.get("downloaded_files", [])],
            "screenshot_files": download_result.get("screenshot_files", [])
        })

        if "error" in download_result:
            result["download_error"] = download_result["error"]

    logger.info(
        f"Final result: Downloaded {result['report_count']} reports and {result['screenshot_count']} screenshots")
    return result

class MeegoViewLoader:
    def __init__(self):
        """
        初始化MeegoViewLoader
        """
        self.cookies = {}
        self.meego_csrf_token = None
        self.workspace = os.getenv("IRIS_WORKSPACE_PATH", os.getcwd())
        self.sso_dir = "/tmp/sso"
        logger.info(f"MeegoViewLoader initialized, workspace: {self.workspace}")

        # 从SSO文件中加载cookies
        self._load_cookies_from_sso()

    def _load_cookies_from_sso(self):
        """
        从sso目录中找到meego相关的sso文件，如果没找到，找lark相关的sso文件，如果再没找到，就找最新的sso文件
        """
        logger.info(f"Loading cookies from SSO directory: {self.sso_dir}")

        if not os.path.exists(self.sso_dir):
            logger.warning(f"SSO directory does not exist: {self.sso_dir}")
            return

        # 获取所有json文件
        sso_files = glob.glob(os.path.join(self.sso_dir, "*.json"))

        if not sso_files:
            logger.warning("No SSO files found")
            return

        logger.info(f"Found {len(sso_files)} SSO files")

        # 优先级顺序：meego相关 -> lark相关 -> 最新的
        selected_file = None

        # 1. 查找meego相关的文件
        meego_files = [f for f in sso_files if 'meego' in os.path.basename(f).lower()]
        if meego_files:
            selected_file = max(meego_files, key=os.path.getmtime)
            logger.info(f"Found meego SSO file: {os.path.basename(selected_file)}")
        else:
            # 2. 查找lark相关的文件
            lark_files = [f for f in sso_files if 'lark' in os.path.basename(f).lower()]
            if lark_files:
                selected_file = max(lark_files, key=os.path.getmtime)
                logger.info(f"Found lark SSO file: {os.path.basename(selected_file)}")
            else:
                # 3. 选择最新的文件
                selected_file = max(sso_files, key=os.path.getmtime)
                logger.info(f"Using newest SSO file: {os.path.basename(selected_file)}")

        # 加载选中的SSO文件
        try:
            with open(selected_file, 'r', encoding='utf-8') as f:
                cookies_data = json.load(f)
                logger.info(f"Loaded cookies from: {os.path.basename(selected_file)}")

            # 转换cookie格式并查找meego_csrf_token
            for cookie in cookies_data:
                cookie_name = cookie.get('name', '')
                cookie_value = cookie.get('value', '')

                if cookie_name:
                    self.cookies[cookie_name] = cookie_value

                if cookie_name == 'meego_csrf_token':
                    self.meego_csrf_token = cookie_value
                    logger.info("Found existing meego_csrf_token in cookies")

            logger.info(f"Loaded {len(self.cookies)} cookies")

        except Exception as e:
            logger.error(f"Error loading SSO file {selected_file}: {str(e)}")

        # 如果没有找到meego_csrf_token，生成一个新的
        if not self.meego_csrf_token:
            self.meego_csrf_token = str(uuid.uuid4()).replace('-', '')[:16] + '-' + str(uuid.uuid4()).replace('-', '')[:4] + '-' + str(uuid.uuid4()).replace('-', '')[:4] + '-' + str(uuid.uuid4()).replace('-', '')[:4] + '-' + str(uuid.uuid4()).replace('-', '')[:12]
            self.cookies['meego_csrf_token'] = self.meego_csrf_token
            logger.info(f"Generated new meego_csrf_token: {self.meego_csrf_token}")

            # 保存更新后的cookies到原文件
            try:
                if selected_file:
                    with open(selected_file, 'r', encoding='utf-8') as f:
                        cookies_data = json.load(f)

                    # 添加新的meego_csrf_token cookie
                    new_cookie = {
                        'name': 'meego_csrf_token',
                        'value': self.meego_csrf_token,
                        'domain': '.meego.net',  # 假设是meego域名
                        'path': '/',
                        'httpOnly': False,
                        'secure': True,
                        'sameSite': 'Lax'
                    }
                    cookies_data.append(new_cookie)

                    with open(selected_file, 'w', encoding='utf-8') as f:
                        json.dump(cookies_data, f, indent=2, ensure_ascii=False)

                    logger.info(f"Saved updated cookies with meego_csrf_token to: {os.path.basename(selected_file)}")

            except Exception as e:
                logger.error(f"Error saving updated cookies: {str(e)}")

    def _get_cookie_string(self):
        """
        将cookies转换为cookie字符串格式
        """
        return '; '.join([f"{name}={value}" for name, value in self.cookies.items()])

    def _meego_api_request(self, method, endpoint, data=None, params=None):
        """
        向Meego API发送请求
        """
        headers = {
            'Cookie': self._get_cookie_string(),
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36',
            'x-meego-csrf-token': self.meego_csrf_token
        }

        # 处理代理设置
        proxies = {
            'http': 'http://strato-proxy-rd-relay.byted.org:8118',
            'https': 'http://strato-proxy-rd-relay.byted.org:8118'
        }

        try:
            if method.upper() == 'GET':
                session = requests.Session()
                session.trust_env = False
                response = session.get(
                    endpoint, headers=headers, params=params, proxies=proxies)
            elif method.upper() == 'POST':
                session = requests.Session()
                session.trust_env = False
                response = session.post(endpoint, headers=headers, json=data, proxies=proxies)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")

            logger.info(f"API request: {method} {endpoint} - Status: {response.status_code}")

            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"API request failed: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            logger.error(f"Error making API request to {endpoint}: {str(e)}")
            return None

    def parse_meego_url(self, url):
        """
        解析Meego URL，提取项目信息和视图参数
        """
        logger.info(f"Parsing Meego URL: {url}")

        parsed_url = urlparse(url)
        path = parsed_url.path
        query = parsed_url.query
        params = parse_qs(query)

        project = path.split('/')[1] if len(path.split('/')) > 1 else None
        viewtype = path.split('/')[2] if len(path.split('/')) > 2 else None

        chart_id = None
        filter_id = None
        view = None

        if viewtype == 'chart':
            chart_id = path.split('/')[-1]
            parent_url = urlparse(params.get('parentUrl', [''])[0])
            if parent_url.path:
                viewtype = parent_url.path.split('/')[2] if len(parent_url.path.split('/')) > 2 else viewtype
                view = parent_url.path.split('/')[3] if len(parent_url.path.split('/')) > 3 else None
                parent_params = parse_qs(parent_url.query)
                if 'quickFilterId' in parent_params:
                    filter_id = parent_params['quickFilterId'][0]
        else:
            if viewtype == 'workObjectView':
                view = path.split('/')[4] if len(path.split('/')) > 4 else None
            else:
                view = path.split('/')[3] if len(path.split('/')) > 3 else None
            if 'quickFilterId' in params:
                filter_id = params['quickFilterId'][0]

        wktype = 'story'
        if viewtype == 'issueView':
            wktype = 'issue'
        elif viewtype == 'workObjectView':
            wktype = path.split('/')[3] if len(path.split('/')) > 3 else wktype

        result = {
            'project': project,
            'viewtype': viewtype,
            'view': view,
            'filter_id': filter_id,
            'chart_id': chart_id,
            'wktype': wktype
        }

        logger.info(f"Parsed URL parameters: {result}")
        return result

    def get_project_key(self, project_simple_name):
        """
        通过项目简单名称获取项目key
        """
        logger.info(f"Getting project key for: {project_simple_name}")

        endpoint = 'https://meego.feishu.cn/goapi/v1/project/trans_simple_name'
        data = {"simple_name_list": [project_simple_name]}

        response = self._meego_api_request('POST', endpoint, data=data)

        if response and response.get('code') == 0:
            project_key_map = response.get('data', {}).get('project_key_map', {})
            project_key = project_key_map.get(project_simple_name)
            logger.info(f"Found project key: {project_key}")
            return project_key
        else:
            logger.error(f"Failed to get project key for {project_simple_name}")
            return None

    def get_chart_data(self, project_key, chart_id, bql_filter=None):
        """
        获取图表数据
        """
        logger.info(f"Getting chart data for chart_id: {chart_id}")

        endpoint = 'https://meego.feishu.cn/goapi/v5/measurement_platform/charts/data'

        chart_req = {
            "project_key": project_key,
            "chart_id": chart_id,
            "is_latest": True,
            "is_complete_config": False,
            "latest": False,
            "client_info": {"device_id": "", "reference_subscription": ""}
        }

        if bql_filter:
            chart_req['bql_filter'] = bql_filter

        response = self._meego_api_request('POST', endpoint, data=chart_req)

        if not response or response.get('code') != 0:
            logger.error(f"Failed to get chart data for chart_id: {chart_id}")
            return None

        try:
            chart_data = response['data']['data']['data_based_chart']
            chart_name = chart_data['name']

            # 构建指标映射
            metric_map = {}
            for idx, quota in enumerate(chart_data['magicDefinition']['quotas']):
                metric_map[str(idx)] = quota['display']['showName']

            # 构建维度映射
            dim_map = {}
            for idx, quota in enumerate(chart_data['magicDefinition']['dims']):
                dim_map[str(idx)] = quota['display']['showName'] + '[维度]'

            # 提取参考线数据（只取第一条参考线的值）
            reference_lines = chart_data.get('referenceLines', [])
            reference_value = None
            if reference_lines:
                reference_value = reference_lines[0].get('val', '')

            # 解析数据
            rows = []
            chart_data_list = response['data']['data']['chart_data_list']

            for x in chart_data_list:
                if 'data_sets' not in x:
                    continue
                for dim in x['data_sets']:
                    row = {}
                    for metric in dim['quotaValues']:
                        if metric in metric_map:
                            row[metric_map[metric]] = dim['quotaValues'][metric]
                    for metric in dim['dimValues']:
                        if metric in dim_map:
                            row[dim_map[metric]] = dim['dimValues'][metric]

                    # 添加参考线数据
                    if reference_value is not None:
                        row['参考线'] = reference_value

                    rows.append(row)

            # 处理分析信息（如果有）
            analyze_info = chart_data.get('analyzeInfo', [])
            if len(analyze_info) > 0 and len(rows) >= 2:
                rows[0]['_cycle'] = 'Current'
                rows[1]['_cycle'] = 'Previous'

            # 创建DataFrame
            df = pd.DataFrame(rows)

            reference_info = f", with reference line: {reference_value}" if reference_value else ""
            logger.info(f"Successfully parsed chart data: {chart_name}, {len(rows)} rows{reference_info}")

            return {
                "name": chart_name,
                "format": "csv",
                "data": df.to_csv(index=False)
            }

        except Exception as e:
            logger.error(f"Error parsing chart data: {str(e)}")
            return None

    def get_table_data(self, project_key, view, viewtype, wktype):
        """
        获取表格数据
        """
        logger.info(f"Getting table data for view: {view}")

        # 获取运行时配置
        config_endpoint = 'https://meego.feishu.cn/goapi/v5/search/general/runtime_config/get/v2'
        config_data = {
            "locator": {"view_id": view},
            "view_context": {
                "project_key": project_key,
                "view_support_features": {
                    "view_scenario": "Unknown",
                    "view_feature_point": "Page"
                }
            },
            "fg_map": {
                "meego.view.straight_out": True,
                "meego.view.shuchaijie": True,
                "meego.view.resource_lib": True,
                "meego.view.sub_task_current_operator_edit": True,
                "meego.workitem.custom_button": True,
                "meego.workitem.node_finished_split": True,
                "meego.view.ui_batch_switch": True
            },
            "slim_mode": True,
            "need_front_setting": False
        }

        config_response = self._meego_api_request('POST', config_endpoint, data=config_data)

        if not config_response or config_response.get('code') != 0:
            logger.error("Failed to get runtime config")
            return None

        runtime_config = config_response['data']['runtime_config']
        title = runtime_config['meta']['title']
        runtime_config_id = runtime_config['runtime_config_id']

        # 获取工作项结构和详情
        detail_endpoint = 'https://meego.feishu.cn/goapi/v5/search/general/work_item_structure_and_detail/get'
        detail_data = {
            "view_id": view,
            "instant_query": False,
            "project_key": project_key,
            "runtime_config_id": runtime_config_id,
            "tags_map": {
                "MinPermissions": False,
                "NeedRaw": False,
                "NeedEditPermission": True,
                "NeedSubTask": False
            },
            "current_view_mode": "table"
        }

        detail_response = self._meego_api_request('POST', detail_endpoint, data=detail_data)

        if not detail_response or detail_response.get('code') != 0:
            logger.error("Failed to get work item details")
            return None

        try:
            work_item_structure = detail_response['data']['work_item_structure']
            work_item_detail_v2 = detail_response['data']['work_item_detail_v2']

            # 简化处理：直接提取所有工作项
            all_items = []
            for node_id, items in work_item_detail_v2.items():
                all_items.extend(items)

            # 获取字段映射
            fields_endpoint = 'https://meego.feishu.cn/goapi/v3/settings/fields/project_keys'
            fields_data = [{
                "language": "zh",
                "project_key": project_key,
                "work_item_type_key": wktype
            }]

            fields_response = self._meego_api_request('POST', fields_endpoint, data=fields_data)

            field_map = {}
            if fields_response and fields_response.get('code') == 0 and len(fields_response.get('data', [])) > 0:
                for field in fields_response['data'][0]['fields']:
                    field_map[field['key']] = field['name']

            # 处理工作项数据
            records = []
            for item in all_items:
                record = {}
                ui_data_map = item.get('uiDataMap', {})

                for fkey, ui_data in ui_data_map.items():
                    if 'uiValue' not in ui_data:
                        continue

                    field_name = field_map.get(fkey, fkey)
                    ui_value = ui_data['uiValue']
                    ui_type = ui_data['uiType']

                    # 简化字段值提取
                    if ui_type == 'text' and 'text' in ui_value:
                        record[field_name] = ui_value['text'].get('value', '')
                    elif ui_type == 'number' and 'number' in ui_value:
                        record[field_name] = ui_value['number'].get('value', '')
                    elif ui_type == 'date' and 'date' in ui_value:
                        record[field_name] = ui_value['date'].get('value', '')
                    elif ui_type == 'select' and 'select' in ui_value:
                        values = [item['label'] for item in ui_value['select'].get('value', [])]
                        record[field_name] = ','.join(values)
                    elif ui_type == 'user' and 'user' in ui_value:
                        users = []
                        for user in ui_value['user'].get('value', []):
                            user_name = user.get('name_en') or user.get('name_cn') or user.get('email', '').split(',')[0] or user.get('employeeId', 'Unknown')
                            users.append(user_name)
                        record[field_name] = ','.join(users)
                    else:
                        # 其他类型暂时转为字符串
                        record[field_name] = str(ui_value)

                records.append(record)

            # 创建DataFrame
            df = pd.DataFrame(records)

            logger.info(f"Successfully parsed table data: {title}, {len(records)} rows")

            return {
                "name": title,
                "format": "csv",
                "data": df.to_csv(index=False)
            }

        except Exception as e:
            logger.error(f"Error parsing table data: {str(e)}")
            return None

    def save_data_to_file(self, data_info, output_dir=None):
        """
        将数据保存到文件
        """
        if not output_dir:
            output_dir = self.workspace

        if not data_info:
            logger.error("No data to save")
            return None

        try:
            # 清理文件名
            safe_name = re.sub(r'[<>:"/\\|?*\s]', '_', data_info['name'])
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{safe_name}_{timestamp}.csv"
            filepath = os.path.join(output_dir, filename)

            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)

            # 保存数据
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(data_info['data'])

            logger.info(f"Data saved to: {filepath}")
            return filepath

        except Exception as e:
            logger.error(f"Error saving data to file: {str(e)}")
            return None

    def process_meego_url(self, url):
        """
        处理Meego URL，获取并保存数据
        """
        logger.info(f"Processing Meego URL: {url}")

        try:
            # 解析URL
            url_params = self.parse_meego_url(url)
            # 保存原始URL用于获取viewMode
            url_params['original_url'] = url

            if not url_params['project']:
                logger.error("No project found in URL")
                return {"error": "No project found in URL"}

            # 获取项目key
            project_key = self.get_project_key(url_params['project'])
            if not project_key:
                return {"error": f"Failed to get project key for {url_params['project']}"}

            results = []

            # 获取viewMode来判断是图表还是表格
            view_mode = self.get_view_mode(url_params)
            logger.info(f"View mode: {view_mode}")

            if view_mode == 'table':
                # 处理表格数据
                logger.info("Processing table data")
                table_data = self.get_table_data(
                    project_key,
                    url_params['view'],
                    url_params['viewtype'],
                    url_params['wktype']
                )
                if table_data:
                    filepath = self.save_data_to_file(table_data)
                    if filepath:
                        results.append({
                            "type": "table",
                            "name": table_data['name'],
                            "file": os.path.basename(filepath)
                        })
            else:
                # 处理图表数据
                logger.info("Processing chart data")

                # 先获取所有图表ID
                title, bql_filter, chartids = self.get_chartids(
                    project_key,
                    url_params['view'],
                    url_params['viewtype'],
                    url_params['filter_id']
                )

                # 如果URL中指定了chart_id，只处理那一个
                if url_params['chart_id']:
                    chartids = [str(url_params['chart_id'])]
                    logger.info(f"Using specific chart_id from URL: {url_params['chart_id']}")

                if not chartids:
                    logger.error("No chart IDs found")
                    return {"error": "No chart IDs found"}

                logger.info(f"Processing {len(chartids)} charts")

                # 处理每个图表
                for chart_id in chartids:
                    chart_data = self.get_chart_data(project_key, chart_id, bql_filter)
                    if chart_data:
                        filepath = self.save_data_to_file(chart_data)
                        if filepath:
                            results.append({
                                "type": "chart",
                                "name": chart_data['name'],
                                "file": os.path.basename(filepath)
                            })

            if results:
                logger.info(f"Successfully processed {len(results)} data items")
                return {
                    "success": True,
                    "project": url_params['project'],
                    "project_key": project_key,
                    "results": results
                }
            else:
                return {"error": "No data was successfully processed"}

        except Exception as e:
            logger.error(f"Error processing Meego URL: {str(e)}")
            return {"error": f"Failed to process URL: {str(e)}"}

    def get_view_mode(self, url_params):
        """
        获取视图模式（viewMode）
        根据MeegoView.ipynb的逻辑，优先从URL参数获取，否则通过API获取
        """
        logger.info("Getting view mode...")

        # 解析URL参数
        parsed_url = urlparse(url_params.get('original_url', ''))
        query_params = parse_qs(parsed_url.query)

        # 首先检查URL参数中是否有viewMode
        if 'viewMode' in query_params:
            view_mode = query_params['viewMode'][0]
            logger.info(f"Found viewMode in URL params: {view_mode}")
            return view_mode

        # 如果URL参数中没有viewMode，通过API获取
        view_id = url_params.get('view')
        if not view_id:
            logger.error("No view_id available to get view mode")
            return None

        try:
            endpoint = 'https://meego.feishu.cn/goapi/v5/search/general/get_tiny_rtc_config'
            data = {"view_id": view_id}

            response = self._meego_api_request('POST', endpoint, data=data)

            if response and response.get('code') == 0:
                view_mode = response['data']['current_view_mode']
                logger.info(f"Got viewMode from API: {view_mode}")
                return view_mode
            else:
                logger.error(f"Failed to get view mode from API")
                return None
        except Exception as e:
            logger.error(f"Error getting view mode: {str(e)}")
            return None

    def get_chartids(self, project_key, view, viewtype, filter_id=None):
        """
        获取图表ID列表
        根据MeegoView.ipynb的逻辑实现
        """
        logger.info(f"Getting chart IDs for view: {view}")

        subfilter = None
        bql_filter = None
        chartids = []
        title = ""

        if viewtype in ['multi-project-view', 'multiProjectView', 'storyView', 'issueView', 'workObjectView']:
            try:
                endpoint = 'https://meego.feishu.cn/goapi/v5/search/general/get_chart_runtime_config'
                data = {'view_id': view}

                response = self._meego_api_request('POST', endpoint, data=data)

                if not response or response.get('code') != 0:
                    logger.error(f"Failed to get chart runtime config for view: {view}")
                    return title, bql_filter, chartids

                cfgres = response

                # 处理过滤器
                if filter_id:
                    query_configs = cfgres['data']['chart_runtime_config']['query']['sub_query_configs']['queryList']
                    for subquery in query_configs:
                        if subquery['uuid'] == filter_id:
                            subfilter = subquery['value']['filter']
                            break

                if subfilter:
                    import json
                    mainfilter = cfgres['data']['chart_runtime_config']['query']['query']['filter']
                    bql_filter = json.dumps({
                        "conditions": [],
                        "conjunction": "AND",
                        "groups": [mainfilter, subfilter]
                    })

                # 获取标题和图表ID列表
                title = cfgres['data']['chart_runtime_config']['meta']['title']
                for chart in cfgres['data']['chart_runtime_config']['chart']['chart_coordinate_list']:
                    chartids.append(chart['chart_id'])

                logger.info(f"Found {len(chartids)} charts in view: {view}")

            except Exception as e:
                logger.error(f"Error getting chart IDs: {str(e)}")

        else:
            logger.error(f"Unsupported viewtype: {viewtype}")

        return title, bql_filter, chartids
async def monitor_meego_view_and_download_data(page):
    """
    监控Meego页面并下载数据

    Args:
        page: Playwright page对象

    Returns:
        dict: 包含下载结果和统计信息的字典
    """

    # 获取workspace目录
    # 刷新
    page.reload()

    workspace = os.getenv("IRIS_WORKSPACE_PATH")
    if not workspace:
        workspace = os.getcwd()
        logger.info(f"IRIS_WORKSPACE_PATH not set, using current directory: {workspace}")

    # 设置输出目录
    output_dir = os.path.join(workspace)

    # 确保目录存在
    if not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
        logger.info(f"Created output directory: {output_dir}")

    # 获取当前页面URL
    current_url = page.url
    logger.info(f"Starting Meego data extraction on page: {current_url}")

    try:
        # 等待页面完全加载
        logger.info("Waiting for page to fully load...")
        await page.wait_for_load_state("networkidle", timeout=10000)
        await page.wait_for_timeout(3000)  # 额外等待3秒确保页面完全渲染

        # 创建MeegoViewLoader实例
        logger.info("Initializing MeegoViewLoader...")
        meego_loader = MeegoViewLoader()

        # 处理Meego URL并获取数据
        logger.info("Processing Meego URL and extracting data...")
        result = meego_loader.process_meego_url(current_url)

        if "error" in result:
            logger.error(f"Error processing Meego URL: {result['error']}")
            return {
                "success": False,
                "message": f"Failed to process Meego URL: {result['error']}",
                "data_count": 0,
                "files": []
            }

        # 提取结果信息
        results = result.get("results", [])
        project = result.get("project", "unknown")
        project_key = result.get("project_key", "unknown")

        # 统计信息
        data_count = len(results)
        files = [item.get("file", "") for item in results if item.get("file")]

        logger.info(f"Meego data extraction completed successfully")
        logger.info(f"Project: {project} (key: {project_key})")
        logger.info(f"Extracted {data_count} data items")
        for i, item in enumerate(results, 1):
            logger.info(f"  {i}. {item.get('type', 'unknown')} - {item.get('name', 'unnamed')} -> {item.get('file', 'no file')}")

        # 准备最终结果
        final_result = {
            "success": True,
            "message": f"Successfully extracted data from Meego project: {project}",
            "project": project,
            "project_key": project_key,
            "data_count": data_count,
            "files": files,
            "details": results,
            "workspace": output_dir
        }

        logger.info(f"Final result: Extracted {data_count} data items from project {project}")
        return final_result

    except Exception as e:
        logger.error(f"Error in Meego data extraction: {str(e)}")

        # 返回错误结果
        return {
            "success": False,
            "message": f"Error during Meego data extraction: {str(e)}",
            "data_count": 0,
            "files": [],
            "error": str(e)
        }


if __name__ == '__main__':
    asyncio.run(main(["--transport", "sse", "--port", "9227"]))


