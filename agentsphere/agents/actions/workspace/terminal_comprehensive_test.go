package workspace

// TestTerminalComprehensive 完整的Terminal功能综合测试
/*
func TestTerminalComprehensive(t *testing.T) {

	// 1. 基本命令执行测试
	t.Run("基本命令执行", func(t *testing.T) {
		cmd := exec.Command("echo", "Hello World")
		terminal := createTestTerminal()

		result, err := terminal.ExecuteCmd(cmd, ExecuteCmdOption{})

		assert.NoError(t, err, "基本命令执行应该成功")
		assert.Contains(t, result, "Hello World", "命令输出应该包含预期内容")
		assert.NotEmpty(t, result, "命令结果不应为空")
	})

	// 2. stdout和stderr混合输出测试
	t.Run("stdout和stderr混合输出顺序", func(t *testing.T) {
		// 子测试1：快速输出（验证数据完整性和基本顺序）
		t.Run("快速输出", func(t *testing.T) {
			cmd := exec.Command("bash", "-c", `
				echo "step1-stdout"; echo "step1-stderr" >&2;
				echo "step2-stdout"; echo "step2-stderr" >&2;
				echo "step3-stdout"; echo "step3-stderr" >&2;
				echo "step4-stdout"; echo "step4-stderr" >&2;
				echo "step5-stdout"; echo "step5-stderr" >&2
			`)

			terminal := createTestTerminal()
			result, err := terminal.ExecuteCmd(cmd, ExecuteCmdOption{})

			// 基本断言
			assert.NoError(t, err, "stdout/stderr混合命令应该执行成功")
			assert.NotEmpty(t, result, "输出结果不应为空")

			lines := strings.Split(strings.TrimSpace(result), "\n")
			assert.GreaterOrEqual(t, len(lines), 8, "应该至少有8行输出")

			// 数据完整性验证
			stdoutCount := 0
			stderrCount := 0
			for _, line := range lines {
				if strings.Contains(line, "stdout") {
					stdoutCount++
				}
				if strings.Contains(line, "stderr") {
					stderrCount++
				}
			}

			assert.Equal(t, 5, stdoutCount, "应该有5行stdout输出")
			assert.Equal(t, 5, stderrCount, "应该有5行stderr输出")

			// 时间顺序验证：不应该出现"所有stdout在前，所有stderr在后"的错误模式
			firstStderrIndex := -1
			lastStdoutIndex := -1

			for i, line := range lines {
				line = strings.TrimSpace(line)
				if strings.Contains(line, "stderr") && firstStderrIndex == -1 {
					firstStderrIndex = i
				}
				if strings.Contains(line, "stdout") {
					lastStdoutIndex = i
				}
			}

			if firstStderrIndex != -1 && lastStdoutIndex != -1 {
				assert.GreaterOrEqual(t, lastStdoutIndex, firstStderrIndex,
					"stdout和stderr应该交替出现，不应该所有stdout都在stderr前面")
			}
		})

		// 子测试2：慢速输出（验证严格的时间顺序）
		t.Run("慢速输出_强制时间顺序", func(t *testing.T) {
			cmd := exec.Command("bash", "-c", `
				echo "A-stdout"; sleep 0.1; echo "A-stderr" >&2; sleep 0.1;
				echo "B-stdout"; sleep 0.1; echo "B-stderr" >&2; sleep 0.1;
				echo "C-stdout"; sleep 0.1; echo "C-stderr" >&2
			`)

			terminal := createTestTerminal()
			result, err := terminal.ExecuteCmd(cmd, ExecuteCmdOption{})

			assert.NoError(t, err, "慢速输出命令应该执行成功")
			assert.NotEmpty(t, result, "输出结果不应为空")

			lines := strings.Split(strings.TrimSpace(result), "\n")
			assert.GreaterOrEqual(t, len(lines), 6, "应该至少有6行输出")

			// 验证完美的时间顺序：期望 A-stdout -> A-stderr -> B-stdout -> B-stderr -> C-stdout -> C-stderr
			validOrderCount := 0
			for i := 0; i < len(lines)-1; i++ {
				current := strings.TrimSpace(lines[i])
				next := strings.TrimSpace(lines[i+1])

				if strings.Contains(current, "stdout") && strings.Contains(next, "stderr") {
					// 检查是否是同一组（如A-stdout -> A-stderr）
					if len(current) > 0 && len(next) > 0 {
						currentPrefix := strings.Split(current, "-")[0]
						nextPrefix := strings.Split(next, "-")[0]
						if currentPrefix == nextPrefix {
							validOrderCount++
						}
					}
				}
			}

			assert.GreaterOrEqual(t, validOrderCount, 2, "应该至少发现2对正确的时间顺序")
		})
	})

	// 3. 超长行处理测试
	t.Run("超长行处理", func(t *testing.T) {
		// 创建多种大小的超长行
		testCases := []struct {
			name          string
			size          int
			maxDurationMs int // 最大允许处理时间(毫秒)
		}{
			{"64KB行", 64 * 1024, 100},
			{"1MB行", 1024 * 1024, 200},
			{"10MB行", 10 * 1024 * 1024, 1000},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				// 创建临时文件包含超长行，避免命令行参数长度限制
				tempDir := t.TempDir()
				testFile := filepath.Join(tempDir, "largeline.txt")

				largeLine := strings.Repeat("X", tc.size)
				content := fmt.Sprintf("BEFORE\n%s\nAFTER\n", largeLine)

				err := os.WriteFile(testFile, []byte(content), 0644)
				defer os.Remove(testFile)
				require.NoError(t, err, "创建测试文件应该成功")

				// 使用cat命令读取文件，避免参数长度限制
				cmd := exec.Command("cat", testFile)
				terminal := createTestTerminal()

				start := time.Now()
				result, err := terminal.ExecuteCmd(cmd, ExecuteCmdOption{
					Timeout: 30, // 30秒超时
				})
				duration := time.Since(start)

				// 基本断言
				assert.NoError(t, err, "%s处理应该成功", tc.name)
				assert.NotEmpty(t, result, "输出结果不应为空")

				// 性能断言
				assert.Less(t, int(duration.Milliseconds()), tc.maxDurationMs,
					"%s处理时间应该在%dms内", tc.name, tc.maxDurationMs)

				// 数据完整性验证
				lines := strings.Split(result, "\n")
				hasBefore := false
				hasAfter := false
				hasLargeLine := false

				for _, line := range lines {
					line = strings.TrimSpace(line)
					switch {
					case line == "BEFORE":
						hasBefore = true
					case line == "AFTER":
						hasAfter = true
					case len(line) > tc.size/2:
						hasLargeLine = true
						assert.Equal(t, tc.size, len(line),
							"%s实际长度应该匹配预期", tc.name)
					}
				}

				assert.True(t, hasBefore, "%s应该包含BEFORE标记", tc.name)
				assert.True(t, hasAfter, "%s应该包含AFTER标记", tc.name)
				assert.True(t, hasLargeLine, "%s应该包含超长行", tc.name)
			})
		}
	})

	// 4. MaxDisplayLines功能测试（概念验证）
	t.Run("MaxDisplayLines概念验证", func(t *testing.T) {
		cmd := exec.Command("bash", "-c", `
			# 输出少量行便于观察
			for i in {1..5}; do
				echo "stdout line $i"
				echo "stderr line $i" >&2
			done
		`)

		terminal := createTestTerminal()
		result, err := terminal.ExecuteCmd(cmd, ExecuteCmdOption{
			MaxDisplayLines: 8, // 设置限制
		})

		assert.NoError(t, err, "MaxDisplayLines测试命令应该执行成功")
		assert.NotEmpty(t, result, "输出结果不应为空")

		// 检查最终结果包含所有数据
		allLines := strings.Split(strings.TrimSpace(result), "\n")
		assert.GreaterOrEqual(t, len(allLines), 8, "最终结果应该包含足够的行数")

		// 统计内容
		stdoutLines := 0
		stderrLines := 0
		for _, line := range allLines {
			if strings.Contains(line, "stdout") {
				stdoutLines++
			}
			if strings.Contains(line, "stderr") {
				stderrLines++
			}
		}

		assert.Equal(t, 5, stdoutLines, "应该包含5行stdout输出")
		assert.Equal(t, 5, stderrLines, "应该包含5行stderr输出")
	})

	// 5. 文件处理测试
	t.Run("大文件处理", func(t *testing.T) {
		// 创建测试文件
		tempDir := t.TempDir()

		testFiles := []struct {
			name          string
			content       string
			description   string
			maxDurationMs int
		}{
			{
				name:          "normal.txt",
				content:       strings.Repeat("这是一行普通文本\n", 1000),
				description:   "普通文件",
				maxDurationMs: 100,
			},
			{
				name:          "large_lines.txt",
				content:       strings.Repeat("X", 500*1024) + "\n" + "正常行\n",
				description:   "包含超长行的文件",
				maxDurationMs: 50,
			},
		}

		for _, tf := range testFiles {
			t.Run(tf.description, func(t *testing.T) {
				filePath := filepath.Join(tempDir, tf.name)
				err := os.WriteFile(filePath, []byte(tf.content), 0644)
				require.NoError(t, err, "创建测试文件应该成功")

				cmd := exec.Command("cat", filePath)
				terminal := createTestTerminal()

				start := time.Now()
				result, err := terminal.ExecuteCmd(cmd, ExecuteCmdOption{
					Timeout: 10,
				})
				duration := time.Since(start)

				assert.NoError(t, err, "%s读取应该成功", tf.description)
				assert.NotEmpty(t, result, "文件内容不应为空")
				assert.Less(t, int(duration.Milliseconds()), tf.maxDurationMs,
					"%s读取时间应该在%dms内", tf.description, tf.maxDurationMs)
			})
		}
	})

	// 6. 超时处理测试
	t.Run("超时处理", func(t *testing.T) {
		// 使用多个短暂sleep的循环，而不是单一的长sleep，这样更容易被interrupt
		cmd := exec.Command("bash", "-c", `
			echo "开始长时间任务"
			for i in {1..20}; do
				echo "循环 $i"
				sleep 1
			done
			echo "任务完成"
		`)

		terminal := createTestTerminal()
		start := time.Now()

		result, _ := terminal.ExecuteCmd(cmd, ExecuteCmdOption{
			Timeout: 2, // 2秒超时
		})
		duration := time.Since(start)

		// 根据实际测试：使用多个短sleep的命令更容易被interrupt，应该在6秒内完成
		assert.Less(t, int(duration.Seconds()), 6, "超时处理应该在6秒内完成（实际约5秒+缓冲）")
		assert.NotEmpty(t, result, "超时结果不应为空")

		// 应该包含超时相关信息
		containsTimeoutMsg := strings.Contains(result, "timeout exceeded") ||
			strings.Contains(result, "killed") ||
			strings.Contains(result, "开始长时间任务")
		assert.True(t, containsTimeoutMsg, "结果应该包含超时或任务开始信息")
	})

	// 7. 后台任务测试
	t.Run("后台任务", func(t *testing.T) {
		cmd := exec.Command("bash", "-c", `
			echo "后台任务开始"
			sleep 3
			echo "后台任务结束"
		`)

		terminal := createTestTerminal()
		start := time.Now()

		result, err := terminal.ExecuteCmd(cmd, ExecuteCmdOption{
			Timeout: -1, // -1表示后台运行
		})
		duration := time.Since(start)

		assert.NoError(t, err, "后台任务启动应该成功")
		assert.NotEmpty(t, result, "后台任务结果不应为空")
		assert.Contains(t, result, "PID", "后台任务应该返回PID信息")

		// 后台任务应该立即返回
		assert.Less(t, int(duration.Milliseconds()), 1000,
			"后台任务应该在1秒内返回")
	})

	// 8. 环境变量测试
	t.Run("环境变量", func(t *testing.T) {
		cmd := exec.Command("bash", "-c", `echo "TEST_VAR=$TEST_VAR"`)

		terminal := createTestTerminal()
		result, err := terminal.ExecuteCmd(cmd, ExecuteCmdOption{
			Environment: map[string]string{
				"TEST_VAR": "test_value",
			},
		})

		assert.NoError(t, err, "环境变量测试应该成功")
		assert.Contains(t, result, "TEST_VAR=test_value",
			"输出应该包含正确的环境变量值")
	})

	// 9. 错误处理测试
	t.Run("错误处理", func(t *testing.T) {
		// 不存在的命令
		cmd := exec.Command("非常不可能存在的命令")

		terminal := createTestTerminal()
		_, err := terminal.ExecuteCmd(cmd, ExecuteCmdOption{})

		assert.Error(t, err, "不存在的命令应该返回错误")
		assert.Contains(t, err.Error(), "executable file not found",
			"错误信息应该包含可执行文件未找到的提示")
	})

	// 10. 并发安全测试
	t.Run("并发安全", func(t *testing.T) {
		terminal := createTestTerminal()

		// 并发执行多个命令
		const concurrency = 3
		done := make(chan error, concurrency)

		for i := 0; i < concurrency; i++ {
			go func(id int) {
				cmd := exec.Command("bash", "-c", fmt.Sprintf(`
					echo "任务%d开始"
					sleep 1
					echo "任务%d结束"
				`, id, id))

				_, err := terminal.ExecuteCmd(cmd, ExecuteCmdOption{})
				done <- err
			}(i)
		}

		// 等待所有任务完成并收集结果
		var errors []error
		for i := 0; i < concurrency; i++ {
			err := <-done
			errors = append(errors, err)
		}

		// 验证所有任务都成功完成
		successCount := 0
		for _, err := range errors {
			if err == nil {
				successCount++
			}
		}

		assert.Equal(t, concurrency, successCount,
			"所有并发任务都应该成功完成")
	})
}

// TestLogger 测试用的Logger实现
type TestLogger struct{}

func (t *TestLogger) Trace(args ...any)                 {}
func (t *TestLogger) Debug(args ...any)                 {}
func (t *TestLogger) Info(args ...any)                  {}
func (t *TestLogger) Warn(args ...any)                  {}
func (t *TestLogger) Error(args ...any)                 {}
func (t *TestLogger) Fatal(args ...any)                 {}
func (t *TestLogger) Tracef(format string, args ...any) {}
func (t *TestLogger) Debugf(format string, args ...any) {}
func (t *TestLogger) Infof(format string, args ...any)  {}
func (t *TestLogger) Warnf(format string, args ...any)  {}
func (t *TestLogger) Errorf(format string, args ...any) {}
func (t *TestLogger) Fatalf(format string, args ...any) {}

// EventRecorder 简化的事件记录器，实现必要的接口
type EventRecorder struct{}

func (r *EventRecorder) ReportShellStdio(event iris.EventShellStdio) {
	// 测试中不做实际处理
}

// createTestTerminal 创建用于测试的Terminal实例
func createTestTerminal() *Terminal {
	// 创建真实的AgentEventPublisher（但不连接到实际的客户端）
	testLogger := logrus.New()
	testLogger.SetLevel(logrus.FatalLevel) // 静默模式，只记录严重错误
	publisher := iris.NewAgentEventPublisher(testLogger, iris.NewContextStorage(iris.DefaultStorageDir, testLogger), false)

	return &Terminal{
		Name:    "test-terminal",
		channel: publisher,
		logger:  &TestLogger{},
	}
}


*/
