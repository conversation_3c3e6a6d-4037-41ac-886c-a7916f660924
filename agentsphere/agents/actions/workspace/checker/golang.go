package checker

import (
	"context"
	"fmt"
	"os/exec"
	"regexp"
	"strconv"
	"strings"

	"github.com/pkg/errors"
)

var goFmtErrRe = regexp.MustCompile(`(?m)(^.*):([0-9]+):([0-9]+): (.*)$`)

type Go<PERSON><PERSON><PERSON><PERSON> struct {
	WorkingDirectory string
}

func parseLogs(logs string) []CheckError {
	match := goFmtErrRe.FindAllStringSubmatch(logs, -1)

	errs := make([]CheckError, 0)
	for _, m := range match {
		line, _ := strconv.Atoi(m[2])
		col, _ := strconv.Atoi(m[3])
		if line == 0 {
			continue
		}
		errs = append(errs, CheckError{
			FilePath: m[1],
			Line:     line,
			Col:      col,
			Message:  m[4],
			Type:     CheckErrorTypeInvalidSyntax,
		})
	}
	return errs
}

func (g *GolangChecker) Check(ctx context.Context, path string) (errs []CheckError, err error) {
	if !strings.HasSuffix(path, ".go") {
		return nil, fmt.Errorf("not a go file: %s", path)
	}

	cmd := exec.CommandContext(ctx, "gofmt", "-l", path)
	cmd.Dir = g.WorkingDirectory
	output, err := cmd.CombinedOutput()
	logs := string(output)
	if err != nil {
		errors := parseLogs(logs)
		return errors, fmt.Errorf("go fmt check failed")
	}

	// TODO: try build & test after fmt
	return []CheckError{}, nil
}

func (g *GolangChecker) Format(ctx context.Context, path string) (err error) {
	if !strings.HasSuffix(path, ".go") {
		return nil
	}

	cmd := exec.CommandContext(ctx, "gofmt", "-w", path)
	cmd.Dir = g.WorkingDirectory
	output, err := cmd.CombinedOutput()
	if err != nil {
		logs := string(output)
		return errors.WithMessagef(err, "failed to run go fmt command: \n%s", logs)
	}
	return nil
}
