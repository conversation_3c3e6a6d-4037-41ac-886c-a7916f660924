package planact_agent

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestPlanActAgent_generateFilePath(t *testing.T) {
	a := NewPlanActAgent(t.Name())
	path := a.generateFilePath("investigation.md")
	assert.Equal(t, path, "investigation.md")
	a.websearchReportFileName["investigation.md"] = true
	path = a.generateFilePath("investigation.md")
	assert.Equal(t, path, "investigation_0.md")
	a.websearchReportFileName["investigation_0.md"] = true
	path = a.generateFilePath("investigation.md")
	assert.Equal(t, path, "investigation_1.md")
}
