package prompts

import (
	"embed"
	"text/template"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
)

var (
	//go:embed *
	prompts embed.FS
	Set     = prompt.MustNewPromptSet(prompts, ".")

	// template names
	PlannerSystemPrompt        = "planner_system"
	PlannerUserPrompt          = "planner_user"
	DynamicPlannerSystemPrompt = "dynplanner_system"
	DynamicPlannerUserPrompt   = "dynplanner_user"
	ThoughtPrompt              = "thought"
	EnhanceReqSystemPrompt     = "enhance_req_system"
	EnhanceReqUserPrompt       = "enhance_req_user"
	ExecutionPrompt            = "execution"
	ActorInputPrompt           = "actor_input"
	RefineTaskSystemPrompt     = "refine_task_system"
	RefineTaskUserPrompt       = "refine_task_user"
	UserInterruptPrompt        = "user_interrupt_md"
	PhaseInputPrompt           = "phase_input"
)

func GetPlannerTemplate(variant string) prompt.TemplateSet {
	return prompt.TemplateSet{
		SystemTmpl:  Set.GetTemplate(PlannerSystemPrompt, variant),
		UserTmpl:    Set.GetTemplate(PlannerUserPrompt, variant),
		ThoughtTmpl: Set.GetTemplate(ThoughtPrompt, variant),
	}
}

func GetDynamicPlannerTemplate(variant string) prompt.TemplateSet {
	return prompt.TemplateSet{
		SystemTmpl:  Set.GetTemplate(DynamicPlannerSystemPrompt, variant),
		UserTmpl:    Set.GetTemplate(DynamicPlannerUserPrompt, variant),
		ThoughtTmpl: Set.GetTemplate(ThoughtPrompt, variant),
	}
}

func GetEnhanceReqTemplate(variant string) prompt.TemplateSet {
	return prompt.TemplateSet{
		SystemTmpl:  Set.GetTemplate(EnhanceReqSystemPrompt, variant),
		UserTmpl:    Set.GetTemplate(EnhanceReqUserPrompt, variant),
		ThoughtTmpl: Set.GetTemplate(ThoughtPrompt, variant),
	}
}

func GetRefineTemplate(variant string) prompt.TemplateSet {
	return prompt.TemplateSet{
		SystemTmpl: Set.GetTemplate(RefineTaskSystemPrompt, variant),
		UserTmpl:   Set.GetTemplate(RefineTaskUserPrompt, variant),
	}
}

func GetUserInterruptTemplate(variant string) *template.Template {
	return Set.GetTemplate(UserInterruptPrompt, variant)
}

func GetPhaseInputTemplate(variant string) *template.Template {
	return Set.GetTemplate(PhaseInputPrompt, variant)
}
