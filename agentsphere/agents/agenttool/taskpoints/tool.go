package taskpointsagenttool

import (
	"fmt"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/workspace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/agenttool"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/knowledges"
	planactentity "code.byted.org/devgpt/kiwis/agentsphere/agents/planact/entity"
	"github.com/samber/lo"
)

type TaskPointsToolArgs struct {
	Requirements string `json:"requirements" mapstructure:"requirements" description:"The user original requirements or task description that needs to be analyzed for generating task points"`
	Reference    string `json:"reference" mapstructure:"reference" description:"Reference files or urls that should be considered when generating task points, in markdown format"`
}

func taskPointsInputFunc(run *iris.AgentRunContext, args TaskPointsToolArgs) string {
	input := fmt.Sprintf("Your task is to analyze the following user requirements and reference materials, and generate a checklist of essential, verifiable task items based on them.\n\nRequirements to analyze:\n%s\n\nReference:\n%s\n", args.Requirements, args.Reference)

	// Parse reference files if provided
	refs := iris.ParseReference(args.Reference)
	ws := workspace.GetWorkspace(run)
	files, _ := ws.Editor.ReadMarkdownFiles(workspace.ReadMarkdownFilesArgs{
		Paths: lo.Map(refs, func(ref iris.ReferenceItem, _ int) string {
			return ref.URI
		}),
	})

	if len(files.Files) > 0 {
		input += "Here are some related reference files:\n"
		for _, file := range files.Files {
			input += fmt.Sprintf("\n[file path: %s]\n%s\n", file.FileName, file.Content)
		}
	}

	return input
}

func taskPointsOptionFunc(run *iris.AgentRunContext, args TaskPointsToolArgs) actors.RunOptions {
	return actors.RunOptions{
		//DynamicPersona: "你是一位专业的任务要点分析专家，擅长将复杂的用户需求分解为具体的、可验证的任务要点。你的目标是确保任务规划和执行过程中不遗漏关键要点。",
	}
}

func NewTaskPointsAgentTool(run *iris.AgentRunContext, step *iris.AgentRunStep) iris.Action {
	sysKnowledgesID := []string{
		//"dynamic_5",
		//"dynamic_25",
	}

	referenceStore := iris.RetrieveStoreByKey[planactentity.ReferenceStore](run, planactentity.ReferenceStoreKey)
	if len(referenceStore.SearchedRef.List) > 0 {
		sysKnowledgesID = []string{
			//"dynamic_5_citation",
			//"dynamic_25",
		}
	}

	return agenttool.NewAgentTool[TaskPointsToolArgs](
		taskPointsInputFunc,
		taskPointsOptionFunc,
		agenttool.AgentToolOption{
			Name: "task_points",
			Description: `Task Points tool analyzes user requirements and generates a checklist of mandatory task completion items to ensure comprehensive coverage and verification.

### Key Functions ###
1. **Requirement Breakdown**: Dissect user input into actionable completion items
2. **Task Checklist Generation**: Output each item as a one-line must-do requirement
3. **Quality Assurance**: Ensure each point is concrete, verifiable, and essential

### Output Format ###
The tool returns a checklist:
- One line per item
- Each item is a concrete, verifiable task requirement
- No numbering or extra explanation
- Suitable for guiding agent execution and validating completion

### Usage Examples ###

param="requirements"
Analyze product usage data and generate a weekly performance summary

param="reference"
- [Daily metrics dashboard](./metrics-daily.md)
- [Feature rollout calendar](./release-schedule.md)

Output might include:
- Summarize daily key business metrics (e.g., DAU, retention rate, conversion rate)
- Highlight dates with significant metric changes and provide reason analysis
- Identify major operational activities or version releases impacting metrics
- Generate visual charts to present key trends (e.g., daily trend lines, week-over-week comparisons)
- Provide a concise conclusion summarizing business performance and action recommendations for the week`,
			Toolsets: []string{
				//"files",
				//"terminal",
			},
			CandidateKnowledges: knowledges.LoadKnowledge(),
			SystemKnowledges:    knowledges.FilterKnowledgeItems(knowledges.LoadKnowledge(), sysKnowledgesID),
			ParentStep:          step,
			Persona:             "You are an expert in task analysis and quality assurance. Your task is to convert a user’s task description—along with all relevant background context—into a single, complete, and verifiable **Markdown checklist file**.\n\nThis checklist must be written using proper Markdown syntax (i.e., with `- [ ]` for each item), and it should serve two purposes:\n1. Guide Planners and Actors in executing the task step-by-step;\n2. Act as the official standard for final task validation and acceptance.\n\n**IMPORTANT**: Your final output must be delivered strictly following the `# Output Format` section. That means your checklist should be wrapped inside a tool call (e.g., `create_file`) as specified. Do not directly output the Markdown content.\n\nWhen necessary, retrieve supporting context (documents, URLs, references) via tool calls only—do not ask the user for clarification. Focus only on actionable, logically essential, and verifiable items. Avoid speculative, vague, or redundant content.",
		})
}
