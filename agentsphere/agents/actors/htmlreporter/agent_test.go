//go:build integration

// 如果要运行这个集成测试，就在 go test 添加 `-tags integration` 参数
// 如果要开发这个测试，将上方的 go:build 删除

package reporter

import (
	"context"
	"os"
	"testing"

	actorstest "code.byted.org/devgpt/kiwis/agentsphere/agents/actors/testhelper"
	agententity "code.byted.org/devgpt/kiwis/agentsphere/agents/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/lib/config"
	"github.com/stretchr/testify/require"
)

func TestRepoExplorerAgent(t *testing.T) {
	os.Chdir("../../..")
	wd, err := os.Getwd()
	require.NoError(t, err)

	runtime := actorstest.SetupTestRuntime(actorstest.SetupTestRuntimeOption{
		WorkDir:   wd,
		SessionID: "test",
	})
	agent := New(CreateOption{
		MaxSteps: 10,
	})
	runtime.RegisterAgent(&iris.DefaultConfigurableAgent{
		RunnableAgent: agent.ReAct,
	})

	run, err := runtime.CreateRunContext(context.Background(), entity.RunAgentRequest{
		AgentName:  agent.Name(),
		Parameters: map[string]any{},
		User:       entity.NewUser(entity.UserTypeAgent, "test"),
		Config: &config.AgentRunConfig{
			Model: config.AgentRunLLMConfig{
				Model: string(agententity.Qwen25Coder),
			},
		},
		Environ: map[string]string{},
	})
	require.NoError(t, err)
	conclusion, success := agent.Run(run, nil, "find where codfish.PeripheralClient is initialized")
	t.Logf("conclusion: %s", conclusion)
	require.True(t, success)
	actorstest.AssertConclusion(t, conclusion, "found `codfish.PeripheralClient` is initialized in `./runtime/eventbus/remote/remote_bus.go`")
}
