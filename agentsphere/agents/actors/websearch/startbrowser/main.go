package main

import (
	"context"
	"fmt"
	"os"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/background/browser"
	actorstest "code.byted.org/devgpt/kiwis/agentsphere/agents/actors/testhelper"
	planact_agent "code.byted.org/devgpt/kiwis/agentsphere/agents/planact/agent"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/lib/config"
)

/*
run this file in docker container
docker run -it -v $PWD:/home -w /home -p 9226:9226 hub.byted.org/base/iris_runtime_general:latest bash
*/
func main() {
	wd, _ := os.Getwd()
	runtime := actorstest.SetupTestRuntime(actorstest.SetupTestRuntimeOption{
		WorkDir:   wd,
		SessionID: "local_session_id",
	})
	agent := planact_agent.NewPlanActAgent(planact_agent.AgentIDV1)
	runtime.RegisterAgent(agent)
	runtime.Ready()
	runContext, err := runtime.CreateRunContext(context.Background(), entity.RunAgentRequest{
		AgentName: planact_agent.AgentIDV1,
		Config:    &config.AgentRunConfig{},
	})
	_ = runContext
	if err != nil {
		panic(err)
	}
	err = os.MkdirAll("/workspace/log", 0755)
	if err != nil {
		panic(err)
	}
	fmt.Printf("start browser mcp service\n")
	f, err := os.OpenFile("/workspace/log/browse-use.log-9227", os.O_RDWR|os.O_CREATE|os.O_APPEND, 0644)
	if err != nil {
		panic(err)
	}
	f.Close()
	_, err = browser.StartMCPService(runContext)
	if err != nil {
		runContext.GetLogger().Errorf("failed to start browser mcp service: %v", err)
		panic(err)
	}
	fmt.Printf("browser is running")
	// 可能是某些 bug 导致有多个 browser-use 实例，先删掉然后上线
	_ = os.Remove("/root/.config/browseruse/profiles/default/SingletonLock")
	a := make(chan interface{})
	<-a
}
