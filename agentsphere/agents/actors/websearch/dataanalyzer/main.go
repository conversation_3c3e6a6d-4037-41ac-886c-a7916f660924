package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"net/http"
	"sync"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/genexp"
	"github.com/samber/lo"
)

func main() {
	var sessionToken string
	flag.StringVar(&sessionToken, "token", "", "session token")
	flag.Parse()
	StartTokenMonitor(sessionToken)
	trajectory, err := GetTrajectory(flag.Args()[0], GetJWTToken())
	if err != nil {
		panic(err)
	}
	researchToolInputs := make([]map[string]any, 0)
	for _, node := range trajectory {
		round := node.Round
		if round == nil {
			continue
		}
		if !lo.Contains(round.Plan.Tools, "research") {
			continue
		}
		for _, detailRound := range round.Execution.Detail.Rounds {
			if detailRound.Action.Tool != "research" {
				continue
			}
			researchToolInputs = append(researchToolInputs, detailRound.Action.Parameters)
		}
	}
	marshal, err := json.Marshal(researchToolInputs)
	if err != nil {
		panic(err)
	}
	fmt.Printf("%s", string(marshal))
}

func StartTokenMonitor(sessionToken string) {
	refreshToken(sessionToken)
	go func() {
		for {
			<-time.After(time.Hour)
			refreshToken(sessionToken)
		}
	}()
}
func refreshToken(sessionToken string) {
	jwt, err := getBytecloudUserJWT(sessionToken)
	if err != nil {
		fmt.Printf("error: %v\n", err)
		return
	}
	jwtTokenLock.Lock()
	jwtToken = jwt
	jwtTokenLock.Unlock()
}

// 获取 JWT Token 的函数，传入 session 字符串，返回 token 和错误
func getBytecloudUserJWT(session string) (string, error) {
	url := "https://cloud.bytedance.net/auth/api/v1/jwt"

	// 创建请求
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return "", err
	}

	// 设置 Cookie 头
	cookieValue := fmt.Sprintf("__Host_CAS_SESSION_ss=%s; CAS_SESSION=%s; CAS_SESSION_ss=%s;", session, session, session)
	req.Header.Set("Cookie", cookieValue)

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 读取响应体（可选，方便调试）
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	fmt.Println("Response body:", string(body))

	// 获取 X-Jwt-Token 响应头
	jwtToken := resp.Header.Get("X-Jwt-Token")
	if jwtToken == "" {
		return "", fmt.Errorf("X-Jwt-Token header not found")
	}

	return jwtToken, nil
}

var jwtToken = ""
var jwtTokenLock = &sync.RWMutex{}

func GetJWTToken() string {
	jwtTokenLock.RLock()
	defer jwtTokenLock.RUnlock()
	return jwtToken
}

func GetTrajectory(sessionID string, token string) ([]genexp.TaskTrajectoryNode, error) {
	url := fmt.Sprintf("https://aime.bytedance.net/api/agents/v2/ops/sessions/%s/trajectory", sessionID)

	client := &http.Client{}
	req, err := http.NewRequest(http.MethodGet, url, nil)

	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", "Byte-Cloud-JWT "+token)

	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		return nil, err
	}
	var data struct {
		Data []genexp.TaskTrajectoryNode `json:"data"`
	}
	err = json.Unmarshal(body, &data)
	if err != nil {
		return nil, err
	}
	return data.Data, nil
}
