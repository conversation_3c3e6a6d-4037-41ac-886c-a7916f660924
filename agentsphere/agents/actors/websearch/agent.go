package websearch

import (
	"context"
	_ "embed"
	"fmt"
	"regexp"
	"strings"
	"sync"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/background/browser"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/agentrace"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"golang.org/x/exp/slices"
	"golang.org/x/sync/errgroup"

	"code.byted.org/devgpt/kiwis/lib/metrics"

	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/gopkg/lang/v2/mathx"
	"code.byted.org/gopkg/logid"

	controltool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/control"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/util"
	runtimeentity "code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/devgpt/kiwis/lib/tokenizer"
)

var (
	Identifier = "web_searcher"
	//go:embed description.go.tmpl
	description string
	//go:embed description.v1_5.go.tmpl
	descriptionV15 string
	parameters     = []actors.ParameterDescription{
		{
			Name:        "task",
			Description: "Detailed description of the task, with the language the same as the user's input",
		},
	}
)
var _ actors.Actor = &Agent{}

const (
	InternalSearchMCPID              = "aime_enable_internal_search"
	EnableInternalSearchParameterKey = "enable_internal_search"
)

type SearchError string

func (s SearchError) Error() string {
	return string(s)
}

type Agent struct {
	actors.BaseActor
	mcpMode bool
	llm     framework.LLM
	logger  iris.Logger
	config  *iris.AgentRunConfig
	// enableBytedSearch is used to enable byted search.
	enableBytedSearch bool
	maxToken          int
	Local             bool

	languageStyle         string
	languageCode          string
	cloudToken            string
	larkToken             string
	step                  *iris.AgentRunStep
	stepID                string
	agentCtx              *iris.AgentRunContext
	readURLCache          sync.Map
	searchURLsCounter     map[string]int
	searchURLsCounterLock sync.Mutex
	toolCallLock          sync.Mutex
}
type CreateOption struct {
	EnableBytedSearch bool
	DeepOnly          bool
	ActorStep         *iris.AgentRunStep
}

func New(option CreateOption) *Agent {
	return &Agent{
		BaseActor: actors.BaseActor{
			AgentInfo: iris.AgentInfo{
				Identifier: Identifier,
				Desc:       lo.Ternary(option.DeepOnly, descriptionV15, description),
			},
			Parameters: parameters,
			ActorStep:  option.ActorStep,
		},
		searchURLsCounter: map[string]int{},
		enableBytedSearch: option.EnableBytedSearch,
	}
}

var _ Context = (*iris.AgentRunContext)(nil)

type Context interface {
	context.Context
	GetLogger() iris.Logger
	GetLLM() framework.LLM
	GetEnv(string) string
	GetConfig() *iris.AgentRunConfig
}

func (a *Agent) Run(ctx *iris.AgentRunContext, input string, options actors.RunOptions) (result controltool.ConclusionOutput) {
	a.step = a.ActorStep
	a.stepID = a.ActorStep.StepID
	a.agentCtx = ctx
	var err error
	response, err := a.InternalRunWithPlanFirst(ctx, input)
	if err != nil {
		var searchError SearchError
		if errors.As(err, &searchError) {
			a.logger.Warnf("search error: %v", searchError)
			result = controltool.ConclusionOutput{
				Content:    searchError.Error(),
				Evaluation: controltool.ConclusionEvaluationFailed,
			}
			return result
		}
		a.logger.Errorf("failed to run: %+v", err)
		a.agentCtx.GetPublisher().ReportStep(&iris.AgentRunStep{
			Status: iris.AgentRunStepStatusFailed,
			Thought: &iris.Thought{
				Content: fmt.Sprintf("%+v", err),
			},
		})
		a.logger.Errorf("[web search] failed to run: %v", err)
		result = controltool.ConclusionOutput{
			Content:    "没有搜索到内容",
			Evaluation: controltool.ConclusionEvaluationFailed,
		}
		return result
	}
	a.publishThink("[web search] FinalResult: %s", response.Answer)
	var progress string
	for _, subTask := range response.SubTasks {
		if subTask.NoAnswer {
			progress += fmt.Sprintf("\n- [ ] %s", subTask.Task)
			continue
		}
		progress += fmt.Sprintf("\n- [x] %s", subTask.Task)
	}
	a.logger.Infof("[web search] progress: %s", progress)
	result = controltool.ConclusionOutput{
		Content: response.Answer,
		Reference: lo.Map(response.Reference, func(item SearchItem, index int) iris.ReferenceItem {
			return iris.ReferenceItem{
				URI:      item.NormalizedURL,
				Title:    item.Title,
				MetaData: util.ValueToMap(item),
			}
		}),
		Evaluation: controltool.ConclusionEvaluationSuccess,
		Progress:   progress,
	}
	if ctx.Err() != nil {
		result.Evaluation = controltool.ConclusionEvaluationFailed
	}
	return result
}

func getComposeUserPrompt(originalTask, thinking string, subTasks []SubTaskResult) string {
	var subTaskContent string
	for idx, answer := range subTasks {
		subTaskContent += fmt.Sprintf(`<sub-task-%d>
%s
<answer>
%s
</answer>
</sub-task-%d>
`, idx+1, answer.Task, answer.Answer, idx+1)
	}
	return fmt.Sprintf(`
**Previoud Thinking:**
%s
**Sub-tasks and Answers:**
%s
**Original Task:**
%s
`, thinking, subTaskContent, originalTask)
}

//go:embed compose_system_prompt.go.tmpl
var composeSystemPrompt string

func getComposeSystemPrompt(languageCode string) string {
	return fmt.Sprintf(composeSystemPrompt, languageCode)
}
func getAnswerWithPlanUserPrompt(originalTask string, allKnowledge []SearchItem, think string) string {
	var knowledgeInfos []string
	for idx, value := range allKnowledge {
		knowledgeInfos = append(knowledgeInfos, fmt.Sprintf(`
<info-%d>
<title>%s</title>
<url>%s</url>
<content>%s</content>
</info-%d>
`, idx+1, value.Title, value.URL, value.Description, idx+1))
	}
	userPrompt := fmt.Sprintf(`
<think>
%v
</think>

<search-info>
%v
<search-info>

%v
`, think, strings.Join(knowledgeInfos, "\n"), originalTask,
	)
	return userPrompt
}

func getAnswerWithPlanSystemPrompt(languageCode string) string {
	var sections []string

	sections = append(sections, fmt.Sprintf(`Current date: %s

You are an advanced AI research agent. Your objective is to generate a comprehensive and highly accurate report, which answers the user's question based *solely* on the provided search info snippets. Aim for a response grounded in certainty derived *only* from the supplied information. 
`, nowString()))
	sections = append(sections, fmt.Sprintf(`
<rules>
1. **Accuracy and Filtering:**
    * Strictly filter the search info snippets. Reference *only* those snippets that directly answer the user's question OR provide essential background/context necessary to understand the answer.
    * Snippets that are merely related to the topic but do not contribute directly to answering the question or providing essential context are considered interference and must be ignored.
    * Ensure every statement in your report is directly supported by the selected search info snippets. Do not introduce outside information or assumptions.
2. **No Answer Condition:**  If the provided information is completely insufficient to answer the user's question, respond with "no_answer" and no other content.

3. **Comprehensiveness and Detail:**
    * The final report must be detailed and comprehensive. Go beyond a minimal answer.
    * Provide relevant context, background information, definitions of key terms, and supporting details *as found within the selected search info snippets*.
    * Explain *why* the answer is what it is, based on the evidence in the knowledge.

4. **Synthesis and Structure:**
    * Synthesize information from multiple relevant snippets to create a coherent and logical narrative. Do not just list isolated facts.
    * Structure the report logically using paragraphs and bullet points for clarity, following paper style. 
    * Use Markdown formatting for readability where appropriate (e.g., headings, lists, bold text).

5. **Certainty:** Frame your answer with high confidence, reflecting that it is based *exclusively* on the provided info. If the search info is insufficient or contradictory for a certain answer, state that clearly.

6. **Language:** Keep the same language with user's question, the current language code is %s.
</rules>
`, languageCode))
	sections = append(sections, `<citation>
- Requirements: If the model's response references a info fragment, a citation must be provided. However, do not cite info that is irrelevant to the user's query.
- Citation Scope: Citations should only refer to info fragments within the <search-info>. <search-info> is in XML format with list of info. Each info is in <info-{id}> XML segment.
- Format: The format for a single citation is "[1]", and for multiple citations, it is "[1][2]"  "1" and "2" represent the IDs of the info fragments cited in the sentence.
- Avoid Invalid Citations: Do not generate empty or invalid citations (e.g., "[None]" or "[无]").

**--- STRICT FORMATTING RULES ---**
	a. **Mandatory Bracket Style:** ALL citation markers MUST use standard square brackets [...]. No other bracket types or escaped brackets (like \[...\]) are permitted.
	b. **Single Citation Format:** A single citation supporting a piece of information MUST be formatted as [N], where N is the source number (e.g. [1][5][23]).
	c. **Multiple Citation Format:** If multiple different source numbers (e.g. [1], [5], [6]) support the exact same piece of information, they MUST be represented as separate, adjacent, individually bracketed numbers. For example: [1][5][6].
	d. **Strictly Forbidden Formats:** The following formats are ABSOLUTELY FORBIDDEN and MUST NOT be used:
		· Comma-separated lists within single brackets (e.g., [1, 5, 6], [1,5,6], [1, 2]).
		· Escaped brackets (e.g. \[1\], \[1]\[5]).
		· Repeated citation (e.g. [1][1], [3][1][3], [3][4][5][1][6][7][60][7])
		· Any other variation not explicitly matching format [N] or [N][M]...
**--- PLACEMENT ---**
    · Integrate the correctly formatted citation(s) into the report text immediately following the specific fact or statement they pertain to, with no leading space before the first bracket (e.g., ...statement[1], ...statement[1][5]). Crucially, when integrating information or if the source answers lead to identical citations appearing immediately next to each other (e.g., [1][1]), consolidate these into a single instance (e.g., [1]). The goal is to accurately represent the sourcing, not to duplicate citation markers unnecessarily.
**--- EXAMPLE ---**
	· CORRECT: Shanghai offers diverse cuisine[2][7].
	· CORRECT: The Bund is a famous landmark[1].
    · INCORRECT: Shanghai offers diverse cuisine[75][6][75].
	· INCORRECT: The west side of the Bund features 52 classical revival buildings with various styles, including Gothic, Romanesque, Baroque, and a blend of Chinese and Western styles [3][4][5][1][6][7][60][7].
</citation>
`)
	return removeExtraLineBreaks(strings.Join(sections, "\n\n"))

}

type SubTaskResult struct {
	Task string
	// Answer to the Task.
	Answer string
	// No Answer is true if the knowledge cannot answer the task.
	NoAnswer  bool
	Knowledge []SearchItem
}

func (a *Agent) initContext(ctx Context) error {
	if !a.Local {
		browser.WaitForMCPService(a.agentCtx)
	}
	a.logger = ctx.GetLogger()
	a.llm = ctx.GetLLM()
	a.cloudToken = ctx.GetEnv(runtimeentity.RuntimeEnvironUserCloudJWT)
	a.larkToken = ctx.GetEnv(runtimeentity.RunTimeLarkUserAccessToken)
	a.config = ctx.GetConfig()
	modelConfig := ctx.GetConfig().GetModelByScene("web_searcher")
	a.maxToken = int(modelConfig.PromptMaxTokens)

	if a.maxToken <= 0 {
		a.maxToken = 200_000
		a.logger.Infof("use max token: %d", a.maxToken)
	}
	return nil
}

func (a *Agent) InternalRunWithPlanFirst(c Context, input string) (response *Response, err error) {
	if err = a.initContext(c); err != nil {
		return nil, errors.WithMessage(err, "failed to init context")
	}
	var (
		span agentrace.Span
		ctx  context.Context
	)
	span, ctx = agentrace.GetRuntimeTracerFromContext(c).StartCustomSpan(c, agentrace.SpanTypeStep, "run",
		agentrace.WithSpanID(a.stepID), agentrace.WithObjectSpanData(map[string]any{"input": input}))
	defer func() {
		agentrace.AddErrorTag(span, err)
		span.Finish()
	}()
	response, err = a.internalRunWithPlanFirst(ctx, input)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	span.UpdateData(agentrace.NewObjectSpanData(map[string]any{"answer": response.Answer}))
	return response, err
}

func (a *Agent) internalRunWithPlanFirst(ctx context.Context, input string) (response *Response, err error) {
	logID, ok := ctxvalues.LogID(ctx)
	if !ok {
		logID = logid.GenLogID()
		ctx = ctxvalues.SetLogID(ctx, logID)
	}
	ctx = setLogger(ctx, a.logger)
	start := time.Now()
	defer func() {
		_ = metrics.AR.ActorWebSearcherPhraseCost.WithTags(&metrics.AgentActorWebSearcherPhraseTag{
			Phrase: "whole_run",
		}).Observe(float64(time.Since(start).Milliseconds()))
	}()
	a.logger.Infof("websearch: %s, log_id: %s", input, logID)
	query := extractQuery(input)
	originalTask := strings.TrimSpace(query)
	getLanguageResult, err := a.getLanguage(ctx, originalTask)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	a.languageStyle, a.languageCode = getLanguageResult.LangStyle, getLanguageResult.LangCode
	a.logger.Infof("got language: %s, %s", a.languageCode, a.languageStyle)

	planGroup := &errgroup.Group{}

	var subTasks []string
	var think string
	var isOnlyInternal bool

	planGroup.Go(func() error {
		subTasks, think, err = a.Plan(ctx, originalTask)
		if err != nil {
			a.logger.Errorf("[InternalRunWithPlanFirst] Plan err: %v", err)
			return nil
		}
		a.logger.Infof("[InternalRunWithPlanFirst] plan: %v, think %v", subTasks, think)
		return nil
	})

	planGroup.Go(func() error {
		var err error
		var think string
		isOnlyInternal, think, err = a.IsOnlyInternal(ctx, originalTask)
		if err != nil {
			isOnlyInternal = false
			a.logger.Errorf("[InternalRunWithPlanFirst] isOnlyInternal err: %v", err)
			return nil
		}
		a.logger.Infof("[InternalRunWithPlanFirst] internal source detect: %v, think %v", isOnlyInternal, think)
		return nil
	})
	_ = planGroup.Wait()
	if err != nil {
		return nil, errors.WithMessage(err, "failed to plan")
	}
	if !a.enableBytedSearch && isOnlyInternal {
		a.logger.Infof("abort search")
		return nil, SearchError("无法完成检索请求，因为用户禁用了内部检索工具")
	}

	stepTaskMaxToken := mathx.MinInteger(a.maxToken, 50_000)
	a.logger.Infof("[web search] input question is: %s", originalTask)
	subTaskGroup := &errgroup.Group{}
	subTaskGroup.SetLimit(2)
	var subTaskResults []SubTaskResult
	var subTaskLock sync.RWMutex
	for _, task := range subTasks {
		task := task
		subTaskGroup.Go(func() error {
			ctx := a.newContext(ctx)
			span, ctx := agentrace.GetRuntimeTracerFromContext(ctx).StartCustomSpan(ctx, agentrace.SpanTypeStep, "sub_task", agentrace.WithObjectSpanData(map[string]any{"sub_task": task}))
			a.ctxInfof(ctx, "start to process task: %s", task)
			subTaskResult, err := a.subTask(ctx, task, think, stepTaskMaxToken, isOnlyInternal, true)
			if err != nil {
				a.logger.Errorf("failed to process task: %s, err: %v", task, err)
				return nil
			}
			span.UpdateData(agentrace.NewObjectSpanData(map[string]any{"answer": subTaskResult.Answer, "no_answer": subTaskResult.NoAnswer}))
			span.Finish()
			subTaskLock.Lock()
			subTaskResults = append(subTaskResults, subTaskResult)
			subTaskLock.Unlock()
			return nil
		})
	}
	_ = subTaskGroup.Wait()
	var squashedCitations []SearchItem
	subTaskResults, squashedCitations = rearrangeCitations(subTaskResults)
	answer, err := a.generate(ctx, []tool{answerTool, backupTool}, getComposeSystemPrompt(a.languageCode), getComposeUserPrompt(originalTask, think, subTaskResults))
	if err != nil {
		a.logger.Errorf("failed to compose answer: %+v", err)
		answer = strings.Join(lo.Map(subTaskResults, func(item SubTaskResult, index int) string {
			return fmt.Sprintf("## The answer to the question: '%s'\n%s", item.Task, item.Answer)
		}), "\n\n")
	}
	answer, squashedCitations = processBrackets(ctx, answer, squashedCitations)

	return &Response{
		Answer:    answer,
		Reference: squashedCitations,
		SubTasks:  subTaskResults,
	}, nil
}

func (a *Agent) subTask(ctx context.Context, task string, think string, stepTaskMaxToken int, isOnlyInternal bool, needSummary bool) (SubTaskResult, error) {
	select {
	case <-ctx.Done():
		return SubTaskResult{}, ctx.Err()
	default:
	}
	subTaskResult := SubTaskResult{
		Task: task,
	}
	taskStart := time.Now()
	defer func() {
		_ = metrics.AR.ActorWebSearcherPhraseCost.WithTags(&metrics.AgentActorWebSearcherPhraseTag{
			Phrase: "sub_task",
		}).Observe(float64(time.Since(taskStart).Milliseconds()))
	}()
	result, err := a.queryRewrite(ctx, task, think)
	if err != nil {
		a.logger.Errorf("[InternalRunWithPlanFirst] generateObject err: %v", err)
		return subTaskResult, errors.WithMessage(err, "failed to generate object")
	}
	if len(result.Queries) == 0 {
		a.logger.Errorf("[InternalRunWithPlanFirst] generateObject result query is none")
		return subTaskResult, errors.Errorf("failed to generate object: query is none")
	}

	// 处理当前 Task 真正需要的知识，形成子报告
	var taskKnowledge []string
	stepTokenCountStr := ""
	items, err := a.SearchV2(ctx, task, result.Queries, isOnlyInternal)
	if err != nil {
		a.logger.Errorf("[InternalRunWithPlanFirst] search err: %v", err)
		return subTaskResult, errors.WithMessage(err, "failed to search result")
	}
	for idx, item := range items {
		k := fmt.Sprintf("%v\n%v", item.Title, item.Description)
		if slices.Contains(taskKnowledge, k) {
			continue
		}
		if idx == 0 && countToken(k) > stepTaskMaxToken {
			break
		}
		if countToken(stepTokenCountStr+k) > stepTaskMaxToken {
			break
		}
		taskKnowledge = append(taskKnowledge, k)
		stepTokenCountStr += k
		subTaskResult.Knowledge = append(subTaskResult.Knowledge, *item)
	}
	if !needSummary {
		return subTaskResult, nil
	}
	answerToSubTask, err := a.answerSubTask(ctx, task, subTaskResult.Knowledge, think)
	if err != nil {
		a.logger.Errorf("failed to generate answer: %+v", err)
		return subTaskResult, errors.WithStack(err)
	}
	subTaskResult.Answer = answerToSubTask
	if strings.TrimSpace(answerToSubTask) == noAnswerResponse {
		subTaskResult.NoAnswer = true
	}
	a.logger.Infof("sub task report: %s: %s", subTaskResult.Task, answerToSubTask)
	return subTaskResult, nil
}

func (a *Agent) queryRewrite(ctx context.Context, task string, think string) (result rewriteQueryResponse, err error) {
	span, ctx := agentrace.GetRuntimeTracerFromContext(ctx).StartCustomSpan(ctx, agentrace.SpanTypeStep, "query_rewrite", agentrace.WithObjectSpanData(map[string]any{"task": task}))
	defer func() {
		agentrace.AddErrorTag(span, err)
		span.Finish()
	}()
	prompt := getQueryRewritePrompt(task, think)
	err = a.generateObject(ctx, queryRewriterTool, a.getQueryRewriterSchema(), prompt.System, prompt.User, &result)
	if err != nil {
		return result, nil
	}
	span.UpdateData(agentrace.NewObjectSpanData(map[string]any{"queries": result.Queries, "think": result.Think}))
	return result, err
}

func (a *Agent) recordURL(url string) int {
	a.searchURLsCounterLock.Lock()
	a.searchURLsCounter[url]++
	c := a.searchURLsCounter[url]
	a.searchURLsCounterLock.Unlock()
	return c
}

func (a *Agent) createAndRunningStep(input map[string]interface{}) *stepReporter {
	if a.agentCtx != nil {
		parent := a.step
		if a.mcpMode {
			parent = a.agentCtx.GetStep(parent.Parent)
		}
		step := a.agentCtx.CreateStep(&iris.CreateStepOption{
			ExecutorAgent: a.Name(),
			Parent:        parent,
		})
		stepReporter := &stepReporter{
			step:     step,
			agentCtx: a.agentCtx,
		}
		stepReporter.Update(iris.AgentRunStepStatusCreated)
		step.Inputs = input
		stepReporter.Update(iris.AgentRunStepStatusRunning)
		return stepReporter
	}
	return &stepReporter{}
}

var inputReg = regexp.MustCompile(`(?s)<task>(.*?)</task>`)

func extractQuery(text string) string {
	matches := inputReg.FindStringSubmatch(text)
	if len(matches) > 1 {
		return strings.TrimSpace(matches[1])
	}
	return text
}

const (
	maxQueriesPerStep = 7
)

type Response struct {
	Answer    string
	Reference []SearchItem
	SubTasks  []SubTaskResult
}

func nowString() string {
	return time.Now().UTC().Format(time.RFC1123)
}

func removeExtraLineBreaks(s string) string {
	return strings.ReplaceAll(strings.ReplaceAll(s, "\n\n\n", "\n\n"), "\n\n\n", "\n\n")
}

func countToken(prompt string) int {
	// The tokenize vocabulary is GPT's. It's better chose by model.
	return tokenizer.CountTokensByTokenizer(prompt)
}
