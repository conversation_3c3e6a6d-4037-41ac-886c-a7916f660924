package main

import (
	"bytes"
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"net/http"
	"os"
	"time"

	actorstest "code.byted.org/devgpt/kiwis/agentsphere/agents/actors/testhelper"
	planact_agent "code.byted.org/devgpt/kiwis/agentsphere/agents/planact/agent"
	"code.byted.org/devgpt/kiwis/lib/config"

	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/devai"

	"github.com/pkg/errors"
	"github.com/sourcegraph/conc/panics"

	"github.com/samber/lo"
	"github.com/sashabaranov/go-openai"
	log "github.com/sirupsen/logrus"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors/websearch"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	runtimeentity "code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/devgpt/kiwis/lib/stream"
	"gopkg.in/yaml.v3"
)

type Report struct {
	Answer  string `json:"answer"`
	UseTime int    `json:"use_time"`
}

type Config struct {
	LarkToken    string `yaml:"LarkToken"`
	DefaultModel string `yaml:"DefaultModel"`
	Models       []struct {
		Scene string `yaml:"Scene"`
		Model string `yaml:"Model"`
	} `yaml:"Models"`
}

func main() {
	var (
		configPath string
	)
	flag.StringVar(&configPath, "config", "config.yaml", "config file path")
	flag.Parse()

	// Load config
	configFile, err := os.ReadFile(configPath)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to read config file: %v\n", err)
		os.Exit(1)
	}
	var conf Config
	err = yaml.Unmarshal(configFile, &conf)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to parse config file: %v\n", err)
		os.Exit(1)
	}

	larkToken := conf.LarkToken
	if len(larkToken) > 0 {
		err := checkLarkToken(larkToken)
		fmt.Printf("visit https://open.larkoffice.com/document/server-docs/docs/drive-v1/export_task/download?appId=cli_9a31b280a1f3d101 to refresh token")
		if err != nil {
			panic(err)
		}
	} else {
		log.Warnf("lark token is empty, bytedance search is disabled")
	}
	agent := websearch.New(websearch.CreateOption{
		EnableBytedSearch: len(larkToken) > 0,
	})
	agent.Local = true
	start := time.Now()

	// Prepare models by scene
	models := map[string]string{}
	for _, m := range conf.Models {
		models[m.Scene] = m.Model
	}

	wd, _ := os.Getwd()
	runtime := actorstest.SetupTestRuntime(actorstest.SetupTestRuntimeOption{
		WorkDir:   wd,
		SessionID: "local_session_id",
	})
	runtime.RegisterAgent(planact_agent.NewPlanActAgent(planact_agent.AgentIDV1))

	// The following oConfig types are not defined, so we use the config values directly
	// and pass them to the agent as needed. If you need to use specific types, import or define them.

	// For demonstration, we skip sceneSpec and related code, and just use conf.DefaultModel
	agentRunConfig := &config.AgentRunConfig{}
	agentRunConfig.ModelScenesConfig.SceneSpecifications = nil
	for _, m := range conf.Models {
		agentRunConfig.ModelScenesConfig.SceneSpecifications = append(agentRunConfig.ModelScenesConfig.SceneSpecifications, config.ScenesModelConfig{
			Scenes:      []string{m.Scene},
			ModelConfig: config.ModelDetailConfig{Name: m.Model},
		})
	}
	runContext, err := runtime.CreateRunContext(context.Background(), runtimeentity.RunAgentRequest{
		AgentName: planact_agent.AgentIDV1,
		Config:    agentRunConfig,
		Environ: map[string]string{
			runtimeentity.RuntimeEnvironUserCloudJWT: "",
			runtimeentity.RunTimeLarkUserAccessToken: larkToken,
		},
		ExitOnFinish: false,
	})
	if err != nil {
		panic(err)
	}
	var a struct {
		Task     string   `json:"Task"`
		SubTasks []string `json:"SubTasks"`
	}
	err = json.NewDecoder(os.Stdin).Decode(&a)
	runContext.State.CurrentStep = &iris.AgentRunStep{StepID: "step_1"}
	response, err := agent.InternalSimpleAIResearch(runContext, a.Task,
		a.SubTasks)
	if err != nil {
		panic(err)
	}
	fmt.Printf("final answer:\n%s\n", response.FinalReport)
	report := Report{
		Answer:  response.FinalReport,
		UseTime: int(time.Now().Sub(start).Seconds()),
	}
	marshal, err := json.Marshal(report)
	if err != nil {
		panic(err)
	}
	fmt.Printf("%s", string(marshal))
}

type Context struct {
	*iris.AgentRunContext
	c              *openai.Client
	model          string
	modelsByScenes map[string]string
	cloudToken     string
	larkToken      string
}

func (c *Context) ChatCompletion(ctx context.Context, promptMsgs []*framework.ChatMessage, opt framework.LLMCompletionOption) (*framework.LLMResult, error) {
	response, err := c.c.CreateChatCompletion(ctx, openai.ChatCompletionRequest{
		Model: opt.Model,
		Messages: lo.Map(promptMsgs, func(item *framework.ChatMessage, index int) openai.ChatCompletionMessage {
			return openai.ChatCompletionMessage{
				Role:    item.Role,
				Content: item.Content,
			}
		}),
		MaxTokens:   int(opt.MaxTokens),
		Temperature: opt.Temperature,
		TopP:        lo.FromPtr(opt.TopP),
	})
	if err != nil {
		return nil, err
	}
	return &framework.LLMResult{
		Content: response.Choices[0].Message.Content,
		TokenUsage: &framework.TokenUsage{
			PromptTokens:     response.Usage.PromptTokens,
			CompletionTokens: response.Usage.CompletionTokens,
			TotalTokens:      response.Usage.TotalTokens,
		},
	}, nil
}

func (c *Context) ChatCompletionStream(ctx context.Context, promptMsgs []*framework.ChatMessage, opt framework.LLMCompletionOption) *stream.RecvChannel[framework.LLMChunk] {
	streamResponse, err := c.c.CreateChatCompletionStream(ctx, openai.ChatCompletionRequest{
		Model: opt.Model,
		Messages: lo.Map(promptMsgs, func(item *framework.ChatMessage, index int) openai.ChatCompletionMessage {
			return openai.ChatCompletionMessage{
				Role:    item.Role,
				Content: item.Content,
			}
		}),
		MaxTokens:   int(opt.MaxTokens),
		Temperature: opt.Temperature,
		TopP:        lo.FromPtr(opt.TopP),
	})
	send, recv := stream.NewChannel[framework.LLMChunk](10)
	if err != nil {
		send.ErrorChannel <- err
		return recv
	}
	go panics.Try(func() {
		for {
			recvOk, err := streamResponse.Recv()
			if err != nil {
				if errors.Is(err, io.EOF) {
					close(send.DataChannel)
				} else {
					send.ErrorChannel <- err
				}
				return
			}
			send.DataChannel <- framework.LLMChunk{
				Content: recvOk.Choices[0].Delta.Content,
			}
		}
	})
	return recv
}

func (c *Context) GetLLM() framework.LLM {
	return c
}

func (c *Context) GetAgentRunContext() *iris.AgentRunContext {
	return c.AgentRunContext
}

func checkLarkToken(token string) error {
	r := devai.CheckLarkTokenRequest{
		Token: token,
	}
	reqPayload, err := json.Marshal(r)
	if err != nil {
		panic(err)
	}
	req, err := http.NewRequestWithContext(context.Background(), http.MethodPost,
		"https://bitsai.bytedance.net/openapi/knowledge/v1/debug/check_lark_token", bytes.NewReader(reqPayload))
	if err != nil {
		return err
	}
	req.Header.Set("content-type", "application/json")
	do, err := http.DefaultClient.Do(req)
	if err != nil {
		return err
	}
	if do.StatusCode != http.StatusOK {
		respBody, err := io.ReadAll(do.Body)
		if err != nil {
			return err
		}
		return errors.Errorf("Bad Status: %s, body: %s", do.Status, string(respBody))
	}
	return nil
}
