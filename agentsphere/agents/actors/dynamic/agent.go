package dynamicactor

import (
	"embed"
	"strings"
	"time"

	"github.com/samber/lo"
	"golang.org/x/sync/errgroup"

	controltool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/control"
	action_prompts "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/prompts"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/agenttool"
	agents "code.byted.org/devgpt/kiwis/agentsphere/agents/core"
	agententity "code.byted.org/devgpt/kiwis/agentsphere/agents/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/agentrace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/telemetry"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/knowledges"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/knowledges/experience"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/memory"
	planactentity "code.byted.org/devgpt/kiwis/agentsphere/agents/planact/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/util"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	condenser "code.byted.org/devgpt/kiwis/agentsphere/memory/condenser/impl"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/mapstructure"
	"code.byted.org/devgpt/kiwis/search/dal/llm"
)

var (
	Identifier = "mewtwo"
	parameters = []actors.ParameterDescription{
		{
			Name:        "task",
			Description: "A self contained task prompt that can be completed by the agent. The description should give out background context and the specific goals, but not detailed datapoints or libraries to use.",
		},
		{
			Name:        "persona",
			Description: "Persona to put at the very first of the system prompt. Describe the agent's role, capabilities, and any other relevant information",
		},
		{
			Name:        "tools_selection_thought",
			Description: "Iterate through the toolset list ONE BY ONE, consider each toolset's suitability for the task. Format: `<toolset_name>:yes/maybe/nice to have/no`. Do not skip any toolset.",
		},
		{
			Name:        "tools",
			Description: "Tools given to the created agent in comma separated strings, e.g. `tool1,tool2`. Include all toolsets possible to be used by the agent",
		},
	}
	parametersNewbie = []actors.ParameterDescription{
		{
			Name:        "task",
			Description: "A self contained task prompt that can be completed by the agent. The description should give out background context ,the specific goals and `Important Notes`, but not detailed datapoints or libraries to use. `Important Notes` section should containing all relevant items from the `Important Notes` section of the [Problem Statement] that pertain to the current task. At the same time, add some important points of attention that can help complete the task with higher quality in this section too.",
		},
		{
			Name:        "persona",
			Description: "Persona to put at the very first of the system prompt. Describe the agent's role, capabilities, and any other relevant information. Should establish: 1) Specific expertise domain and professional level 2) Working methodology and quality standards 3) Problem-solving approach and attention to detail 4) Communication style that balances clarity with technical accuracy. The persona should inspire confidence while setting high performance expectations.",
		},
		{
			Name:        "tools_selection_thought",
			Description: "Iterate through the toolset list ONE BY ONE, consider each toolset's suitability for the task. Format: `<toolset_name>:yes/maybe/nice to have/no`. Do not skip any toolset.",
		},
		{
			Name:        "tools",
			Description: "Tools given to the created agent in comma separated strings, e.g. `tool1,tool2`. Include all toolsets possible to be used by the agent",
		},
	}
	//go:embed prompts
	prompts   embed.FS
	promptset = prompt.MustNewPromptSet(prompts, "prompts")

	_ = lo.Must(promptset.ExecutePrompt("description", "", nil))
)

type DynamicAgentStore struct {
	CurrentTask string
}

type Agent struct {
	actors.BaseActor

	ProgreAct *agents.ProgreActAgent
	Condenser *condenser.AutoSummarizer
	Variant   string

	Persona    string
	Knowledges []knowledges.KnowledgeItem

	ContextParts ContextParts

	Toolsets []Toolset

	PromptCache *agents.PromptCache
}

var _ actors.Actor = &Agent{}

type CreateOption struct {
	Name      string
	MaxSteps  int
	LLMHooks  *prompt.LLMCallHook
	ActorStep *iris.AgentRunStep

	// dynamically decided by model
	Variant        string
	Persona        string
	Knowledges     []knowledges.KnowledgeItem
	Toolsets       []string
	TaskContext    string
	ExecutionTrace string

	DisableSummarizer bool // whether to summarize tool calls, defaults to true, disabled for testing&benchmarking
}

func New(run *iris.AgentRunContext, opt CreateOption) *Agent {
	toolsets, _ := GetToolsets(run, opt.ActorStep)
	userDefineToolsets, _ := GetUserDefineToolsets(run)
	plannerStore := iris.RetrieveStoreByKey[planactentity.PlannerStore](run, planactentity.PlannerStoreKey)
	toolsets = append(toolsets, userDefineToolsets...)
	// toolsets和userDefineToolsets一起放进去过滤
	tools := lo.Filter(append(toolsets, userDefineToolsets...), func(toolset Toolset, _ int) bool {
		_, ok := lo.Find(opt.Toolsets, func(name string) bool {
			return toolset.Identifier == name
		})
		return ok
	})

	tools = append(tools, GetBuildInMCP(run)...)
	toolsetStore := iris.RetrieveStoreByKey[agenttool.ToolsetStore](run, agenttool.ToolsetStoreKey)
	toolsetKeys := lo.Map(tools, func(toolset Toolset, _ int) string {
		return toolset.Identifier
	})
	toolsetStore.Toolset = toolsetKeys
	iris.UpdateStoreByKey(run, agenttool.ToolsetStoreKey, toolsetStore)
	run.GetLogger().Infof("dynamic actor toolsets: %+v", toolsetStore.Toolset)

	info := iris.AgentInfo{
		Identifier: lo.Ternary(opt.Name == "", Identifier, opt.Name),
		Desc: lo.Must(promptset.ExecutePrompt("description", opt.Variant, map[string]any{
			"Toolsets":           lo.Filter(toolsets, func(t Toolset, _ int) bool { return !t.HiddenFromPlanner }),
			"UserDefineToolsets": userDefineToolsets,
		})),
	}
	reactor := agents.NewProgreActAgent(agents.ProgreActAgentConfig{
		Info:              info,
		Tools:             BuildTools(run, tools, opt.ActorStep),
		MaxSteps:          opt.MaxSteps,
		MaxFailures:       3,
		DisableSummarizer: opt.DisableSummarizer,
	})

	adapterParams := parameters
	if opt.Variant == agententity.VariantNewbie {
		adapterParams = parametersNewbie
	}
	agent := &Agent{
		BaseActor: actors.BaseActor{
			AgentInfo:  info,
			Parameters: adapterParams,
		},
		Variant:    opt.Variant,
		Persona:    opt.Persona,
		Knowledges: opt.Knowledges,
		ContextParts: ContextParts{
			TaskContext:    opt.TaskContext,
			ExecutionTrace: opt.ExecutionTrace,
			Locale:         agents.GetUserLanguage(run.Parameters),
			UserRequest:    strings.TrimSpace(lo.Ternary(plannerStore.EnhancedRequirements != "", plannerStore.EnhancedRequirements, plannerStore.Requirements)),
		},
		Condenser: condenser.NewAutoSummarizer(&condenser.NewAutoSummarizerConfig{
			Name:    Identifier,
			LLM:     run.GetLLM(),
			Config:  run.Config,
			Variant: opt.Variant,
		}),
		Toolsets: tools,

		PromptCache: nil,
	}
	if opt.Variant == agententity.VariantExpert {
		agent.PromptCache = &agents.PromptCache{
			MaxCachePoints:       4,
			CacheType:            "ephemeral",
			NoCacheLastNMessages: 1,
			NewCachePointChars:   5000,
			CachePoints:          map[int]bool{},
		}
	}
	reactor.Composer = agent.Compose
	reactor.Condenser = agent.Condenser
	agent.ProgreAct = reactor
	agent.ActorStep = opt.ActorStep

	return agent
}

func Create(run *iris.AgentRunContext, input string, actorStep *iris.AgentRunStep, llmHook *prompt.LLMCallHook) *Agent {
	variant := run.GetConfig().GetVariantByScene(Identifier)
	tags, err := prompt.ParseTopTagsV2(input)
	if err != nil {
		run.GetLogger().Errorf("failed to parse top tags: %s", err)
		return nil
	}
	persona, _ := lo.Find(tags, func(tag prompt.Tag) bool {
		return tag.XMLName.Local == "persona"
	})
	toolsetsContent, _ := lo.Find(tags, func(tag prompt.Tag) bool {
		return tag.XMLName.Local == "tools"
	})
	toolsets := lo.Map(strings.Split(toolsetsContent.Content, ","), func(toolset string, _ int) string {
		return strings.TrimSpace(toolset)
	})
	run.GetLogger().Infof("toolsets: %+v", toolsets)
	task, _ := lo.Find(tags, func(tag prompt.Tag) bool {
		return tag.XMLName.Local == "task"
	})
	executionTrace, _ := lo.Find(tags, func(tag prompt.Tag) bool {
		return tag.XMLName.Local == "execution_trace"
	})

	return New(run, CreateOption{
		ActorStep:         actorStep,
		Variant:           variant,
		Persona:           persona.Content,
		Toolsets:          toolsets,
		TaskContext:       task.Content,
		ExecutionTrace:    executionTrace.Content,
		Knowledges:        nil,
		LLMHooks:          llmHook,
		DisableSummarizer: conv.DefaultAny[bool](run.Parameters[entity.RuntimeParametersDisableToolSummarizer]),
	})
}

func (a *Agent) Compose(run *iris.AgentRunContext, previousSteps []*iris.AgentRunStep, state *agents.ProgreActState) []*framework.ChatMessage {
	systemMessages, err := prompt.ComposeVaryMessages([]prompt.ComposeVaryMessageOption{
		prompt.WithSystemMessage(promptset.GetTemplate("system", a.Variant), util.ValueToMap(a.GetSystemParts(run))),
		prompt.WithUserMessage(promptset.GetTemplate("user", a.Variant), util.ValueToMap(a.ContextParts)),
	})
	if err != nil {
		run.GetLogger().Errorf("failed to compose messages: %s", err)
	}
	cacheType := ""
	// Use ephemeral cache for agent using claude.
	if a.Variant == agententity.VariantExpert {
		cacheType = "ephemeral"
	}
	a.Condenser.SetPrefix(run, systemMessages) // 压缩时估算前序消息 token 数防止超 token
	workspaceStructure := GetWorkspaceStructure(run)

	// 自定义观察格式化函数，为 Action 结果添加前缀
	observationFormatFunc := func(run *iris.AgentRunContext, step *iris.AgentRunStep) framework.ChatMessage {
		var message string
		if step.Action != nil {
			message = action_prompts.ToolObservation(run, step.StepID, step.Action.Name(), step.Inputs, step.Outputs, step.Error)
		} else if step.Error != nil {
			message = step.Error.Error()
		}
		if len(message) == 0 {
			message = "No action is executed."
		}
		// 当 AI 看到 user 消息以 [ACTION_RESULT] 开头时，减少不必要的注意力转移
		return framework.ChatMessage{
			Role:    llm.RoleUser,
			Content: "[ACTION_RESULT]\n" + message,
		}
	}

	messages, err := prompt.ComposeVaryMessages([]prompt.ComposeVaryMessageOption{
		prompt.WithSystemMessage(promptset.GetTemplate("system", a.Variant), util.ValueToMap(a.GetSystemParts(run))),
		prompt.WithPromptCache(
			prompt.WithUserMessage(promptset.GetTemplate("user", a.Variant), util.ValueToMap(a.ContextParts)),
			cacheType,
		),
		action_prompts.WithReActToolHistory(run, previousSteps, promptset.GetTemplate("thought", a.Variant), promptset.GetTemplate("user", a.Variant), observationFormatFunc, a.Condenser),
		prompt.WithUserMessage(promptset.GetTemplate("last_message", a.Variant), map[string]any{
			"Workspace":             workspaceStructure.Workspace,
			"WorkspaceRepositories": workspaceStructure.WorkspaceRepositories,
			"RootStructure":         workspaceStructure.RootStructure,
		}),
	})
	if err != nil {
		run.GetLogger().Errorf("failed to compose messages: %s", err)
	}
	if a.PromptCache != nil {
		a.PromptCache.UpdateCachePoints(messages)
	}
	return messages
}

func (a *Agent) Run(run *iris.AgentRunContext, input string, options actors.RunOptions) (result controltool.ConclusionOutput) {
	logger := run.GetLogger()
	tags, _ := prompt.ParseTopTagsV2(input)
	task, _ := lo.Find(tags, func(tag prompt.Tag) bool {
		return tag.XMLName.Local == "task"
	})
	if options.ReuseTrace {
		mem := memory.GetAgentMemory(run)
		previousSteps := lo.Filter(mem.ActionMemory, func(step *memory.ActionMemoryItem, _ int) bool {
			return step.ExecutorAgent == a.ProgreAct.Agent.Name()
		})
		lastStep, exist := lo.Last(previousSteps)
		logger.Infof("reuse trace last step: %+v of total %d steps", lastStep, len(previousSteps))
		if exist {
			lastStep.Outputs = map[string]any{"new_user_requirements": "The previous task is finished. Here is the new requirements of user:\n" + input}
		}
	}
	span, ctx := agentrace.GetRuntimeTracerFromContext(run).
		StartCustomSpan(
			run,
			agentrace.SpanTypeStep,
			"mewtwo_run",
			agentrace.WithObjectSpanData(
				map[string]any{
					"input": input,
				},
			),
		)
	run = run.WithContext(ctx)
	defer func() {
		span.UpdateData(agentrace.NewObjectSpanData(
			map[string]any{
				"result": result,
			},
		))
		span.Finish()
	}()
	start := time.Now()
	defer func() {
		telemetry.EmitActorExecution(run, a.Name(), lo.Map(a.Toolsets, func(t Toolset, _ int) string { return t.Identifier }), string(result.Evaluation), time.Since(start))
	}()

	// 并行执行知识召回和经验召回
	logger.Infof("retrieving knowledge and experience for task: %s", input)

	experienceRetriever, err := experience.NewExperienceRetriever()
	if err != nil {
		logger.Errorf("failed to create experience retriever: %v", err)
	}

	group := errgroup.Group{}
	group.SetLimit(-1)

	waitChan := make(chan struct{}, 1)

	// Retrieve builtin knowledges.
	group.Go(func() error {
		defer func() {
			if r := recover(); r != nil {
				logger.Errorf("knowledge retrieval panic: %v", r)
			}
		}()

		kb := knowledges.CreateKnowledgebase(run)
		referenceStore := iris.RetrieveStoreByKey[planactentity.ReferenceStore](run, planactentity.ReferenceStoreKey)

		knowledgeList, err := kb.RetrieveKnowledge(run, knowledges.KgRetrieveOption{
			Query: task.Content,
			Param: knowledges.RetrieveParam{
				Agent:        Identifier,
				Variant:      a.Variant,
				Tools:        lo.Map(a.Toolsets, func(t Toolset, _ int) string { return t.Identifier }),
				WithCitation: len(referenceStore.SearchedRef.List) > 0,
			},
			Sampling: 2,
		})
		if err != nil {
			logger.Errorf("failed to retrieve knowledge: %s", err)
			return nil
		}

		knowledge, _ := promptset.ExecutePrompt("system_knowledge", a.Variant, map[string]any{
			"Knowledges": knowledgeList,
		})
		logger.Infof("retrieved system knowledge: %s", knowledge)

		// update knowledge store
		knowledgeStore := iris.RetrieveStoreByKey[knowledges.Store](run, knowledges.StoreKey)
		knowledgeStore.Renders = make([]*knowledges.KnowledgeRender, 0)

		for _, knowledge := range knowledgeList {
			knowledgeStore.Renders = append(knowledgeStore.Renders, &knowledges.KnowledgeRender{
				Knowledge: knowledge,
				StepID:    knowledges.SystemKnowledge,
			})
		}
		iris.UpdateStoreByKey(run, knowledges.StoreKey, knowledgeStore)
		a.Knowledges = knowledgeList

		return nil
	})

	// Retrieve experience knowledges.
	group.Go(func() error {
		defer func() {
			if r := recover(); r != nil {
				logger.Errorf("experience retrieval panic: %v", r)
			}
		}()

		if a.Variant != agententity.VariantNewbie {
			return nil
		}
		logger.Infof("retrieving experience for newbie variant")

		if experienceRetriever == nil {
			logger.Errorf("experience retriever is nil")
			return nil
		}

		experienceTypes := []string{
			experience.Insights,
		}
		queryInput := task.Content
		if queryInput == "" {
			logger.Warn("no valid query input for experience retrieval")
			return nil
		}

		experiencePrompt, err := experienceRetriever.RetrieveMultipleExperiences(
			run,
			queryInput,
			experience.MewtwoApplyType,
			experienceTypes,
			experience.MediumRelevanceThreshold,
		)
		if err != nil {
			logger.Errorf("failed to retrieve experience: %s", err)
			return nil
		}

		if experiencePrompt != "" {
			// 更新 ContextParts 中的经验信息
			a.ContextParts.Experience = experiencePrompt
			logger.Infof("retrieved experience prompt: %s", experiencePrompt)
		} else {
			logger.Infof("no relevant experience found")
		}

		return nil
	})

	// Retrieve experience reusable workflows.
	group.Go(func() error {
		defer func() {
			if r := recover(); r != nil {
				logger.Errorf("experience retrieval panic: %v", r)
			}
		}()

		logger.Info("retrieving experience reusable workflows")

		return nil
	})

	go func() {
		defer func() {
			close(waitChan)
		}()

		group.Wait()
	}()

	select {
	case <-time.After(60 * time.Second):
		run.GetLogger().Errorf("retrieval timeout after 60 seconds")
		// Timeout, use current results.
		// FIXME(cyx): cancel the retrieval goroutines to avoid leaking and potential concurrent access.
		goto retrievalComplete
	case <-waitChan:
		run.GetLogger().Info("retrieval completed")
	}

retrievalComplete:
	err = a.ProgreAct.RunWithOption(run, &agents.ProgreActAgentRunOption{
		Step: a.ActorStep,
	})
	if err != nil {
		// the actor need to return a iris.NewFatal(err) with a non-nil error to send a fatal error message to user
		isFatal := iris.IsFatal(err)
		result = controltool.ConclusionOutput{
			Content:    err.Error(),
			Evaluation: lo.Ternary(isFatal, controltool.ConclusionEvaluationFatal, controltool.ConclusionEvaluationFailed),
		}
	}

	lastStep := run.State.LastStep()
	mapstructure.Decode(lastStep.Outputs, &result)
	return result
}
