package merger

import (
	"context"
	"testing"

	"go.uber.org/mock/gomock"
	"gotest.tools/v3/assert"

	"code.byted.org/devgpt/kiwis/devai/retrieval/entity"
	"code.byted.org/devgpt/kiwis/lib/metrics"
)

func TestCombinationConvexOperator(t *testing.T) {
	ctx := context.Background()

	_ = metrics.InitDevAIKnowledgeMetric()

	tests := []struct {
		name      string
		mockFunc  func(ctx context.Context, mockOption *mockOption)
		checkFunc func(t *testing.T, h *CombinationConvexOperator)
	}{
		{
			name: "flow rank operator",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
			},
			checkFunc: func(t *testing.T, operator *CombinationConvexOperator) {
				_, err := operator.Run(ctx)
				assert.NilError(t, err)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			operator, opt := newTestCombinationConvexOperator(t)
			tt.mockFunc(ctx, opt)
			tt.checkFunc(t, operator)
		})
	}

}

func newTestCombinationConvexOperator(t *testing.T) (*CombinationConvexOperator, *mockOption) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	operator := &CombinationConvexOperator{
		MetaData: NewCombinationConvexMetaData(
			[]*entity.Segment{
				{
					SegmentID: "1",
					Content:   "test",
				},
			}, map[entity.RankOperatorProvider]map[string]float64{
				entity.OperatorProviderFlowRank: {
					"1": 0.1,
				},
			}, map[string]float64{
				"1": 0.1,
			},
			5,
		),
	}
	opt := &mockOption{}
	return operator, opt
}

type mockOption struct {
}
