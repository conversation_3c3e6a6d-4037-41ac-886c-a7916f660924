package taskqueue

import (
	"context"
	"time"

	"code.byted.org/gopkg/logs/v2/log"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/devai/common/constant"
	"code.byted.org/devgpt/kiwis/devai/data/entity"
	datasourceservice "code.byted.org/devgpt/kiwis/devai/data/service/datasource"
	taskservice "code.byted.org/devgpt/kiwis/devai/data/service/task"
	"code.byted.org/devgpt/kiwis/devai/data/service/taskmanager"
	"code.byted.org/devgpt/kiwis/port/faas"
)

type DatasourceTaskRunner struct {
	// 初始化字段
	datasourceID      int64
	forceUpdate       bool
	datasourceService datasourceservice.DatasourceService
	taskService       taskservice.DatasourceTaskService
	faasCli           faas.Client
	// 运行时字段
	taskID    int64
	startTime time.Time
	isFailed  bool
}

func NewDatasourceTaskRunner(datasourceID int64, forceUpdate bool, datasourceService datasourceservice.DatasourceService,
	taskService taskservice.DatasourceTaskService, faasCli faas.Client) *DatasourceTaskRunner {
	return &DatasourceTaskRunner{
		datasourceID:      datasourceID,
		forceUpdate:       forceUpdate,
		datasourceService: datasourceService,
		taskService:       taskService,
		faasCli:           faasCli,
	}
}

func (t *DatasourceTaskRunner) Run(ctx context.Context) error {
	t.startTime = time.Now()
	datasource, err := t.datasourceService.GetDatasourceByID(ctx, t.datasourceID)
	if err != nil {
		return errors.WithMessage(err, "failed to get datasource")
	}
	tasks, err := t.taskService.ListDatasourceTasksByDatasourceID(ctx, t.datasourceID)
	if err != nil {
		return errors.WithMessage(err, "failed to get datasource task")
	}
	if len(tasks) == 0 {
		return errors.New("datasource task not found")
	}
	latestTask := tasks[0]
	// 如果12小时内执行过，则不执行自动更新。完成的可重新更新，未完成视为之前的task异常重新更新。
	if latestTask.CreatedAt.After(time.Now().Add(-time.Hour * 12)) {
		return nil
	}
	var timeRange []time.Time
	if latestTask.SourceType == constant.SourceTypeOncall { // oncall 的任务需要更新时间范围
		latestSuccessTask, ok := lo.Find(tasks, func(task *entity.DatasourceTask) bool {
			return task.ProcessStatus == entity.TaskStatusSucceeded
		})
		if !ok {
			return errors.New("latest success task not found")
		}
		timeRange = make([]time.Time, 2)
		if latestSuccessTask.Source.TimeRange[0].After(latestSuccessTask.Source.TimeRange[1]) {
			timeRange[0] = latestSuccessTask.Source.TimeRange[0]
		} else {
			timeRange[0] = latestSuccessTask.Source.TimeRange[1]
		}
		timeRange[1] = time.Now()
	}
	tm := taskmanager.NewUpsertDatasourceTaskManager(taskmanager.NewUpsertDatasourceTaskManagerOption{
		TaskService:  t.taskService,
		FaasClient:   t.faasCli,
		DatasourceID: datasource.ID,
		SourceType:   datasource.SourceType,
		TaskOrigin:   entity.TaskOriginAutoUpdate,
		SourceFiles:  latestTask.Source.SourceFiles,
		TimeRange:    timeRange,
		NodeID:       datasource.NodeID,
		ForceUpdate:  t.forceUpdate,
		Username:     datasource.Creator,
	})
	task, err := tm.Create(ctx)
	if err != nil {
		return errors.WithMessage(err, "failed to create task")
	}
	err = tm.Run(ctx, task)
	if err != nil {
		return errors.WithMessage(err, "failed to run task")
	}

	t.taskID = task.ID
	return nil
}

func (t *DatasourceTaskRunner) Check(ctx context.Context) bool {
	if t.taskID <= 0 {
		return true // 任务没有创建，此时直接返回结束
	}
	task, err := t.taskService.GetDatasourceTaskByID(ctx, t.taskID)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get datasource task: %v", err)
		return false
	}

	if !task.ProcessStatus.IsCompleted() {
		return false
	}
	if task.ProcessStatus.IsFailed() {
		t.isFailed = true
	}
	return true
}
