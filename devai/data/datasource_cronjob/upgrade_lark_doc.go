package datasource_cronjob

import (
	"context"

	"code.byted.org/gopkg/logs/v2/log"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/devai/common/constant"
	datasourceservice "code.byted.org/devgpt/kiwis/devai/data/service/datasource"
	"code.byted.org/devgpt/kiwis/port/db"
	"code.byted.org/devgpt/kiwis/port/lark"
)

type UpgradeLarkDoc struct {
	datasourceService datasourceservice.DatasourceService
	larkCli           lark.Client
}

func NewUpgradeLarkDoc(datasourceService datasourceservice.DatasourceService, larkCli lark.Client) *UpgradeLarkDoc {
	return &UpgradeLarkDoc{
		datasourceService: datasourceService,
		larkCli:           larkCli,
	}
}

// HandleUpgradeLarkDoc 升级飞书文档
func (h *UpgradeLarkDoc) HandleUpgradeLarkDoc(ctx context.Context) {
	// 1. 从数据库中获取所有旧飞书文档
	datasourceLarkDocs, err := h.datasourceService.GetDatasourceBySourceType(ctx, constant.SourceTypeLarkDoc)
	if db.IsRecordNotFoundError(err) {
		log.V1.CtxInfo(ctx, "no lark docs found")
		return
	}
	if err != nil {
		log.V1.CtxError(ctx, "failed to get lark docs, err: %v", err)
		return
	}
	// 2. 遍历文档，调用飞书接口获取文档内容, 并更新数据库中的文档内容
	for _, datasourceLarkDoc := range datasourceLarkDocs {
		// 调用飞书接口获取文档内容
		docContent, err := h.larkCli.GetLarkDoc(ctx, datasourceLarkDoc.SourceKey, datasourceLarkDoc.Creator)
		if err != nil {
			log.V1.CtxError(ctx, "failed to get lark doc, err: %v, datasoure_id:%v", err, datasourceLarkDoc.ID)
			continue
		}
		if docContent.Data.IsUpgraded {
			log.V1.CtxInfo(ctx, "lark doc is already upgraded, datasoure_id:%v", datasourceLarkDoc.ID)
			opt := datasourceservice.UpdateDatasourceOption{
				SourceKey:  lo.ToPtr(docContent.Data.UpgradedToken),
				SourceType: lo.ToPtr(constant.SourceTypeLarkDocx),
			}
			err = h.datasourceService.UpdateDatasourceByID(ctx, datasourceLarkDoc.ID, opt)
			if err != nil {
				log.V1.CtxError(ctx, "failed to update lark doc, err: %v, datasoure_id:%v", err, datasourceLarkDoc.ID)
				continue
			}
		}

	}
}
