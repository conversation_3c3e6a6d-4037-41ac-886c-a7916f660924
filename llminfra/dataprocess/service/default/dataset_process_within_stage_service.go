package defaultservice

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/gopkg/logid"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	overpasscommon "code.byted.org/overpass/kiwis_llminfra_foundation/kitex_gen/common"
	"github.com/bytedance/gopkg/util/gopool"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/fx"

	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/llminfra"
	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	libtcc "code.byted.org/devgpt/kiwis/lib/tcc"
	analysisdatasetdal "code.byted.org/devgpt/kiwis/llminfra/analysisdataset/dal"
	analysisdatasetentity "code.byted.org/devgpt/kiwis/llminfra/analysisdataset/entity"
	analysisservice "code.byted.org/devgpt/kiwis/llminfra/analysisdataset/service"
	commonconsts "code.byted.org/devgpt/kiwis/llminfra/common/consts"
	commonentity "code.byted.org/devgpt/kiwis/llminfra/common/entity"
	commonfaas "code.byted.org/devgpt/kiwis/llminfra/common/faas"
	"code.byted.org/devgpt/kiwis/llminfra/dataprocess/consts"
	"code.byted.org/devgpt/kiwis/llminfra/dataprocess/dal"
	"code.byted.org/devgpt/kiwis/llminfra/dataprocess/entity"
	"code.byted.org/devgpt/kiwis/llminfra/dataprocess/service"
	datasetcommonentity "code.byted.org/devgpt/kiwis/llminfra/datasetcommon/entity"
	datasetcommonservice "code.byted.org/devgpt/kiwis/llminfra/datasetcommon/service"
	editorservice "code.byted.org/devgpt/kiwis/llminfra/editor/service"
	evaluationservice "code.byted.org/devgpt/kiwis/llminfra/evaluation/service"
	"code.byted.org/devgpt/kiwis/port/cloudjwt"
)

var _ service.DatasetProcessWithinStageService = &DatasetProcessWithinStageService{}

type DatasetProcessWithinStageService struct {
	DatasetProcessDAO         dal.DatasetProcessWithinStageDAO
	TaskDAO                   dal.DataProcessTaskDAO
	StageDAO                  dal.DataProcessStageDAO
	AnalysisDatasetService    analysisservice.AnalysisDatasetService
	AnalysisDatasetDal        analysisdatasetdal.DatasetDal
	CommonDatasetService      datasetcommonservice.CommonDatasetService
	DataProcessMqService      service.DataProcessMqService
	DataCleanService          editorservice.DataCleanService
	EvalTaskService           evaluationservice.EvalTaskService
	CloudJWTClient            *cloudjwt.CloudJWTClient
	LLMInfraDataProcessConfig *libtcc.GenericConfig[config.LLMInfraDataProcessConfig]
}

type DatasetProcessWithinStageServiceOption struct {
	fx.In
	DatasetProcessDAO         dal.DatasetProcessWithinStageDAO
	StageDAO                  dal.DataProcessStageDAO
	TaskDAO                   dal.DataProcessTaskDAO
	AnalysisDatasetService    analysisservice.AnalysisDatasetService
	AnalysisDatasetDal        analysisdatasetdal.DatasetDal
	CommonDatasetService      datasetcommonservice.CommonDatasetService
	DataProcessMqService      service.DataProcessMqService
	DataCleanService          editorservice.DataCleanService
	EvalTaskService           evaluationservice.EvalTaskService
	CloudJWTClient            *cloudjwt.CloudJWTClient
	LLMInfraDataProcessConfig *libtcc.GenericConfig[config.LLMInfraDataProcessConfig]
}

func NewDatasetProcessWithinStageService(opt DatasetProcessWithinStageServiceOption) *DatasetProcessWithinStageService {
	return &DatasetProcessWithinStageService{
		DatasetProcessDAO:         opt.DatasetProcessDAO,
		TaskDAO:                   opt.TaskDAO,
		StageDAO:                  opt.StageDAO,
		AnalysisDatasetService:    opt.AnalysisDatasetService,
		AnalysisDatasetDal:        opt.AnalysisDatasetDal,
		CommonDatasetService:      opt.CommonDatasetService,
		DataProcessMqService:      opt.DataProcessMqService,
		DataCleanService:          opt.DataCleanService,
		EvalTaskService:           opt.EvalTaskService,
		CloudJWTClient:            opt.CloudJWTClient,
		LLMInfraDataProcessConfig: opt.LLMInfraDataProcessConfig,
	}
}

func (d *DatasetProcessWithinStageService) OnDatasetCopying(ctx context.Context, event *entity.DataProcessEvent) (err error) {
	logs.CtxInfo(ctx, "[DatasetProcessService.OnDatasetCopying] in, event: %v", event)

	if lo.FromPtr(event.StageID) <= 0 {
		logs.CtxError(ctx, "[DatasetProcessService.OnDatasetCopying] invalid stage id: %v", event.StageID)
		return nil
	}
	datasetProcessWithinStageID := lo.FromPtr(lo.FromPtr(event.DatasetParam).DatasetProcessWithinStageID)
	if datasetProcessWithinStageID <= 0 {
		logs.CtxError(ctx, "[DatasetProcessService.OnDatasetCopying] invalid dataset process id")
		return nil
	}
	ctx = logs.CtxAddKVs(ctx, "dataset_process_id", datasetProcessWithinStageID)
	logs.CtxInfo(ctx, "[DatasetProcessService.OnDatasetCopying] start, event: %v", event)

	// todo：数据集复制限流

	defer func() {
		if err != nil && !errors.Is(err, commonconsts.ErrUpdateNoMatchedRecord) {
			logs.CtxError(ctx, "[DatasetProcessService.OnDatasetCopying] error: %v", err)
			d.updateProcessFailStatus(ctx, datasetProcessWithinStageID, llminfra.FailType_DataProcessDatasetCopyErr, lo.ToPtr(err.Error()))
		}
	}()

	datasetProcessWithinStage, err := d.DatasetProcessDAO.GetDatasetProcessWithinStage(ctx, dal.GetDatasetProcessWithinStageOpt{ID: datasetProcessWithinStageID})
	if err != nil {
		return err
	}

	var logIDPointer *string
	if datasetProcessWithinStage.LogID == "" {
		logID, hasLogid := ctxvalues.LogID(ctx)
		if !hasLogid {
			logID = logid.GenLogID()
			ctx = ctxvalues.SetLogID(ctx, logID)
		}
		logIDPointer = lo.ToPtr(logID)
	}

	if event.DatasetParam.NextStepIndex != nil && *event.DatasetParam.NextStepIndex != datasetProcessWithinStage.CurrentStepIndex+1 {
		logs.CtxInfo(ctx, "[DatasetProcessService.OnDatasetCopying] step not match, expected: %d, actual: %d, skip", *event.DatasetParam.NextStepIndex-1, datasetProcessWithinStage.CurrentStepIndex)
		return nil
	}

	stage, err := d.StageDAO.GetDataProcessStage(ctx, dal.GetDataProcessStageOpt{
		ID: &datasetProcessWithinStage.StageID,
	})
	if err != nil {
		return errors.WithMessage(err, "fail to get stage")
	}

	if datasetProcessWithinStage.CurrentStepIndex+1 >= int8(len(stage.StageConfig.Steps)) {
		logs.CtxInfo(ctx, "step index is last step, skip")
		return d.turnToNextStep(ctx, TurnToNextStepOption{
			DatasetProcess: datasetProcessWithinStage,
			StageID:        &stage.ID,
			Event:          event,
		})
	}

	// 判断是否跳过当前步骤
	if lo.FromPtr(stage.StageConfig.Steps[datasetProcessWithinStage.CurrentStepIndex+1].IsSkipped) {
		logs.CtxInfo(ctx, "[DatasetProcessService.OnDatasetCopying] step %d is skipped", datasetProcessWithinStage.CurrentStepIndex+1)
		datasetProcessWithinStage, err = d.UpdateDatasetProcessAndPubEvent(ctx, dal.UpdateDatasetProcessWithinStageOpt{
			ID:               datasetProcessWithinStage.ID,
			CurrentStatus:    lo.ToPtr(datasetProcessWithinStage.Status),
			Status:           lo.ToPtr(llminfra.DatasetProcessStatus_Processing),
			CurrentStepIndex: lo.ToPtr(datasetProcessWithinStage.CurrentStepIndex + 1),
		})
		if err != nil {
			err = errors.WithMessage(err, "fail to update dataset process within stage")
			logs.CtxError(ctx, "[DatasetProcessService.OnDatasetCopying] err: %v", err)
			return err
		}
		return d.turnToNextStep(ctx, TurnToNextStepOption{
			DatasetProcess: datasetProcessWithinStage,
			StageID:        &stage.ID,
			Event:          event,
			StepExecMsg:    lo.ToPtr("skipped"),
		})
	}

	if datasetProcessWithinStage.IsStepOutputDatasetReady(datasetProcessWithinStage.CurrentStepIndex + 1) {
		logs.CtxInfo(ctx, "[DatasetProcessService.OnDatasetCopying] step output dataset already exists, skip")
		return nil
	}

	// 执行条件校验
	switch datasetProcessWithinStage.Status {
	case llminfra.DatasetProcessStatus_Created:
		logs.CtxInfo(ctx, "[DatasetProcessService.OnDatasetCopying] status is created")
	default:
		logs.CtxError(ctx, "[DatasetProcessService.OnDatasetCopying] status is %v, can not to copy dataset", datasetProcessWithinStage.Status)
		return nil
	}

	datasetProcessWithinStage, err = d.UpdateDatasetProcessAndPubEvent(ctx, dal.UpdateDatasetProcessWithinStageOpt{
		ID:            datasetProcessWithinStageID,
		CurrentStatus: lo.ToPtr(datasetProcessWithinStage.Status),
		Status:        lo.ToPtr(llminfra.DatasetProcessStatus_InputDatasetCopying),
	})
	if err != nil {
		err = errors.WithMessage(err, "fail to update dataset process within stage")
		logs.CtxError(ctx, "[DatasetProcessService.OnDatasetCopying] err: %v", err)
		return err
	}

	logs.CtxInfo(ctx, "[DatasetProcessService.OnDatasetCopying] start to copy dataset, datasetProcessWithinStageID: %v", datasetProcessWithinStageID)
	outputDataset, err := d.createOutputDataset(ctx, event, stage, datasetProcessWithinStage)
	if err != nil {
		return errors.WithMessage(err, "fail to create output dateset")
	}

	if datasetProcessWithinStage.Extra.StepInfos == nil {
		datasetProcessWithinStage.Extra.StepInfos = map[int8]*entity.StepInfo{}
	}
	datasetProcessWithinStage.Extra.StepInfos[datasetProcessWithinStage.CurrentStepIndex+1] = &entity.StepInfo{
		OutputDatasetType:   outputDataset.DatasetType,
		OutputDatasetUniqID: lo.ToPtr(outputDataset.DatasetUniqID),
	}
	datasetProcessWithinStage, err = d.UpdateDatasetProcessAndPubEvent(ctx, dal.UpdateDatasetProcessWithinStageOpt{
		ID:                  lo.FromPtr(event.DatasetParam.DatasetProcessWithinStageID),
		OutputDatasetType:   lo.ToPtr(outputDataset.DatasetType),
		OutputDatasetUniqID: lo.ToPtr(outputDataset.DatasetUniqID),
		LogID:               logIDPointer,
		Extra: &entity.DatasetProcessWithinStageExtra{
			StepInfos: datasetProcessWithinStage.Extra.StepInfos,
		},
	})
	if err != nil {
		return errors.WithMessage(err, "fail to update dataset process within stage")
	}

	return d.processNextStep(ctx, datasetProcessWithinStage, event)
}

func (d *DatasetProcessWithinStageService) createOutputDataset(
	ctx context.Context,
	event *entity.DataProcessEvent,
	stage *entity.DataProcessStage,
	datasetProcessWithinStage *entity.DatasetProcessWithinStage,
) (*llminfra.DataProcessDataset, error) {
	// 取前一个未skip的步骤的输出作为当前step的输入
	targetStep := datasetProcessWithinStage.CurrentStepIndex + 1
	preStepIndex, err := stage.GetPreStepIndex(targetStep)
	if err != nil {
		logs.CtxError(ctx, "GetPreStepIndex error, %v", err)
		return nil, err
	}

	stepInputDatasetType := datasetProcessWithinStage.InputDatasetType
	stepInputDatasetUniqID := datasetProcessWithinStage.InputDatasetUniqID
	if preStepIndex > -1 {
		if preStepOutput := datasetProcessWithinStage.GetStepOutputDataset(preStepIndex); preStepOutput != nil {
			logs.CtxInfo(ctx, "create from pre step output, pre step: %d", preStepIndex)
			stepInputDatasetType = preStepOutput.DatasetType
			stepInputDatasetUniqID = preStepOutput.DatasetUniqID
		} else {
			errMsg := fmt.Sprintf("pre step output not found, pre step: %d", preStepIndex)
			logs.CtxInfo(ctx, errMsg)
			return nil, errors.New(errMsg)
		}
	}

	sourceDataset, sourceDatasetVersion, err := d.getSourceDatasetInfo(ctx, event, stepInputDatasetType, stepInputDatasetUniqID)
	if err != nil {
		return nil, err
	}

	account := &authentity.Account{
		Username: event.Username,
		Type:     lo.Ternary(event.IsServiceAccount, "service_account", "user_account"),
		Email:    fmt.Sprintf("%<EMAIL>", event.Username),
	}
	datasetIndexInStage := lo.FromPtr(datasetProcessWithinStage.Extra.DatasetIndexInStage)
	stepName := stage.StageConfig.Steps[datasetProcessWithinStage.CurrentStepIndex+1].Name
	datasetName := fmt.Sprintf("%s_[%s]_[%s]_[%s]_%d_%v", event.Username, event.TaskName, stage.Name, stepName, datasetIndexInStage, time.Now().UnixMilli())
	sourceDataFilter := generateSourceDataFilter(ctx, sourceDatasetVersion.GetColumns(),
		sourceDatasetVersion.GetAnalysisFields(), stage.StageConfig.FilterRule)
	logs.CtxInfo(ctx, "[createOutputDataset] sourceDataFilter: %v", sourceDataFilter)
	isSkipInferFailedCase := lo.FromPtr(datasetProcessWithinStage.GetStepInfo(datasetProcessWithinStage.CurrentStepIndex)).IsFailureTolerable
	createOption := &analysisservice.CreateDatasetOption{
		Account:                account,
		BizLineID:              sourceDataset.GetBizLineID(),
		Name:                   datasetName,
		SceneIDs:               sourceDataset.GetSceneIDs(),
		Desc:                   datasetName,
		IsHidden:               true,
		HiddenReason:           llminfra.DatasetHiddenReason_DataProcessTaskOutput,
		BlockLarkNotification:  true,
		IsTemporary:            false,
		SourceDataFilter:       sourceDataFilter,
		DisablePermissionCheck: true,
		MaxCaseNum:             datasetProcessWithinStage.Extra.MaxCaseNum,
		ResetStatus:            true,
		ResetInferInfos:        true,
		IsSkipInferFailedCase:  isSkipInferFailedCase,
		SensitiveType:          lo.ToPtr(sourceDatasetVersion.GetSensitiveType()),
	}

	switch stepInputDatasetType {
	case llminfra.DatasetType_Analysis:
		createOption.SourceType = llminfra.AnalysisDataSource_Analysis
		createOption.SourceDatasetID = lo.ToPtr(stepInputDatasetUniqID)
	case llminfra.DatasetType_Eval:
		createOption.SourceType = llminfra.AnalysisDataSource_EvalDataDerived
		createOption.SourceEvalDataVersionID = lo.ToPtr(stepInputDatasetUniqID)
	default:
		return nil, fmt.Errorf("%w: invalid dataset type - %v to derived analysis dataset", commonconsts.ErrParamInvalid, stepInputDatasetType)
	}

	dataset, err := d.AnalysisDatasetService.CreateAnalysisDataset(ctx, createOption)
	if err != nil {
		return nil, err
	}
	if lo.FromPtr(dataset.TotalNum) == 0 {
		return nil, errors.New("data num of derived dataset is 0")
	}

	return &llminfra.DataProcessDataset{
		DatasetType:   llminfra.DatasetType_Analysis,
		DatasetUniqID: dataset.ID,
	}, nil
}

// generateSourceDataFilter 将数据任务中仅包含字段名的过滤条件对应到数据集的原始字段和标注字段
func generateSourceDataFilter(
	_ context.Context,
	columns []string,
	analysisFields []*datasetcommonentity.AnalysisField,
	filterRule *llminfra.DataProcessFilterRule,
) (sourceDataFilter *llminfra.DataFilter) {
	if filterRule == nil {
		return nil
	}

	sourceDataFilter = &llminfra.DataFilter{
		Statuses:         filterRule.Statuses,
		Analysts:         filterRule.Analysts,
		DataFilterParams: []*llminfra.AnalysisFilterParam{},
	}

	columnKeyMap := lo.SliceToMap(columns, func(column string) (string, bool) {
		return column, true
	})
	analysisFieldKeyMap := lo.SliceToMap(analysisFields, func(analysisField *datasetcommonentity.AnalysisField) (string, int32) {
		return analysisField.FieldName, analysisField.ID
	})
	// 用字段名匹配，优先原始字段
	for _, filter := range filterRule.FilterParams {
		if columnKeyMap[filter.Key] {
			filterParam := &llminfra.AnalysisFilterParam{
				FilterParam:     filter,
				IsAnalysisField: lo.ToPtr(false),
			}
			sourceDataFilter.DataFilterParams = append(sourceDataFilter.DataFilterParams, filterParam)
			continue
		}
		if fieldID, ok := analysisFieldKeyMap[filter.Key]; ok {
			filterParam := &llminfra.AnalysisFilterParam{
				ID:              lo.ToPtr(fieldID),
				FilterParam:     filter,
				IsAnalysisField: lo.ToPtr(true),
			}
			sourceDataFilter.DataFilterParams = append(sourceDataFilter.DataFilterParams, filterParam)
			continue
		}
	}
	return
}

func (d *DatasetProcessWithinStageService) getSourceDatasetInfo(
	ctx context.Context,
	event *entity.DataProcessEvent,
	inputDatasetType llminfra.DatasetType,
	inputDatasetUniqID int64,
) (sourceDataset datasetcommonentity.ICommonDataset, sourceDatasetVersion datasetcommonentity.ICommonDatasetVersion, err error) {
	sourceDatasetVersion, err = d.CommonDatasetService.GetDatasetVersion(ctx, &datasetcommonservice.GetDatasetVersionOption{
		Username:               event.Username,
		IsServiceAccount:       event.IsServiceAccount,
		UniqID:                 inputDatasetUniqID,
		DatasetType:            inputDatasetType,
		DisablePermissionCheck: true,
	})
	if err != nil {
		logs.CtxError(ctx, "get source dataset version error: %v", err)
		return nil, nil, errors.WithMessage(err, "get source dataset version error")
	}

	sourceDataset, err = d.CommonDatasetService.GetDataset(ctx, &datasetcommonservice.GetDatasetOption{
		Username:               event.Username,
		IsServiceAccount:       event.IsServiceAccount,
		DatasetID:              sourceDatasetVersion.GetDatasetID(),
		DatasetType:            inputDatasetType,
		DisablePermissionCheck: true,
	})
	if err != nil {
		logs.CtxError(ctx, "get source dataset error: %v", err)
		return nil, nil, errors.WithMessage(err, "get source dataset error")
	}
	return
}

func (d *DatasetProcessWithinStageService) OnDatasetProcessing(ctx context.Context, event *entity.DataProcessEvent) (err error) {
	logs.CtxInfo(ctx, "[DatasetProcessService.OnDatasetProcessing] in, event: %v", event)

	if lo.FromPtr(event.StageID) <= 0 {
		logs.CtxError(ctx, "[DatasetProcessService.OnDatasetProcessing] invalid stage id: %v", event.StageID)
		return nil
	}
	datasetProcessWithinStageID := lo.FromPtr(lo.FromPtr(event.DatasetParam).DatasetProcessWithinStageID)
	if datasetProcessWithinStageID <= 0 {
		logs.CtxError(ctx, "[DatasetProcessService.OnDatasetProcessing] invalid dataset process id")
		return nil
	}
	ctx = logs.CtxAddKVs(ctx, "dataset_process_id", datasetProcessWithinStageID)
	logs.CtxInfo(ctx, "[DatasetProcessService.OnDatasetProcessing] start, event: %v", event)

	if lo.FromPtr(event.DatasetParam).NextStepIndex == nil {
		logs.CtxError(ctx, "[DatasetProcessService.OnDatasetProcessing] invalid next step index")
		return nil
	}
	nextStepIndex := lo.FromPtr(lo.FromPtr(event.DatasetParam).NextStepIndex)
	logs.CtxInfo(ctx, "[DatasetProcessService.OnDatasetProcessing] start, datasetProcessWithinStageID: %v, nextStepIndex: %v",
		datasetProcessWithinStageID, nextStepIndex)

	defer func() {
		if err != nil && !errors.Is(err, commonconsts.ErrUpdateNoMatchedRecord) {
			logs.CtxError(ctx, "[DatasetProcessService.OnDatasetProcessing] error: %v", err)
			d.updateProcessFailStatus(ctx, datasetProcessWithinStageID, llminfra.FailType_DataProcessDatasetProcessErr, lo.ToPtr(err.Error()))
		}
	}()

	datasetProcessWithinStage, err := d.DatasetProcessDAO.GetDatasetProcessWithinStage(ctx, dal.GetDatasetProcessWithinStageOpt{ID: datasetProcessWithinStageID})
	if err != nil {
		return errors.WithMessage(err, "fail to get dataset process within stage")
	}

	if datasetProcessWithinStage.CurrentStepIndex+1 != nextStepIndex {
		logs.CtxInfo(ctx, "[DatasetProcessService.OnDatasetProcessing] invalid next step index, currentStepIndex: %v, nextStepIndex: %v",
			datasetProcessWithinStage.CurrentStepIndex, nextStepIndex)
		return nil
	}

	dataNum, err := d.checkDataNum(ctx, event, datasetProcessWithinStage)
	if dataNum == 0 {
		return err
	}

	// 执行条件校验
	if datasetProcessWithinStage.Status != llminfra.DatasetProcessStatus_ReadyToProcess {
		logs.CtxInfo(ctx, "[DatasetProcessService.OnDatasetProcessing] dataset process within stage status is not ready to process, status: %v", datasetProcessWithinStage.Status)
		return nil
	}

	stage, err := d.StageDAO.GetDataProcessStage(ctx, dal.GetDataProcessStageOpt{
		ID: &datasetProcessWithinStage.StageID,
	})
	if err != nil {
		return errors.WithMessage(err, "fail to get stage")
	}

	if nextStepIndex >= int8(len(stage.StageConfig.Steps)) {
		logs.CtxError(ctx, "[DatasetProcessService.OnDatasetProcessing] step index out of bound, nextStepIndex: %v, total steps: %v", nextStepIndex, len(stage.StageConfig.Steps))
		return nil
	}

	if stage.Status == llminfra.DataProcessStageStatus_Canceling || stage.Status == llminfra.DataProcessStageStatus_Canceled {
		_, updateErr := d.UpdateDatasetProcessAndPubEvent(ctx, dal.UpdateDatasetProcessWithinStageOpt{
			ID:            datasetProcessWithinStageID,
			CurrentStatus: lo.ToPtr(datasetProcessWithinStage.Status),
			Status:        lo.ToPtr(llminfra.DatasetProcessStatus_Canceled),
		})
		if updateErr != nil {
			logs.CtxError(ctx, "[DatasetProcessService.updateProcessFailStatus] update dataset process error: %v", updateErr)
			return updateErr
		}
		return nil
	}

	return d.runCurrentStep(ctx, datasetProcessWithinStage, event, stage, nextStepIndex)
}

func (d *DatasetProcessWithinStageService) runCurrentStep(
	ctx context.Context,
	datasetProcessWithinStage *entity.DatasetProcessWithinStage,
	event *entity.DataProcessEvent,
	stage *entity.DataProcessStage,
	stepIndex int8,
) (err error) {
	datasetProcessWithinStage, err = d.UpdateDatasetProcessAndPubEvent(ctx, dal.UpdateDatasetProcessWithinStageOpt{
		ID:               datasetProcessWithinStage.ID,
		CurrentStatus:    lo.ToPtr(datasetProcessWithinStage.Status),
		CurrentStepIndex: lo.ToPtr(stepIndex),
		Status:           lo.ToPtr(llminfra.DatasetProcessStatus_Processing),
		FailType:         lo.ToPtr(llminfra.FailType_Success),
		FailMsg:          lo.ToPtr(""),
		Extra: &entity.DatasetProcessWithinStageExtra{
			IsCurrentStepSuccess: lo.ToPtr(false),
		},
	})
	if err != nil {
		err = errors.WithMessage(err, "fail to update dataset process within stage")
		logs.CtxError(ctx, "[DatasetProcessService.OnDatasetProcessing] err: %v", err)
		return err
	}

	task, err := d.TaskDAO.GetDataProcessTask(ctx, dal.GetDataProcessTaskOption{ID: &stage.TaskID})
	if err != nil {
		return errors.WithMessage(err, "fail to get task")
	}

	tag := metrics.DataProcessStepTag{
		BizLineID:  strconv.Itoa(int(task.BizLineID)),
		TaskID:     strconv.Itoa(int(stage.TaskID)),
		StageID:    strconv.Itoa(int(stage.ID)),
		StageIndex: strconv.Itoa(int(stage.StageIndex)),
		StepIndex:  strconv.Itoa(int(stepIndex)),
		StepType:   stage.StageConfig.Steps[stepIndex].StepType.String(),
	}
	if err := metrics.LM.DataProcessStepThroughput.
		WithTags(&tag).
		Add(1); err != nil {
		logs.CtxError(ctx, "[DatasetProcessService.runCurrentStep] emit step throughput failed: %s", err.Error())
	}

	curStepConfig := stage.StageConfig.Steps[stepIndex]
	switch curStepConfig.StepType {
	case llminfra.DataProcessStepType_DataCleanByFaaS:
		dataCleanTaskID, err := d.DataCleanService.UpdateDataCleanURL(ctx, editorservice.UpdateDataCleanURLOption{
			Username:               event.Username,
			IsServiceAccount:       event.IsServiceAccount,
			DatasetVersionID:       lo.FromPtr(datasetProcessWithinStage.OutputDatasetUniqID),
			Type:                   commonentity.DatasetType(datasetProcessWithinStage.OutputDatasetType),
			DataCleanURL:           lo.FromPtr(curStepConfig.Param.DataCleanURL),
			DataCleanType:          lo.FromPtr(curStepConfig.Param.DataCleanType),
			UserParamMapStr:        curStepConfig.Param.DataCleanUserParamMapStr,
			BatchNumOfCaseClean:    curStepConfig.Param.BatchNumOfCaseClean,
			DisablePermissionCheck: true,
			AlwaysCreateNewTask:    true,
		})
		if err != nil {
			return errors.WithMessage(err, "UpdateDataCleanURL err")
		}

		if datasetProcessWithinStage.Extra.DataCleanStepTaskIDMap == nil {
			datasetProcessWithinStage.Extra.DataCleanStepTaskIDMap = make(map[int8]int64)
		}
		datasetProcessWithinStage.Extra.DataCleanStepTaskIDMap[datasetProcessWithinStage.CurrentStepIndex] = dataCleanTaskID
		_, err = d.UpdateDatasetProcessAndPubEvent(ctx, dal.UpdateDatasetProcessWithinStageOpt{
			ID: datasetProcessWithinStage.ID,
			Extra: &entity.DatasetProcessWithinStageExtra{
				DataCleanStepTaskIDMap: datasetProcessWithinStage.Extra.DataCleanStepTaskIDMap,
			},
		})
		if err != nil {
			return errors.WithMessage(err, "fail to update data clean step task id map")
		}

		return d.DataCleanService.DataClean(ctx, editorservice.DataCleanOption{
			Username:               event.Username,
			IsServiceAccount:       event.IsServiceAccount,
			DataCleanTaskID:        dataCleanTaskID,
			Operation:              llminfra.DataCleanOperationType_SubmitCleanTask,
			DisablePermissionCheck: true,
			IsDataProcessTask:      true,
			DatasetProcessContext:  event,
		})
	case llminfra.DataProcessStepType_ModelInference:
		outputDataset, err := d.AnalysisDatasetService.UpdateAnalysisDataset(ctx, &analysisservice.UpdateDatasetOption{
			Username:                event.Username,
			IsServiceAccount:        event.IsServiceAccount,
			DatasetID:               lo.FromPtr(datasetProcessWithinStage.OutputDatasetUniqID),
			InferenceStatus:         lo.ToPtr(llminfra.DatasetInferenceStatus_NotStart),
			InferenceParams:         curStepConfig.Param.InferenceParams,
			IsDataProcessTaskOutput: lo.ToPtr(true),
			DisablePermissionCheck:  true,
		})
		if err != nil {
			return errors.WithMessage(err, "fail to update dataset with inference params")
		}

		jwt, err := d.CloudJWTClient.GenJWT(ctx)
		if err != nil {
			return errors.WithMessage(err, "fail to gen jwt")
		}
		httpRequest := &overpasscommon.HTTPRequest{
			XJwtToken: lo.ToPtr(jwt),
		}
		inferenceTaskID, err := d.AnalysisDatasetService.CreateInferenceTask(ctx, &analysisservice.CreateInferenceTaskOption{
			Username:              event.Username,
			IsServiceAccount:      event.IsServiceAccount,
			DatasetID:             outputDataset.ID,
			Email:                 fmt.Sprintf("%<EMAIL>", event.Username),
			LogID:                 datasetProcessWithinStage.LogID,
			HTTPRequest:           httpRequest,
			BlockLarkNotification: true,
			TriggerSource:         llminfra.EvalTaskTriggerSource_DataProcess,
		})
		if err != nil {
			return err
		}
		if inferenceTaskID == 0 {
			return errors.New("create inference task err")
		}

		if datasetProcessWithinStage.Extra.ModelInferStepTaskIDMap == nil {
			datasetProcessWithinStage.Extra.ModelInferStepTaskIDMap = make(map[int8]int64)
		}
		datasetProcessWithinStage.Extra.ModelInferStepTaskIDMap[datasetProcessWithinStage.CurrentStepIndex] = inferenceTaskID
		_, err = d.UpdateDatasetProcessAndPubEvent(ctx, dal.UpdateDatasetProcessWithinStageOpt{
			ID: datasetProcessWithinStage.ID,
			Extra: &entity.DatasetProcessWithinStageExtra{
				ModelInferStepTaskIDMap: datasetProcessWithinStage.Extra.ModelInferStepTaskIDMap,
			},
		})
		if err != nil {
			return errors.WithMessage(err, "fail to update model infer step task id map")
		}
	default:
		logs.CtxError(ctx, "[DatasetProcessService.runCurrentStep] invalid step type: %v", curStepConfig.StepType)
	}
	return
}

func (d *DatasetProcessWithinStageService) updateProcessFailStatus(
	ctx context.Context,
	datasetProcessWithinStageID int64,
	failType llminfra.FailType,
	failMsg *string,
) {
	_, updateErr := d.UpdateDatasetProcessAndPubEvent(ctx, dal.UpdateDatasetProcessWithinStageOpt{
		ID:       datasetProcessWithinStageID,
		Status:   lo.ToPtr(llminfra.DatasetProcessStatus_Failed),
		FailType: lo.ToPtr(failType),
		FailMsg:  failMsg,
	})
	if updateErr != nil {
		logs.CtxError(ctx, "[DatasetProcessService.updateProcessFailStatus] update dataset process error: %v", updateErr)
	}
}

func (d *DatasetProcessWithinStageService) OnDatasetCleanFinish(ctx context.Context, event *entity.DataProcessEvent) (err error) {
	logs.CtxInfo(ctx, "[DatasetProcessService.OnDatasetCleanFinish] in, event: %v", event)

	datasetProcessWithinStageID := lo.FromPtr(lo.FromPtr(event.DatasetParam).DatasetProcessWithinStageID)
	if datasetProcessWithinStageID <= 0 {
		logs.CtxError(ctx, "[DatasetProcessService.OnDatasetCleanFinish] invalid dataset process id")
		return nil
	}
	ctx = logs.CtxAddKVs(ctx, "dataset_process_id", datasetProcessWithinStageID)
	logs.CtxInfo(ctx, "[DatasetProcessService.OnDatasetCleanFinish] start, event: %v", event)

	defer func() {
		if err != nil && !errors.Is(err, commonconsts.ErrUpdateNoMatchedRecord) {
			logs.CtxError(ctx, "[DatasetProcessService.OnDatasetCleanFinish] error: %v", err)
			d.updateProcessFailStatus(ctx, datasetProcessWithinStageID, llminfra.FailType_DataProcessDatasetProcessErr, lo.ToPtr(err.Error()))
		}
	}()

	datasetProcessWithinStage, err := d.DatasetProcessDAO.GetDatasetProcessWithinStage(ctx, dal.GetDatasetProcessWithinStageOpt{
		ID: datasetProcessWithinStageID,
	})
	if err != nil {
		return errors.WithMessage(err, "GetDatasetProcessWithinStage fail in OnDatasetCleanFinish")
	}

	// todo: cancel的时候也更新状态
	// 执行条件校验
	if datasetProcessWithinStage.Status != llminfra.DatasetProcessStatus_Processing {
		logs.CtxInfo(ctx, "[DatasetProcessService.OnDatasetCleanFinish] status is %v, not processing, pass", datasetProcessWithinStage.Status)
		return nil
	}

	stage, err := d.StageDAO.GetDataProcessStage(ctx, dal.GetDataProcessStageOpt{
		ID: &datasetProcessWithinStage.StageID,
	})
	if err != nil {
		return errors.WithMessage(err, "fail to get stage")
	}

	stepIndexInEvent := lo.FromPtr(event.DatasetParam.NextStepIndex)
	if stepIndexInEvent != datasetProcessWithinStage.CurrentStepIndex {
		logs.CtxInfo(ctx, "[DatasetProcessService.OnDatasetCleanFinish] step not match, in event: %d, cur: %d, pass", stepIndexInEvent, datasetProcessWithinStage.CurrentStepIndex)
		return nil
	}
	dataCleanTaskIDInEvent := lo.FromPtr(event.DatasetParam.DataCleanTaskID)
	targetDataCleanTaskID := datasetProcessWithinStage.GetDataCleanTaskID(stepIndexInEvent)
	if dataCleanTaskIDInEvent != targetDataCleanTaskID {
		logs.CtxInfo(ctx, "[DatasetProcessService.OnDatasetCleanFinish] task id expired, pass")
		return nil
	}

	dataCleanStatus := lo.FromPtr(event.DatasetParam.Status)
	switch dataCleanStatus {
	case llminfra.DatasetProcessStatus_Failed:
		failMsg := fmt.Sprintf("data clean in step %d failed, data clean task id: %d, fail msg: %v", stepIndexInEvent, dataCleanTaskIDInEvent, lo.FromPtr(event.DatasetParam.FailMsg))
		logs.CtxError(ctx, "[DatasetProcessService.OnDatasetCleanFinish] failed, fail msg: %v", failMsg)
		d.updateProcessFailStatus(ctx, datasetProcessWithinStage.ID, llminfra.FailType_DataProcessDatasetProcessErr, lo.ToPtr(failMsg))
		return nil
	case llminfra.DatasetProcessStatus_Success:
		err = d.DataCleanService.DataClean(ctx, editorservice.DataCleanOption{
			Username:               event.Username,
			IsServiceAccount:       event.IsServiceAccount,
			DataCleanTaskID:        dataCleanTaskIDInEvent,
			Operation:              llminfra.DataCleanOperationType_CommitCleanResult,
			DisablePermissionCheck: true,
			IsDataProcessTask:      true,
		})
		if err != nil {
			return errors.WithMessage(err, fmt.Sprintf("commit clean result in step %d failed, data clean task id: %d", stepIndexInEvent, dataCleanTaskIDInEvent))
		}

		return d.turnToNextStep(ctx, TurnToNextStepOption{
			DatasetProcess: datasetProcessWithinStage,
			StageID:        &stage.ID,
			Event:          event,
		})
	default:
		logs.CtxInfo(ctx, "[DatasetProcessService.OnDatasetCleanFinish] invalid data clean status: %v", dataCleanStatus)
		return nil
	}
}

func (d *DatasetProcessWithinStageService) processNextStep(
	ctx context.Context,
	datasetProcessWithinStage *entity.DatasetProcessWithinStage,
	event *entity.DataProcessEvent,
) error {
	// 获取最新stage信息
	stage, err := d.StageDAO.GetDataProcessStage(ctx, dal.GetDataProcessStageOpt{
		ID: event.StageID,
	})
	if err != nil {
		return errors.WithMessage(err, "fail to get stage")
	}
	stageStatus := stage.Status

	datasetProcessStatus := llminfra.DatasetProcessStatus_ReadyToProcess
	if stageStatus == llminfra.DataProcessStageStatus_Canceling || stageStatus == llminfra.DataProcessStageStatus_Canceled {
		logs.CtxInfo(ctx, "[DatasetProcessService.processNextStep] stage status is %v, not processing", stageStatus)
		datasetProcessStatus = llminfra.DatasetProcessStatus_Canceled
	}

	datasetProcessWithinStage, err = d.UpdateDatasetProcessAndPubEvent(ctx, dal.UpdateDatasetProcessWithinStageOpt{
		ID:            datasetProcessWithinStage.ID,
		CurrentStatus: lo.ToPtr(datasetProcessWithinStage.Status),
		Status:        lo.ToPtr(datasetProcessStatus),
	})
	if err != nil {
		return errors.WithMessage(err, "fail to update dataset process within stage")
	}

	if datasetProcessStatus == llminfra.DatasetProcessStatus_Canceled {
		logs.CtxInfo(ctx, "[DatasetProcessService.processNextStep] dataset process status is %v, not processing, pass", datasetProcessStatus)
		return nil
	}

	if pubErr := d.DataProcessMqService.PubEvent(ctx, &entity.DataProcessEvent{
		Username:         event.Username,
		IsServiceAccount: event.IsServiceAccount,
		EventType:        consts.EventType_DatasetEvent_ProcessDataset,
		TaskID:           event.TaskID,
		TaskName:         event.TaskName,
		StageID:          lo.ToPtr(stage.ID),
		DatasetParam: &entity.DatasetParam{
			DatasetProcessWithinStageID: lo.ToPtr(datasetProcessWithinStage.ID),
			NextStepIndex:               lo.ToPtr(datasetProcessWithinStage.CurrentStepIndex + 1),
		},
	}); pubErr != nil {
		logs.CtxError(ctx, "[DatasetProcessService.processNextStep] pub event err, %v", pubErr)
	}
	return nil
}

type TurnToNextStepOption struct {
	DatasetProcess     *entity.DatasetProcessWithinStage
	StageID            *int64
	Event              *entity.DataProcessEvent
	IsFailureTolerable *bool
	StepExecMsg        *string
}

func (d *DatasetProcessWithinStageService) turnToNextStep(ctx context.Context, opt TurnToNextStepOption) error {
	// 获取最新stage信息
	stage, err := d.StageDAO.GetDataProcessStage(ctx, dal.GetDataProcessStageOpt{
		ID: opt.StageID,
	})
	if err != nil {
		return errors.WithMessage(err, "fail to get stage")
	}

	stageStatus := stage.Status
	steps := stage.StageConfig.Steps

	task, err := d.TaskDAO.GetDataProcessTask(ctx, dal.GetDataProcessTaskOption{
		ID: &stage.TaskID,
	})
	if err != nil {
		return errors.WithMessage(err, "fail to get task")
	}

	var outputDataset *analysisdatasetentity.AnalysisDatasetPacked
	// 一开始复制失败时，OutputDatasetUniqID为空，CurrentStepIndex可能为-1
	if lo.FromPtr(opt.DatasetProcess.OutputDatasetUniqID) > 0 && opt.DatasetProcess.CurrentStepIndex >= 0 {
		currStep := steps[opt.DatasetProcess.CurrentStepIndex]
		outputDataset, err = d.AnalysisDatasetService.GetAnalysisDataset(ctx, &analysisservice.GetAnalysisDatasetOption{
			Username:               opt.Event.Username,
			IsServiceAccount:       opt.Event.IsServiceAccount,
			DatasetID:              *opt.DatasetProcess.OutputDatasetUniqID,
			DisablePermissionCheck: true,
		})
		if err != nil {
			return errors.WithMessage(err, "fail to get output dataset")
		}

		tag := metrics.DataProcessStepTag{
			BizLineID:  strconv.Itoa(int(task.BizLineID)),
			TaskID:     strconv.Itoa(int(stage.TaskID)),
			StageID:    strconv.Itoa(int(stage.ID)),
			StageIndex: strconv.Itoa(int(stage.StageIndex)),
			StepIndex:  strconv.Itoa(int(opt.DatasetProcess.CurrentStepIndex)),
			StepType:   currStep.StepType.String(),
		}
		if err := metrics.LM.DataProcessStepLatency.
			WithTags(&tag).
			Observe(float64(time.Since(outputDataset.CreatedAt).Milliseconds())); err != nil {
			logs.CtxError(ctx, "[DatasetProcessService.turnToNextStep] emit step latency failed: %s", err.Error())
		}

	}

	// 构造当前step执行信息
	if opt.IsFailureTolerable != nil || opt.StepExecMsg != nil {
		if opt.DatasetProcess.Extra.StepInfos == nil {
			opt.DatasetProcess.Extra.StepInfos = map[int8]*entity.StepInfo{}
		}
		stepInfo := opt.DatasetProcess.Extra.StepInfos[opt.DatasetProcess.CurrentStepIndex]
		if stepInfo == nil {
			stepInfo = &entity.StepInfo{}
		}
		stepInfo.IsFailureTolerable = lo.FromPtr(opt.IsFailureTolerable)
		stepInfo.ExecMsg = lo.FromPtr(opt.StepExecMsg)
		opt.DatasetProcess.Extra.StepInfos[opt.DatasetProcess.CurrentStepIndex] = stepInfo

	}

	// 转存数据集至 TOS
	if outputDataset != nil && lo.FromPtr(opt.StepExecMsg) != "skipped" {
		resData, err := commonfaas.PublishByFaaS(ctx, commonfaas.PublishByFaaSOption{
			DatasetVersionID: outputDataset.ID,
			DatasetType:      commonentity.DatasetTypeAnalysis,
			NeedMongoID:      false,
			DataFilePurpose:  commonentity.DataFilePurposeClean,
		})
		if err != nil || resData == nil || len(resData.DataAddress) == 0 {
			logs.CtxError(ctx, "[DatasetProcessService.turnToNextStep] publish by faas error: %v", err)
			return errors.WithMessage(err, "fail to publish dataset to tos by faas")
		}

		if resData.MinID == 0 || resData.MaxID == 0 || resData.MinID > resData.MaxID {
			failMsg := fmt.Sprintf("min id or max id is invalid, min id: %d, max id: %d", resData.MinID, resData.MaxID)
			log.V1.CtxError(ctx, "[DatasetProcessService.turnToNextStep] %s", failMsg)
			return errors.WithMessage(err, failMsg)
		}

		stepInfo := opt.DatasetProcess.Extra.StepInfos[opt.DatasetProcess.CurrentStepIndex]
		if stepInfo == nil {
			stepInfo = &entity.StepInfo{}
		}
		stepInfo.TOSAddress = resData.DataAddress
		opt.DatasetProcess.Extra.StepInfos[opt.DatasetProcess.CurrentStepIndex] = stepInfo

		// 更新数据集信息
		_, _, err = d.DatasetProcessDAO.UpdateDatasetProcessWithinStage(ctx, dal.UpdateDatasetProcessWithinStageOpt{
			ID: opt.DatasetProcess.ID,
			Extra: &entity.DatasetProcessWithinStageExtra{
				StepInfos: opt.DatasetProcess.Extra.StepInfos,
			},
		})
		if err != nil {
			log.V1.CtxError(ctx, "[DatasetProcessService.turnToNextStep] update dataset process error: %v", err)
			return errors.WithMessage(err, "fail to update dataset process")
		}
	}

	if opt.DatasetProcess.CurrentStepIndex+1 < int8(len(steps)) {
		datasetProcessStatus := llminfra.DatasetProcessStatus_Created
		if stageStatus == llminfra.DataProcessStageStatus_Canceling || stageStatus == llminfra.DataProcessStageStatus_Canceled {
			logs.CtxInfo(ctx, "[DatasetProcessService.turnToNextStep] stage status is %v, not processing", stageStatus)
			datasetProcessStatus = llminfra.DatasetProcessStatus_Canceled
		}

		datasetProcessWithinStage, err := d.UpdateDatasetProcessAndPubEvent(ctx, dal.UpdateDatasetProcessWithinStageOpt{
			ID:            opt.DatasetProcess.ID,
			CurrentStatus: lo.ToPtr(opt.DatasetProcess.Status),
			Status:        lo.ToPtr(datasetProcessStatus),
			Extra: &entity.DatasetProcessWithinStageExtra{
				IsCurrentStepSuccess: lo.ToPtr(true),
				StepInfos:            opt.DatasetProcess.Extra.StepInfos,
			},
		})
		if err != nil {
			return errors.WithMessage(err, "fail to update dataset process within stage")
		}

		if datasetProcessStatus == llminfra.DatasetProcessStatus_Canceled {
			logs.CtxInfo(ctx, "[DatasetProcessService.turnToNextStep] dataset process status is %v, not processing, pass", datasetProcessStatus)
			return nil
		}

		if pubErr := d.DataProcessMqService.PubEvent(ctx, &entity.DataProcessEvent{
			Username:         opt.Event.Username,
			IsServiceAccount: opt.Event.IsServiceAccount,
			EventType:        consts.EventType_DatasetEvent_CopyDataset,
			TaskID:           opt.Event.TaskID,
			TaskName:         opt.Event.TaskName,
			StageID:          lo.ToPtr(stage.ID),
			DatasetParam: &entity.DatasetParam{
				DatasetProcessWithinStageID: lo.ToPtr(datasetProcessWithinStage.ID),
				NextStepIndex:               lo.ToPtr(datasetProcessWithinStage.CurrentStepIndex + 1),
			},
		}); pubErr != nil {
			logs.CtxError(ctx, "[DatasetProcessService.turnToNextStep] pub event err, %v", pubErr)
		}
	} else {
		_, updateErr := d.UpdateDatasetProcessAndPubEvent(ctx, dal.UpdateDatasetProcessWithinStageOpt{
			ID:     opt.DatasetProcess.ID,
			Status: lo.ToPtr(llminfra.DatasetProcessStatus_Success),
			Extra: &entity.DatasetProcessWithinStageExtra{
				IsCurrentStepSuccess: lo.ToPtr(true),
				StepInfos:            opt.DatasetProcess.Extra.StepInfos,
			},
		})
		if updateErr != nil {
			logs.CtxError(ctx, "[DatasetProcessService.turnToNextStep] update dataset process error: %v", updateErr)
		}
	}
	return nil
}

func (d *DatasetProcessWithinStageService) OnDatasetInferFinish(ctx context.Context, event *entity.DataProcessEvent) (err error) {
	logs.CtxInfo(ctx, "[DatasetProcessService.OnDatasetInferFinish] in, event: %v", event)

	datasetProcessWithinStage, err := d.DatasetProcessDAO.GetDatasetProcessWithinStage(ctx, dal.GetDatasetProcessWithinStageOpt{
		OutputDatasetType:   event.DatasetParam.OutputDatasetType,
		OutputDatasetUniqID: lo.FromPtr(event.DatasetParam.OutputDatasetUniqID),
	})
	if err != nil {
		logs.CtxError(ctx, "[DatasetProcessService.OnDatasetInferFinish] error: %v", err)
		return nil
	}

	if datasetProcessWithinStage.LogID != "" {
		ctx = ctxvalues.SetLogID(ctx, datasetProcessWithinStage.LogID)
	}
	ctx = logs.CtxAddKVs(ctx, "dataset_process_id", datasetProcessWithinStage.ID)
	logs.CtxInfo(ctx, "[DatasetProcessService.OnDatasetInferFinish] start, after log id changed, event: %v", event)

	// 执行条件校验
	if datasetProcessWithinStage.Status != llminfra.DatasetProcessStatus_Processing {
		logs.CtxInfo(ctx, "[DatasetProcessService.OnDatasetInferFinish] status is %v, not processing, pass", datasetProcessWithinStage.Status)
		return nil
	}

	eventOutputDatasetUniqID := lo.FromPtr(event.DatasetParam.OutputDatasetUniqID)
	if eventOutputDatasetUniqID != *datasetProcessWithinStage.OutputDatasetUniqID {
		logs.CtxInfo(ctx, "[DatasetProcessService.OnDatasetInferFinish] output dataset uniq id not match, event: %d, cur: %d, pass",
			eventOutputDatasetUniqID, *datasetProcessWithinStage.OutputDatasetUniqID)
		return nil
	}

	defer func() {
		if err != nil && !errors.Is(err, commonconsts.ErrUpdateNoMatchedRecord) {
			logs.CtxError(ctx, "[DatasetProcessService.OnDatasetInferFinish] error: %v", err)
			d.updateProcessFailStatus(ctx, datasetProcessWithinStage.ID, llminfra.FailType_DataProcessDatasetProcessErr, lo.ToPtr(err.Error()))
		}
	}()

	stage, err := d.StageDAO.GetDataProcessStage(ctx, dal.GetDataProcessStageOpt{
		ID: &datasetProcessWithinStage.StageID,
	})
	if err != nil {
		return errors.WithMessage(err, "fail to get stage")
	}

	task, err := d.TaskDAO.GetDataProcessTask(ctx, dal.GetDataProcessTaskOption{ID: &stage.TaskID})
	if err != nil {
		logs.CtxError(ctx, "[DatasetProcessService.OnDatasetInferFinish] fail to get task")
		return err
	}
	event.Username = stage.Extra.Username
	event.IsServiceAccount = lo.FromPtr(stage.Extra.IsServiceAccount)
	event.TaskID = stage.TaskID
	event.StageID = lo.ToPtr(stage.ID)
	event.TaskName = task.Name

	switch lo.FromPtr(event.DatasetParam.OutputDatasetInferStatus) {
	case llminfra.DatasetInferenceStatus_Failed:
		isFailureTolerable, stepExecMsg := d.isFailureTolerable(ctx, event, datasetProcessWithinStage)
		if !isFailureTolerable {
			logs.CtxInfo(ctx, "[DatasetProcessService.OnDatasetInferFinish] infer in step %d failed", datasetProcessWithinStage.CurrentStepIndex)
			d.updateProcessFailStatus(ctx, datasetProcessWithinStage.ID, lo.FromPtr(event.DatasetParam.FailType), event.DatasetParam.FailMsg)
			return nil
		} else {
			logs.CtxInfo(ctx, "[DatasetProcessService.OnDatasetInferFinish] infer in step %d failed, but is tolerable", datasetProcessWithinStage.CurrentStepIndex)
			return d.turnToNextStep(ctx, TurnToNextStepOption{
				DatasetProcess:     datasetProcessWithinStage,
				StageID:            &stage.ID,
				Event:              event,
				IsFailureTolerable: &isFailureTolerable,
				StepExecMsg:        &stepExecMsg,
			})
		}
	case llminfra.DatasetInferenceStatus_Success:
		return d.turnToNextStep(ctx, TurnToNextStepOption{
			DatasetProcess:     datasetProcessWithinStage,
			StageID:            &stage.ID,
			Event:              event,
			IsFailureTolerable: lo.ToPtr(false),
			StepExecMsg:        lo.ToPtr(""),
		})
	default:
		return nil
	}
}

func (d *DatasetProcessWithinStageService) isFailureTolerable(
	ctx context.Context,
	event *entity.DataProcessEvent,
	datasetProcessWithinStage *entity.DatasetProcessWithinStage,
) (isFailureTolerable bool, msg string) {
	failCaseNum := lo.FromPtr(event.DatasetParam.FailCaseNum)
	if failCaseNum == 0 {
		logs.CtxError(ctx, "[isFailureTolerable] infer task failed, but failNum is 0, dataset id: %d", *datasetProcessWithinStage.OutputDatasetUniqID)
		return
	}

	totalCaseNum := lo.FromPtr(event.DatasetParam.TotalCaseNum)
	if totalCaseNum == 0 {
		logs.CtxError(ctx, "[isFailureTolerable] total case num is 0, dataset id: %d", *datasetProcessWithinStage.OutputDatasetUniqID)
		return
	}

	dataProcessConfig := d.LLMInfraDataProcessConfig.GetValue()
	failRatio := float32(failCaseNum) / float32(totalCaseNum)
	logs.CtxInfo(ctx, "[isFailureTolerable] total case num: %d, fail case num: %d, fail ratio: %v, ModelInferFailTolerateRatio: %v", totalCaseNum, failCaseNum, failRatio, dataProcessConfig.ModelInferFailTolerateRatio)

	isFailureTolerable = failRatio <= dataProcessConfig.ModelInferFailTolerateRatio
	if isFailureTolerable {
		msg = fmt.Sprintf("总case数：%d, 失败case数：%d", totalCaseNum, failCaseNum)
	}
	return
}

func (d *DatasetProcessWithinStageService) checkDataNum(
	ctx context.Context,
	event *entity.DataProcessEvent,
	datasetProcessWithinStage *entity.DatasetProcessWithinStage,
) (int64, error) {
	outputDatasetUniqID := lo.FromPtr(datasetProcessWithinStage.OutputDatasetUniqID)
	if outputDatasetUniqID == 0 {
		return 0, errors.New("invalid output dataset id 0")
	}

	outputDataset, err := d.AnalysisDatasetService.GetAnalysisDataset(ctx, &analysisservice.GetAnalysisDatasetOption{
		Username:               event.Username,
		IsServiceAccount:       event.IsServiceAccount,
		DatasetID:              outputDatasetUniqID,
		DisablePermissionCheck: true,
	})
	if err != nil {
		return 0, errors.WithMessage(err, "fail to get output dataset")
	}
	dataNum := lo.FromPtr(outputDataset.TotalNum)
	if dataNum == 0 {
		_, updateErr := d.UpdateDatasetProcessAndPubEvent(ctx, dal.UpdateDatasetProcessWithinStageOpt{
			ID:       datasetProcessWithinStage.ID,
			Status:   lo.ToPtr(llminfra.DatasetProcessStatus_Failed),
			FailType: lo.ToPtr(llminfra.FailType_DataProcessDatasetProcessErr),
			FailMsg:  lo.ToPtr("input data num of current step is 0"),
		})
		if updateErr != nil {
			logs.CtxError(ctx, "[DatasetProcessService.updateProcessFailStatus] update dataset process error: %v", updateErr)
			return 0, updateErr
		}
	}
	return dataNum, nil
}

func (d *DatasetProcessWithinStageService) OnDatasetProcessRetry(ctx context.Context, event *entity.DataProcessEvent) (err error) {
	ctx = logs.CtxAddKVs(ctx, "dataset_process_id", *event.DatasetParam.DatasetProcessWithinStageID)
	logs.CtxInfo(ctx, "[DatasetProcessService.OnDatasetProcessRetry] start, event: %v", event)
	datasetProcessWithinStage, err := d.DatasetProcessDAO.GetDatasetProcessWithinStage(ctx, dal.GetDatasetProcessWithinStageOpt{
		ID: *event.DatasetParam.DatasetProcessWithinStageID,
	})
	if err != nil {
		logs.CtxError(ctx, "[DatasetProcessService.OnDatasetProcessRetry] error: %v", err)
		return nil
	}

	switch datasetProcessWithinStage.Status {
	case llminfra.DatasetProcessStatus_Failed,
		llminfra.DatasetProcessStatus_Canceled:
		logs.CtxInfo(ctx, "[DatasetProcessService.OnDatasetProcessRetry] status is %v, start to retry", datasetProcessWithinStage.Status)
	default:
		logs.CtxInfo(ctx, "[DatasetProcessService.OnDatasetProcessRetry] status is %v, pass", datasetProcessWithinStage.Status)
		return nil
	}

	defer func() {
		if err != nil && !errors.Is(err, commonconsts.ErrUpdateNoMatchedRecord) {
			logs.CtxError(ctx, "[DatasetProcessService.OnDatasetProcessRetry] error: %v", err)
			d.updateProcessFailStatus(ctx, datasetProcessWithinStage.ID, llminfra.FailType_DataProcessDatasetProcessErr, lo.ToPtr(err.Error()))
		}
	}()

	nextStepIndex := datasetProcessWithinStage.GetNextStepToContinueRun()

	// 数据集没有复制
	if !datasetProcessWithinStage.IsStepOutputDatasetReady(nextStepIndex) {
		datasetProcessWithinStage, err = d.ResetToTargetStepWithSuccess(ctx, service.ResetToTargetStepOpt{
			DatasetProcess:            datasetProcessWithinStage,
			TargetStepIndex:           nextStepIndex - 1,
			KeepNextStepOutputDataset: false,
		})
		if err != nil {
			logs.CtxError(ctx, "[DatasetProcessService.OnDatasetProcessRetry] fail to reset to target step, error: %v", err)
			return err
		}
		return d.turnToNextStep(ctx, TurnToNextStepOption{
			DatasetProcess: datasetProcessWithinStage,
			StageID:        event.StageID,
			Event:          event,
		})
	}

	dataNum, err := d.checkDataNum(ctx, event, datasetProcessWithinStage)
	if dataNum == 0 {
		return err
	}

	stage, err := d.StageDAO.GetDataProcessStage(ctx, dal.GetDataProcessStageOpt{
		ID: &datasetProcessWithinStage.StageID,
	})
	if err != nil {
		return errors.WithMessage(err, "fail to get stage")
	}

	nextStep := stage.StageConfig.Steps[nextStepIndex]
	switch nextStep.StepType {
	case llminfra.DataProcessStepType_DataCleanByFaaS:
		logs.CtxInfo(ctx, "[DatasetProcessService.OnDatasetProcessRetry] start retry data clean step index: %v", nextStepIndex)
		// 数据清洗直接发起新任务
		datasetProcessWithinStage, err = d.ResetToTargetStepWithSuccess(ctx, service.ResetToTargetStepOpt{
			DatasetProcess:            datasetProcessWithinStage,
			TargetStepIndex:           nextStepIndex - 1,
			KeepNextStepOutputDataset: true,
		})
		if err != nil {
			logs.CtxError(ctx, "[DatasetProcessService.OnDatasetProcessRetry] fail to reset to target step, error: %v", err)
			return err
		}
		return d.processNextStep(ctx, datasetProcessWithinStage, event)
	case llminfra.DataProcessStepType_ModelInference:
		// 模型推理根据是否有历史推理任务做不同操作
		inferenceTaskID := datasetProcessWithinStage.GetModelInferTaskID(nextStepIndex)
		logs.CtxInfo(ctx, "[DatasetProcessService.OnDatasetProcessRetry] start retry model infer step index: %v, inference task id: %d", nextStepIndex, inferenceTaskID)

		isAbleToRetryInferTask, err := d.EvalTaskService.CanRetryFromInterrupt(ctx, evaluationservice.CanRetryFromInterruptOption{
			EvalTaskID:     inferenceTaskID,
			DatasetCaseNum: dataNum,
		})
		if err != nil {
			logs.CtxError(ctx, "[DatasetProcessService.OnDatasetProcessRetry] CanRetryFromInterrupt: %v", err)
			return err
		}

		if !isAbleToRetryInferTask {
			logs.CtxInfo(ctx, "[DatasetProcessService.OnDatasetProcessRetry] no inference task, reset step index and run next step")
			datasetProcessWithinStage, err = d.ResetToTargetStepWithSuccess(ctx, service.ResetToTargetStepOpt{
				DatasetProcess:            datasetProcessWithinStage,
				TargetStepIndex:           nextStepIndex - 1,
				KeepNextStepOutputDataset: true,
			})
			if err != nil {
				logs.CtxError(ctx, "[DatasetProcessService.OnDatasetProcessRetry] fail to reset to target step, error: %v", err)
				return err
			}
			return d.processNextStep(ctx, datasetProcessWithinStage, event)
		} else {
			logs.CtxInfo(ctx, "[DatasetProcessService.OnDatasetProcessRetry] retry inference task id: %v at step index: %v", inferenceTaskID, nextStepIndex)
			datasetProcessWithinStage, err = d.UpdateDatasetProcessAndPubEvent(ctx, dal.UpdateDatasetProcessWithinStageOpt{
				ID:            datasetProcessWithinStage.ID,
				CurrentStatus: lo.ToPtr(datasetProcessWithinStage.Status),
				Status:        lo.ToPtr(llminfra.DatasetProcessStatus_Processing),
				FailType:      lo.ToPtr(llminfra.FailType_Success),
				FailMsg:       lo.ToPtr(""),
			})
			if err != nil {
				return errors.WithMessage(err, "fail to update dataset process status")
			}

			if err = d.resetAnalysisInferStatus(ctx, *datasetProcessWithinStage.OutputDatasetUniqID); err != nil {
				return err
			}

			account := &authentity.Account{
				Username: event.Username,
				Type:     lo.Ternary(event.IsServiceAccount, "service_account", "user_account"),
				Email:    fmt.Sprintf("%<EMAIL>", event.Username),
			}
			_, err = d.EvalTaskService.RetryEvalTaskFailedCases(ctx, evaluationservice.RetryEvalTaskFailedCasesOption{
				Account:    account,
				EvalTaskID: inferenceTaskID,
			})
			if err != nil {
				return errors.WithMessage(err, "fail to retry eval task")
			}
		}
	default:
		logs.CtxInfo(ctx, "[DatasetProcessService.OnDatasetProcessRetry] step type is %v, not need to retry", nextStep.StepType)
	}
	return
}

func (d *DatasetProcessWithinStageService) resetAnalysisInferStatus(ctx context.Context, id int64) error {
	dataset, err := d.AnalysisDatasetDal.GetAnalysisDataset(ctx, analysisdatasetdal.GetAnalysisDatasetOption{DatasetID: id})
	if err != nil {
		logs.CtxError(ctx, "get analysis dataset error, %v", err)
		return err
	}
	for k, _ := range dataset.Extra.InferenceSubTaskInfos {
		dataset.Extra.InferenceSubTaskInfos[k] = llminfra.EvalStatus_Running
	}

	_, err = d.AnalysisDatasetDal.UpdateAnalysisDataset(ctx, analysisdatasetdal.UpdateAnalysisDatasetOption{
		DatasetID: id,
		Extra: &analysisdatasetentity.AnalysisDatasetExtra{
			InferenceFailReason:   lo.ToPtr(""),
			InferenceSubTaskInfos: dataset.Extra.InferenceSubTaskInfos,
		},
		InferenceStatus: lo.ToPtr(llminfra.DatasetInferenceStatus_Inferring),
	})
	if err != nil {
		logs.CtxError(ctx, "update dataset err:%v", err)
		return err
	}
	return nil
}

func (d *DatasetProcessWithinStageService) OnDatasetProcessCancel(ctx context.Context, event *entity.DataProcessEvent) (err error) {
	ctx = logs.CtxAddKVs(ctx, "dataset_process_id", *event.DatasetParam.DatasetProcessWithinStageID)
	logs.CtxInfo(ctx, "[DatasetProcessService.OnDatasetProcessCancel] start, event: %v", event)
	datasetProcessWithinStage, err := d.DatasetProcessDAO.GetDatasetProcessWithinStage(ctx, dal.GetDatasetProcessWithinStageOpt{
		ID: *event.DatasetParam.DatasetProcessWithinStageID,
	})
	if err != nil {
		logs.CtxError(ctx, "[DatasetProcessService.OnDatasetProcessCancel] error: %v", err)
		return nil
	}

	switch datasetProcessWithinStage.Status {
	case llminfra.DatasetProcessStatus_ReadyToProcess,
		llminfra.DatasetProcessStatus_Processing,
		llminfra.DatasetProcessStatus_Created:
		logs.CtxInfo(ctx, "[DatasetProcessService.OnDatasetProcessCancel] status is %v, start to cancel", datasetProcessWithinStage.Status)
	case llminfra.DatasetProcessStatus_InputDatasetCopying:
		logs.CtxInfo(ctx, "[DatasetProcessService.OnDatasetProcessCancel] status is %v, will cancel in processNextStep", datasetProcessWithinStage.Status)
		return nil
	default:
		logs.CtxInfo(ctx, "[DatasetProcessService.OnDatasetProcessCancel] status is %v, pass", datasetProcessWithinStage.Status)
		// stage层cancel时非终态，执行回调时状态变化，需要发响应消息
		if pubErr := d.DataProcessMqService.PubEvent(ctx, &entity.DataProcessEvent{
			EventType: consts.EventType_StageEvent_DatasetProcessStatusChanged,
			StageID:   lo.ToPtr(datasetProcessWithinStage.StageID),
		}); pubErr != nil {
			logs.CtxError(ctx, "[DatasetProcessService.updateProcessStatus] pub event error: %v", pubErr)
		}
		return nil
	}

	defer func() {
		if err != nil && !errors.Is(err, commonconsts.ErrUpdateNoMatchedRecord) {
			logs.CtxError(ctx, "[DatasetProcessService.OnDatasetProcessCancel] error: %v", err)
			d.updateProcessFailStatus(ctx, datasetProcessWithinStage.ID, llminfra.FailType_DataProcessDatasetProcessErr, lo.ToPtr(err.Error()))
		}
	}()

	if datasetProcessWithinStage.Status == llminfra.DatasetProcessStatus_ReadyToProcess ||
		datasetProcessWithinStage.Status == llminfra.DatasetProcessStatus_Created ||
		datasetProcessWithinStage.CurrentStepIndex == -1 {
		_, err = d.UpdateDatasetProcessAndPubEvent(ctx, dal.UpdateDatasetProcessWithinStageOpt{
			ID:            datasetProcessWithinStage.ID,
			CurrentStatus: lo.ToPtr(datasetProcessWithinStage.Status),
			Status:        lo.ToPtr(llminfra.DatasetProcessStatus_Canceled),
		})
		if err != nil {
			return errors.WithMessage(err, "fail to set status to canceled")
		}
		return nil
	}

	stage, err := d.StageDAO.GetDataProcessStage(ctx, dal.GetDataProcessStageOpt{
		ID: &datasetProcessWithinStage.StageID,
	})
	if err != nil {
		return errors.WithMessage(err, "fail to get stage")
	}

	curStep := stage.StageConfig.Steps[datasetProcessWithinStage.CurrentStepIndex]
	switch curStep.StepType {
	case llminfra.DataProcessStepType_DataCleanByFaaS:
		dataCleanTaskID := datasetProcessWithinStage.Extra.DataCleanStepTaskIDMap[datasetProcessWithinStage.CurrentStepIndex]
		if dataCleanTaskID > 0 {
			if err = d.DataCleanService.CancelDataCleanTask(ctx, dataCleanTaskID); err != nil {
				return errors.WithMessage(err, "fail to cancel data clean task")
			}
		}
	case llminfra.DataProcessStepType_ModelInference:
		if lo.FromPtr(datasetProcessWithinStage.OutputDatasetUniqID) == 0 {
			logs.CtxInfo(ctx, "[DatasetProcessService.OnDatasetProcessCancel] output dataset uniq id is 0, not need to cancel")
		} else {
			account := &authentity.Account{
				Username: event.Username,
				Type:     lo.Ternary(event.IsServiceAccount, "service_account", "user_account"),
				Email:    fmt.Sprintf("%<EMAIL>", event.Username),
			}
			inferenceTaskID := datasetProcessWithinStage.Extra.ModelInferStepTaskIDMap[datasetProcessWithinStage.CurrentStepIndex]
			if inferenceTaskID > 0 {
				if err = d.EvalTaskService.CancelEvalTask(ctx, evaluationservice.CancelEvalTaskOption{
					Account:    account,
					EvalTaskID: inferenceTaskID,
				}); err != nil {
					return errors.WithMessage(err, "fail to cancel model inference task")
				}
			}
		}
	default:
		logs.CtxInfo(ctx, "[DatasetProcessService.OnDatasetProcessCancel] step type is %v, not need to cancel", curStep.StepType)
	}

	_, err = d.UpdateDatasetProcessAndPubEvent(ctx, dal.UpdateDatasetProcessWithinStageOpt{
		ID:            datasetProcessWithinStage.ID,
		CurrentStatus: lo.ToPtr(datasetProcessWithinStage.Status),
		Status:        lo.ToPtr(llminfra.DatasetProcessStatus_Canceled),
	})
	return err
}

// DeleteDatasetProcessWithinStage 删除没有保存step执行结果的记录
func (d *DatasetProcessWithinStageService) DeleteDatasetProcessWithinStage(ctx context.Context, stageID int64, stepIndexGteValue int8) error {
	dataProcesses, err := d.DatasetProcessDAO.ListDatasetProcessWithinStage(ctx, stageID)
	if err != nil {
		return errors.WithMessage(err, "fail to list dataset process")
	}

	if len(dataProcesses) == 0 {
		return nil
	}

	processIDToDel := make([]int64, 0)
	outputDatasetIDToDel := make([]int64, 0)
	for _, dataProcess := range dataProcesses {
		if !consts.IsFinalDatasetProcessStatus(dataProcess.Status) {
			logs.CtxWarn(ctx, "[DatasetProcessService.DeleteDatasetProcessWithinStage] dataset process status is %v, not final status", dataProcess.Status)
			return errors.New("dataset process status is not final status")
		}

		// 如果没有执行到stepIndexGteValue，可以继续执行，不用删除
		if dataProcess.GetNextStepToContinueRun() < stepIndexGteValue {
			logs.CtxInfo(ctx, "[DatasetProcessService.DeleteDatasetProcessWithinStage] next step less than target, not need to delete")
			continue
		} else {
			// 如果执行过且保留所有stepIndexGteValue以前的信息时，可以reset状态再继续执行，不用删除
			allPreStepInfoExists := true
			for i := int8(0); i < stepIndexGteValue; i++ {
				if dataProcess.GetStepInfo(i) == nil {
					allPreStepInfoExists = false
					break
				}
			}
			if allPreStepInfoExists {
				logs.CtxInfo(ctx, "[DatasetProcessService.DeleteDatasetProcessWithinStage] all pre step info exists, not need to delete")
				continue
			}
		}

		processIDToDel = append(processIDToDel, dataProcess.ID)
		for _, stepInfo := range dataProcess.Extra.StepInfos {
			if lo.FromPtr(stepInfo.OutputDatasetUniqID) > 0 && stepInfo.OutputDatasetType == llminfra.DatasetType_Analysis {
				outputDatasetIDToDel = append(outputDatasetIDToDel, lo.FromPtr(stepInfo.OutputDatasetUniqID))
			}
		}
	}

	logs.CtxInfo(ctx, "[DatasetProcessService.DeleteDatasetProcessWithinStage] delete process id: %v", processIDToDel)
	if len(processIDToDel) > 0 {
		if err = d.DatasetProcessDAO.DeleteDatasetProcessWithinStage(ctx, dal.DeleteDatasetProcessOpt{
			IDs:               processIDToDel,
			DisableSoftDelete: true,
		}); err != nil {
			return errors.WithMessage(err, "fail to delete dataset process")
		}
	}

	// 清理数据集
	gopool.CtxGo(ctx, func() {
		logs.CtxInfo(ctx, "[DatasetProcessService.DeleteDatasetProcessWithinStage] delete output dataset ids: %v", outputDatasetIDToDel)
		for _, datasetID := range outputDatasetIDToDel {
			if err = d.AnalysisDatasetService.DeleteAnalysisDataset(ctx, &analysisservice.DeleteAnalysisDatasetOption{
				Username:               "service",
				IsServiceAccount:       true,
				DatasetID:              datasetID,
				DisableSoftDelete:      true,
				DisablePermissionCheck: true,
			}); err != nil {
				logs.CtxError(ctx, "[DatasetProcessService.DeleteDatasetProcessWithinStage] fail to delete analysis dataset, %v", err)
			}
		}
	})
	return nil
}

func (d *DatasetProcessWithinStageService) UpdateDatasetProcessAndPubEvent(ctx context.Context, opt dal.UpdateDatasetProcessWithinStageOpt) (datasetProcessWithinStage *entity.DatasetProcessWithinStage, err error) {
	datasetProcessWithinStage, statusChanged, err := d.DatasetProcessDAO.UpdateDatasetProcessWithinStage(ctx, opt)
	if err != nil {
		err = errors.WithMessage(err, "fail to update dataset process within stage")
		logs.CtxError(ctx, "[DatasetProcessService.OnDatasetCopying] err: %v", err)
		return nil, err
	}

	if statusChanged {
		if pubErr := d.DataProcessMqService.PubEvent(ctx, &entity.DataProcessEvent{
			EventType: consts.EventType_StageEvent_DatasetProcessStatusChanged,
			StageID:   lo.ToPtr(datasetProcessWithinStage.StageID),
		}); pubErr != nil {
			logs.CtxError(ctx, "[DatasetProcessService.updateProcessStatus] pub event error: %v", pubErr)
		}
	}
	return datasetProcessWithinStage, nil
}

func (d *DatasetProcessWithinStageService) ResetToTargetStepWithSuccess(ctx context.Context, opt service.ResetToTargetStepOpt) (*entity.DatasetProcessWithinStage, error) {
	ctx = logs.CtxAddKVs(ctx, "dataset_process_id", opt.DatasetProcess.ID)
	logs.CtxInfo(ctx, "[DatasetProcessService.ResetToTargetStepWithSuccess] start, opt: %v", opt)
	datasetProcess := opt.DatasetProcess
	dataCleanStepTaskIDMap := make(map[int8]int64)
	modelInferStepTaskIDMap := make(map[int8]int64)
	stepInfos := make(map[int8]*entity.StepInfo)
	var finalOutputDatasetType *llminfra.DatasetType
	var finalOutputDatasetUniqID *int64
	for i := int8(0); i <= opt.TargetStepIndex; i++ {
		if dataCleanTaskID := datasetProcess.GetDataCleanTaskID(i); dataCleanTaskID > 0 {
			dataCleanStepTaskIDMap[i] = dataCleanTaskID
		}
		if modelInferTaskID := datasetProcess.GetModelInferTaskID(i); modelInferTaskID > 0 {
			modelInferStepTaskIDMap[i] = modelInferTaskID
		}
		if stepInfo := datasetProcess.GetStepInfo(i); stepInfo != nil {
			stepInfos[i] = stepInfo
			if stepInfo.OutputDatasetType != llminfra.DatasetType_Unknown && lo.FromPtr(stepInfo.OutputDatasetUniqID) > 0 {
				finalOutputDatasetType = &stepInfo.OutputDatasetType
				finalOutputDatasetUniqID = stepInfo.OutputDatasetUniqID
			}
		}
	}

	deleteOutputIndex := opt.TargetStepIndex + 1
	if opt.KeepNextStepOutputDataset {
		deleteOutputIndex += 1
		if stepInfo := datasetProcess.GetStepInfo(opt.TargetStepIndex + 1); stepInfo != nil {
			stepInfos[opt.TargetStepIndex+1] = stepInfo
			finalOutputDatasetType = &stepInfo.OutputDatasetType
			finalOutputDatasetUniqID = stepInfo.OutputDatasetUniqID
		}
	}

	// 删除无用数据集
	gopool.CtxGo(ctx, func() {
		for index, stepInfo := range datasetProcess.Extra.StepInfos {
			if index >= deleteOutputIndex && lo.FromPtr(stepInfo.OutputDatasetUniqID) > 0 && stepInfo.OutputDatasetType == llminfra.DatasetType_Analysis {
				if err := d.AnalysisDatasetService.DeleteAnalysisDataset(ctx, &analysisservice.DeleteAnalysisDatasetOption{
					Username:               "service",
					IsServiceAccount:       true,
					DatasetID:              lo.FromPtr(stepInfo.OutputDatasetUniqID),
					DisableSoftDelete:      true,
					DisablePermissionCheck: true,
				}); err != nil {
					logs.CtxError(ctx, "[DatasetProcessService.ResetToTargetStepWithSuccess] fail to delete analysis dataset, %v", err)
				}
			}
		}
	})

	resetOutputDataset := false
	if finalOutputDatasetType == nil && finalOutputDatasetUniqID == nil {
		resetOutputDataset = true
	}
	updateOpt := dal.UpdateDatasetProcessWithinStageOpt{
		ID:                  datasetProcess.ID,
		CurrentStepIndex:    lo.ToPtr(opt.TargetStepIndex),
		OutputDatasetType:   finalOutputDatasetType,
		OutputDatasetUniqID: finalOutputDatasetUniqID,
		ResetOutputDataset:  resetOutputDataset,
		Extra: &entity.DatasetProcessWithinStageExtra{
			IsCurrentStepSuccess:    lo.ToPtr(true),
			DataCleanStepTaskIDMap:  dataCleanStepTaskIDMap,
			ModelInferStepTaskIDMap: modelInferStepTaskIDMap,
			StepInfos:               stepInfos,
		},
	}
	return d.UpdateDatasetProcessAndPubEvent(ctx, updateOpt)
}
