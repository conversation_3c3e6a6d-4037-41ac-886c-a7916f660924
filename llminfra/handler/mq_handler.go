package handler

import (
	"context"
	"encoding/json"

	analysisdatasetentity "code.byted.org/devgpt/kiwis/llminfra/analysisdataset/entity"
	analysisdatasethandler "code.byted.org/devgpt/kiwis/llminfra/analysisdataset/handler"
	dataprocesshandler "code.byted.org/devgpt/kiwis/llminfra/dataprocess/handler"
	evaluationhandler "code.byted.org/devgpt/kiwis/llminfra/evaluation/handler"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/rocketmq/rocketmq-go-proxy/pkg/proto"
	"github.com/pkg/errors"
	"go.uber.org/fx"
)

type AnalysisDatasetMqHandlerOption struct {
	fx.In
	EvaluationHandler  *evaluationhandler.AnalysisDatasetMqHandler
	DataProcessHandler *dataprocesshandler.AnalysisDatasetMqHandler
	AnalysisHandler    *analysisdatasethandler.AnalysisDatasetMqHandler
}

func NewAnalysisDatasetMqHandler(opt AnalysisDatasetMqHandlerOption) *AnalysisDatasetMqHandler {
	return &AnalysisDatasetMqHandler{
		EvaluationHandler:  opt.Evaluation<PERSON><PERSON>ler,
		DataProcessHandler: opt.DataProcessHandler,
		AnalysisHandler:    opt.AnalysisHandler,
	}
}

type AnalysisDatasetMqHandler struct {
	EvaluationHandler  *evaluationhandler.AnalysisDatasetMqHandler
	DataProcessHandler *dataprocesshandler.AnalysisDatasetMqHandler
	AnalysisHandler    *analysisdatasethandler.AnalysisDatasetMqHandler
}

func (h *AnalysisDatasetMqHandler) HandleEvent(ctx context.Context, message []byte, ext *proto.MessageExt) (err error) {
	event := analysisdatasetentity.Event{}
	if err = json.Unmarshal(message, &event); err != nil {
		logs.CtxError(ctx, "[AnalysisDatasetMqHandler] failed to unmarshal event: %v", err)
		return
	}
	logs.CtxInfo(ctx, "[AnalysisDatasetMqHandler] receive event: %v", event)
	err = h.handle(ctx, &event, ext)
	if err != nil {
		logs.CtxError(ctx, "failed to handle event: %v", err)
		return
	}
	logs.CtxInfo(ctx, "[AnalysisDatasetMqHandler] handle evaluation event success")

	return
}

func (h *AnalysisDatasetMqHandler) handle(ctx context.Context, event *analysisdatasetentity.Event, ext *proto.MessageExt) (err error) {
	if err = h.EvaluationHandler.HandleEvent(ctx, event, ext); err != nil {
		return errors.WithMessage(err, "EvaluationHandler")
	}

	if err = h.DataProcessHandler.HandleEvent(ctx, event, ext); err != nil {
		return errors.WithMessage(err, "DataProcessHandler")
	}

	if err = h.AnalysisHandler.HandleEvent(ctx, event, ext); err != nil {
		return errors.WithMessage(err, "AnalysisHandler")
	}

	return
}
