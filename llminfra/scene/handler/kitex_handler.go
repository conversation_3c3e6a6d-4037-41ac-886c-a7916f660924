package handler

import (
	"context"

	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/common"
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/llminfra"
	authhandler "code.byted.org/devgpt/kiwis/copilotstack/common/auth/handler"
	"code.byted.org/devgpt/kiwis/lib/kitex"
	"code.byted.org/devgpt/kiwis/llminfra/scene/objconv"
	"code.byted.org/devgpt/kiwis/llminfra/scene/service"
	"code.byted.org/devgpt/kiwis/llminfra/utils"
)

type KitexHandler struct {
	SceneService service.SceneService
}

func (h *KitexHandler) CreateScene(ctx context.Context, req *llminfra.CreateSceneRequest) (r *llminfra.CreateSceneResponse, err error) {
	if req.BizLineID == 0 {
		return nil, kitex.NewKitexError(common.ErrorCode_ErrParamInvalid, errors.Errorf("biz line id is required"))
	}
	if req.Name == "" {
		return nil, kitex.NewKitexError(common.ErrorCode_ErrParamInvalid, errors.Errorf("name is required"))
	}
	if req.Desc == "" {
		return nil, kitex.NewKitexError(common.ErrorCode_ErrParamInvalid, errors.Errorf("desc is required"))
	}
	account, _ := authhandler.GetAccount(ctx)
	if account == nil {
		return nil, kitex.NewKitexError(common.ErrorCode_ErrNoAuth, errors.Errorf("account is required"))
	}

	scene, err := h.SceneService.CreateScene(ctx, service.CreateSceneOption{
		BizLineID: req.BizLineID,
		Name:      req.Name,
		Desc:      req.Desc,
		Account:   account,
	})
	if err != nil {
		return nil, utils.ConvToKitexError(errors.WithMessagef(err, "failed to create scene"))
	}
	r = &llminfra.CreateSceneResponse{
		Scene: objconv.SceneEntity2DTO(scene),
	}
	return
}

func (h *KitexHandler) GetScene(ctx context.Context, req *llminfra.GetSceneRequest) (r *llminfra.GetSceneResponse, err error) {
	if req.SceneID == 0 {
		return nil, kitex.NewKitexError(common.ErrorCode_ErrParamInvalid, errors.Errorf("scene id should not be zero"))
	}

	account, _ := authhandler.GetAccount(ctx)
	if account == nil {
		return nil, kitex.NewKitexError(common.ErrorCode_ErrNoAuth, errors.Errorf("account is required"))
	}
	scene, err := h.SceneService.GetScene(ctx, service.GetSceneOption{
		Username:               account.Username,
		IsServiceAccount:       account.IsServiceAccount(),
		SceneID:                req.SceneID,
		WithSoftDeletedRecords: req.WithSoftDeletedRecords,
		NeedPermissionInject:   true,
		NeedPermissionCheck:    true,
	})
	if err != nil {
		return nil, utils.ConvToKitexError(errors.WithMessagef(err, "failed to get scene"))
	}
	r = &llminfra.GetSceneResponse{
		Scene: objconv.SceneEntity2DTO(scene),
	}

	return
}

func (h *KitexHandler) ListScene(ctx context.Context, req *llminfra.ListSceneRequest) (r *llminfra.ListSceneResponse, err error) {
	if req.BizLineID == 0 {
		return nil, kitex.NewKitexError(common.ErrorCode_ErrParamInvalid, errors.Errorf("biz line id is required"))
	}
	if len(req.Creators) == 0 && req.Creator != nil {
		req.Creators = []string{*req.Creator}
	}
	account, _ := authhandler.GetAccount(ctx)
	if account == nil {
		return nil, kitex.NewKitexError(common.ErrorCode_ErrNoAuth, errors.Errorf("account is required"))
	}

	opt := service.ListSceneOption{
		Username:               account.Username,
		IsServiceAccount:       account.IsServiceAccount(),
		BizLineID:              &req.BizLineID,
		PageNum:                req.PageNum,
		PageSize:               req.PageSize,
		IsFavorite:             req.IsFavorite,
		SceneIDs:               req.SceneIDs,
		Creators:               req.Creators,
		Name:                   req.Name,
		WithSoftDeletedRecords: req.GetWithSoftDeletedRecords(),
		NeedPermissionInject:   true,
	}
	if opt.PageNum == nil || *opt.PageNum <= 0 {
		opt.PageNum = lo.ToPtr[int64](1)
	}
	if opt.PageSize == nil || *opt.PageSize <= 0 {
		opt.PageSize = lo.ToPtr[int64](10)
	}

	total, scenes, err := h.SceneService.ListScene(ctx, opt)
	if err != nil {
		return nil, utils.ConvToKitexError(errors.WithMessagef(err, "failed to list scene"))
	}
	r = &llminfra.ListSceneResponse{
		PagingInfo: &llminfra.PagingInfo{
			Total:     total,
			TotalPage: lo.Ternary((total%(*opt.PageSize)) == 0, total/(*opt.PageSize), total/(*opt.PageSize)+1),
			PageNum:   *opt.PageNum,
			PageSize:  *opt.PageSize,
		},
		Items: make([]*llminfra.Scene, 0, len(scenes)),
	}
	for _, scene := range scenes {
		r.Items = append(r.Items, objconv.SceneEntity2DTO(scene))
	}

	return
}

func (h *KitexHandler) DeleteScene(ctx context.Context, req *llminfra.DeleteSceneRequest) (r *llminfra.CommonResp, err error) {
	if req.SceneID == 0 {
		return nil, kitex.NewKitexError(common.ErrorCode_ErrParamInvalid, errors.Errorf("scene id should not be zero"))
	}
	account, _ := authhandler.GetAccount(ctx)
	if account == nil {
		return nil, kitex.NewKitexError(common.ErrorCode_ErrNoAuth, errors.Errorf("account is required"))
	}

	err = h.SceneService.DeleteScene(ctx, service.DeleteSceneOption{
		ID:                req.SceneID,
		Account:           account,
		DisableSoftDelete: lo.FromPtr(req.DisableSoftDelete),
	})
	if err != nil {
		return nil, utils.ConvToKitexError(err)
	}
	r = &llminfra.CommonResp{}
	return
}

func (h *KitexHandler) UpdateScene(ctx context.Context, req *llminfra.UpdateSceneRequest) (r *llminfra.UpdateSceneResponse, err error) {
	if req.SceneID == 0 {
		return nil, kitex.NewKitexError(common.ErrorCode_ErrParamInvalid, errors.Errorf("scene id should not be zero"))
	}
	account, _ := authhandler.GetAccount(ctx)
	if account == nil {
		return nil, kitex.NewKitexError(common.ErrorCode_ErrNoAuth, errors.Errorf("account is required"))
	}

	scene, err := h.SceneService.UpdateScene(ctx, service.UpdateSceneOption{
		SceneID: req.SceneID,
		Account: account,
		Desc:    req.Desc,
	})
	if err != nil {
		return nil, utils.ConvToKitexError(errors.WithMessagef(err, "failed to update scene"))
	}
	r = &llminfra.UpdateSceneResponse{
		Scene: objconv.SceneEntity2DTO(scene),
	}

	return
}
