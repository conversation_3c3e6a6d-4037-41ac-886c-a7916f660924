package service

import (
	"context"

	overpasscommon "code.byted.org/overpass/kiwis_llminfra_foundation/kitex_gen/common"

	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/common"
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/llminfra"
	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	"code.byted.org/devgpt/kiwis/llminfra/analysisdataset/entity"
	asyncfaasentity "code.byted.org/devgpt/kiwis/llminfra/asyncfaas/entity"
	commonentity "code.byted.org/devgpt/kiwis/llminfra/common/entity"
	detailentity "code.byted.org/devgpt/kiwis/llminfra/datasetcommon/entity"
	datasetcommonservice "code.byted.org/devgpt/kiwis/llminfra/datasetcommon/service"
	editorentity "code.byted.org/devgpt/kiwis/llminfra/editor/entity"
)

type AnalysisDatasetService interface {
	// CopyFromDataProportioning 从数据配比导入推理集数据
	CopyFromDataProportioning(ctx context.Context, dataset *entity.AnalysisDataset) (err error)

	// CreateAnalysisDataset 创建分析数据集
	CreateAnalysisDataset(ctx context.Context, opt *CreateDatasetOption) (r *entity.AnalysisDatasetPacked, err error)
	// UpdateAnalysisDataset 更新分析数据集，包括分析字段
	UpdateAnalysisDataset(ctx context.Context, opt *UpdateDatasetOption) (r *entity.AnalysisDatasetPacked, err error)
	// DeleteAnalysisDataset 删除分析数据集
	DeleteAnalysisDataset(ctx context.Context, opt *DeleteAnalysisDatasetOption) (err error)
	// GetAnalysisDataset 获取分析数据集元信息
	GetAnalysisDataset(ctx context.Context, opt *GetAnalysisDatasetOption) (r *entity.AnalysisDatasetPacked, err error)
	// ListAnalysisDataset 获取分析数据集列表
	ListAnalysisDataset(ctx context.Context, opt *ListDatasetOption) (r *ListDatasetResponse, err error)
	// RefreshAnalysisStatusAndTotalNum 更新数据量和数据集状态
	RefreshAnalysisStatusAndTotalNum(ctx context.Context, datasetID int64) (*entity.AnalysisDataset, error)
	// SubmitOrRequestFaaSStatus 调用异步faas，查询任务执行状态
	SubmitOrRequestFaaSStatus(ctx context.Context, opt SubmitOrRequestFaaSStatusOption) *editorentity.InvokeAsyncFaaSResult
	// DealAsyncFaaSStatus 处理异步FaaS状态
	DealAsyncFaaSStatus(ctx context.Context, opt DealAsyncFaaSStatusOption) error
	// RequestAndDealDumpTask 请求并处理dump任务
	RequestAndDealDumpTask(ctx context.Context, event *editorentity.DataReflowEvent) error
	// CreateInferenceTask 创建推理任务
	CreateInferenceTask(ctx context.Context, opt *CreateInferenceTaskOption) (int64, error)

	/* 调用公共方法 */
	// ListAnalysisDataDetail 获取分析数据集数据详情列表
	ListAnalysisDataDetail(ctx context.Context, opt *ListDataDetailOption) (r *datasetcommonservice.ListDataDetailResponse, err error)
	// GetAnalysisGroupedMeta 获取分组后的数据元信息
	GetAnalysisGroupedMeta(ctx context.Context, opt *GetGroupedMetaOption) (total int64, r []*llminfra.GroupedRowData, err error)
	// GetAnalysisDataDetail 获取某条数据详情
	GetAnalysisDataDetail(ctx context.Context, opt *GetAnalysisDataDetailOption) (r *datasetcommonservice.GetDataDetailResponse, err error)
	// UpdateAnalysisRowData 更新分析数据集某条数据
	UpdateAnalysisRowData(ctx context.Context, opt *UpdateRowDataOption) (r *datasetcommonservice.UpdateRowDataResponse, err error)
	// DeleteAnalysisRowData 删除分析数据集某条数据
	DeleteAnalysisRowData(ctx context.Context, opt *DeleteRowDataOption) (r *datasetcommonservice.DeleteDataDetailResponse, err error)
	// BatchUpdateAnalysisRowData 批量更新数据
	BatchUpdateAnalysisRowData(ctx context.Context, opt *BatchUpdateRowDataOption) (r []*detailentity.GeneralDataDetail, err error)
	// BatchDeleteAnalysisRowData 批量删除数据
	BatchDeleteAnalysisRowData(ctx context.Context, opt *BatchDeleteAnalysisRowDataOption) (err error)
	// AnalysisDataStatistic 统计字段信息
	AnalysisDataStatistic(ctx context.Context, opt *AnalysisDataStatisticOption) (r *datasetcommonservice.StatisticDataResponse, err error)
}

type CreateInferenceTaskOption struct {
	// context
	Username         string
	IsServiceAccount bool
	// required
	DatasetID             int64
	Email                 string
	LogID                 string
	HTTPRequest           *overpasscommon.HTTPRequest
	BlockLarkNotification bool
	TriggerSource         llminfra.EvalTaskTriggerSource
}

type DealAsyncFaaSStatusOption struct {
	InferenceStatus llminfra.DatasetInferenceStatus
	AsyncFaaSStatus asyncfaasentity.AsyncFaaSStatus
	Message         string
	Err             error
	DataImportEvent *editorentity.DataImportEvent
}

type SubmitOrRequestFaaSStatusOption struct {
	FileURL         string
	SourceType      llminfra.AnalysisDataSource
	Columns         []string
	DatasetID       int64
	IsNeedSubmit    bool
	RequestID       string
	KeepExistedData bool
	OngoingWrite    bool
}

type AnalysisDataStatisticOption struct {
	// context
	Username         string
	IsServiceAccount bool
	// required
	DatasetID            int64
	Status               []llminfra.AnalysisStatus
	Analysts             []string
	DataFilterParams     []*llminfra.AnalysisFilterParam
	RawFields            []string
	AnalysisFieldIDs     []int32
	InlineWeightedParams []*llminfra.InlineWeightedParam
}

type DataStatisticResponse struct {
	StatusCountOverall map[llminfra.AnalysisStatus]int64
	StatusCount        map[llminfra.AnalysisStatus]int64
	DataFieldStatistic []*llminfra.DataStatisticInfo
	DataCount          int64
}

type GetAnalysisDatasetOption struct {
	// context
	Username         string
	IsServiceAccount bool
	// required
	DatasetID int64
	// optional
	WithSoftDeletedRecords *bool // 是否包含软删除的记录
	DisablePermissionCheck bool
}

type GetDataFieldValuesOption struct {
	// context
	Username         string
	IsServiceAccount bool
	// required
	DatasetID int64
	FieldName string
}

type DeleteRowDataOption struct {
	// context
	Username         string
	IsServiceAccount bool
	// required
	ID        int64
	UpdatedAt int64
}

type BatchUpdateRowDataOption struct {
	// context
	Username         string
	IsServiceAccount bool
	// required
	IDs               []int64
	AnalysisFieldInfo map[int32]string
}

type BatchDeleteAnalysisRowDataOption struct {
	// context
	Username         string
	IsServiceAccount bool
	// required
	IDs []int64
}

type UpdateRowDataOption struct {
	// context
	Username         string
	IsServiceAccount bool
	// required
	ID                int64
	UpdatedAt         int64
	Operation         llminfra.AnalysisOperation
	AnalysisFieldData *string
}

type GetAnalysisDataDetailOption struct {
	// context
	Username         string
	IsServiceAccount bool
	// required
	DataDetailID int64
	FilterStr    *string
}

type ListDataDetailOption struct {
	// context
	Account     *authentity.Account
	HTTPRequest *common.HTTPRequest

	Username         string
	IsServiceAccount bool
	// optional
	PageNum              *int64
	PageSize             *int64
	AnalysisStatuses     []llminfra.AnalysisStatus
	Analysts             []string
	RawDataFilterParams  []*llminfra.AnalysisFilterParam
	AnaFieldFilterParams []*llminfra.AnalysisFilterParam
	DataFilterParams     []*llminfra.AnalysisFilterParam
	LogicOperate         *llminfra.LogicOperate
	SortParams           []*llminfra.SortParam
	DataCleanResultTypes []llminfra.DataCleanResultType
	IDs                  []int64
	DatasetIDIn          []int64
	DatasetID            *int64
	TagIDIn              []int64
	CreatedAtFrom        *int64
	CreatedAtTo          *int64
	BizLineID            *int64
}

type GetGroupedMetaOption struct {
	// context
	Username         string
	IsServiceAccount bool
	// required
	DatasetID              *int64
	Status                 []llminfra.AnalysisStatus
	Analysts               []string
	RawDataFilterParams    []*llminfra.AnalysisFilterParam
	AnaFieldFilterParams   []*llminfra.AnalysisFilterParam
	LogicOperate           *llminfra.LogicOperate
	GroupParams            []*llminfra.GroupParam
	DatasetIDIn            []int64
	TagIDIn                []int64
	DataCleanResultTypes   []llminfra.DataCleanResultType
	IDs                    []int64
	DisablePermissionCheck bool
	CreatedAtFrom          *int64
	CreatedAtTo            *int64
	BizLineID              *int64
}

type ListDataDetailResponse struct {
	Items   []*detailentity.GeneralDataDetail
	Total   int64
	Columns []string
}

type ListDatasetOption struct {
	// context
	Username         string
	IsServiceAccount bool
	// optional
	BizLineID              *int64
	PageNum                *int64
	PageSize               *int64
	SceneIDs               []int64
	Name                   *string
	Creators               []string
	Statuses               []llminfra.AnalysisStatus
	IsFavorite             *bool
	DatasetIDs             []int64
	InferenceStatuses      []llminfra.DatasetInferenceStatus
	WithSoftDeletedRecords bool // 是否包含软删除的记录
	IsHidden               *bool
	IncludeHiddenReasons   []llminfra.DatasetHiddenReason
	IsTemporary            *bool
	CreatedAt              *commonentity.RangeOption
	ProjectionOptions      commonentity.ProjectionOptions
	IsGeneral              *bool
	NeedPermissionInject   bool
}

type ListDatasetResponse struct {
	Items []*entity.AnalysisDatasetPacked
	Total int64
}

type CreateDatasetOption struct {
	// context
	Account *authentity.Account
	// required
	BizLineID                int64
	Name                     string
	SceneIDs                 []int64
	Desc                     string
	SourceType               llminfra.AnalysisDataSource
	RawDataAddress           *string
	RawDataSize              *int64
	SourceEvalDataVersionID  *int64
	From                     *int64
	To                       *int64
	FilterParams             []*llminfra.FilterParam
	DataFields               []string
	ApiID                    *string
	SamplingConfig           *llminfra.SamplingConfig
	InferenceParams          []*llminfra.InferenceParam
	HTTPRequest              *common.HTTPRequest
	IsHidden                 bool
	HiddenReason             llminfra.DatasetHiddenReason
	BlockLarkNotification    bool // 是否不发送飞书通知
	IsTemporary              bool
	SensitiveType            *llminfra.SensitiveType
	SourceDatasetID          *int64               // 从推理集创建时传
	SourceDataFilter         *llminfra.DataFilter // 源数据集过滤条件
	DisablePermissionCheck   bool
	MaxCaseNum               *int64                             // 最大case数，目前只支持从推理、评测数据集创建的方式
	ResetStatus              bool                               // 是否重置状态，从推理、评测集创建的方式可以设置
	ResetInferInfos          bool                               // 是否重置推理信息，从推理集创建的方式可以设置
	IsSkipInferFailedCase    bool                               // 是否跳过推理失败的case，从推理集创建的方式可以设置
	DatasetDetailFilterParam *llminfra.DatasetDetailFilterParam // 数据配比使用
	MultiRoundChatConfig     *llminfra.MultiRoundChatDatasetConfig
	RestoreDatasetID         *int64 // 用于数据恢复
}

type UpdateDatasetOption struct {
	// context
	Username         string
	IsServiceAccount bool
	// required
	DatasetID int64
	// optional
	Name                    *string
	Desc                    *string
	SceneIDs                []int64
	AnalysisFields          []*llminfra.AnalysisField
	Status                  *int8
	DataFields              *[]string
	TotalNum                *int64
	InferenceStatus         *llminfra.DatasetInferenceStatus
	InferenceParams         []*llminfra.InferenceParam
	IsDataProcessTaskOutput *bool
	DisablePermissionCheck  bool
	IsHidden                *bool
}

type DeleteAnalysisDatasetOption struct {
	// context
	Username         string
	IsServiceAccount bool
	// required
	DatasetID int64
	// optional
	DisableSoftDelete      bool // 默认软删除，设置为true则彻底删除
	DisablePermissionCheck bool
	DeleteDatasetDetail    bool // 是否删除数据集详情
}
