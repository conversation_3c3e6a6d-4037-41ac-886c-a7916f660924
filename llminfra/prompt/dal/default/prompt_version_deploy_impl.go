package defaultdal

import (
	"context"

	"gorm.io/datatypes"

	"code.byted.org/devgpt/kiwis/llminfra/prompt/dal"
	"code.byted.org/devgpt/kiwis/llminfra/prompt/dal/po"
	"code.byted.org/devgpt/kiwis/llminfra/prompt/entity"
	"code.byted.org/devgpt/kiwis/llminfra/prompt/objconv"
	dbclient "code.byted.org/devgpt/kiwis/port/db"
)

type PromptVersionDeployDAO struct {
	cli dbclient.Client
}

var _ dal.PromptVersionDeployDAO = &PromptVersionDeployDAO{}

func NewPromptVersionDeployDAO(cli dbclient.Client) *PromptVersionDeployDAO {
	return &PromptVersionDeployDAO{cli}
}

func (dao *PromptVersionDeployDAO) CreatePromptVersionDeploy(ctx context.Context, opts dal.CreatePromptVersionDeployOpts) (*entity.PromptVersionDeploy, error) {
	db := dao.cli.NewRequest(ctx)
	deploy := &po.PromptVersionDeployPO{
		PromptID:        opts.PromptID,
		PromptVersionID: opts.PromptVersionID,
		TccEnv:          opts.TccEnv,
		TccEnvLabel:     opts.TccEnvLabel,
		TccLabels:       datatypes.NewJSONType(po.PromptVersionDeployTccLabels(opts.TccLabels)),
		TccConfigID:     opts.TccConfigID,
		TccRegion:       opts.TccRegion,
		Creator:         opts.Creator,
		LastUpdater:     opts.LastUpdator,
	}
	if err := db.Create(deploy).Error; err != nil {
		return nil, err
	}
	return objconv.DeployPO2Entity(deploy), nil
}

func (dao *PromptVersionDeployDAO) CreateOrUpdatePromptVersionDeploy(ctx context.Context, opts dal.CreatePromptVersionDeployOpts) (*entity.PromptVersionDeploy, error) {
	db := dao.cli.NewRequest(ctx)
	db = db.Model(&po.PromptVersionDeployPO{})
	findDeploy := &po.PromptVersionDeployPO{
		PromptID:        opts.PromptID,
		PromptVersionID: opts.PromptVersionID,
		TccEnv:          opts.TccEnv,
		TccEnvLabel:     opts.TccEnvLabel,
	}
	updateDeploy := &po.PromptVersionDeployPO{
		TccLabels:   datatypes.NewJSONType(po.PromptVersionDeployTccLabels(opts.TccLabels)),
		Creator:     opts.Creator,
		LastUpdater: opts.LastUpdator,
	}
	deploy := &po.PromptVersionDeployPO{
		PromptID:        opts.PromptID,
		PromptVersionID: opts.PromptVersionID,
		TccEnv:          opts.TccEnv,
		TccEnvLabel:     opts.TccEnvLabel,
		TccLabels:       datatypes.NewJSONType(po.PromptVersionDeployTccLabels(opts.TccLabels)),
		TccConfigID:     opts.TccConfigID,
		Creator:         opts.Creator,
		LastUpdater:     opts.LastUpdator,
	}
	result := db.Where(findDeploy).Assign(updateDeploy).FirstOrCreate(deploy)
	if err := result.Error; err != nil {
		return nil, err
	}
	return objconv.DeployPO2Entity(deploy), nil
}

func (dao *PromptVersionDeployDAO) ListPromptVersionDeploy(ctx context.Context, option dal.ListPromptVersionDeployOption) ([]*entity.PromptVersionDeploy, error) {
	db := dao.cli.NewRequest(ctx)

	query := db.Model(&po.PromptVersionDeployPO{})
	if option.PromptID != nil {
		query = query.Where("prompt_id = ?", *option.PromptID)
	}
	if option.PromptVersionID != nil {
		query = query.Where("prompt_version_id = ?", *option.PromptVersionID)
	}
	if option.TccEnv != nil {
		query = query.Where("tcc_env = ?", *option.TccEnv)
	}
	if option.TccEnvLabel != nil {
		query = query.Where("tcc_env_label = ?", *option.TccEnvLabel)
	}
	if option.TccRegion != nil {
		query = query.Where("tcc_region = ?", *option.TccRegion)
	}

	pos := make([]*po.PromptVersionDeployPO, 0)
	if err := query.Find(&pos).Error; err != nil {
		return nil, err
	}
	if len(pos) == 0 {
		return nil, nil
	}

	var res []*entity.PromptVersionDeploy
	for _, po := range pos {
		deploy := objconv.DeployPO2Entity(po)
		res = append(res, deploy)
	}
	return res, nil
}

func (dao *PromptVersionDeployDAO) UpdatePromptVersionDeploy(ctx context.Context, option dal.UpdatePromptVersionDeployOption) (*entity.PromptVersionDeploy, error) {
	db := dao.cli.NewRequest(ctx)
	db = db.Model(&po.PromptVersionDeployPO{})
	// 匹配筛选条件
	db = db.Where("prompt_version_id = ? AND tcc_env = ? AND tcc_env_label = ? AND tcc_region = ?", option.PromptVersionID, option.TccEnv, option.TccEnvLabel, option.TccRegion)
	updater := make(map[string]interface{})
	updater["tcc_labels"] = datatypes.NewJSONType(po.PromptVersionDeployTccLabels(option.Labels))

	if err := db.Updates(updater).Error; err != nil {
		return nil, err
	}
	return dao.GetPromptVersionDeploy(ctx, option.PromptVersionID, option.TccEnv, option.TccEnvLabel, option.TccRegion)
}

func (dao *PromptVersionDeployDAO) GetPromptVersionDeploy(ctx context.Context, promptVersionID int64, env, envLabel, region string) (*entity.PromptVersionDeploy, error) {
	db := dao.cli.NewRequest(ctx)
	db = db.Model(&po.PromptVersionDeployPO{})
	po := &po.PromptVersionDeployPO{}
	if err := db.Where("prompt_version_id = ? AND tcc_env = ? AND tcc_env_label = ? AND tcc_region = ?", promptVersionID, env, envLabel, region).First(po).Error; err != nil {
		return nil, err
	}
	return objconv.DeployPO2Entity(po), nil
}
