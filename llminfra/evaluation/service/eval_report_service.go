package service

import (
	"context"

	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/llminfra"
	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	"code.byted.org/devgpt/kiwis/llminfra/evaluation/entity"
)

type EvalReportService interface {
	// GetEvalReport 获取评测报告
	GetEvalReport(ctx context.Context, opt GetEvalReportOption) (evalReport *entity.EvalReport, err error)
	// ListEvalReportCaseV2 获取评测报告用例列表
	ListEvalReportCaseV2(ctx context.Context, opt ListEvalReportCaseV2Option) (total int64, items []*entity.EvalReportCaseV2, columns []string, err error)
	// GetEvalReportCaseV2 获取单条case详情
	GetEvalReportCaseV2(ctx context.Context, opt GetEvalReportCaseV2Option) (res *GetEvalReportCaseV2Result, err error)
	// CountEvalReportCaseV2 获取评测报告用例数量
	CountEvalReportCaseV2(ctx context.Context, opt ListEvalReportCaseV2Option) (total int64, err error)
}

type ListEvalReportCaseV2Option struct {
	// required
	Account *authentity.Account
	// optional
	EvalTaskID           *int64
	EvalSubtaskID        *int64
	EvalReportID         *int64
	EvalCaseID           []int64
	StatusList           []llminfra.EvalCaseStatus
	InferenceStatusList  []llminfra.InferenceStatus
	EvaluationStatusList []llminfra.EvaluationStatus
	PageNum              *int64
	PageSize             *int64
}

type GetEvalReportOption struct {
	// context
	Account *authentity.Account
	// optional
	EvalReportID  *int64
	EvalSubtaskID *int64
}

type GetEvalReportCaseV2Option struct {
	// context
	Account *authentity.Account
	// required
	EvalCaseID int64
}

type GetEvalReportCaseV2Result struct {
	EvalReportCase *entity.EvalReportCaseV2
	Columns        []string
	Total          int64
	Index          int64
	PrevID         int64
	NextID         int64
}
