package searchapi

//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/gen hertztool model --snake_tag --idl ide.thrift --model_dir ../hertz_gen -t=template=slim
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/gen hertztool model --snake_tag --idl ceto.thrift --model_dir ../hertz_gen -t=template=slim
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/gen hertztool model --snake_tag --idl bits.thrift --model_dir ../hertz_gen -t=template=slim
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/gen hertztool model --snake_tag --idl openapi.thrift --model_dir ../hertz_gen -t=template=slim
