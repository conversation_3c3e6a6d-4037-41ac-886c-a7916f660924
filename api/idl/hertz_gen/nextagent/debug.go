// Code generated by thriftgo (0.4.1). DO NOT EDIT.

package nextagent

import (
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/common"
	"database/sql"
	"database/sql/driver"
	"fmt"
)

type DebugSessionEventStatus int64

const (
	DebugSessionEventStatus_Success DebugSessionEventStatus = 1
	DebugSessionEventStatus_Failed  DebugSessionEventStatus = 2
)

func (p DebugSessionEventStatus) String() string {
	switch p {
	case DebugSessionEventStatus_Success:
		return "Success"
	case DebugSessionEventStatus_Failed:
		return "Failed"
	}
	return "<UNSET>"
}

func DebugSessionEventStatusFromString(s string) (DebugSessionEventStatus, error) {
	switch s {
	case "Success":
		return DebugSessionEventStatus_Success, nil
	case "Failed":
		return DebugSessionEventStatus_Failed, nil
	}
	return DebugSessionEventStatus(0), fmt.Errorf("not a valid DebugSessionEventStatus string")
}

func DebugSessionEventStatusPtr(v DebugSessionEventStatus) *DebugSessionEventStatus { return &v }
func (p *DebugSessionEventStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = DebugSessionEventStatus(result.Int64)
	return
}

func (p *DebugSessionEventStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type GetDebugSessionRequest struct {
	SessionID string `thrift:"SessionID,1,required" json:"session_id,required" query:"session_id,required"`
}

func NewGetDebugSessionRequest() *GetDebugSessionRequest {
	return &GetDebugSessionRequest{}
}

func (p *GetDebugSessionRequest) InitDefault() {
}

func (p *GetDebugSessionRequest) GetSessionID() (v string) {
	return p.SessionID
}

func (p *GetDebugSessionRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetDebugSessionRequest(%+v)", *p)
}

type GetDebugSessionResponse struct {
	ID        string               `thrift:"ID,1,required" json:"id"`
	Title     string               `thrift:"Title,2,required" json:"title"`
	Context   *DebugSessionContext `thrift:"Context,3,required" json:"context"`
	CreatedAt string               `thrift:"CreatedAt,4,required" json:"created_at"`
	UpdatedAt string               `thrift:"UpdatedAt,5,required" json:"updated_at"`
}

func NewGetDebugSessionResponse() *GetDebugSessionResponse {
	return &GetDebugSessionResponse{}
}

func (p *GetDebugSessionResponse) InitDefault() {
}

func (p *GetDebugSessionResponse) GetID() (v string) {
	return p.ID
}

func (p *GetDebugSessionResponse) GetTitle() (v string) {
	return p.Title
}

var GetDebugSessionResponse_Context_DEFAULT *DebugSessionContext

func (p *GetDebugSessionResponse) GetContext() (v *DebugSessionContext) {
	if !p.IsSetContext() {
		return GetDebugSessionResponse_Context_DEFAULT
	}
	return p.Context
}

func (p *GetDebugSessionResponse) GetCreatedAt() (v string) {
	return p.CreatedAt
}

func (p *GetDebugSessionResponse) GetUpdatedAt() (v string) {
	return p.UpdatedAt
}

func (p *GetDebugSessionResponse) IsSetContext() bool {
	return p.Context != nil
}

func (p *GetDebugSessionResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetDebugSessionResponse(%+v)", *p)
}

type DebugSessionContext struct {
	MCPs []*MCPDetail `thrift:"MCPs,1,required" json:"mcp"`
}

func NewDebugSessionContext() *DebugSessionContext {
	return &DebugSessionContext{}
}

func (p *DebugSessionContext) InitDefault() {
}

func (p *DebugSessionContext) GetMCPs() (v []*MCPDetail) {
	return p.MCPs
}

func (p *DebugSessionContext) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DebugSessionContext(%+v)", *p)
}

type MCPDetail struct {
	ID          int64      `thrift:"ID,1,required" json:"id"`
	Name        string     `thrift:"Name,2,required" json:"name"`
	Description string     `thrift:"Description,3,required" json:"description"`
	Source      MCPSource  `thrift:"Source,4,required" json:"source"`
	Tools       []*MCPTool `thrift:"Tools,5,required" json:"tools"`
}

func NewMCPDetail() *MCPDetail {
	return &MCPDetail{}
}

func (p *MCPDetail) InitDefault() {
}

func (p *MCPDetail) GetID() (v int64) {
	return p.ID
}

func (p *MCPDetail) GetName() (v string) {
	return p.Name
}

func (p *MCPDetail) GetDescription() (v string) {
	return p.Description
}

func (p *MCPDetail) GetSource() (v MCPSource) {
	return p.Source
}

func (p *MCPDetail) GetTools() (v []*MCPTool) {
	return p.Tools
}

func (p *MCPDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MCPDetail(%+v)", *p)
}

type MCPTool struct {
	Name        string `thrift:"Name,1,required" json:"name"`
	Description string `thrift:"Description,2,required" json:"description"`
}

func NewMCPTool() *MCPTool {
	return &MCPTool{}
}

func (p *MCPTool) InitDefault() {
}

func (p *MCPTool) GetName() (v string) {
	return p.Name
}

func (p *MCPTool) GetDescription() (v string) {
	return p.Description
}

func (p *MCPTool) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MCPTool(%+v)", *p)
}

type GetDebugSessionEventsRequest struct {
	SessionID string `thrift:"SessionID,1,required" json:"session_id,required" query:"session_id,required"`
}

func NewGetDebugSessionEventsRequest() *GetDebugSessionEventsRequest {
	return &GetDebugSessionEventsRequest{}
}

func (p *GetDebugSessionEventsRequest) InitDefault() {
}

func (p *GetDebugSessionEventsRequest) GetSessionID() (v string) {
	return p.SessionID
}

func (p *GetDebugSessionEventsRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetDebugSessionEventsRequest(%+v)", *p)
}

type GetDebugSessionEventsResponse struct {
	Events []*DebugSessionEvent `thrift:"Events,1,required" json:"events"`
}

func NewGetDebugSessionEventsResponse() *GetDebugSessionEventsResponse {
	return &GetDebugSessionEventsResponse{}
}

func (p *GetDebugSessionEventsResponse) InitDefault() {
}

func (p *GetDebugSessionEventsResponse) GetEvents() (v []*DebugSessionEvent) {
	return p.Events
}

func (p *GetDebugSessionEventsResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetDebugSessionEventsResponse(%+v)", *p)
}

type DebugSessionEvent struct {
	ID        string               `thrift:"ID,1,required" json:"id"`
	Type      string               `thrift:"Type,2,required" json:"type"`
	CreatedAt string               `thrift:"CreatedAt,3,required" json:"created_at"`
	Data      common.JsonVariables `thrift:"Data,4,required" json:"data"`
}

func NewDebugSessionEvent() *DebugSessionEvent {
	return &DebugSessionEvent{}
}

func (p *DebugSessionEvent) InitDefault() {
}

func (p *DebugSessionEvent) GetID() (v string) {
	return p.ID
}

func (p *DebugSessionEvent) GetType() (v string) {
	return p.Type
}

func (p *DebugSessionEvent) GetCreatedAt() (v string) {
	return p.CreatedAt
}

func (p *DebugSessionEvent) GetData() (v common.JsonVariables) {
	return p.Data
}

func (p *DebugSessionEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DebugSessionEvent(%+v)", *p)
}

type GetSessionDebugEventsStreamRequest struct {
	SessionID string `thrift:"SessionID,1,required" json:"session_id,required" query:"session_id,required"`
	EventID   string `thrift:"EventID,2,required" json:"event_id,required" query:"event_id,required"`
}

func NewGetSessionDebugEventsStreamRequest() *GetSessionDebugEventsStreamRequest {
	return &GetSessionDebugEventsStreamRequest{}
}

func (p *GetSessionDebugEventsStreamRequest) InitDefault() {
}

func (p *GetSessionDebugEventsStreamRequest) GetSessionID() (v string) {
	return p.SessionID
}

func (p *GetSessionDebugEventsStreamRequest) GetEventID() (v string) {
	return p.EventID
}

func (p *GetSessionDebugEventsStreamRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSessionDebugEventsStreamRequest(%+v)", *p)
}

type DebugSessionEventRequest struct {
	SessionID string               `thrift:"SessionID,1,required" json:"session_id,required" query:"session_id,required"`
	EventID   string               `thrift:"EventID,2,required" json:"event_id,required" query:"event_id,required"`
	Context   *DebugSessionContext `thrift:"Context,3,optional" json:"context"`
}

func NewDebugSessionEventRequest() *DebugSessionEventRequest {
	return &DebugSessionEventRequest{}
}

func (p *DebugSessionEventRequest) InitDefault() {
}

func (p *DebugSessionEventRequest) GetSessionID() (v string) {
	return p.SessionID
}

func (p *DebugSessionEventRequest) GetEventID() (v string) {
	return p.EventID
}

var DebugSessionEventRequest_Context_DEFAULT *DebugSessionContext

func (p *DebugSessionEventRequest) GetContext() (v *DebugSessionContext) {
	if !p.IsSetContext() {
		return DebugSessionEventRequest_Context_DEFAULT
	}
	return p.Context
}

func (p *DebugSessionEventRequest) IsSetContext() bool {
	return p.Context != nil
}

func (p *DebugSessionEventRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DebugSessionEventRequest(%+v)", *p)
}

type DebugSessionEventResponse struct {
	SessionID    string                     `thrift:"SessionID,1,required" json:"session_id"`
	EventID      string                     `thrift:"EventID,2,required" json:"event_id"`
	Status       DebugSessionEventStatus    `thrift:"Status,3,required" json:"status"`
	Data         *common.JsonVariables      `thrift:"Data,4,optional" json:"data"`
	ErrorMessage *string                    `thrift:"ErrorMessage,5,optional" json:"error_message"`
	Metadata     *DebugSessionEventMetadata `thrift:"Metadata,6,optional" json:"metadata"`
}

func NewDebugSessionEventResponse() *DebugSessionEventResponse {
	return &DebugSessionEventResponse{}
}

func (p *DebugSessionEventResponse) InitDefault() {
}

func (p *DebugSessionEventResponse) GetSessionID() (v string) {
	return p.SessionID
}

func (p *DebugSessionEventResponse) GetEventID() (v string) {
	return p.EventID
}

func (p *DebugSessionEventResponse) GetStatus() (v DebugSessionEventStatus) {
	return p.Status
}

var DebugSessionEventResponse_Data_DEFAULT common.JsonVariables

func (p *DebugSessionEventResponse) GetData() (v common.JsonVariables) {
	if !p.IsSetData() {
		return DebugSessionEventResponse_Data_DEFAULT
	}
	return *p.Data
}

var DebugSessionEventResponse_ErrorMessage_DEFAULT string

func (p *DebugSessionEventResponse) GetErrorMessage() (v string) {
	if !p.IsSetErrorMessage() {
		return DebugSessionEventResponse_ErrorMessage_DEFAULT
	}
	return *p.ErrorMessage
}

var DebugSessionEventResponse_Metadata_DEFAULT *DebugSessionEventMetadata

func (p *DebugSessionEventResponse) GetMetadata() (v *DebugSessionEventMetadata) {
	if !p.IsSetMetadata() {
		return DebugSessionEventResponse_Metadata_DEFAULT
	}
	return p.Metadata
}

func (p *DebugSessionEventResponse) IsSetData() bool {
	return p.Data != nil
}

func (p *DebugSessionEventResponse) IsSetErrorMessage() bool {
	return p.ErrorMessage != nil
}

func (p *DebugSessionEventResponse) IsSetMetadata() bool {
	return p.Metadata != nil
}

func (p *DebugSessionEventResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DebugSessionEventResponse(%+v)", *p)
}

type DebugSessionEventMetadata struct {
	ExecutedAt      string `thrift:"ExecutedAt,1,required" json:"executed_at"`
	ExecutionTimeMs *int64 `thrift:"ExecutionTimeMs,2,optional" json:"execution_time_ms"`
}

func NewDebugSessionEventMetadata() *DebugSessionEventMetadata {
	return &DebugSessionEventMetadata{}
}

func (p *DebugSessionEventMetadata) InitDefault() {
}

func (p *DebugSessionEventMetadata) GetExecutedAt() (v string) {
	return p.ExecutedAt
}

var DebugSessionEventMetadata_ExecutionTimeMs_DEFAULT int64

func (p *DebugSessionEventMetadata) GetExecutionTimeMs() (v int64) {
	if !p.IsSetExecutionTimeMs() {
		return DebugSessionEventMetadata_ExecutionTimeMs_DEFAULT
	}
	return *p.ExecutionTimeMs
}

func (p *DebugSessionEventMetadata) IsSetExecutionTimeMs() bool {
	return p.ExecutionTimeMs != nil
}

func (p *DebugSessionEventMetadata) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DebugSessionEventMetadata(%+v)", *p)
}
