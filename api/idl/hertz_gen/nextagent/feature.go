// Code generated by thriftgo (0.4.1). DO NOT EDIT.

package nextagent

import (
	"fmt"
)

// GetCurrentActivity
type GetGlobalFeaturesRequest struct {
}

func NewGetGlobalFeaturesRequest() *GetGlobalFeaturesRequest {
	return &GetGlobalFeaturesRequest{}
}

func (p *GetGlobalFeaturesRequest) InitDefault() {
}

func (p *GetGlobalFeaturesRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetGlobalFeaturesRequest(%+v)", *p)
}

type RoleFeature struct {
	Role SessionRole `thrift:"Role,1,required" json:"role"`
	// 休眠后最长可唤醒的天数
	MaxResumeDays int32 `thrift:"MaxResumeDays,2,required" json:"max_resume_days"`
	// 事件不更新后多久时间进入休眠
	SleepingTimeHours int32 `thrift:"SleepingTimeHours,3,required" json:"sleeping_time_hours"`
}

func NewRoleFeature() *RoleFeature {
	return &RoleFeature{}
}

func (p *RoleFeature) InitDefault() {
}

func (p *RoleFeature) GetRole() (v SessionRole) {
	return p.Role
}

func (p *RoleFeature) GetMaxResumeDays() (v int32) {
	return p.MaxResumeDays
}

func (p *RoleFeature) GetSleepingTimeHours() (v int32) {
	return p.SleepingTimeHours
}

func (p *RoleFeature) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RoleFeature(%+v)", *p)
}

type GetGlobalFeaturesResponse struct {
	CurrentActivity *Activity      `thrift:"CurrentActivity,1,optional" json:"current_activity"`
	RoleFeatures    []*RoleFeature `thrift:"RoleFeatures,2,optional" json:"role_features"`
}

func NewGetGlobalFeaturesResponse() *GetGlobalFeaturesResponse {
	return &GetGlobalFeaturesResponse{}
}

func (p *GetGlobalFeaturesResponse) InitDefault() {
}

var GetGlobalFeaturesResponse_CurrentActivity_DEFAULT *Activity

func (p *GetGlobalFeaturesResponse) GetCurrentActivity() (v *Activity) {
	if !p.IsSetCurrentActivity() {
		return GetGlobalFeaturesResponse_CurrentActivity_DEFAULT
	}
	return p.CurrentActivity
}

var GetGlobalFeaturesResponse_RoleFeatures_DEFAULT []*RoleFeature

func (p *GetGlobalFeaturesResponse) GetRoleFeatures() (v []*RoleFeature) {
	if !p.IsSetRoleFeatures() {
		return GetGlobalFeaturesResponse_RoleFeatures_DEFAULT
	}
	return p.RoleFeatures
}

func (p *GetGlobalFeaturesResponse) IsSetCurrentActivity() bool {
	return p.CurrentActivity != nil
}

func (p *GetGlobalFeaturesResponse) IsSetRoleFeatures() bool {
	return p.RoleFeatures != nil
}

func (p *GetGlobalFeaturesResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetGlobalFeaturesResponse(%+v)", *p)
}

type GetUserFeaturesRequest struct {
}

func NewGetUserFeaturesRequest() *GetUserFeaturesRequest {
	return &GetUserFeaturesRequest{}
}

func (p *GetUserFeaturesRequest) InitDefault() {
}

func (p *GetUserFeaturesRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetUserFeaturesRequest(%+v)", *p)
}

type GetUserFeaturesResponse struct {
	Invited bool `thrift:"Invited,1,required" json:"invited"`
	// 没有返回该字段表示不限制
	SessionLimit   *int64               `thrift:"SessionLimit,2,optional" json:"session_limit,omitempty"`
	MessageLimit   *int64               `thrift:"MessageLimit,3,optional" json:"message_limit,omitempty"`
	MessageWarning *int64               `thrift:"MessageWarning,4,optional" json:"message_warning,omitempty"`
	RoleConfigs    []*SessionRoleConfig `thrift:"RoleConfigs,5,optional" json:"role_configs,omitempty"`
	// 新版空间灰度字段
	UseSpace *bool `thrift:"UseSpace,6,optional" json:"use_space,omitempty"`
}

func NewGetUserFeaturesResponse() *GetUserFeaturesResponse {
	return &GetUserFeaturesResponse{}
}

func (p *GetUserFeaturesResponse) InitDefault() {
}

func (p *GetUserFeaturesResponse) GetInvited() (v bool) {
	return p.Invited
}

var GetUserFeaturesResponse_SessionLimit_DEFAULT int64

func (p *GetUserFeaturesResponse) GetSessionLimit() (v int64) {
	if !p.IsSetSessionLimit() {
		return GetUserFeaturesResponse_SessionLimit_DEFAULT
	}
	return *p.SessionLimit
}

var GetUserFeaturesResponse_MessageLimit_DEFAULT int64

func (p *GetUserFeaturesResponse) GetMessageLimit() (v int64) {
	if !p.IsSetMessageLimit() {
		return GetUserFeaturesResponse_MessageLimit_DEFAULT
	}
	return *p.MessageLimit
}

var GetUserFeaturesResponse_MessageWarning_DEFAULT int64

func (p *GetUserFeaturesResponse) GetMessageWarning() (v int64) {
	if !p.IsSetMessageWarning() {
		return GetUserFeaturesResponse_MessageWarning_DEFAULT
	}
	return *p.MessageWarning
}

var GetUserFeaturesResponse_RoleConfigs_DEFAULT []*SessionRoleConfig

func (p *GetUserFeaturesResponse) GetRoleConfigs() (v []*SessionRoleConfig) {
	if !p.IsSetRoleConfigs() {
		return GetUserFeaturesResponse_RoleConfigs_DEFAULT
	}
	return p.RoleConfigs
}

var GetUserFeaturesResponse_UseSpace_DEFAULT bool

func (p *GetUserFeaturesResponse) GetUseSpace() (v bool) {
	if !p.IsSetUseSpace() {
		return GetUserFeaturesResponse_UseSpace_DEFAULT
	}
	return *p.UseSpace
}

func (p *GetUserFeaturesResponse) IsSetSessionLimit() bool {
	return p.SessionLimit != nil
}

func (p *GetUserFeaturesResponse) IsSetMessageLimit() bool {
	return p.MessageLimit != nil
}

func (p *GetUserFeaturesResponse) IsSetMessageWarning() bool {
	return p.MessageWarning != nil
}

func (p *GetUserFeaturesResponse) IsSetRoleConfigs() bool {
	return p.RoleConfigs != nil
}

func (p *GetUserFeaturesResponse) IsSetUseSpace() bool {
	return p.UseSpace != nil
}

func (p *GetUserFeaturesResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetUserFeaturesResponse(%+v)", *p)
}
