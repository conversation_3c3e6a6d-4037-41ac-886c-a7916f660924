// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package agentservice

import (
	"fmt"
)

const (
	SessionStatusCreated = "created"

	SessionStatusRunning = "running"

	SessionStatusStopped = "stopped"
)

type SessionStatus = string

type Session struct {
	ID        string        `thrift:"ID,1,required" json:"id"`
	Status    SessionStatus `thrift:"Status,2,required" json:"status"`
	Title     string        `thrift:"Title,3,required" json:"title"`
	Context   string        `thrift:"Context,4,required" json:"context"`
	CreatedAt string        `thrift:"CreatedAt,5,required" json:"created_at"`
	UpdatedAt string        `thrift:"UpdatedAt,6,required" json:"updated_at"`
}

func NewSession() *Session {
	return &Session{}
}

func (p *Session) InitDefault() {
}

func (p *Session) GetID() (v string) {
	return p.ID
}

func (p *Session) GetStatus() (v SessionStatus) {
	return p.Status
}

func (p *Session) GetTitle() (v string) {
	return p.Title
}

func (p *Session) GetContext() (v string) {
	return p.Context
}

func (p *Session) GetCreatedAt() (v string) {
	return p.CreatedAt
}

func (p *Session) GetUpdatedAt() (v string) {
	return p.UpdatedAt
}

func (p *Session) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Session(%+v)", *p)
}

type StepGraphNode struct {
	ID        string            `thrift:"ID,1,required" json:"id"`
	DependsOn []string          `thrift:"DependsOn,2,required" json:"depends_on"`
	Data      map[string]string `thrift:"Data,3,required" json:"data"`
	Timestamp *int64            `thrift:"Timestamp,4,optional" json:"timestamp"`
}

func NewStepGraphNode() *StepGraphNode {
	return &StepGraphNode{}
}

func (p *StepGraphNode) InitDefault() {
}

func (p *StepGraphNode) GetID() (v string) {
	return p.ID
}

func (p *StepGraphNode) GetDependsOn() (v []string) {
	return p.DependsOn
}

func (p *StepGraphNode) GetData() (v map[string]string) {
	return p.Data
}

var StepGraphNode_Timestamp_DEFAULT int64

func (p *StepGraphNode) GetTimestamp() (v int64) {
	if !p.IsSetTimestamp() {
		return StepGraphNode_Timestamp_DEFAULT
	}
	return *p.Timestamp
}

func (p *StepGraphNode) IsSetTimestamp() bool {
	return p.Timestamp != nil
}

func (p *StepGraphNode) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StepGraphNode(%+v)", *p)
}

type Step struct {
	StepID string            `thrift:"StepID,1,required" json:"step_id"`
	Type   string            `thrift:"Type,2,required" json:"type"`
	Data   map[string]string `thrift:"Data,3,required" json:"data"`
}

func NewStep() *Step {
	return &Step{}
}

func (p *Step) InitDefault() {
}

func (p *Step) GetStepID() (v string) {
	return p.StepID
}

func (p *Step) GetType() (v string) {
	return p.Type
}

func (p *Step) GetData() (v map[string]string) {
	return p.Data
}

func (p *Step) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Step(%+v)", *p)
}
