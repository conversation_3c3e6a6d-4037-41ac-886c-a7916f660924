include "../base.thrift"
include "../common.thrift"
include "../api_gateway/agw_common_param.thrift"
namespace go playground

typedef string JsonVariables

struct ChatMessage {
    // @example: user
    1: required string Role (go.tag="json:\"role,required\""),
    // @example: Hello!
    2: required string Content (go.tag="json:\"content,required\""),
}

struct RequestCommonParams {
    // 从 api gateway session_loader 获取，参考 https://bytedance.larkoffice.com/wiki/wikcnspqmkAGnn5JVA2jaoPeVnc
    1: agw_common_param.Session                                 Session
    // 以下字段通过 api gateway 的协议转换获取，参考 https://bytedance.larkoffice.com/wiki/wikcncmO7hvkPf3D0c83sjgrtkf
    2: optional map<string, list<string>> Headers (agw.source="headers"),
    3: optional binary Request (agw.source="raw_body"),
    4: optional string Cookie (api.header="<PERSON>ie"),
    5: optional string UserAgent (api.header="User-Agent"),
    6: optional string Forwarded (api.header="X-Forwarded-For"),
    7: optional string Referer (api.header="Referer"),
    8: optional string WebSignRes (api.header="web_sign_res"),
    9: optional string WebMSToken (api.header="web_ms_token"),
    10: optional string TtWid (api.cookie="ttwid"),
    11: optional string FP (api.header="fp"),
}

struct ChatContextResolver {
    // ResolverId is the unique ID for each context resolver.
    // @example: current-selection
    1: required string ResolverId (go.tag="json:\"resolver_id,required\""),
    // Content is the resolved context.
    // @example: Here is the user's current selection text: xxx
    2: optional string Content (go.tag="json:\"content\""),
    // JsonVariables is the variables of this context, it is a JSON encoded object string.
    // Thrift does not support `map[string]any`, so we use JSON string here, and not using `variables` as field name.
    // @example: {"before_code": "xxx"}
    3: optional JsonVariables Variables (go.tag="json:\"variables\""),
}

// Request for IDE copilot chat.
struct PlaygroundChatRequest {
    // Unique ID for the APP, usually UUID.
    // @example: 8f11c5a8-f433-42f2-baad-ca676a2705e6
    1: required string AppId (api.header="X-App-Id"),
    // App Version.
    // @example: default
    2: optional string AppVersion (api.header="X-App-Version"),
    // User's direct input string.
    // @example: Explain this code.
    3: required string UserInput (go.tag="json:\"user_input,required\""),
    // JsonVariables is the global variables for this session, it is a JSON encoded object string.
    // Thrift does not support `map[string]any`, so we use JSON string here, and not using `variables` as field name.
    // @example: {"locale": "zh-cn"}
    4: required JsonVariables Variables (go.tag="json:\"variables,required\""),
    // User's chat history, if any.
    5: required list<ChatMessage> ChatHistory (go.tag="json:\"chat_history,required\""),
    // Context intent prompts.
    6: required list<ChatContextResolver> ContextResolvers (go.tag="json:\"context_resolvers,required\""),
    7: optional string SessionId (go.tag="json:\"session_id\""),
    8: RequestCommonParams RequestCommonParams (agw.source="not_body_struct"),

    201: optional common.HTTPRequest HTTPRequest(agw.source="not_body_struct"),
    255: optional base.Base Base,
}