namespace go nextagent

include "session.thrift"
include "mcp.thrift"
include "../common.thrift"

struct GetDebugSessionRequest {
    1: required string SessionID (api.query = "session_id"),
}

struct GetDebugSessionResponse {
    1: required string ID (go.tag = "json:\"id\""),
    2: required string Title (go.tag = "json:\"title\""),
    3: required DebugSessionContext Context (go.tag = "json:\"context\""),
    4: required string CreatedAt (go.tag = "json:\"created_at\""),
    5: required string UpdatedAt (go.tag = "json:\"updated_at\""),
}

struct DebugSessionContext {
    1: required list<MCPDetail> MCPs (go.tag = "json:\"mcp\""),
}

struct MCPDetail {
    1: required i64 ID (go.tag = "json:\"id\""),
    2: required string Name (go.tag = "json:\"name\""),
    3: required string Description (go.tag = "json:\"description\""),
    4: required mcp.MCPSource Source (go.tag = "json:\"source\""),
    5: required list<MCPTool> Tools (go.tag = "json:\"tools\""),
}

struct MCPTool {
    1: required string Name (go.tag = "json:\"name\""),
    2: required string Description (go.tag = "json:\"description\""),
}

struct GetDebugSessionEventsRequest {
    1: required string SessionID (api.query = "session_id"),
}

struct GetDebugSessionEventsResponse {
    1: required list<DebugSessionEvent> Events (go.tag = "json:\"events\""),
}

struct DebugSessionEvent {
    1: required string ID (go.tag = "json:\"id\""),
    2: required string Type (go.tag = "json:\"type\""),
    3: required string CreatedAt (go.tag = "json:\"created_at\""),
    4: required common.JsonVariables Data (go.tag = "json:\"data\"" api.type='json'),
}

struct GetSessionDebugEventsStreamRequest {
    1: required string SessionID (api.query = "session_id"),
    2: required string EventID (api.query = "event_id"),
}

struct DebugSessionEventRequest {
    1: required string SessionID (api.query = "session_id"),
    2: required string EventID (api.query = "event_id"),
    3: optional DebugSessionContext Context (go.tag = "json:\"context\""),
}

struct DebugSessionEventResponse {
    1: required string SessionID (go.tag = "json:\"session_id\""),
    2: required string EventID (go.tag = "json:\"event_id\""),
    3: required DebugSessionEventStatus Status (go.tag = "json:\"status\""),
    4: optional common.JsonVariables Data (go.tag = "json:\"data\""),
    5: optional string ErrorMessage (go.tag = "json:\"error_message\""),
    6: optional DebugSessionEventMetadata Metadata (go.tag = "json:\"metadata\""),
}

enum DebugSessionEventStatus {
    Success = 1,
    Failed = 2,
}

struct DebugSessionEventMetadata {
    1: required string ExecutedAt (go.tag = "json:\"executed_at\""),
    2: optional i64 ExecutionTimeMs (go.tag = "json:\"execution_time_ms\""),
}