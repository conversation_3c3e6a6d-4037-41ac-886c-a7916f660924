package nextagent

//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/gen hertztool model --rm_tag form --rm_tag query --snake_tag --idl types.thrift --model_dir ../hertz_gen -t=template=slim
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/gen hertztool model --rm_tag form --rm_tag query --snake_tag --idl mcp.thrift --model_dir ../hertz_gen -t=template=slim
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/gen hertztool model --rm_tag form --rm_tag query --snake_tag --idl session.thrift --model_dir ../hertz_gen -t=template=slim
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/gen hertztool model --rm_tag form --rm_tag query --snake_tag --idl event.thrift --model_dir ../hertz_gen -t=template=slim
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/gen hertztool model --rm_tag form --rm_tag query --snake_tag --idl artifact.thrift --model_dir ../hertz_gen -t=template=slim
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/gen hertztool model --rm_tag form --rm_tag query --snake_tag --idl lark.thrift --model_dir ../hertz_gen -t=template=slim
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/gen hertztool model --rm_tag form --rm_tag query --snake_tag --idl deployment.thrift --model_dir ../hertz_gen -t=template=slim
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/gen hertztool model --rm_tag form --rm_tag query --snake_tag --idl replay.thrift --model_dir ../hertz_gen -t=template=slim
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/gen hertztool model --rm_tag form --rm_tag query --snake_tag --idl share.thrift --model_dir ../hertz_gen -t=template=slim
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/gen hertztool model --rm_tag form --rm_tag query --snake_tag --idl session_collection.thrift --model_dir ../hertz_gen -t=template=slim
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/gen hertztool model --rm_tag form --rm_tag query --snake_tag --idl user.thrift --model_dir ../hertz_gen -t=template=slim
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/gen hertztool model --rm_tag form --rm_tag query --snake_tag --idl trace.thrift --model_dir ../hertz_gen -t=template=slim
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/gen hertztool model --rm_tag form --rm_tag query --snake_tag --idl showcase.thrift --model_dir ../hertz_gen -t=template=slim
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/gen hertztool model --rm_tag form --rm_tag query --snake_tag --idl agent.thrift --model_dir ../hertz_gen -t=template=slim
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/gen hertztool model --rm_tag form --rm_tag query --snake_tag --idl prompt.thrift --model_dir ../hertz_gen -t=template=slim
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/gen hertztool model --rm_tag form --rm_tag query --snake_tag --idl activity.thrift --model_dir ../hertz_gen -t=template=slim
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/gen hertztool model --rm_tag form --rm_tag query --snake_tag --idl feature.thrift --model_dir ../hertz_gen -t=template=slim
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/gen hertztool model --rm_tag form --rm_tag query --snake_tag --idl template.thrift --model_dir ../hertz_gen -t=template=slim
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/gen hertztool model --rm_tag form --rm_tag query --snake_tag --idl permission.thrift --model_dir ../hertz_gen -t=template=slim
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/gen hertztool model --rm_tag form --rm_tag query --snake_tag --idl space.thrift --model_dir ../hertz_gen -t=template=slim
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/gen hertztool model --rm_tag form --rm_tag query --snake_tag --idl ops.thrift --model_dir ../hertz_gen -t=template=slim
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/gen hertztool model --rm_tag form --rm_tag query --snake_tag --idl knowledgebase.thrift --model_dir ../hertz_gen -t=template=slim
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/gen hertztool model --rm_tag form --rm_tag query --snake_tag --idl mention.thrift --model_dir ../hertz_gen -t=template=slim
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/gen hertztool model --rm_tag form --rm_tag query --snake_tag --idl debug.thrift --model_dir ../hertz_gen -t=template=slim
