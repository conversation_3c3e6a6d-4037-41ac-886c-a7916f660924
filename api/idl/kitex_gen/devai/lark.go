// Code generated by thriftgo (0.4.1). DO NOT EDIT.

package devai

import (
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/base"
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/common"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type LarkPhrase struct {
	Name      *string   `thrift:"Name,1,optional" frugal:"1,optional,string" json:"name"`
	EntityIDs []string  `thrift:"EntityIDs,2,optional" frugal:"2,optional,list<string>" json:"entity_ids"`
	Span      *LarkSpan `thrift:"Span,3,optional" frugal:"3,optional,LarkSpan" json:"span"`
}

func NewLarkPhrase() *LarkPhrase {
	return &LarkPhrase{}
}

func (p *LarkPhrase) InitDefault() {
}

var LarkPhrase_Name_DEFAULT string

func (p *LarkPhrase) GetName() (v string) {
	if !p.IsSetName() {
		return LarkPhrase_Name_DEFAULT
	}
	return *p.Name
}

var LarkPhrase_EntityIDs_DEFAULT []string

func (p *LarkPhrase) GetEntityIDs() (v []string) {
	if !p.IsSetEntityIDs() {
		return LarkPhrase_EntityIDs_DEFAULT
	}
	return p.EntityIDs
}

var LarkPhrase_Span_DEFAULT *LarkSpan

func (p *LarkPhrase) GetSpan() (v *LarkSpan) {
	if !p.IsSetSpan() {
		return LarkPhrase_Span_DEFAULT
	}
	return p.Span
}
func (p *LarkPhrase) SetName(val *string) {
	p.Name = val
}
func (p *LarkPhrase) SetEntityIDs(val []string) {
	p.EntityIDs = val
}
func (p *LarkPhrase) SetSpan(val *LarkSpan) {
	p.Span = val
}

var fieldIDToName_LarkPhrase = map[int16]string{
	1: "Name",
	2: "EntityIDs",
	3: "Span",
}

func (p *LarkPhrase) IsSetName() bool {
	return p.Name != nil
}

func (p *LarkPhrase) IsSetEntityIDs() bool {
	return p.EntityIDs != nil
}

func (p *LarkPhrase) IsSetSpan() bool {
	return p.Span != nil
}

func (p *LarkPhrase) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_LarkPhrase[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *LarkPhrase) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Name = _field
	return nil
}
func (p *LarkPhrase) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.EntityIDs = _field
	return nil
}
func (p *LarkPhrase) ReadField3(iprot thrift.TProtocol) error {
	_field := NewLarkSpan()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Span = _field
	return nil
}

func (p *LarkPhrase) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("LarkPhrase"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *LarkPhrase) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetName() {
		if err = oprot.WriteFieldBegin("Name", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Name); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *LarkPhrase) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetEntityIDs() {
		if err = oprot.WriteFieldBegin("EntityIDs", thrift.LIST, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.EntityIDs)); err != nil {
			return err
		}
		for _, v := range p.EntityIDs {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *LarkPhrase) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetSpan() {
		if err = oprot.WriteFieldBegin("Span", thrift.STRUCT, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Span.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *LarkPhrase) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LarkPhrase(%+v)", *p)

}

func (p *LarkPhrase) DeepEqual(ano *LarkPhrase) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Name) {
		return false
	}
	if !p.Field2DeepEqual(ano.EntityIDs) {
		return false
	}
	if !p.Field3DeepEqual(ano.Span) {
		return false
	}
	return true
}

func (p *LarkPhrase) Field1DeepEqual(src *string) bool {

	if p.Name == src {
		return true
	} else if p.Name == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Name, *src) != 0 {
		return false
	}
	return true
}
func (p *LarkPhrase) Field2DeepEqual(src []string) bool {

	if len(p.EntityIDs) != len(src) {
		return false
	}
	for i, v := range p.EntityIDs {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *LarkPhrase) Field3DeepEqual(src *LarkSpan) bool {

	if !p.Span.DeepEqual(src) {
		return false
	}
	return true
}

type LarkSpan struct {
	Start *int32 `thrift:"Start,1,optional" frugal:"1,optional,i32" json:"start"`
	End   *int32 `thrift:"End,2,optional" frugal:"2,optional,i32" json:"end"`
}

func NewLarkSpan() *LarkSpan {
	return &LarkSpan{}
}

func (p *LarkSpan) InitDefault() {
}

var LarkSpan_Start_DEFAULT int32

func (p *LarkSpan) GetStart() (v int32) {
	if !p.IsSetStart() {
		return LarkSpan_Start_DEFAULT
	}
	return *p.Start
}

var LarkSpan_End_DEFAULT int32

func (p *LarkSpan) GetEnd() (v int32) {
	if !p.IsSetEnd() {
		return LarkSpan_End_DEFAULT
	}
	return *p.End
}
func (p *LarkSpan) SetStart(val *int32) {
	p.Start = val
}
func (p *LarkSpan) SetEnd(val *int32) {
	p.End = val
}

var fieldIDToName_LarkSpan = map[int16]string{
	1: "Start",
	2: "End",
}

func (p *LarkSpan) IsSetStart() bool {
	return p.Start != nil
}

func (p *LarkSpan) IsSetEnd() bool {
	return p.End != nil
}

func (p *LarkSpan) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_LarkSpan[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *LarkSpan) ReadField1(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Start = _field
	return nil
}
func (p *LarkSpan) ReadField2(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.End = _field
	return nil
}

func (p *LarkSpan) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("LarkSpan"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *LarkSpan) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetStart() {
		if err = oprot.WriteFieldBegin("Start", thrift.I32, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.Start); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *LarkSpan) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetEnd() {
		if err = oprot.WriteFieldBegin("End", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.End); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *LarkSpan) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LarkSpan(%+v)", *p)

}

func (p *LarkSpan) DeepEqual(ano *LarkSpan) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Start) {
		return false
	}
	if !p.Field2DeepEqual(ano.End) {
		return false
	}
	return true
}

func (p *LarkSpan) Field1DeepEqual(src *int32) bool {

	if p.Start == src {
		return true
	} else if p.Start == nil || src == nil {
		return false
	}
	if *p.Start != *src {
		return false
	}
	return true
}
func (p *LarkSpan) Field2DeepEqual(src *int32) bool {

	if p.End == src {
		return true
	} else if p.End == nil || src == nil {
		return false
	}
	if *p.End != *src {
		return false
	}
	return true
}

type LarkEntity struct {
	ID          *string                    `thrift:"ID,1,optional" frugal:"1,optional,string" json:"id"`
	MainKeys    []*LarkTerm                `thrift:"MainKeys,2,optional" frugal:"2,optional,list<LarkTerm>" json:"main_keys"`
	Aliases     []*LarkTerm                `thrift:"Aliases,3,optional" frugal:"3,optional,list<LarkTerm>" json:"aliases"`
	Description *string                    `thrift:"Description,4,optional" frugal:"4,optional,string" json:"description"`
	Creator     *string                    `thrift:"Creator,5,optional" frugal:"5,optional,string" json:"creator"`
	CreateTime  *string                    `thrift:"CreateTime,6,optional" frugal:"6,optional,string" json:"create_time"`
	Updater     *string                    `thrift:"Updater,7,optional" frugal:"7,optional,string" json:"updater"`
	UpdateTime  *string                    `thrift:"UpdateTime,8,optional" frugal:"8,optional,string" json:"update_time"`
	RelatedMeta *LarkRelatedMeta           `thrift:"RelatedMeta,9,optional" frugal:"9,optional,LarkRelatedMeta" json:"related_meta"`
	RichText    *string                    `thrift:"RichText,10,optional" frugal:"10,optional,string" json:"rich_text"`
	I18nDescs   []*LarkEntityI18nEntryDesc `thrift:"I18nDescs,11,optional" frugal:"11,optional,list<LarkEntityI18nEntryDesc>" json:"i18n_descs"`
}

func NewLarkEntity() *LarkEntity {
	return &LarkEntity{}
}

func (p *LarkEntity) InitDefault() {
}

var LarkEntity_ID_DEFAULT string

func (p *LarkEntity) GetID() (v string) {
	if !p.IsSetID() {
		return LarkEntity_ID_DEFAULT
	}
	return *p.ID
}

var LarkEntity_MainKeys_DEFAULT []*LarkTerm

func (p *LarkEntity) GetMainKeys() (v []*LarkTerm) {
	if !p.IsSetMainKeys() {
		return LarkEntity_MainKeys_DEFAULT
	}
	return p.MainKeys
}

var LarkEntity_Aliases_DEFAULT []*LarkTerm

func (p *LarkEntity) GetAliases() (v []*LarkTerm) {
	if !p.IsSetAliases() {
		return LarkEntity_Aliases_DEFAULT
	}
	return p.Aliases
}

var LarkEntity_Description_DEFAULT string

func (p *LarkEntity) GetDescription() (v string) {
	if !p.IsSetDescription() {
		return LarkEntity_Description_DEFAULT
	}
	return *p.Description
}

var LarkEntity_Creator_DEFAULT string

func (p *LarkEntity) GetCreator() (v string) {
	if !p.IsSetCreator() {
		return LarkEntity_Creator_DEFAULT
	}
	return *p.Creator
}

var LarkEntity_CreateTime_DEFAULT string

func (p *LarkEntity) GetCreateTime() (v string) {
	if !p.IsSetCreateTime() {
		return LarkEntity_CreateTime_DEFAULT
	}
	return *p.CreateTime
}

var LarkEntity_Updater_DEFAULT string

func (p *LarkEntity) GetUpdater() (v string) {
	if !p.IsSetUpdater() {
		return LarkEntity_Updater_DEFAULT
	}
	return *p.Updater
}

var LarkEntity_UpdateTime_DEFAULT string

func (p *LarkEntity) GetUpdateTime() (v string) {
	if !p.IsSetUpdateTime() {
		return LarkEntity_UpdateTime_DEFAULT
	}
	return *p.UpdateTime
}

var LarkEntity_RelatedMeta_DEFAULT *LarkRelatedMeta

func (p *LarkEntity) GetRelatedMeta() (v *LarkRelatedMeta) {
	if !p.IsSetRelatedMeta() {
		return LarkEntity_RelatedMeta_DEFAULT
	}
	return p.RelatedMeta
}

var LarkEntity_RichText_DEFAULT string

func (p *LarkEntity) GetRichText() (v string) {
	if !p.IsSetRichText() {
		return LarkEntity_RichText_DEFAULT
	}
	return *p.RichText
}

var LarkEntity_I18nDescs_DEFAULT []*LarkEntityI18nEntryDesc

func (p *LarkEntity) GetI18nDescs() (v []*LarkEntityI18nEntryDesc) {
	if !p.IsSetI18nDescs() {
		return LarkEntity_I18nDescs_DEFAULT
	}
	return p.I18nDescs
}
func (p *LarkEntity) SetID(val *string) {
	p.ID = val
}
func (p *LarkEntity) SetMainKeys(val []*LarkTerm) {
	p.MainKeys = val
}
func (p *LarkEntity) SetAliases(val []*LarkTerm) {
	p.Aliases = val
}
func (p *LarkEntity) SetDescription(val *string) {
	p.Description = val
}
func (p *LarkEntity) SetCreator(val *string) {
	p.Creator = val
}
func (p *LarkEntity) SetCreateTime(val *string) {
	p.CreateTime = val
}
func (p *LarkEntity) SetUpdater(val *string) {
	p.Updater = val
}
func (p *LarkEntity) SetUpdateTime(val *string) {
	p.UpdateTime = val
}
func (p *LarkEntity) SetRelatedMeta(val *LarkRelatedMeta) {
	p.RelatedMeta = val
}
func (p *LarkEntity) SetRichText(val *string) {
	p.RichText = val
}
func (p *LarkEntity) SetI18nDescs(val []*LarkEntityI18nEntryDesc) {
	p.I18nDescs = val
}

var fieldIDToName_LarkEntity = map[int16]string{
	1:  "ID",
	2:  "MainKeys",
	3:  "Aliases",
	4:  "Description",
	5:  "Creator",
	6:  "CreateTime",
	7:  "Updater",
	8:  "UpdateTime",
	9:  "RelatedMeta",
	10: "RichText",
	11: "I18nDescs",
}

func (p *LarkEntity) IsSetID() bool {
	return p.ID != nil
}

func (p *LarkEntity) IsSetMainKeys() bool {
	return p.MainKeys != nil
}

func (p *LarkEntity) IsSetAliases() bool {
	return p.Aliases != nil
}

func (p *LarkEntity) IsSetDescription() bool {
	return p.Description != nil
}

func (p *LarkEntity) IsSetCreator() bool {
	return p.Creator != nil
}

func (p *LarkEntity) IsSetCreateTime() bool {
	return p.CreateTime != nil
}

func (p *LarkEntity) IsSetUpdater() bool {
	return p.Updater != nil
}

func (p *LarkEntity) IsSetUpdateTime() bool {
	return p.UpdateTime != nil
}

func (p *LarkEntity) IsSetRelatedMeta() bool {
	return p.RelatedMeta != nil
}

func (p *LarkEntity) IsSetRichText() bool {
	return p.RichText != nil
}

func (p *LarkEntity) IsSetI18nDescs() bool {
	return p.I18nDescs != nil
}

func (p *LarkEntity) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_LarkEntity[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *LarkEntity) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ID = _field
	return nil
}
func (p *LarkEntity) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*LarkTerm, 0, size)
	values := make([]LarkTerm, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.MainKeys = _field
	return nil
}
func (p *LarkEntity) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*LarkTerm, 0, size)
	values := make([]LarkTerm, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Aliases = _field
	return nil
}
func (p *LarkEntity) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Description = _field
	return nil
}
func (p *LarkEntity) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Creator = _field
	return nil
}
func (p *LarkEntity) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CreateTime = _field
	return nil
}
func (p *LarkEntity) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Updater = _field
	return nil
}
func (p *LarkEntity) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.UpdateTime = _field
	return nil
}
func (p *LarkEntity) ReadField9(iprot thrift.TProtocol) error {
	_field := NewLarkRelatedMeta()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.RelatedMeta = _field
	return nil
}
func (p *LarkEntity) ReadField10(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RichText = _field
	return nil
}
func (p *LarkEntity) ReadField11(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*LarkEntityI18nEntryDesc, 0, size)
	values := make([]LarkEntityI18nEntryDesc, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.I18nDescs = _field
	return nil
}

func (p *LarkEntity) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("LarkEntity"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *LarkEntity) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetID() {
		if err = oprot.WriteFieldBegin("ID", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *LarkEntity) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetMainKeys() {
		if err = oprot.WriteFieldBegin("MainKeys", thrift.LIST, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.MainKeys)); err != nil {
			return err
		}
		for _, v := range p.MainKeys {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *LarkEntity) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetAliases() {
		if err = oprot.WriteFieldBegin("Aliases", thrift.LIST, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Aliases)); err != nil {
			return err
		}
		for _, v := range p.Aliases {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *LarkEntity) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetDescription() {
		if err = oprot.WriteFieldBegin("Description", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Description); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *LarkEntity) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreator() {
		if err = oprot.WriteFieldBegin("Creator", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Creator); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *LarkEntity) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreateTime() {
		if err = oprot.WriteFieldBegin("CreateTime", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.CreateTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *LarkEntity) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetUpdater() {
		if err = oprot.WriteFieldBegin("Updater", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Updater); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *LarkEntity) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetUpdateTime() {
		if err = oprot.WriteFieldBegin("UpdateTime", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.UpdateTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *LarkEntity) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetRelatedMeta() {
		if err = oprot.WriteFieldBegin("RelatedMeta", thrift.STRUCT, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.RelatedMeta.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *LarkEntity) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetRichText() {
		if err = oprot.WriteFieldBegin("RichText", thrift.STRING, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RichText); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *LarkEntity) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetI18nDescs() {
		if err = oprot.WriteFieldBegin("I18nDescs", thrift.LIST, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.I18nDescs)); err != nil {
			return err
		}
		for _, v := range p.I18nDescs {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *LarkEntity) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LarkEntity(%+v)", *p)

}

func (p *LarkEntity) DeepEqual(ano *LarkEntity) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ID) {
		return false
	}
	if !p.Field2DeepEqual(ano.MainKeys) {
		return false
	}
	if !p.Field3DeepEqual(ano.Aliases) {
		return false
	}
	if !p.Field4DeepEqual(ano.Description) {
		return false
	}
	if !p.Field5DeepEqual(ano.Creator) {
		return false
	}
	if !p.Field6DeepEqual(ano.CreateTime) {
		return false
	}
	if !p.Field7DeepEqual(ano.Updater) {
		return false
	}
	if !p.Field8DeepEqual(ano.UpdateTime) {
		return false
	}
	if !p.Field9DeepEqual(ano.RelatedMeta) {
		return false
	}
	if !p.Field10DeepEqual(ano.RichText) {
		return false
	}
	if !p.Field11DeepEqual(ano.I18nDescs) {
		return false
	}
	return true
}

func (p *LarkEntity) Field1DeepEqual(src *string) bool {

	if p.ID == src {
		return true
	} else if p.ID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ID, *src) != 0 {
		return false
	}
	return true
}
func (p *LarkEntity) Field2DeepEqual(src []*LarkTerm) bool {

	if len(p.MainKeys) != len(src) {
		return false
	}
	for i, v := range p.MainKeys {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *LarkEntity) Field3DeepEqual(src []*LarkTerm) bool {

	if len(p.Aliases) != len(src) {
		return false
	}
	for i, v := range p.Aliases {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *LarkEntity) Field4DeepEqual(src *string) bool {

	if p.Description == src {
		return true
	} else if p.Description == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Description, *src) != 0 {
		return false
	}
	return true
}
func (p *LarkEntity) Field5DeepEqual(src *string) bool {

	if p.Creator == src {
		return true
	} else if p.Creator == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Creator, *src) != 0 {
		return false
	}
	return true
}
func (p *LarkEntity) Field6DeepEqual(src *string) bool {

	if p.CreateTime == src {
		return true
	} else if p.CreateTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.CreateTime, *src) != 0 {
		return false
	}
	return true
}
func (p *LarkEntity) Field7DeepEqual(src *string) bool {

	if p.Updater == src {
		return true
	} else if p.Updater == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Updater, *src) != 0 {
		return false
	}
	return true
}
func (p *LarkEntity) Field8DeepEqual(src *string) bool {

	if p.UpdateTime == src {
		return true
	} else if p.UpdateTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.UpdateTime, *src) != 0 {
		return false
	}
	return true
}
func (p *LarkEntity) Field9DeepEqual(src *LarkRelatedMeta) bool {

	if !p.RelatedMeta.DeepEqual(src) {
		return false
	}
	return true
}
func (p *LarkEntity) Field10DeepEqual(src *string) bool {

	if p.RichText == src {
		return true
	} else if p.RichText == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RichText, *src) != 0 {
		return false
	}
	return true
}
func (p *LarkEntity) Field11DeepEqual(src []*LarkEntityI18nEntryDesc) bool {

	if len(p.I18nDescs) != len(src) {
		return false
	}
	for i, v := range p.I18nDescs {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type LarkEntityI18nEntryDesc struct {
	Language    *int32  `thrift:"Language,1,optional" frugal:"1,optional,i32" json:"language"`
	Description *string `thrift:"Description,2,optional" frugal:"2,optional,string" json:"description"`
	RichText    *string `thrift:"RichText,3,optional" frugal:"3,optional,string" json:"rich_text"`
}

func NewLarkEntityI18nEntryDesc() *LarkEntityI18nEntryDesc {
	return &LarkEntityI18nEntryDesc{}
}

func (p *LarkEntityI18nEntryDesc) InitDefault() {
}

var LarkEntityI18nEntryDesc_Language_DEFAULT int32

func (p *LarkEntityI18nEntryDesc) GetLanguage() (v int32) {
	if !p.IsSetLanguage() {
		return LarkEntityI18nEntryDesc_Language_DEFAULT
	}
	return *p.Language
}

var LarkEntityI18nEntryDesc_Description_DEFAULT string

func (p *LarkEntityI18nEntryDesc) GetDescription() (v string) {
	if !p.IsSetDescription() {
		return LarkEntityI18nEntryDesc_Description_DEFAULT
	}
	return *p.Description
}

var LarkEntityI18nEntryDesc_RichText_DEFAULT string

func (p *LarkEntityI18nEntryDesc) GetRichText() (v string) {
	if !p.IsSetRichText() {
		return LarkEntityI18nEntryDesc_RichText_DEFAULT
	}
	return *p.RichText
}
func (p *LarkEntityI18nEntryDesc) SetLanguage(val *int32) {
	p.Language = val
}
func (p *LarkEntityI18nEntryDesc) SetDescription(val *string) {
	p.Description = val
}
func (p *LarkEntityI18nEntryDesc) SetRichText(val *string) {
	p.RichText = val
}

var fieldIDToName_LarkEntityI18nEntryDesc = map[int16]string{
	1: "Language",
	2: "Description",
	3: "RichText",
}

func (p *LarkEntityI18nEntryDesc) IsSetLanguage() bool {
	return p.Language != nil
}

func (p *LarkEntityI18nEntryDesc) IsSetDescription() bool {
	return p.Description != nil
}

func (p *LarkEntityI18nEntryDesc) IsSetRichText() bool {
	return p.RichText != nil
}

func (p *LarkEntityI18nEntryDesc) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_LarkEntityI18nEntryDesc[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *LarkEntityI18nEntryDesc) ReadField1(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Language = _field
	return nil
}
func (p *LarkEntityI18nEntryDesc) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Description = _field
	return nil
}
func (p *LarkEntityI18nEntryDesc) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RichText = _field
	return nil
}

func (p *LarkEntityI18nEntryDesc) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("LarkEntityI18nEntryDesc"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *LarkEntityI18nEntryDesc) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetLanguage() {
		if err = oprot.WriteFieldBegin("Language", thrift.I32, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.Language); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *LarkEntityI18nEntryDesc) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetDescription() {
		if err = oprot.WriteFieldBegin("Description", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Description); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *LarkEntityI18nEntryDesc) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetRichText() {
		if err = oprot.WriteFieldBegin("RichText", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RichText); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *LarkEntityI18nEntryDesc) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LarkEntityI18nEntryDesc(%+v)", *p)

}

func (p *LarkEntityI18nEntryDesc) DeepEqual(ano *LarkEntityI18nEntryDesc) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Language) {
		return false
	}
	if !p.Field2DeepEqual(ano.Description) {
		return false
	}
	if !p.Field3DeepEqual(ano.RichText) {
		return false
	}
	return true
}

func (p *LarkEntityI18nEntryDesc) Field1DeepEqual(src *int32) bool {

	if p.Language == src {
		return true
	} else if p.Language == nil || src == nil {
		return false
	}
	if *p.Language != *src {
		return false
	}
	return true
}
func (p *LarkEntityI18nEntryDesc) Field2DeepEqual(src *string) bool {

	if p.Description == src {
		return true
	} else if p.Description == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Description, *src) != 0 {
		return false
	}
	return true
}
func (p *LarkEntityI18nEntryDesc) Field3DeepEqual(src *string) bool {

	if p.RichText == src {
		return true
	} else if p.RichText == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RichText, *src) != 0 {
		return false
	}
	return true
}

type LarkTerm struct {
	Key           *string                `thrift:"Key,1,optional" frugal:"1,optional,string" json:"key"`
	DisplayStatus *LarkTermDisplayStatus `thrift:"DisplayStatus,2,optional" frugal:"2,optional,LarkTermDisplayStatus" json:"display_status"`
}

func NewLarkTerm() *LarkTerm {
	return &LarkTerm{}
}

func (p *LarkTerm) InitDefault() {
}

var LarkTerm_Key_DEFAULT string

func (p *LarkTerm) GetKey() (v string) {
	if !p.IsSetKey() {
		return LarkTerm_Key_DEFAULT
	}
	return *p.Key
}

var LarkTerm_DisplayStatus_DEFAULT *LarkTermDisplayStatus

func (p *LarkTerm) GetDisplayStatus() (v *LarkTermDisplayStatus) {
	if !p.IsSetDisplayStatus() {
		return LarkTerm_DisplayStatus_DEFAULT
	}
	return p.DisplayStatus
}
func (p *LarkTerm) SetKey(val *string) {
	p.Key = val
}
func (p *LarkTerm) SetDisplayStatus(val *LarkTermDisplayStatus) {
	p.DisplayStatus = val
}

var fieldIDToName_LarkTerm = map[int16]string{
	1: "Key",
	2: "DisplayStatus",
}

func (p *LarkTerm) IsSetKey() bool {
	return p.Key != nil
}

func (p *LarkTerm) IsSetDisplayStatus() bool {
	return p.DisplayStatus != nil
}

func (p *LarkTerm) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_LarkTerm[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *LarkTerm) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Key = _field
	return nil
}
func (p *LarkTerm) ReadField2(iprot thrift.TProtocol) error {
	_field := NewLarkTermDisplayStatus()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.DisplayStatus = _field
	return nil
}

func (p *LarkTerm) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("LarkTerm"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *LarkTerm) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetKey() {
		if err = oprot.WriteFieldBegin("Key", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Key); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *LarkTerm) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetDisplayStatus() {
		if err = oprot.WriteFieldBegin("DisplayStatus", thrift.STRUCT, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.DisplayStatus.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *LarkTerm) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LarkTerm(%+v)", *p)

}

func (p *LarkTerm) DeepEqual(ano *LarkTerm) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Key) {
		return false
	}
	if !p.Field2DeepEqual(ano.DisplayStatus) {
		return false
	}
	return true
}

func (p *LarkTerm) Field1DeepEqual(src *string) bool {

	if p.Key == src {
		return true
	} else if p.Key == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Key, *src) != 0 {
		return false
	}
	return true
}
func (p *LarkTerm) Field2DeepEqual(src *LarkTermDisplayStatus) bool {

	if !p.DisplayStatus.DeepEqual(src) {
		return false
	}
	return true
}

type LarkTermDisplayStatus struct {
	AllowHighlight *bool `thrift:"AllowHighlight,1,optional" frugal:"1,optional,bool" json:"allow_highlight"`
	AllowSearch    *bool `thrift:"AllowSearch,2,optional" frugal:"2,optional,bool" json:"allow_search"`
}

func NewLarkTermDisplayStatus() *LarkTermDisplayStatus {
	return &LarkTermDisplayStatus{}
}

func (p *LarkTermDisplayStatus) InitDefault() {
}

var LarkTermDisplayStatus_AllowHighlight_DEFAULT bool

func (p *LarkTermDisplayStatus) GetAllowHighlight() (v bool) {
	if !p.IsSetAllowHighlight() {
		return LarkTermDisplayStatus_AllowHighlight_DEFAULT
	}
	return *p.AllowHighlight
}

var LarkTermDisplayStatus_AllowSearch_DEFAULT bool

func (p *LarkTermDisplayStatus) GetAllowSearch() (v bool) {
	if !p.IsSetAllowSearch() {
		return LarkTermDisplayStatus_AllowSearch_DEFAULT
	}
	return *p.AllowSearch
}
func (p *LarkTermDisplayStatus) SetAllowHighlight(val *bool) {
	p.AllowHighlight = val
}
func (p *LarkTermDisplayStatus) SetAllowSearch(val *bool) {
	p.AllowSearch = val
}

var fieldIDToName_LarkTermDisplayStatus = map[int16]string{
	1: "AllowHighlight",
	2: "AllowSearch",
}

func (p *LarkTermDisplayStatus) IsSetAllowHighlight() bool {
	return p.AllowHighlight != nil
}

func (p *LarkTermDisplayStatus) IsSetAllowSearch() bool {
	return p.AllowSearch != nil
}

func (p *LarkTermDisplayStatus) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_LarkTermDisplayStatus[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *LarkTermDisplayStatus) ReadField1(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AllowHighlight = _field
	return nil
}
func (p *LarkTermDisplayStatus) ReadField2(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AllowSearch = _field
	return nil
}

func (p *LarkTermDisplayStatus) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("LarkTermDisplayStatus"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *LarkTermDisplayStatus) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetAllowHighlight() {
		if err = oprot.WriteFieldBegin("AllowHighlight", thrift.BOOL, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.AllowHighlight); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *LarkTermDisplayStatus) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetAllowSearch() {
		if err = oprot.WriteFieldBegin("AllowSearch", thrift.BOOL, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.AllowSearch); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *LarkTermDisplayStatus) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LarkTermDisplayStatus(%+v)", *p)

}

func (p *LarkTermDisplayStatus) DeepEqual(ano *LarkTermDisplayStatus) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.AllowHighlight) {
		return false
	}
	if !p.Field2DeepEqual(ano.AllowSearch) {
		return false
	}
	return true
}

func (p *LarkTermDisplayStatus) Field1DeepEqual(src *bool) bool {

	if p.AllowHighlight == src {
		return true
	} else if p.AllowHighlight == nil || src == nil {
		return false
	}
	if *p.AllowHighlight != *src {
		return false
	}
	return true
}
func (p *LarkTermDisplayStatus) Field2DeepEqual(src *bool) bool {

	if p.AllowSearch == src {
		return true
	} else if p.AllowSearch == nil || src == nil {
		return false
	}
	if *p.AllowSearch != *src {
		return false
	}
	return true
}

type LarkRelatedMeta struct {
	Users   []*LarkReferer `thrift:"Users,1,optional" frugal:"1,optional,list<LarkReferer>" json:"users"`
	Chats   []*LarkReferer `thrift:"Chats,2,optional" frugal:"2,optional,list<LarkReferer>" json:"chats"`
	Docs    []*LarkReferer `thrift:"Docs,3,optional" frugal:"3,optional,list<LarkReferer>" json:"docs"`
	Oncalls []*LarkReferer `thrift:"Oncalls,4,optional" frugal:"4,optional,list<LarkReferer>" json:"oncalls"`
	Links   []*LarkReferer `thrift:"Links,5,optional" frugal:"5,optional,list<LarkReferer>" json:"links"`
}

func NewLarkRelatedMeta() *LarkRelatedMeta {
	return &LarkRelatedMeta{}
}

func (p *LarkRelatedMeta) InitDefault() {
}

var LarkRelatedMeta_Users_DEFAULT []*LarkReferer

func (p *LarkRelatedMeta) GetUsers() (v []*LarkReferer) {
	if !p.IsSetUsers() {
		return LarkRelatedMeta_Users_DEFAULT
	}
	return p.Users
}

var LarkRelatedMeta_Chats_DEFAULT []*LarkReferer

func (p *LarkRelatedMeta) GetChats() (v []*LarkReferer) {
	if !p.IsSetChats() {
		return LarkRelatedMeta_Chats_DEFAULT
	}
	return p.Chats
}

var LarkRelatedMeta_Docs_DEFAULT []*LarkReferer

func (p *LarkRelatedMeta) GetDocs() (v []*LarkReferer) {
	if !p.IsSetDocs() {
		return LarkRelatedMeta_Docs_DEFAULT
	}
	return p.Docs
}

var LarkRelatedMeta_Oncalls_DEFAULT []*LarkReferer

func (p *LarkRelatedMeta) GetOncalls() (v []*LarkReferer) {
	if !p.IsSetOncalls() {
		return LarkRelatedMeta_Oncalls_DEFAULT
	}
	return p.Oncalls
}

var LarkRelatedMeta_Links_DEFAULT []*LarkReferer

func (p *LarkRelatedMeta) GetLinks() (v []*LarkReferer) {
	if !p.IsSetLinks() {
		return LarkRelatedMeta_Links_DEFAULT
	}
	return p.Links
}
func (p *LarkRelatedMeta) SetUsers(val []*LarkReferer) {
	p.Users = val
}
func (p *LarkRelatedMeta) SetChats(val []*LarkReferer) {
	p.Chats = val
}
func (p *LarkRelatedMeta) SetDocs(val []*LarkReferer) {
	p.Docs = val
}
func (p *LarkRelatedMeta) SetOncalls(val []*LarkReferer) {
	p.Oncalls = val
}
func (p *LarkRelatedMeta) SetLinks(val []*LarkReferer) {
	p.Links = val
}

var fieldIDToName_LarkRelatedMeta = map[int16]string{
	1: "Users",
	2: "Chats",
	3: "Docs",
	4: "Oncalls",
	5: "Links",
}

func (p *LarkRelatedMeta) IsSetUsers() bool {
	return p.Users != nil
}

func (p *LarkRelatedMeta) IsSetChats() bool {
	return p.Chats != nil
}

func (p *LarkRelatedMeta) IsSetDocs() bool {
	return p.Docs != nil
}

func (p *LarkRelatedMeta) IsSetOncalls() bool {
	return p.Oncalls != nil
}

func (p *LarkRelatedMeta) IsSetLinks() bool {
	return p.Links != nil
}

func (p *LarkRelatedMeta) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_LarkRelatedMeta[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *LarkRelatedMeta) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*LarkReferer, 0, size)
	values := make([]LarkReferer, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Users = _field
	return nil
}
func (p *LarkRelatedMeta) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*LarkReferer, 0, size)
	values := make([]LarkReferer, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Chats = _field
	return nil
}
func (p *LarkRelatedMeta) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*LarkReferer, 0, size)
	values := make([]LarkReferer, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Docs = _field
	return nil
}
func (p *LarkRelatedMeta) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*LarkReferer, 0, size)
	values := make([]LarkReferer, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Oncalls = _field
	return nil
}
func (p *LarkRelatedMeta) ReadField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*LarkReferer, 0, size)
	values := make([]LarkReferer, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Links = _field
	return nil
}

func (p *LarkRelatedMeta) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("LarkRelatedMeta"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *LarkRelatedMeta) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetUsers() {
		if err = oprot.WriteFieldBegin("Users", thrift.LIST, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Users)); err != nil {
			return err
		}
		for _, v := range p.Users {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *LarkRelatedMeta) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetChats() {
		if err = oprot.WriteFieldBegin("Chats", thrift.LIST, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Chats)); err != nil {
			return err
		}
		for _, v := range p.Chats {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *LarkRelatedMeta) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetDocs() {
		if err = oprot.WriteFieldBegin("Docs", thrift.LIST, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Docs)); err != nil {
			return err
		}
		for _, v := range p.Docs {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *LarkRelatedMeta) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetOncalls() {
		if err = oprot.WriteFieldBegin("Oncalls", thrift.LIST, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Oncalls)); err != nil {
			return err
		}
		for _, v := range p.Oncalls {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *LarkRelatedMeta) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetLinks() {
		if err = oprot.WriteFieldBegin("Links", thrift.LIST, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Links)); err != nil {
			return err
		}
		for _, v := range p.Links {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *LarkRelatedMeta) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LarkRelatedMeta(%+v)", *p)

}

func (p *LarkRelatedMeta) DeepEqual(ano *LarkRelatedMeta) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Users) {
		return false
	}
	if !p.Field2DeepEqual(ano.Chats) {
		return false
	}
	if !p.Field3DeepEqual(ano.Docs) {
		return false
	}
	if !p.Field4DeepEqual(ano.Oncalls) {
		return false
	}
	if !p.Field5DeepEqual(ano.Links) {
		return false
	}
	return true
}

func (p *LarkRelatedMeta) Field1DeepEqual(src []*LarkReferer) bool {

	if len(p.Users) != len(src) {
		return false
	}
	for i, v := range p.Users {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *LarkRelatedMeta) Field2DeepEqual(src []*LarkReferer) bool {

	if len(p.Chats) != len(src) {
		return false
	}
	for i, v := range p.Chats {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *LarkRelatedMeta) Field3DeepEqual(src []*LarkReferer) bool {

	if len(p.Docs) != len(src) {
		return false
	}
	for i, v := range p.Docs {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *LarkRelatedMeta) Field4DeepEqual(src []*LarkReferer) bool {

	if len(p.Oncalls) != len(src) {
		return false
	}
	for i, v := range p.Oncalls {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *LarkRelatedMeta) Field5DeepEqual(src []*LarkReferer) bool {

	if len(p.Links) != len(src) {
		return false
	}
	for i, v := range p.Links {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type LarkReferer struct {
	Id    *string `thrift:"Id,1,optional" frugal:"1,optional,string" json:"id"`
	Title *string `thrift:"Title,2,optional" frugal:"2,optional,string" json:"title"`
	Url   *string `thrift:"Url,3,optional" frugal:"3,optional,string" json:"url"`
}

func NewLarkReferer() *LarkReferer {
	return &LarkReferer{}
}

func (p *LarkReferer) InitDefault() {
}

var LarkReferer_Id_DEFAULT string

func (p *LarkReferer) GetId() (v string) {
	if !p.IsSetId() {
		return LarkReferer_Id_DEFAULT
	}
	return *p.Id
}

var LarkReferer_Title_DEFAULT string

func (p *LarkReferer) GetTitle() (v string) {
	if !p.IsSetTitle() {
		return LarkReferer_Title_DEFAULT
	}
	return *p.Title
}

var LarkReferer_Url_DEFAULT string

func (p *LarkReferer) GetUrl() (v string) {
	if !p.IsSetUrl() {
		return LarkReferer_Url_DEFAULT
	}
	return *p.Url
}
func (p *LarkReferer) SetId(val *string) {
	p.Id = val
}
func (p *LarkReferer) SetTitle(val *string) {
	p.Title = val
}
func (p *LarkReferer) SetUrl(val *string) {
	p.Url = val
}

var fieldIDToName_LarkReferer = map[int16]string{
	1: "Id",
	2: "Title",
	3: "Url",
}

func (p *LarkReferer) IsSetId() bool {
	return p.Id != nil
}

func (p *LarkReferer) IsSetTitle() bool {
	return p.Title != nil
}

func (p *LarkReferer) IsSetUrl() bool {
	return p.Url != nil
}

func (p *LarkReferer) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_LarkReferer[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *LarkReferer) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Id = _field
	return nil
}
func (p *LarkReferer) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Title = _field
	return nil
}
func (p *LarkReferer) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Url = _field
	return nil
}

func (p *LarkReferer) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("LarkReferer"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *LarkReferer) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetId() {
		if err = oprot.WriteFieldBegin("Id", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Id); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *LarkReferer) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetTitle() {
		if err = oprot.WriteFieldBegin("Title", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Title); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *LarkReferer) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetUrl() {
		if err = oprot.WriteFieldBegin("Url", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Url); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *LarkReferer) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LarkReferer(%+v)", *p)

}

func (p *LarkReferer) DeepEqual(ano *LarkReferer) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Id) {
		return false
	}
	if !p.Field2DeepEqual(ano.Title) {
		return false
	}
	if !p.Field3DeepEqual(ano.Url) {
		return false
	}
	return true
}

func (p *LarkReferer) Field1DeepEqual(src *string) bool {

	if p.Id == src {
		return true
	} else if p.Id == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Id, *src) != 0 {
		return false
	}
	return true
}
func (p *LarkReferer) Field2DeepEqual(src *string) bool {

	if p.Title == src {
		return true
	} else if p.Title == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Title, *src) != 0 {
		return false
	}
	return true
}
func (p *LarkReferer) Field3DeepEqual(src *string) bool {

	if p.Url == src {
		return true
	} else if p.Url == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Url, *src) != 0 {
		return false
	}
	return true
}

type HighlightLarkBaikeEntitiesRequest struct {
	Text        string              `thrift:"Text,1,required" frugal:"1,required,string" json:"text"`
	HTTPRequest *common.HTTPRequest `thrift:"HTTPRequest,201,optional" frugal:"201,optional,common.HTTPRequest" json:"HTTPRequest,omitempty"`
	Base        *base.Base          `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewHighlightLarkBaikeEntitiesRequest() *HighlightLarkBaikeEntitiesRequest {
	return &HighlightLarkBaikeEntitiesRequest{}
}

func (p *HighlightLarkBaikeEntitiesRequest) InitDefault() {
}

func (p *HighlightLarkBaikeEntitiesRequest) GetText() (v string) {
	return p.Text
}

var HighlightLarkBaikeEntitiesRequest_HTTPRequest_DEFAULT *common.HTTPRequest

func (p *HighlightLarkBaikeEntitiesRequest) GetHTTPRequest() (v *common.HTTPRequest) {
	if !p.IsSetHTTPRequest() {
		return HighlightLarkBaikeEntitiesRequest_HTTPRequest_DEFAULT
	}
	return p.HTTPRequest
}

var HighlightLarkBaikeEntitiesRequest_Base_DEFAULT *base.Base

func (p *HighlightLarkBaikeEntitiesRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return HighlightLarkBaikeEntitiesRequest_Base_DEFAULT
	}
	return p.Base
}
func (p *HighlightLarkBaikeEntitiesRequest) SetText(val string) {
	p.Text = val
}
func (p *HighlightLarkBaikeEntitiesRequest) SetHTTPRequest(val *common.HTTPRequest) {
	p.HTTPRequest = val
}
func (p *HighlightLarkBaikeEntitiesRequest) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_HighlightLarkBaikeEntitiesRequest = map[int16]string{
	1:   "Text",
	201: "HTTPRequest",
	255: "Base",
}

func (p *HighlightLarkBaikeEntitiesRequest) IsSetHTTPRequest() bool {
	return p.HTTPRequest != nil
}

func (p *HighlightLarkBaikeEntitiesRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *HighlightLarkBaikeEntitiesRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetText bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetText = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 201:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField201(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetText {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_HighlightLarkBaikeEntitiesRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_HighlightLarkBaikeEntitiesRequest[fieldId]))
}

func (p *HighlightLarkBaikeEntitiesRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Text = _field
	return nil
}
func (p *HighlightLarkBaikeEntitiesRequest) ReadField201(iprot thrift.TProtocol) error {
	_field := common.NewHTTPRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.HTTPRequest = _field
	return nil
}
func (p *HighlightLarkBaikeEntitiesRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *HighlightLarkBaikeEntitiesRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("HighlightLarkBaikeEntitiesRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField201(oprot); err != nil {
			fieldId = 201
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *HighlightLarkBaikeEntitiesRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Text", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Text); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *HighlightLarkBaikeEntitiesRequest) writeField201(oprot thrift.TProtocol) (err error) {
	if p.IsSetHTTPRequest() {
		if err = oprot.WriteFieldBegin("HTTPRequest", thrift.STRUCT, 201); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.HTTPRequest.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 end error: ", p), err)
}
func (p *HighlightLarkBaikeEntitiesRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *HighlightLarkBaikeEntitiesRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("HighlightLarkBaikeEntitiesRequest(%+v)", *p)

}

func (p *HighlightLarkBaikeEntitiesRequest) DeepEqual(ano *HighlightLarkBaikeEntitiesRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Text) {
		return false
	}
	if !p.Field201DeepEqual(ano.HTTPRequest) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *HighlightLarkBaikeEntitiesRequest) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Text, src) != 0 {
		return false
	}
	return true
}
func (p *HighlightLarkBaikeEntitiesRequest) Field201DeepEqual(src *common.HTTPRequest) bool {

	if !p.HTTPRequest.DeepEqual(src) {
		return false
	}
	return true
}
func (p *HighlightLarkBaikeEntitiesRequest) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type HighlightLarkBaikeEntitiesResponse struct {
	Phrases      []*LarkPhrase        `thrift:"Phrases,1" frugal:"1,default,list<LarkPhrase>" json:"phrases"`
	Code         *common.ErrorCode    `thrift:"Code,201,optional" frugal:"201,optional,ErrorCode" json:"code"`
	Message      *string              `thrift:"Message,202,optional" frugal:"202,optional,string" json:"message"`
	HTTPCode     *int32               `thrift:"HTTPCode,203,optional" frugal:"203,optional,i32" json:"HTTPCode,omitempty"`
	HTTPResponse *common.HTTPResponse `thrift:"HTTPResponse,204,optional" frugal:"204,optional,common.HTTPResponse" json:"HTTPResponse,omitempty"`
}

func NewHighlightLarkBaikeEntitiesResponse() *HighlightLarkBaikeEntitiesResponse {
	return &HighlightLarkBaikeEntitiesResponse{}
}

func (p *HighlightLarkBaikeEntitiesResponse) InitDefault() {
}

func (p *HighlightLarkBaikeEntitiesResponse) GetPhrases() (v []*LarkPhrase) {
	return p.Phrases
}

var HighlightLarkBaikeEntitiesResponse_Code_DEFAULT common.ErrorCode

func (p *HighlightLarkBaikeEntitiesResponse) GetCode() (v common.ErrorCode) {
	if !p.IsSetCode() {
		return HighlightLarkBaikeEntitiesResponse_Code_DEFAULT
	}
	return *p.Code
}

var HighlightLarkBaikeEntitiesResponse_Message_DEFAULT string

func (p *HighlightLarkBaikeEntitiesResponse) GetMessage() (v string) {
	if !p.IsSetMessage() {
		return HighlightLarkBaikeEntitiesResponse_Message_DEFAULT
	}
	return *p.Message
}

var HighlightLarkBaikeEntitiesResponse_HTTPCode_DEFAULT int32

func (p *HighlightLarkBaikeEntitiesResponse) GetHTTPCode() (v int32) {
	if !p.IsSetHTTPCode() {
		return HighlightLarkBaikeEntitiesResponse_HTTPCode_DEFAULT
	}
	return *p.HTTPCode
}

var HighlightLarkBaikeEntitiesResponse_HTTPResponse_DEFAULT *common.HTTPResponse

func (p *HighlightLarkBaikeEntitiesResponse) GetHTTPResponse() (v *common.HTTPResponse) {
	if !p.IsSetHTTPResponse() {
		return HighlightLarkBaikeEntitiesResponse_HTTPResponse_DEFAULT
	}
	return p.HTTPResponse
}
func (p *HighlightLarkBaikeEntitiesResponse) SetPhrases(val []*LarkPhrase) {
	p.Phrases = val
}
func (p *HighlightLarkBaikeEntitiesResponse) SetCode(val *common.ErrorCode) {
	p.Code = val
}
func (p *HighlightLarkBaikeEntitiesResponse) SetMessage(val *string) {
	p.Message = val
}
func (p *HighlightLarkBaikeEntitiesResponse) SetHTTPCode(val *int32) {
	p.HTTPCode = val
}
func (p *HighlightLarkBaikeEntitiesResponse) SetHTTPResponse(val *common.HTTPResponse) {
	p.HTTPResponse = val
}

var fieldIDToName_HighlightLarkBaikeEntitiesResponse = map[int16]string{
	1:   "Phrases",
	201: "Code",
	202: "Message",
	203: "HTTPCode",
	204: "HTTPResponse",
}

func (p *HighlightLarkBaikeEntitiesResponse) IsSetCode() bool {
	return p.Code != nil
}

func (p *HighlightLarkBaikeEntitiesResponse) IsSetMessage() bool {
	return p.Message != nil
}

func (p *HighlightLarkBaikeEntitiesResponse) IsSetHTTPCode() bool {
	return p.HTTPCode != nil
}

func (p *HighlightLarkBaikeEntitiesResponse) IsSetHTTPResponse() bool {
	return p.HTTPResponse != nil
}

func (p *HighlightLarkBaikeEntitiesResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 201:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField201(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 202:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField202(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 203:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField203(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 204:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField204(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_HighlightLarkBaikeEntitiesResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *HighlightLarkBaikeEntitiesResponse) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*LarkPhrase, 0, size)
	values := make([]LarkPhrase, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Phrases = _field
	return nil
}
func (p *HighlightLarkBaikeEntitiesResponse) ReadField201(iprot thrift.TProtocol) error {

	var _field *common.ErrorCode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := common.ErrorCode(v)
		_field = &tmp
	}
	p.Code = _field
	return nil
}
func (p *HighlightLarkBaikeEntitiesResponse) ReadField202(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Message = _field
	return nil
}
func (p *HighlightLarkBaikeEntitiesResponse) ReadField203(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.HTTPCode = _field
	return nil
}
func (p *HighlightLarkBaikeEntitiesResponse) ReadField204(iprot thrift.TProtocol) error {
	_field := common.NewHTTPResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.HTTPResponse = _field
	return nil
}

func (p *HighlightLarkBaikeEntitiesResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("HighlightLarkBaikeEntitiesResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField201(oprot); err != nil {
			fieldId = 201
			goto WriteFieldError
		}
		if err = p.writeField202(oprot); err != nil {
			fieldId = 202
			goto WriteFieldError
		}
		if err = p.writeField203(oprot); err != nil {
			fieldId = 203
			goto WriteFieldError
		}
		if err = p.writeField204(oprot); err != nil {
			fieldId = 204
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *HighlightLarkBaikeEntitiesResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Phrases", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Phrases)); err != nil {
		return err
	}
	for _, v := range p.Phrases {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *HighlightLarkBaikeEntitiesResponse) writeField201(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err = oprot.WriteFieldBegin("Code", thrift.I32, 201); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Code)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 end error: ", p), err)
}
func (p *HighlightLarkBaikeEntitiesResponse) writeField202(oprot thrift.TProtocol) (err error) {
	if p.IsSetMessage() {
		if err = oprot.WriteFieldBegin("Message", thrift.STRING, 202); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Message); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 202 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 202 end error: ", p), err)
}
func (p *HighlightLarkBaikeEntitiesResponse) writeField203(oprot thrift.TProtocol) (err error) {
	if p.IsSetHTTPCode() {
		if err = oprot.WriteFieldBegin("HTTPCode", thrift.I32, 203); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.HTTPCode); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 203 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 203 end error: ", p), err)
}
func (p *HighlightLarkBaikeEntitiesResponse) writeField204(oprot thrift.TProtocol) (err error) {
	if p.IsSetHTTPResponse() {
		if err = oprot.WriteFieldBegin("HTTPResponse", thrift.STRUCT, 204); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.HTTPResponse.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 204 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 204 end error: ", p), err)
}

func (p *HighlightLarkBaikeEntitiesResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("HighlightLarkBaikeEntitiesResponse(%+v)", *p)

}

func (p *HighlightLarkBaikeEntitiesResponse) DeepEqual(ano *HighlightLarkBaikeEntitiesResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Phrases) {
		return false
	}
	if !p.Field201DeepEqual(ano.Code) {
		return false
	}
	if !p.Field202DeepEqual(ano.Message) {
		return false
	}
	if !p.Field203DeepEqual(ano.HTTPCode) {
		return false
	}
	if !p.Field204DeepEqual(ano.HTTPResponse) {
		return false
	}
	return true
}

func (p *HighlightLarkBaikeEntitiesResponse) Field1DeepEqual(src []*LarkPhrase) bool {

	if len(p.Phrases) != len(src) {
		return false
	}
	for i, v := range p.Phrases {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *HighlightLarkBaikeEntitiesResponse) Field201DeepEqual(src *common.ErrorCode) bool {

	if p.Code == src {
		return true
	} else if p.Code == nil || src == nil {
		return false
	}
	if *p.Code != *src {
		return false
	}
	return true
}
func (p *HighlightLarkBaikeEntitiesResponse) Field202DeepEqual(src *string) bool {

	if p.Message == src {
		return true
	} else if p.Message == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Message, *src) != 0 {
		return false
	}
	return true
}
func (p *HighlightLarkBaikeEntitiesResponse) Field203DeepEqual(src *int32) bool {

	if p.HTTPCode == src {
		return true
	} else if p.HTTPCode == nil || src == nil {
		return false
	}
	if *p.HTTPCode != *src {
		return false
	}
	return true
}
func (p *HighlightLarkBaikeEntitiesResponse) Field204DeepEqual(src *common.HTTPResponse) bool {

	if !p.HTTPResponse.DeepEqual(src) {
		return false
	}
	return true
}

type GetLarkBaikeEntityRequest struct {
	EntityID    string              `thrift:"EntityID,1,required" frugal:"1,required,string" json:"entity_id"`
	HTTPRequest *common.HTTPRequest `thrift:"HTTPRequest,201,optional" frugal:"201,optional,common.HTTPRequest" json:"HTTPRequest,omitempty"`
	Base        *base.Base          `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewGetLarkBaikeEntityRequest() *GetLarkBaikeEntityRequest {
	return &GetLarkBaikeEntityRequest{}
}

func (p *GetLarkBaikeEntityRequest) InitDefault() {
}

func (p *GetLarkBaikeEntityRequest) GetEntityID() (v string) {
	return p.EntityID
}

var GetLarkBaikeEntityRequest_HTTPRequest_DEFAULT *common.HTTPRequest

func (p *GetLarkBaikeEntityRequest) GetHTTPRequest() (v *common.HTTPRequest) {
	if !p.IsSetHTTPRequest() {
		return GetLarkBaikeEntityRequest_HTTPRequest_DEFAULT
	}
	return p.HTTPRequest
}

var GetLarkBaikeEntityRequest_Base_DEFAULT *base.Base

func (p *GetLarkBaikeEntityRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return GetLarkBaikeEntityRequest_Base_DEFAULT
	}
	return p.Base
}
func (p *GetLarkBaikeEntityRequest) SetEntityID(val string) {
	p.EntityID = val
}
func (p *GetLarkBaikeEntityRequest) SetHTTPRequest(val *common.HTTPRequest) {
	p.HTTPRequest = val
}
func (p *GetLarkBaikeEntityRequest) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_GetLarkBaikeEntityRequest = map[int16]string{
	1:   "EntityID",
	201: "HTTPRequest",
	255: "Base",
}

func (p *GetLarkBaikeEntityRequest) IsSetHTTPRequest() bool {
	return p.HTTPRequest != nil
}

func (p *GetLarkBaikeEntityRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *GetLarkBaikeEntityRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetEntityID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetEntityID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 201:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField201(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetEntityID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetLarkBaikeEntityRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GetLarkBaikeEntityRequest[fieldId]))
}

func (p *GetLarkBaikeEntityRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EntityID = _field
	return nil
}
func (p *GetLarkBaikeEntityRequest) ReadField201(iprot thrift.TProtocol) error {
	_field := common.NewHTTPRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.HTTPRequest = _field
	return nil
}
func (p *GetLarkBaikeEntityRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *GetLarkBaikeEntityRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetLarkBaikeEntityRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField201(oprot); err != nil {
			fieldId = 201
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetLarkBaikeEntityRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EntityID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EntityID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetLarkBaikeEntityRequest) writeField201(oprot thrift.TProtocol) (err error) {
	if p.IsSetHTTPRequest() {
		if err = oprot.WriteFieldBegin("HTTPRequest", thrift.STRUCT, 201); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.HTTPRequest.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 end error: ", p), err)
}
func (p *GetLarkBaikeEntityRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetLarkBaikeEntityRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetLarkBaikeEntityRequest(%+v)", *p)

}

func (p *GetLarkBaikeEntityRequest) DeepEqual(ano *GetLarkBaikeEntityRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.EntityID) {
		return false
	}
	if !p.Field201DeepEqual(ano.HTTPRequest) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *GetLarkBaikeEntityRequest) Field1DeepEqual(src string) bool {

	if strings.Compare(p.EntityID, src) != 0 {
		return false
	}
	return true
}
func (p *GetLarkBaikeEntityRequest) Field201DeepEqual(src *common.HTTPRequest) bool {

	if !p.HTTPRequest.DeepEqual(src) {
		return false
	}
	return true
}
func (p *GetLarkBaikeEntityRequest) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type GetLarkBaikeEntityResponse struct {
	Entity       *LarkEntity          `thrift:"Entity,1" frugal:"1,default,LarkEntity" json:"entity"`
	Code         *common.ErrorCode    `thrift:"Code,201,optional" frugal:"201,optional,ErrorCode" json:"code"`
	Message      *string              `thrift:"Message,202,optional" frugal:"202,optional,string" json:"message"`
	HTTPCode     *int32               `thrift:"HTTPCode,203,optional" frugal:"203,optional,i32" json:"HTTPCode,omitempty"`
	HTTPResponse *common.HTTPResponse `thrift:"HTTPResponse,204,optional" frugal:"204,optional,common.HTTPResponse" json:"HTTPResponse,omitempty"`
}

func NewGetLarkBaikeEntityResponse() *GetLarkBaikeEntityResponse {
	return &GetLarkBaikeEntityResponse{}
}

func (p *GetLarkBaikeEntityResponse) InitDefault() {
}

var GetLarkBaikeEntityResponse_Entity_DEFAULT *LarkEntity

func (p *GetLarkBaikeEntityResponse) GetEntity() (v *LarkEntity) {
	if !p.IsSetEntity() {
		return GetLarkBaikeEntityResponse_Entity_DEFAULT
	}
	return p.Entity
}

var GetLarkBaikeEntityResponse_Code_DEFAULT common.ErrorCode

func (p *GetLarkBaikeEntityResponse) GetCode() (v common.ErrorCode) {
	if !p.IsSetCode() {
		return GetLarkBaikeEntityResponse_Code_DEFAULT
	}
	return *p.Code
}

var GetLarkBaikeEntityResponse_Message_DEFAULT string

func (p *GetLarkBaikeEntityResponse) GetMessage() (v string) {
	if !p.IsSetMessage() {
		return GetLarkBaikeEntityResponse_Message_DEFAULT
	}
	return *p.Message
}

var GetLarkBaikeEntityResponse_HTTPCode_DEFAULT int32

func (p *GetLarkBaikeEntityResponse) GetHTTPCode() (v int32) {
	if !p.IsSetHTTPCode() {
		return GetLarkBaikeEntityResponse_HTTPCode_DEFAULT
	}
	return *p.HTTPCode
}

var GetLarkBaikeEntityResponse_HTTPResponse_DEFAULT *common.HTTPResponse

func (p *GetLarkBaikeEntityResponse) GetHTTPResponse() (v *common.HTTPResponse) {
	if !p.IsSetHTTPResponse() {
		return GetLarkBaikeEntityResponse_HTTPResponse_DEFAULT
	}
	return p.HTTPResponse
}
func (p *GetLarkBaikeEntityResponse) SetEntity(val *LarkEntity) {
	p.Entity = val
}
func (p *GetLarkBaikeEntityResponse) SetCode(val *common.ErrorCode) {
	p.Code = val
}
func (p *GetLarkBaikeEntityResponse) SetMessage(val *string) {
	p.Message = val
}
func (p *GetLarkBaikeEntityResponse) SetHTTPCode(val *int32) {
	p.HTTPCode = val
}
func (p *GetLarkBaikeEntityResponse) SetHTTPResponse(val *common.HTTPResponse) {
	p.HTTPResponse = val
}

var fieldIDToName_GetLarkBaikeEntityResponse = map[int16]string{
	1:   "Entity",
	201: "Code",
	202: "Message",
	203: "HTTPCode",
	204: "HTTPResponse",
}

func (p *GetLarkBaikeEntityResponse) IsSetEntity() bool {
	return p.Entity != nil
}

func (p *GetLarkBaikeEntityResponse) IsSetCode() bool {
	return p.Code != nil
}

func (p *GetLarkBaikeEntityResponse) IsSetMessage() bool {
	return p.Message != nil
}

func (p *GetLarkBaikeEntityResponse) IsSetHTTPCode() bool {
	return p.HTTPCode != nil
}

func (p *GetLarkBaikeEntityResponse) IsSetHTTPResponse() bool {
	return p.HTTPResponse != nil
}

func (p *GetLarkBaikeEntityResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 201:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField201(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 202:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField202(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 203:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField203(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 204:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField204(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetLarkBaikeEntityResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetLarkBaikeEntityResponse) ReadField1(iprot thrift.TProtocol) error {
	_field := NewLarkEntity()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Entity = _field
	return nil
}
func (p *GetLarkBaikeEntityResponse) ReadField201(iprot thrift.TProtocol) error {

	var _field *common.ErrorCode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := common.ErrorCode(v)
		_field = &tmp
	}
	p.Code = _field
	return nil
}
func (p *GetLarkBaikeEntityResponse) ReadField202(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Message = _field
	return nil
}
func (p *GetLarkBaikeEntityResponse) ReadField203(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.HTTPCode = _field
	return nil
}
func (p *GetLarkBaikeEntityResponse) ReadField204(iprot thrift.TProtocol) error {
	_field := common.NewHTTPResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.HTTPResponse = _field
	return nil
}

func (p *GetLarkBaikeEntityResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetLarkBaikeEntityResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField201(oprot); err != nil {
			fieldId = 201
			goto WriteFieldError
		}
		if err = p.writeField202(oprot); err != nil {
			fieldId = 202
			goto WriteFieldError
		}
		if err = p.writeField203(oprot); err != nil {
			fieldId = 203
			goto WriteFieldError
		}
		if err = p.writeField204(oprot); err != nil {
			fieldId = 204
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetLarkBaikeEntityResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Entity", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Entity.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetLarkBaikeEntityResponse) writeField201(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err = oprot.WriteFieldBegin("Code", thrift.I32, 201); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Code)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 end error: ", p), err)
}
func (p *GetLarkBaikeEntityResponse) writeField202(oprot thrift.TProtocol) (err error) {
	if p.IsSetMessage() {
		if err = oprot.WriteFieldBegin("Message", thrift.STRING, 202); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Message); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 202 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 202 end error: ", p), err)
}
func (p *GetLarkBaikeEntityResponse) writeField203(oprot thrift.TProtocol) (err error) {
	if p.IsSetHTTPCode() {
		if err = oprot.WriteFieldBegin("HTTPCode", thrift.I32, 203); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.HTTPCode); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 203 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 203 end error: ", p), err)
}
func (p *GetLarkBaikeEntityResponse) writeField204(oprot thrift.TProtocol) (err error) {
	if p.IsSetHTTPResponse() {
		if err = oprot.WriteFieldBegin("HTTPResponse", thrift.STRUCT, 204); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.HTTPResponse.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 204 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 204 end error: ", p), err)
}

func (p *GetLarkBaikeEntityResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetLarkBaikeEntityResponse(%+v)", *p)

}

func (p *GetLarkBaikeEntityResponse) DeepEqual(ano *GetLarkBaikeEntityResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Entity) {
		return false
	}
	if !p.Field201DeepEqual(ano.Code) {
		return false
	}
	if !p.Field202DeepEqual(ano.Message) {
		return false
	}
	if !p.Field203DeepEqual(ano.HTTPCode) {
		return false
	}
	if !p.Field204DeepEqual(ano.HTTPResponse) {
		return false
	}
	return true
}

func (p *GetLarkBaikeEntityResponse) Field1DeepEqual(src *LarkEntity) bool {

	if !p.Entity.DeepEqual(src) {
		return false
	}
	return true
}
func (p *GetLarkBaikeEntityResponse) Field201DeepEqual(src *common.ErrorCode) bool {

	if p.Code == src {
		return true
	} else if p.Code == nil || src == nil {
		return false
	}
	if *p.Code != *src {
		return false
	}
	return true
}
func (p *GetLarkBaikeEntityResponse) Field202DeepEqual(src *string) bool {

	if p.Message == src {
		return true
	} else if p.Message == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Message, *src) != 0 {
		return false
	}
	return true
}
func (p *GetLarkBaikeEntityResponse) Field203DeepEqual(src *int32) bool {

	if p.HTTPCode == src {
		return true
	} else if p.HTTPCode == nil || src == nil {
		return false
	}
	if *p.HTTPCode != *src {
		return false
	}
	return true
}
func (p *GetLarkBaikeEntityResponse) Field204DeepEqual(src *common.HTTPResponse) bool {

	if !p.HTTPResponse.DeepEqual(src) {
		return false
	}
	return true
}

type LarkUser struct {
	Name   *string         `thrift:"Name,1,optional" frugal:"1,optional,string" json:"name"`
	EnName *string         `thrift:"EnName,2,optional" frugal:"2,optional,string" json:"en_name"`
	Email  *string         `thrift:"Email,3,optional" frugal:"3,optional,string" json:"email"`
	Avatar *LarkAvatarInfo `thrift:"Avatar,4,optional" frugal:"4,optional,LarkAvatarInfo" json:"avatar"`
}

func NewLarkUser() *LarkUser {
	return &LarkUser{}
}

func (p *LarkUser) InitDefault() {
}

var LarkUser_Name_DEFAULT string

func (p *LarkUser) GetName() (v string) {
	if !p.IsSetName() {
		return LarkUser_Name_DEFAULT
	}
	return *p.Name
}

var LarkUser_EnName_DEFAULT string

func (p *LarkUser) GetEnName() (v string) {
	if !p.IsSetEnName() {
		return LarkUser_EnName_DEFAULT
	}
	return *p.EnName
}

var LarkUser_Email_DEFAULT string

func (p *LarkUser) GetEmail() (v string) {
	if !p.IsSetEmail() {
		return LarkUser_Email_DEFAULT
	}
	return *p.Email
}

var LarkUser_Avatar_DEFAULT *LarkAvatarInfo

func (p *LarkUser) GetAvatar() (v *LarkAvatarInfo) {
	if !p.IsSetAvatar() {
		return LarkUser_Avatar_DEFAULT
	}
	return p.Avatar
}
func (p *LarkUser) SetName(val *string) {
	p.Name = val
}
func (p *LarkUser) SetEnName(val *string) {
	p.EnName = val
}
func (p *LarkUser) SetEmail(val *string) {
	p.Email = val
}
func (p *LarkUser) SetAvatar(val *LarkAvatarInfo) {
	p.Avatar = val
}

var fieldIDToName_LarkUser = map[int16]string{
	1: "Name",
	2: "EnName",
	3: "Email",
	4: "Avatar",
}

func (p *LarkUser) IsSetName() bool {
	return p.Name != nil
}

func (p *LarkUser) IsSetEnName() bool {
	return p.EnName != nil
}

func (p *LarkUser) IsSetEmail() bool {
	return p.Email != nil
}

func (p *LarkUser) IsSetAvatar() bool {
	return p.Avatar != nil
}

func (p *LarkUser) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_LarkUser[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *LarkUser) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Name = _field
	return nil
}
func (p *LarkUser) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EnName = _field
	return nil
}
func (p *LarkUser) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Email = _field
	return nil
}
func (p *LarkUser) ReadField4(iprot thrift.TProtocol) error {
	_field := NewLarkAvatarInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Avatar = _field
	return nil
}

func (p *LarkUser) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("LarkUser"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *LarkUser) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetName() {
		if err = oprot.WriteFieldBegin("Name", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Name); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *LarkUser) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetEnName() {
		if err = oprot.WriteFieldBegin("EnName", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.EnName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *LarkUser) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetEmail() {
		if err = oprot.WriteFieldBegin("Email", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Email); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *LarkUser) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetAvatar() {
		if err = oprot.WriteFieldBegin("Avatar", thrift.STRUCT, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Avatar.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *LarkUser) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LarkUser(%+v)", *p)

}

func (p *LarkUser) DeepEqual(ano *LarkUser) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Name) {
		return false
	}
	if !p.Field2DeepEqual(ano.EnName) {
		return false
	}
	if !p.Field3DeepEqual(ano.Email) {
		return false
	}
	if !p.Field4DeepEqual(ano.Avatar) {
		return false
	}
	return true
}

func (p *LarkUser) Field1DeepEqual(src *string) bool {

	if p.Name == src {
		return true
	} else if p.Name == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Name, *src) != 0 {
		return false
	}
	return true
}
func (p *LarkUser) Field2DeepEqual(src *string) bool {

	if p.EnName == src {
		return true
	} else if p.EnName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.EnName, *src) != 0 {
		return false
	}
	return true
}
func (p *LarkUser) Field3DeepEqual(src *string) bool {

	if p.Email == src {
		return true
	} else if p.Email == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Email, *src) != 0 {
		return false
	}
	return true
}
func (p *LarkUser) Field4DeepEqual(src *LarkAvatarInfo) bool {

	if !p.Avatar.DeepEqual(src) {
		return false
	}
	return true
}

type LarkAvatarInfo struct {
	Avatar72     *string `thrift:"Avatar72,1,optional" frugal:"1,optional,string" json:"avatar_72"`
	Avatar240    *string `thrift:"Avatar240,2,optional" frugal:"2,optional,string" json:"avatar_240"`
	Avatar640    *string `thrift:"Avatar640,3,optional" frugal:"3,optional,string" json:"avatar_640"`
	AvatarOrigin *string `thrift:"AvatarOrigin,4,optional" frugal:"4,optional,string" json:"avatar_origin"`
}

func NewLarkAvatarInfo() *LarkAvatarInfo {
	return &LarkAvatarInfo{}
}

func (p *LarkAvatarInfo) InitDefault() {
}

var LarkAvatarInfo_Avatar72_DEFAULT string

func (p *LarkAvatarInfo) GetAvatar72() (v string) {
	if !p.IsSetAvatar72() {
		return LarkAvatarInfo_Avatar72_DEFAULT
	}
	return *p.Avatar72
}

var LarkAvatarInfo_Avatar240_DEFAULT string

func (p *LarkAvatarInfo) GetAvatar240() (v string) {
	if !p.IsSetAvatar240() {
		return LarkAvatarInfo_Avatar240_DEFAULT
	}
	return *p.Avatar240
}

var LarkAvatarInfo_Avatar640_DEFAULT string

func (p *LarkAvatarInfo) GetAvatar640() (v string) {
	if !p.IsSetAvatar640() {
		return LarkAvatarInfo_Avatar640_DEFAULT
	}
	return *p.Avatar640
}

var LarkAvatarInfo_AvatarOrigin_DEFAULT string

func (p *LarkAvatarInfo) GetAvatarOrigin() (v string) {
	if !p.IsSetAvatarOrigin() {
		return LarkAvatarInfo_AvatarOrigin_DEFAULT
	}
	return *p.AvatarOrigin
}
func (p *LarkAvatarInfo) SetAvatar72(val *string) {
	p.Avatar72 = val
}
func (p *LarkAvatarInfo) SetAvatar240(val *string) {
	p.Avatar240 = val
}
func (p *LarkAvatarInfo) SetAvatar640(val *string) {
	p.Avatar640 = val
}
func (p *LarkAvatarInfo) SetAvatarOrigin(val *string) {
	p.AvatarOrigin = val
}

var fieldIDToName_LarkAvatarInfo = map[int16]string{
	1: "Avatar72",
	2: "Avatar240",
	3: "Avatar640",
	4: "AvatarOrigin",
}

func (p *LarkAvatarInfo) IsSetAvatar72() bool {
	return p.Avatar72 != nil
}

func (p *LarkAvatarInfo) IsSetAvatar240() bool {
	return p.Avatar240 != nil
}

func (p *LarkAvatarInfo) IsSetAvatar640() bool {
	return p.Avatar640 != nil
}

func (p *LarkAvatarInfo) IsSetAvatarOrigin() bool {
	return p.AvatarOrigin != nil
}

func (p *LarkAvatarInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_LarkAvatarInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *LarkAvatarInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Avatar72 = _field
	return nil
}
func (p *LarkAvatarInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Avatar240 = _field
	return nil
}
func (p *LarkAvatarInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Avatar640 = _field
	return nil
}
func (p *LarkAvatarInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AvatarOrigin = _field
	return nil
}

func (p *LarkAvatarInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("LarkAvatarInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *LarkAvatarInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetAvatar72() {
		if err = oprot.WriteFieldBegin("Avatar72", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Avatar72); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *LarkAvatarInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetAvatar240() {
		if err = oprot.WriteFieldBegin("Avatar240", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Avatar240); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *LarkAvatarInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetAvatar640() {
		if err = oprot.WriteFieldBegin("Avatar640", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Avatar640); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *LarkAvatarInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetAvatarOrigin() {
		if err = oprot.WriteFieldBegin("AvatarOrigin", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.AvatarOrigin); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *LarkAvatarInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LarkAvatarInfo(%+v)", *p)

}

func (p *LarkAvatarInfo) DeepEqual(ano *LarkAvatarInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Avatar72) {
		return false
	}
	if !p.Field2DeepEqual(ano.Avatar240) {
		return false
	}
	if !p.Field3DeepEqual(ano.Avatar640) {
		return false
	}
	if !p.Field4DeepEqual(ano.AvatarOrigin) {
		return false
	}
	return true
}

func (p *LarkAvatarInfo) Field1DeepEqual(src *string) bool {

	if p.Avatar72 == src {
		return true
	} else if p.Avatar72 == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Avatar72, *src) != 0 {
		return false
	}
	return true
}
func (p *LarkAvatarInfo) Field2DeepEqual(src *string) bool {

	if p.Avatar240 == src {
		return true
	} else if p.Avatar240 == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Avatar240, *src) != 0 {
		return false
	}
	return true
}
func (p *LarkAvatarInfo) Field3DeepEqual(src *string) bool {

	if p.Avatar640 == src {
		return true
	} else if p.Avatar640 == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Avatar640, *src) != 0 {
		return false
	}
	return true
}
func (p *LarkAvatarInfo) Field4DeepEqual(src *string) bool {

	if p.AvatarOrigin == src {
		return true
	} else if p.AvatarOrigin == nil || src == nil {
		return false
	}
	if strings.Compare(*p.AvatarOrigin, *src) != 0 {
		return false
	}
	return true
}

type ListLarkUsersRequest struct {
	UserIDs     []string            `thrift:"UserIDs,1" frugal:"1,default,list<string>" json:"user_ids"`
	UserIDType  *string             `thrift:"UserIDType,2,optional" frugal:"2,optional,string" json:"user_id_type"`
	HTTPRequest *common.HTTPRequest `thrift:"HTTPRequest,201,optional" frugal:"201,optional,common.HTTPRequest" json:"HTTPRequest,omitempty"`
	Base        *base.Base          `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewListLarkUsersRequest() *ListLarkUsersRequest {
	return &ListLarkUsersRequest{}
}

func (p *ListLarkUsersRequest) InitDefault() {
}

func (p *ListLarkUsersRequest) GetUserIDs() (v []string) {
	return p.UserIDs
}

var ListLarkUsersRequest_UserIDType_DEFAULT string

func (p *ListLarkUsersRequest) GetUserIDType() (v string) {
	if !p.IsSetUserIDType() {
		return ListLarkUsersRequest_UserIDType_DEFAULT
	}
	return *p.UserIDType
}

var ListLarkUsersRequest_HTTPRequest_DEFAULT *common.HTTPRequest

func (p *ListLarkUsersRequest) GetHTTPRequest() (v *common.HTTPRequest) {
	if !p.IsSetHTTPRequest() {
		return ListLarkUsersRequest_HTTPRequest_DEFAULT
	}
	return p.HTTPRequest
}

var ListLarkUsersRequest_Base_DEFAULT *base.Base

func (p *ListLarkUsersRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return ListLarkUsersRequest_Base_DEFAULT
	}
	return p.Base
}
func (p *ListLarkUsersRequest) SetUserIDs(val []string) {
	p.UserIDs = val
}
func (p *ListLarkUsersRequest) SetUserIDType(val *string) {
	p.UserIDType = val
}
func (p *ListLarkUsersRequest) SetHTTPRequest(val *common.HTTPRequest) {
	p.HTTPRequest = val
}
func (p *ListLarkUsersRequest) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_ListLarkUsersRequest = map[int16]string{
	1:   "UserIDs",
	2:   "UserIDType",
	201: "HTTPRequest",
	255: "Base",
}

func (p *ListLarkUsersRequest) IsSetUserIDType() bool {
	return p.UserIDType != nil
}

func (p *ListLarkUsersRequest) IsSetHTTPRequest() bool {
	return p.HTTPRequest != nil
}

func (p *ListLarkUsersRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *ListLarkUsersRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 201:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField201(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListLarkUsersRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ListLarkUsersRequest) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.UserIDs = _field
	return nil
}
func (p *ListLarkUsersRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.UserIDType = _field
	return nil
}
func (p *ListLarkUsersRequest) ReadField201(iprot thrift.TProtocol) error {
	_field := common.NewHTTPRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.HTTPRequest = _field
	return nil
}
func (p *ListLarkUsersRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *ListLarkUsersRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ListLarkUsersRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField201(oprot); err != nil {
			fieldId = 201
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListLarkUsersRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UserIDs", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.UserIDs)); err != nil {
		return err
	}
	for _, v := range p.UserIDs {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ListLarkUsersRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetUserIDType() {
		if err = oprot.WriteFieldBegin("UserIDType", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.UserIDType); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ListLarkUsersRequest) writeField201(oprot thrift.TProtocol) (err error) {
	if p.IsSetHTTPRequest() {
		if err = oprot.WriteFieldBegin("HTTPRequest", thrift.STRUCT, 201); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.HTTPRequest.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 end error: ", p), err)
}
func (p *ListLarkUsersRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *ListLarkUsersRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListLarkUsersRequest(%+v)", *p)

}

func (p *ListLarkUsersRequest) DeepEqual(ano *ListLarkUsersRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.UserIDs) {
		return false
	}
	if !p.Field2DeepEqual(ano.UserIDType) {
		return false
	}
	if !p.Field201DeepEqual(ano.HTTPRequest) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *ListLarkUsersRequest) Field1DeepEqual(src []string) bool {

	if len(p.UserIDs) != len(src) {
		return false
	}
	for i, v := range p.UserIDs {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *ListLarkUsersRequest) Field2DeepEqual(src *string) bool {

	if p.UserIDType == src {
		return true
	} else if p.UserIDType == nil || src == nil {
		return false
	}
	if strings.Compare(*p.UserIDType, *src) != 0 {
		return false
	}
	return true
}
func (p *ListLarkUsersRequest) Field201DeepEqual(src *common.HTTPRequest) bool {

	if !p.HTTPRequest.DeepEqual(src) {
		return false
	}
	return true
}
func (p *ListLarkUsersRequest) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type ListLarkUsersResponse struct {
	Users        []*LarkUser          `thrift:"Users,1" frugal:"1,default,list<LarkUser>" json:"users"`
	Code         *common.ErrorCode    `thrift:"Code,201,optional" frugal:"201,optional,ErrorCode" json:"code"`
	Message      *string              `thrift:"Message,202,optional" frugal:"202,optional,string" json:"message"`
	HTTPCode     *int32               `thrift:"HTTPCode,203,optional" frugal:"203,optional,i32" json:"HTTPCode,omitempty"`
	HTTPResponse *common.HTTPResponse `thrift:"HTTPResponse,204,optional" frugal:"204,optional,common.HTTPResponse" json:"HTTPResponse,omitempty"`
}

func NewListLarkUsersResponse() *ListLarkUsersResponse {
	return &ListLarkUsersResponse{}
}

func (p *ListLarkUsersResponse) InitDefault() {
}

func (p *ListLarkUsersResponse) GetUsers() (v []*LarkUser) {
	return p.Users
}

var ListLarkUsersResponse_Code_DEFAULT common.ErrorCode

func (p *ListLarkUsersResponse) GetCode() (v common.ErrorCode) {
	if !p.IsSetCode() {
		return ListLarkUsersResponse_Code_DEFAULT
	}
	return *p.Code
}

var ListLarkUsersResponse_Message_DEFAULT string

func (p *ListLarkUsersResponse) GetMessage() (v string) {
	if !p.IsSetMessage() {
		return ListLarkUsersResponse_Message_DEFAULT
	}
	return *p.Message
}

var ListLarkUsersResponse_HTTPCode_DEFAULT int32

func (p *ListLarkUsersResponse) GetHTTPCode() (v int32) {
	if !p.IsSetHTTPCode() {
		return ListLarkUsersResponse_HTTPCode_DEFAULT
	}
	return *p.HTTPCode
}

var ListLarkUsersResponse_HTTPResponse_DEFAULT *common.HTTPResponse

func (p *ListLarkUsersResponse) GetHTTPResponse() (v *common.HTTPResponse) {
	if !p.IsSetHTTPResponse() {
		return ListLarkUsersResponse_HTTPResponse_DEFAULT
	}
	return p.HTTPResponse
}
func (p *ListLarkUsersResponse) SetUsers(val []*LarkUser) {
	p.Users = val
}
func (p *ListLarkUsersResponse) SetCode(val *common.ErrorCode) {
	p.Code = val
}
func (p *ListLarkUsersResponse) SetMessage(val *string) {
	p.Message = val
}
func (p *ListLarkUsersResponse) SetHTTPCode(val *int32) {
	p.HTTPCode = val
}
func (p *ListLarkUsersResponse) SetHTTPResponse(val *common.HTTPResponse) {
	p.HTTPResponse = val
}

var fieldIDToName_ListLarkUsersResponse = map[int16]string{
	1:   "Users",
	201: "Code",
	202: "Message",
	203: "HTTPCode",
	204: "HTTPResponse",
}

func (p *ListLarkUsersResponse) IsSetCode() bool {
	return p.Code != nil
}

func (p *ListLarkUsersResponse) IsSetMessage() bool {
	return p.Message != nil
}

func (p *ListLarkUsersResponse) IsSetHTTPCode() bool {
	return p.HTTPCode != nil
}

func (p *ListLarkUsersResponse) IsSetHTTPResponse() bool {
	return p.HTTPResponse != nil
}

func (p *ListLarkUsersResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 201:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField201(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 202:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField202(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 203:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField203(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 204:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField204(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListLarkUsersResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ListLarkUsersResponse) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*LarkUser, 0, size)
	values := make([]LarkUser, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Users = _field
	return nil
}
func (p *ListLarkUsersResponse) ReadField201(iprot thrift.TProtocol) error {

	var _field *common.ErrorCode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := common.ErrorCode(v)
		_field = &tmp
	}
	p.Code = _field
	return nil
}
func (p *ListLarkUsersResponse) ReadField202(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Message = _field
	return nil
}
func (p *ListLarkUsersResponse) ReadField203(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.HTTPCode = _field
	return nil
}
func (p *ListLarkUsersResponse) ReadField204(iprot thrift.TProtocol) error {
	_field := common.NewHTTPResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.HTTPResponse = _field
	return nil
}

func (p *ListLarkUsersResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ListLarkUsersResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField201(oprot); err != nil {
			fieldId = 201
			goto WriteFieldError
		}
		if err = p.writeField202(oprot); err != nil {
			fieldId = 202
			goto WriteFieldError
		}
		if err = p.writeField203(oprot); err != nil {
			fieldId = 203
			goto WriteFieldError
		}
		if err = p.writeField204(oprot); err != nil {
			fieldId = 204
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListLarkUsersResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Users", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Users)); err != nil {
		return err
	}
	for _, v := range p.Users {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ListLarkUsersResponse) writeField201(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err = oprot.WriteFieldBegin("Code", thrift.I32, 201); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Code)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 end error: ", p), err)
}
func (p *ListLarkUsersResponse) writeField202(oprot thrift.TProtocol) (err error) {
	if p.IsSetMessage() {
		if err = oprot.WriteFieldBegin("Message", thrift.STRING, 202); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Message); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 202 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 202 end error: ", p), err)
}
func (p *ListLarkUsersResponse) writeField203(oprot thrift.TProtocol) (err error) {
	if p.IsSetHTTPCode() {
		if err = oprot.WriteFieldBegin("HTTPCode", thrift.I32, 203); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.HTTPCode); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 203 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 203 end error: ", p), err)
}
func (p *ListLarkUsersResponse) writeField204(oprot thrift.TProtocol) (err error) {
	if p.IsSetHTTPResponse() {
		if err = oprot.WriteFieldBegin("HTTPResponse", thrift.STRUCT, 204); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.HTTPResponse.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 204 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 204 end error: ", p), err)
}

func (p *ListLarkUsersResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListLarkUsersResponse(%+v)", *p)

}

func (p *ListLarkUsersResponse) DeepEqual(ano *ListLarkUsersResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Users) {
		return false
	}
	if !p.Field201DeepEqual(ano.Code) {
		return false
	}
	if !p.Field202DeepEqual(ano.Message) {
		return false
	}
	if !p.Field203DeepEqual(ano.HTTPCode) {
		return false
	}
	if !p.Field204DeepEqual(ano.HTTPResponse) {
		return false
	}
	return true
}

func (p *ListLarkUsersResponse) Field1DeepEqual(src []*LarkUser) bool {

	if len(p.Users) != len(src) {
		return false
	}
	for i, v := range p.Users {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *ListLarkUsersResponse) Field201DeepEqual(src *common.ErrorCode) bool {

	if p.Code == src {
		return true
	} else if p.Code == nil || src == nil {
		return false
	}
	if *p.Code != *src {
		return false
	}
	return true
}
func (p *ListLarkUsersResponse) Field202DeepEqual(src *string) bool {

	if p.Message == src {
		return true
	} else if p.Message == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Message, *src) != 0 {
		return false
	}
	return true
}
func (p *ListLarkUsersResponse) Field203DeepEqual(src *int32) bool {

	if p.HTTPCode == src {
		return true
	} else if p.HTTPCode == nil || src == nil {
		return false
	}
	if *p.HTTPCode != *src {
		return false
	}
	return true
}
func (p *ListLarkUsersResponse) Field204DeepEqual(src *common.HTTPResponse) bool {

	if !p.HTTPResponse.DeepEqual(src) {
		return false
	}
	return true
}
