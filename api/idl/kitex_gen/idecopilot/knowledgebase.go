// Code generated by thriftgo (0.3.19). DO NOT EDIT.

package idecopilot

import (
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/base"
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/common"
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/knowledgebase"
	"database/sql"
	"database/sql/driver"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type SplitContentType int64

const (
	SplitContentType_Code SplitContentType = 0
	SplitContentType_Text SplitContentType = 1
)

func (p SplitContentType) String() string {
	switch p {
	case SplitContentType_Code:
		return "Code"
	case SplitContentType_Text:
		return "Text"
	}
	return "<UNSET>"
}

func SplitContentTypeFromString(s string) (SplitContentType, error) {
	switch s {
	case "Code":
		return SplitContentType_Code, nil
	case "Text":
		return SplitContentType_Text, nil
	}
	return SplitContentType(0), fmt.Errorf("not a valid SplitContentType string")
}

func SplitContentTypePtr(v SplitContentType) *SplitContentType { return &v }
func (p *SplitContentType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = SplitContentType(result.Int64)
	return
}

func (p *SplitContentType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type UpdateWorkspaceKnowledgebaseRequest struct {
	WorkspaceID string              `thrift:"WorkspaceID,1,required" frugal:"1,required,string" json:"WorkspaceID"`
	Projects    []*WorkspaceProject `thrift:"Projects,2,required" frugal:"2,required,list<WorkspaceProject>" json:"projects"`
	IdeID       *string             `thrift:"IdeID,3,optional" frugal:"3,optional,string" json:"ide_id"`
	HTTPRequest *common.HTTPRequest `thrift:"HTTPRequest,201,optional" frugal:"201,optional,common.HTTPRequest" json:"HTTPRequest,omitempty"`
	Base        *base.Base          `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewUpdateWorkspaceKnowledgebaseRequest() *UpdateWorkspaceKnowledgebaseRequest {
	return &UpdateWorkspaceKnowledgebaseRequest{}
}

func (p *UpdateWorkspaceKnowledgebaseRequest) InitDefault() {
}

func (p *UpdateWorkspaceKnowledgebaseRequest) GetWorkspaceID() (v string) {
	return p.WorkspaceID
}

func (p *UpdateWorkspaceKnowledgebaseRequest) GetProjects() (v []*WorkspaceProject) {
	return p.Projects
}

var UpdateWorkspaceKnowledgebaseRequest_IdeID_DEFAULT string

func (p *UpdateWorkspaceKnowledgebaseRequest) GetIdeID() (v string) {
	if !p.IsSetIdeID() {
		return UpdateWorkspaceKnowledgebaseRequest_IdeID_DEFAULT
	}
	return *p.IdeID
}

var UpdateWorkspaceKnowledgebaseRequest_HTTPRequest_DEFAULT *common.HTTPRequest

func (p *UpdateWorkspaceKnowledgebaseRequest) GetHTTPRequest() (v *common.HTTPRequest) {
	if !p.IsSetHTTPRequest() {
		return UpdateWorkspaceKnowledgebaseRequest_HTTPRequest_DEFAULT
	}
	return p.HTTPRequest
}

var UpdateWorkspaceKnowledgebaseRequest_Base_DEFAULT *base.Base

func (p *UpdateWorkspaceKnowledgebaseRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return UpdateWorkspaceKnowledgebaseRequest_Base_DEFAULT
	}
	return p.Base
}
func (p *UpdateWorkspaceKnowledgebaseRequest) SetWorkspaceID(val string) {
	p.WorkspaceID = val
}
func (p *UpdateWorkspaceKnowledgebaseRequest) SetProjects(val []*WorkspaceProject) {
	p.Projects = val
}
func (p *UpdateWorkspaceKnowledgebaseRequest) SetIdeID(val *string) {
	p.IdeID = val
}
func (p *UpdateWorkspaceKnowledgebaseRequest) SetHTTPRequest(val *common.HTTPRequest) {
	p.HTTPRequest = val
}
func (p *UpdateWorkspaceKnowledgebaseRequest) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_UpdateWorkspaceKnowledgebaseRequest = map[int16]string{
	1:   "WorkspaceID",
	2:   "Projects",
	3:   "IdeID",
	201: "HTTPRequest",
	255: "Base",
}

func (p *UpdateWorkspaceKnowledgebaseRequest) IsSetIdeID() bool {
	return p.IdeID != nil
}

func (p *UpdateWorkspaceKnowledgebaseRequest) IsSetHTTPRequest() bool {
	return p.HTTPRequest != nil
}

func (p *UpdateWorkspaceKnowledgebaseRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *UpdateWorkspaceKnowledgebaseRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetWorkspaceID bool = false
	var issetProjects bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetWorkspaceID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetProjects = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 201:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField201(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetWorkspaceID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetProjects {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpdateWorkspaceKnowledgebaseRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_UpdateWorkspaceKnowledgebaseRequest[fieldId]))
}

func (p *UpdateWorkspaceKnowledgebaseRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.WorkspaceID = _field
	return nil
}
func (p *UpdateWorkspaceKnowledgebaseRequest) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*WorkspaceProject, 0, size)
	values := make([]WorkspaceProject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Projects = _field
	return nil
}
func (p *UpdateWorkspaceKnowledgebaseRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.IdeID = _field
	return nil
}
func (p *UpdateWorkspaceKnowledgebaseRequest) ReadField201(iprot thrift.TProtocol) error {
	_field := common.NewHTTPRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.HTTPRequest = _field
	return nil
}
func (p *UpdateWorkspaceKnowledgebaseRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *UpdateWorkspaceKnowledgebaseRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateWorkspaceKnowledgebaseRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField201(oprot); err != nil {
			fieldId = 201
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpdateWorkspaceKnowledgebaseRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("WorkspaceID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.WorkspaceID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *UpdateWorkspaceKnowledgebaseRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Projects", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Projects)); err != nil {
		return err
	}
	for _, v := range p.Projects {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *UpdateWorkspaceKnowledgebaseRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetIdeID() {
		if err = oprot.WriteFieldBegin("IdeID", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.IdeID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *UpdateWorkspaceKnowledgebaseRequest) writeField201(oprot thrift.TProtocol) (err error) {
	if p.IsSetHTTPRequest() {
		if err = oprot.WriteFieldBegin("HTTPRequest", thrift.STRUCT, 201); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.HTTPRequest.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 end error: ", p), err)
}
func (p *UpdateWorkspaceKnowledgebaseRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *UpdateWorkspaceKnowledgebaseRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateWorkspaceKnowledgebaseRequest(%+v)", *p)

}

func (p *UpdateWorkspaceKnowledgebaseRequest) DeepEqual(ano *UpdateWorkspaceKnowledgebaseRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.WorkspaceID) {
		return false
	}
	if !p.Field2DeepEqual(ano.Projects) {
		return false
	}
	if !p.Field3DeepEqual(ano.IdeID) {
		return false
	}
	if !p.Field201DeepEqual(ano.HTTPRequest) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *UpdateWorkspaceKnowledgebaseRequest) Field1DeepEqual(src string) bool {

	if strings.Compare(p.WorkspaceID, src) != 0 {
		return false
	}
	return true
}
func (p *UpdateWorkspaceKnowledgebaseRequest) Field2DeepEqual(src []*WorkspaceProject) bool {

	if len(p.Projects) != len(src) {
		return false
	}
	for i, v := range p.Projects {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *UpdateWorkspaceKnowledgebaseRequest) Field3DeepEqual(src *string) bool {

	if p.IdeID == src {
		return true
	} else if p.IdeID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.IdeID, *src) != 0 {
		return false
	}
	return true
}
func (p *UpdateWorkspaceKnowledgebaseRequest) Field201DeepEqual(src *common.HTTPRequest) bool {

	if !p.HTTPRequest.DeepEqual(src) {
		return false
	}
	return true
}
func (p *UpdateWorkspaceKnowledgebaseRequest) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type UpdateWorkspaceKnowledgebaseResponse struct {
	Code         *common.ErrorCode    `thrift:"Code,201,optional" frugal:"201,optional,ErrorCode" json:"code"`
	Message      *string              `thrift:"Message,202,optional" frugal:"202,optional,string" json:"message"`
	HTTPCode     *int32               `thrift:"HTTPCode,203,optional" frugal:"203,optional,i32" json:"HTTPCode,omitempty"`
	HTTPResponse *common.HTTPResponse `thrift:"HTTPResponse,204,optional" frugal:"204,optional,common.HTTPResponse" json:"HTTPResponse,omitempty"`
}

func NewUpdateWorkspaceKnowledgebaseResponse() *UpdateWorkspaceKnowledgebaseResponse {
	return &UpdateWorkspaceKnowledgebaseResponse{}
}

func (p *UpdateWorkspaceKnowledgebaseResponse) InitDefault() {
}

var UpdateWorkspaceKnowledgebaseResponse_Code_DEFAULT common.ErrorCode

func (p *UpdateWorkspaceKnowledgebaseResponse) GetCode() (v common.ErrorCode) {
	if !p.IsSetCode() {
		return UpdateWorkspaceKnowledgebaseResponse_Code_DEFAULT
	}
	return *p.Code
}

var UpdateWorkspaceKnowledgebaseResponse_Message_DEFAULT string

func (p *UpdateWorkspaceKnowledgebaseResponse) GetMessage() (v string) {
	if !p.IsSetMessage() {
		return UpdateWorkspaceKnowledgebaseResponse_Message_DEFAULT
	}
	return *p.Message
}

var UpdateWorkspaceKnowledgebaseResponse_HTTPCode_DEFAULT int32

func (p *UpdateWorkspaceKnowledgebaseResponse) GetHTTPCode() (v int32) {
	if !p.IsSetHTTPCode() {
		return UpdateWorkspaceKnowledgebaseResponse_HTTPCode_DEFAULT
	}
	return *p.HTTPCode
}

var UpdateWorkspaceKnowledgebaseResponse_HTTPResponse_DEFAULT *common.HTTPResponse

func (p *UpdateWorkspaceKnowledgebaseResponse) GetHTTPResponse() (v *common.HTTPResponse) {
	if !p.IsSetHTTPResponse() {
		return UpdateWorkspaceKnowledgebaseResponse_HTTPResponse_DEFAULT
	}
	return p.HTTPResponse
}
func (p *UpdateWorkspaceKnowledgebaseResponse) SetCode(val *common.ErrorCode) {
	p.Code = val
}
func (p *UpdateWorkspaceKnowledgebaseResponse) SetMessage(val *string) {
	p.Message = val
}
func (p *UpdateWorkspaceKnowledgebaseResponse) SetHTTPCode(val *int32) {
	p.HTTPCode = val
}
func (p *UpdateWorkspaceKnowledgebaseResponse) SetHTTPResponse(val *common.HTTPResponse) {
	p.HTTPResponse = val
}

var fieldIDToName_UpdateWorkspaceKnowledgebaseResponse = map[int16]string{
	201: "Code",
	202: "Message",
	203: "HTTPCode",
	204: "HTTPResponse",
}

func (p *UpdateWorkspaceKnowledgebaseResponse) IsSetCode() bool {
	return p.Code != nil
}

func (p *UpdateWorkspaceKnowledgebaseResponse) IsSetMessage() bool {
	return p.Message != nil
}

func (p *UpdateWorkspaceKnowledgebaseResponse) IsSetHTTPCode() bool {
	return p.HTTPCode != nil
}

func (p *UpdateWorkspaceKnowledgebaseResponse) IsSetHTTPResponse() bool {
	return p.HTTPResponse != nil
}

func (p *UpdateWorkspaceKnowledgebaseResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 201:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField201(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 202:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField202(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 203:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField203(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 204:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField204(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpdateWorkspaceKnowledgebaseResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *UpdateWorkspaceKnowledgebaseResponse) ReadField201(iprot thrift.TProtocol) error {

	var _field *common.ErrorCode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := common.ErrorCode(v)
		_field = &tmp
	}
	p.Code = _field
	return nil
}
func (p *UpdateWorkspaceKnowledgebaseResponse) ReadField202(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Message = _field
	return nil
}
func (p *UpdateWorkspaceKnowledgebaseResponse) ReadField203(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.HTTPCode = _field
	return nil
}
func (p *UpdateWorkspaceKnowledgebaseResponse) ReadField204(iprot thrift.TProtocol) error {
	_field := common.NewHTTPResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.HTTPResponse = _field
	return nil
}

func (p *UpdateWorkspaceKnowledgebaseResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateWorkspaceKnowledgebaseResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField201(oprot); err != nil {
			fieldId = 201
			goto WriteFieldError
		}
		if err = p.writeField202(oprot); err != nil {
			fieldId = 202
			goto WriteFieldError
		}
		if err = p.writeField203(oprot); err != nil {
			fieldId = 203
			goto WriteFieldError
		}
		if err = p.writeField204(oprot); err != nil {
			fieldId = 204
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpdateWorkspaceKnowledgebaseResponse) writeField201(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err = oprot.WriteFieldBegin("Code", thrift.I32, 201); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Code)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 end error: ", p), err)
}
func (p *UpdateWorkspaceKnowledgebaseResponse) writeField202(oprot thrift.TProtocol) (err error) {
	if p.IsSetMessage() {
		if err = oprot.WriteFieldBegin("Message", thrift.STRING, 202); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Message); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 202 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 202 end error: ", p), err)
}
func (p *UpdateWorkspaceKnowledgebaseResponse) writeField203(oprot thrift.TProtocol) (err error) {
	if p.IsSetHTTPCode() {
		if err = oprot.WriteFieldBegin("HTTPCode", thrift.I32, 203); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.HTTPCode); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 203 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 203 end error: ", p), err)
}
func (p *UpdateWorkspaceKnowledgebaseResponse) writeField204(oprot thrift.TProtocol) (err error) {
	if p.IsSetHTTPResponse() {
		if err = oprot.WriteFieldBegin("HTTPResponse", thrift.STRUCT, 204); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.HTTPResponse.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 204 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 204 end error: ", p), err)
}

func (p *UpdateWorkspaceKnowledgebaseResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateWorkspaceKnowledgebaseResponse(%+v)", *p)

}

func (p *UpdateWorkspaceKnowledgebaseResponse) DeepEqual(ano *UpdateWorkspaceKnowledgebaseResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field201DeepEqual(ano.Code) {
		return false
	}
	if !p.Field202DeepEqual(ano.Message) {
		return false
	}
	if !p.Field203DeepEqual(ano.HTTPCode) {
		return false
	}
	if !p.Field204DeepEqual(ano.HTTPResponse) {
		return false
	}
	return true
}

func (p *UpdateWorkspaceKnowledgebaseResponse) Field201DeepEqual(src *common.ErrorCode) bool {

	if p.Code == src {
		return true
	} else if p.Code == nil || src == nil {
		return false
	}
	if *p.Code != *src {
		return false
	}
	return true
}
func (p *UpdateWorkspaceKnowledgebaseResponse) Field202DeepEqual(src *string) bool {

	if p.Message == src {
		return true
	} else if p.Message == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Message, *src) != 0 {
		return false
	}
	return true
}
func (p *UpdateWorkspaceKnowledgebaseResponse) Field203DeepEqual(src *int32) bool {

	if p.HTTPCode == src {
		return true
	} else if p.HTTPCode == nil || src == nil {
		return false
	}
	if *p.HTTPCode != *src {
		return false
	}
	return true
}
func (p *UpdateWorkspaceKnowledgebaseResponse) Field204DeepEqual(src *common.HTTPResponse) bool {

	if !p.HTTPResponse.DeepEqual(src) {
		return false
	}
	return true
}

type WorkspaceProject struct {
	ProjectID string     `thrift:"ProjectID,1,required" frugal:"1,required,string" json:"project_id"`
	Files     []*File    `thrift:"Files,2,optional" frugal:"2,optional,list<File>" json:"files"`
	Segments  []*Segment `thrift:"Segments,3,optional" frugal:"3,optional,list<Segment>" json:"segments"`
}

func NewWorkspaceProject() *WorkspaceProject {
	return &WorkspaceProject{}
}

func (p *WorkspaceProject) InitDefault() {
}

func (p *WorkspaceProject) GetProjectID() (v string) {
	return p.ProjectID
}

var WorkspaceProject_Files_DEFAULT []*File

func (p *WorkspaceProject) GetFiles() (v []*File) {
	if !p.IsSetFiles() {
		return WorkspaceProject_Files_DEFAULT
	}
	return p.Files
}

var WorkspaceProject_Segments_DEFAULT []*Segment

func (p *WorkspaceProject) GetSegments() (v []*Segment) {
	if !p.IsSetSegments() {
		return WorkspaceProject_Segments_DEFAULT
	}
	return p.Segments
}
func (p *WorkspaceProject) SetProjectID(val string) {
	p.ProjectID = val
}
func (p *WorkspaceProject) SetFiles(val []*File) {
	p.Files = val
}
func (p *WorkspaceProject) SetSegments(val []*Segment) {
	p.Segments = val
}

var fieldIDToName_WorkspaceProject = map[int16]string{
	1: "ProjectID",
	2: "Files",
	3: "Segments",
}

func (p *WorkspaceProject) IsSetFiles() bool {
	return p.Files != nil
}

func (p *WorkspaceProject) IsSetSegments() bool {
	return p.Segments != nil
}

func (p *WorkspaceProject) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetProjectID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetProjectID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetProjectID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_WorkspaceProject[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_WorkspaceProject[fieldId]))
}

func (p *WorkspaceProject) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ProjectID = _field
	return nil
}
func (p *WorkspaceProject) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*File, 0, size)
	values := make([]File, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Files = _field
	return nil
}
func (p *WorkspaceProject) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*Segment, 0, size)
	values := make([]Segment, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Segments = _field
	return nil
}

func (p *WorkspaceProject) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("WorkspaceProject"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *WorkspaceProject) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ProjectID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ProjectID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *WorkspaceProject) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetFiles() {
		if err = oprot.WriteFieldBegin("Files", thrift.LIST, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Files)); err != nil {
			return err
		}
		for _, v := range p.Files {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *WorkspaceProject) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetSegments() {
		if err = oprot.WriteFieldBegin("Segments", thrift.LIST, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Segments)); err != nil {
			return err
		}
		for _, v := range p.Segments {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *WorkspaceProject) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("WorkspaceProject(%+v)", *p)

}

func (p *WorkspaceProject) DeepEqual(ano *WorkspaceProject) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ProjectID) {
		return false
	}
	if !p.Field2DeepEqual(ano.Files) {
		return false
	}
	if !p.Field3DeepEqual(ano.Segments) {
		return false
	}
	return true
}

func (p *WorkspaceProject) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ProjectID, src) != 0 {
		return false
	}
	return true
}
func (p *WorkspaceProject) Field2DeepEqual(src []*File) bool {

	if len(p.Files) != len(src) {
		return false
	}
	for i, v := range p.Files {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *WorkspaceProject) Field3DeepEqual(src []*Segment) bool {

	if len(p.Segments) != len(src) {
		return false
	}
	for i, v := range p.Segments {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type File struct {
	FileID      string     `thrift:"FileID,1,required" frugal:"1,required,string" json:"file_id"`
	ContentHash *string    `thrift:"ContentHash,2,optional" frugal:"2,optional,string" json:"content_hash"`
	Content     *string    `thrift:"Content,3,optional" frugal:"3,optional,string" json:"content"`
	Segments    []*Segment `thrift:"Segments,4" frugal:"4,default,list<Segment>" json:"segments"`
	UniqueID    string     `thrift:"UniqueID,5" frugal:"5,default,string" json:"unique_id"`
}

func NewFile() *File {
	return &File{}
}

func (p *File) InitDefault() {
}

func (p *File) GetFileID() (v string) {
	return p.FileID
}

var File_ContentHash_DEFAULT string

func (p *File) GetContentHash() (v string) {
	if !p.IsSetContentHash() {
		return File_ContentHash_DEFAULT
	}
	return *p.ContentHash
}

var File_Content_DEFAULT string

func (p *File) GetContent() (v string) {
	if !p.IsSetContent() {
		return File_Content_DEFAULT
	}
	return *p.Content
}

func (p *File) GetSegments() (v []*Segment) {
	return p.Segments
}

func (p *File) GetUniqueID() (v string) {
	return p.UniqueID
}
func (p *File) SetFileID(val string) {
	p.FileID = val
}
func (p *File) SetContentHash(val *string) {
	p.ContentHash = val
}
func (p *File) SetContent(val *string) {
	p.Content = val
}
func (p *File) SetSegments(val []*Segment) {
	p.Segments = val
}
func (p *File) SetUniqueID(val string) {
	p.UniqueID = val
}

var fieldIDToName_File = map[int16]string{
	1: "FileID",
	2: "ContentHash",
	3: "Content",
	4: "Segments",
	5: "UniqueID",
}

func (p *File) IsSetContentHash() bool {
	return p.ContentHash != nil
}

func (p *File) IsSetContent() bool {
	return p.Content != nil
}

func (p *File) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetFileID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetFileID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetFileID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_File[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_File[fieldId]))
}

func (p *File) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FileID = _field
	return nil
}
func (p *File) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ContentHash = _field
	return nil
}
func (p *File) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Content = _field
	return nil
}
func (p *File) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*Segment, 0, size)
	values := make([]Segment, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Segments = _field
	return nil
}
func (p *File) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UniqueID = _field
	return nil
}

func (p *File) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("File"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *File) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("FileID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.FileID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *File) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetContentHash() {
		if err = oprot.WriteFieldBegin("ContentHash", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ContentHash); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *File) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetContent() {
		if err = oprot.WriteFieldBegin("Content", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Content); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *File) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Segments", thrift.LIST, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Segments)); err != nil {
		return err
	}
	for _, v := range p.Segments {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *File) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UniqueID", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UniqueID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *File) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("File(%+v)", *p)

}

func (p *File) DeepEqual(ano *File) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.FileID) {
		return false
	}
	if !p.Field2DeepEqual(ano.ContentHash) {
		return false
	}
	if !p.Field3DeepEqual(ano.Content) {
		return false
	}
	if !p.Field4DeepEqual(ano.Segments) {
		return false
	}
	if !p.Field5DeepEqual(ano.UniqueID) {
		return false
	}
	return true
}

func (p *File) Field1DeepEqual(src string) bool {

	if strings.Compare(p.FileID, src) != 0 {
		return false
	}
	return true
}
func (p *File) Field2DeepEqual(src *string) bool {

	if p.ContentHash == src {
		return true
	} else if p.ContentHash == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ContentHash, *src) != 0 {
		return false
	}
	return true
}
func (p *File) Field3DeepEqual(src *string) bool {

	if p.Content == src {
		return true
	} else if p.Content == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Content, *src) != 0 {
		return false
	}
	return true
}
func (p *File) Field4DeepEqual(src []*Segment) bool {

	if len(p.Segments) != len(src) {
		return false
	}
	for i, v := range p.Segments {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *File) Field5DeepEqual(src string) bool {

	if strings.Compare(p.UniqueID, src) != 0 {
		return false
	}
	return true
}

type GetKnowledgebaseUnindexedFilesRequest struct {
	WorkspaceID  string              `thrift:"WorkspaceID,1,required" frugal:"1,required,string" json:"WorkspaceID"`
	ProjectFiles []*WorkspaceProject `thrift:"ProjectFiles,2,required" frugal:"2,required,list<WorkspaceProject>" json:"project_files"`
	HTTPRequest  *common.HTTPRequest `thrift:"HTTPRequest,201,optional" frugal:"201,optional,common.HTTPRequest" json:"HTTPRequest,omitempty"`
	Base         *base.Base          `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewGetKnowledgebaseUnindexedFilesRequest() *GetKnowledgebaseUnindexedFilesRequest {
	return &GetKnowledgebaseUnindexedFilesRequest{}
}

func (p *GetKnowledgebaseUnindexedFilesRequest) InitDefault() {
}

func (p *GetKnowledgebaseUnindexedFilesRequest) GetWorkspaceID() (v string) {
	return p.WorkspaceID
}

func (p *GetKnowledgebaseUnindexedFilesRequest) GetProjectFiles() (v []*WorkspaceProject) {
	return p.ProjectFiles
}

var GetKnowledgebaseUnindexedFilesRequest_HTTPRequest_DEFAULT *common.HTTPRequest

func (p *GetKnowledgebaseUnindexedFilesRequest) GetHTTPRequest() (v *common.HTTPRequest) {
	if !p.IsSetHTTPRequest() {
		return GetKnowledgebaseUnindexedFilesRequest_HTTPRequest_DEFAULT
	}
	return p.HTTPRequest
}

var GetKnowledgebaseUnindexedFilesRequest_Base_DEFAULT *base.Base

func (p *GetKnowledgebaseUnindexedFilesRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return GetKnowledgebaseUnindexedFilesRequest_Base_DEFAULT
	}
	return p.Base
}
func (p *GetKnowledgebaseUnindexedFilesRequest) SetWorkspaceID(val string) {
	p.WorkspaceID = val
}
func (p *GetKnowledgebaseUnindexedFilesRequest) SetProjectFiles(val []*WorkspaceProject) {
	p.ProjectFiles = val
}
func (p *GetKnowledgebaseUnindexedFilesRequest) SetHTTPRequest(val *common.HTTPRequest) {
	p.HTTPRequest = val
}
func (p *GetKnowledgebaseUnindexedFilesRequest) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_GetKnowledgebaseUnindexedFilesRequest = map[int16]string{
	1:   "WorkspaceID",
	2:   "ProjectFiles",
	201: "HTTPRequest",
	255: "Base",
}

func (p *GetKnowledgebaseUnindexedFilesRequest) IsSetHTTPRequest() bool {
	return p.HTTPRequest != nil
}

func (p *GetKnowledgebaseUnindexedFilesRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *GetKnowledgebaseUnindexedFilesRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetWorkspaceID bool = false
	var issetProjectFiles bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetWorkspaceID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetProjectFiles = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 201:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField201(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetWorkspaceID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetProjectFiles {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetKnowledgebaseUnindexedFilesRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GetKnowledgebaseUnindexedFilesRequest[fieldId]))
}

func (p *GetKnowledgebaseUnindexedFilesRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.WorkspaceID = _field
	return nil
}
func (p *GetKnowledgebaseUnindexedFilesRequest) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*WorkspaceProject, 0, size)
	values := make([]WorkspaceProject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ProjectFiles = _field
	return nil
}
func (p *GetKnowledgebaseUnindexedFilesRequest) ReadField201(iprot thrift.TProtocol) error {
	_field := common.NewHTTPRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.HTTPRequest = _field
	return nil
}
func (p *GetKnowledgebaseUnindexedFilesRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *GetKnowledgebaseUnindexedFilesRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetKnowledgebaseUnindexedFilesRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField201(oprot); err != nil {
			fieldId = 201
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetKnowledgebaseUnindexedFilesRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("WorkspaceID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.WorkspaceID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetKnowledgebaseUnindexedFilesRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ProjectFiles", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ProjectFiles)); err != nil {
		return err
	}
	for _, v := range p.ProjectFiles {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *GetKnowledgebaseUnindexedFilesRequest) writeField201(oprot thrift.TProtocol) (err error) {
	if p.IsSetHTTPRequest() {
		if err = oprot.WriteFieldBegin("HTTPRequest", thrift.STRUCT, 201); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.HTTPRequest.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 end error: ", p), err)
}
func (p *GetKnowledgebaseUnindexedFilesRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetKnowledgebaseUnindexedFilesRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetKnowledgebaseUnindexedFilesRequest(%+v)", *p)

}

func (p *GetKnowledgebaseUnindexedFilesRequest) DeepEqual(ano *GetKnowledgebaseUnindexedFilesRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.WorkspaceID) {
		return false
	}
	if !p.Field2DeepEqual(ano.ProjectFiles) {
		return false
	}
	if !p.Field201DeepEqual(ano.HTTPRequest) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *GetKnowledgebaseUnindexedFilesRequest) Field1DeepEqual(src string) bool {

	if strings.Compare(p.WorkspaceID, src) != 0 {
		return false
	}
	return true
}
func (p *GetKnowledgebaseUnindexedFilesRequest) Field2DeepEqual(src []*WorkspaceProject) bool {

	if len(p.ProjectFiles) != len(src) {
		return false
	}
	for i, v := range p.ProjectFiles {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *GetKnowledgebaseUnindexedFilesRequest) Field201DeepEqual(src *common.HTTPRequest) bool {

	if !p.HTTPRequest.DeepEqual(src) {
		return false
	}
	return true
}
func (p *GetKnowledgebaseUnindexedFilesRequest) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type GetKnowledgebaseUnindexedFilesResponse struct {
	ProjectFiles []*WorkspaceProject  `thrift:"ProjectFiles,1,required" frugal:"1,required,list<WorkspaceProject>" json:"project_files"`
	Code         *common.ErrorCode    `thrift:"Code,201,optional" frugal:"201,optional,ErrorCode" json:"code"`
	Message      *string              `thrift:"Message,202,optional" frugal:"202,optional,string" json:"message"`
	HTTPCode     *int32               `thrift:"HTTPCode,203,optional" frugal:"203,optional,i32" json:"HTTPCode,omitempty"`
	HTTPResponse *common.HTTPResponse `thrift:"HTTPResponse,204,optional" frugal:"204,optional,common.HTTPResponse" json:"HTTPResponse,omitempty"`
}

func NewGetKnowledgebaseUnindexedFilesResponse() *GetKnowledgebaseUnindexedFilesResponse {
	return &GetKnowledgebaseUnindexedFilesResponse{}
}

func (p *GetKnowledgebaseUnindexedFilesResponse) InitDefault() {
}

func (p *GetKnowledgebaseUnindexedFilesResponse) GetProjectFiles() (v []*WorkspaceProject) {
	return p.ProjectFiles
}

var GetKnowledgebaseUnindexedFilesResponse_Code_DEFAULT common.ErrorCode

func (p *GetKnowledgebaseUnindexedFilesResponse) GetCode() (v common.ErrorCode) {
	if !p.IsSetCode() {
		return GetKnowledgebaseUnindexedFilesResponse_Code_DEFAULT
	}
	return *p.Code
}

var GetKnowledgebaseUnindexedFilesResponse_Message_DEFAULT string

func (p *GetKnowledgebaseUnindexedFilesResponse) GetMessage() (v string) {
	if !p.IsSetMessage() {
		return GetKnowledgebaseUnindexedFilesResponse_Message_DEFAULT
	}
	return *p.Message
}

var GetKnowledgebaseUnindexedFilesResponse_HTTPCode_DEFAULT int32

func (p *GetKnowledgebaseUnindexedFilesResponse) GetHTTPCode() (v int32) {
	if !p.IsSetHTTPCode() {
		return GetKnowledgebaseUnindexedFilesResponse_HTTPCode_DEFAULT
	}
	return *p.HTTPCode
}

var GetKnowledgebaseUnindexedFilesResponse_HTTPResponse_DEFAULT *common.HTTPResponse

func (p *GetKnowledgebaseUnindexedFilesResponse) GetHTTPResponse() (v *common.HTTPResponse) {
	if !p.IsSetHTTPResponse() {
		return GetKnowledgebaseUnindexedFilesResponse_HTTPResponse_DEFAULT
	}
	return p.HTTPResponse
}
func (p *GetKnowledgebaseUnindexedFilesResponse) SetProjectFiles(val []*WorkspaceProject) {
	p.ProjectFiles = val
}
func (p *GetKnowledgebaseUnindexedFilesResponse) SetCode(val *common.ErrorCode) {
	p.Code = val
}
func (p *GetKnowledgebaseUnindexedFilesResponse) SetMessage(val *string) {
	p.Message = val
}
func (p *GetKnowledgebaseUnindexedFilesResponse) SetHTTPCode(val *int32) {
	p.HTTPCode = val
}
func (p *GetKnowledgebaseUnindexedFilesResponse) SetHTTPResponse(val *common.HTTPResponse) {
	p.HTTPResponse = val
}

var fieldIDToName_GetKnowledgebaseUnindexedFilesResponse = map[int16]string{
	1:   "ProjectFiles",
	201: "Code",
	202: "Message",
	203: "HTTPCode",
	204: "HTTPResponse",
}

func (p *GetKnowledgebaseUnindexedFilesResponse) IsSetCode() bool {
	return p.Code != nil
}

func (p *GetKnowledgebaseUnindexedFilesResponse) IsSetMessage() bool {
	return p.Message != nil
}

func (p *GetKnowledgebaseUnindexedFilesResponse) IsSetHTTPCode() bool {
	return p.HTTPCode != nil
}

func (p *GetKnowledgebaseUnindexedFilesResponse) IsSetHTTPResponse() bool {
	return p.HTTPResponse != nil
}

func (p *GetKnowledgebaseUnindexedFilesResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetProjectFiles bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetProjectFiles = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 201:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField201(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 202:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField202(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 203:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField203(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 204:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField204(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetProjectFiles {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetKnowledgebaseUnindexedFilesResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GetKnowledgebaseUnindexedFilesResponse[fieldId]))
}

func (p *GetKnowledgebaseUnindexedFilesResponse) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*WorkspaceProject, 0, size)
	values := make([]WorkspaceProject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ProjectFiles = _field
	return nil
}
func (p *GetKnowledgebaseUnindexedFilesResponse) ReadField201(iprot thrift.TProtocol) error {

	var _field *common.ErrorCode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := common.ErrorCode(v)
		_field = &tmp
	}
	p.Code = _field
	return nil
}
func (p *GetKnowledgebaseUnindexedFilesResponse) ReadField202(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Message = _field
	return nil
}
func (p *GetKnowledgebaseUnindexedFilesResponse) ReadField203(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.HTTPCode = _field
	return nil
}
func (p *GetKnowledgebaseUnindexedFilesResponse) ReadField204(iprot thrift.TProtocol) error {
	_field := common.NewHTTPResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.HTTPResponse = _field
	return nil
}

func (p *GetKnowledgebaseUnindexedFilesResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetKnowledgebaseUnindexedFilesResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField201(oprot); err != nil {
			fieldId = 201
			goto WriteFieldError
		}
		if err = p.writeField202(oprot); err != nil {
			fieldId = 202
			goto WriteFieldError
		}
		if err = p.writeField203(oprot); err != nil {
			fieldId = 203
			goto WriteFieldError
		}
		if err = p.writeField204(oprot); err != nil {
			fieldId = 204
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetKnowledgebaseUnindexedFilesResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ProjectFiles", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ProjectFiles)); err != nil {
		return err
	}
	for _, v := range p.ProjectFiles {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetKnowledgebaseUnindexedFilesResponse) writeField201(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err = oprot.WriteFieldBegin("Code", thrift.I32, 201); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Code)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 end error: ", p), err)
}
func (p *GetKnowledgebaseUnindexedFilesResponse) writeField202(oprot thrift.TProtocol) (err error) {
	if p.IsSetMessage() {
		if err = oprot.WriteFieldBegin("Message", thrift.STRING, 202); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Message); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 202 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 202 end error: ", p), err)
}
func (p *GetKnowledgebaseUnindexedFilesResponse) writeField203(oprot thrift.TProtocol) (err error) {
	if p.IsSetHTTPCode() {
		if err = oprot.WriteFieldBegin("HTTPCode", thrift.I32, 203); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.HTTPCode); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 203 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 203 end error: ", p), err)
}
func (p *GetKnowledgebaseUnindexedFilesResponse) writeField204(oprot thrift.TProtocol) (err error) {
	if p.IsSetHTTPResponse() {
		if err = oprot.WriteFieldBegin("HTTPResponse", thrift.STRUCT, 204); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.HTTPResponse.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 204 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 204 end error: ", p), err)
}

func (p *GetKnowledgebaseUnindexedFilesResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetKnowledgebaseUnindexedFilesResponse(%+v)", *p)

}

func (p *GetKnowledgebaseUnindexedFilesResponse) DeepEqual(ano *GetKnowledgebaseUnindexedFilesResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ProjectFiles) {
		return false
	}
	if !p.Field201DeepEqual(ano.Code) {
		return false
	}
	if !p.Field202DeepEqual(ano.Message) {
		return false
	}
	if !p.Field203DeepEqual(ano.HTTPCode) {
		return false
	}
	if !p.Field204DeepEqual(ano.HTTPResponse) {
		return false
	}
	return true
}

func (p *GetKnowledgebaseUnindexedFilesResponse) Field1DeepEqual(src []*WorkspaceProject) bool {

	if len(p.ProjectFiles) != len(src) {
		return false
	}
	for i, v := range p.ProjectFiles {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *GetKnowledgebaseUnindexedFilesResponse) Field201DeepEqual(src *common.ErrorCode) bool {

	if p.Code == src {
		return true
	} else if p.Code == nil || src == nil {
		return false
	}
	if *p.Code != *src {
		return false
	}
	return true
}
func (p *GetKnowledgebaseUnindexedFilesResponse) Field202DeepEqual(src *string) bool {

	if p.Message == src {
		return true
	} else if p.Message == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Message, *src) != 0 {
		return false
	}
	return true
}
func (p *GetKnowledgebaseUnindexedFilesResponse) Field203DeepEqual(src *int32) bool {

	if p.HTTPCode == src {
		return true
	} else if p.HTTPCode == nil || src == nil {
		return false
	}
	if *p.HTTPCode != *src {
		return false
	}
	return true
}
func (p *GetKnowledgebaseUnindexedFilesResponse) Field204DeepEqual(src *common.HTTPResponse) bool {

	if !p.HTTPResponse.DeepEqual(src) {
		return false
	}
	return true
}

type RetrievalRequest struct {
	WorkspaceID string                 `thrift:"WorkspaceID,1,required" frugal:"1,required,string" json:"WorkspaceID"`
	ProjectIDs  []string               `thrift:"ProjectIDs,2,required" frugal:"2,required,list<string>" json:"project_ids"`
	Content     string                 `thrift:"Content,3" frugal:"3,default,string" json:"content"`
	TopN        int32                  `thrift:"TopN,4" frugal:"4,default,i32" json:"top_n"`
	Match       []*knowledgebase.Match `thrift:"Match,5" frugal:"5,default,list<knowledgebase.Match>" json:"match"`
	HTTPRequest *common.HTTPRequest    `thrift:"HTTPRequest,201,optional" frugal:"201,optional,common.HTTPRequest" json:"HTTPRequest,omitempty"`
	Base        *base.Base             `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewRetrievalRequest() *RetrievalRequest {
	return &RetrievalRequest{}
}

func (p *RetrievalRequest) InitDefault() {
}

func (p *RetrievalRequest) GetWorkspaceID() (v string) {
	return p.WorkspaceID
}

func (p *RetrievalRequest) GetProjectIDs() (v []string) {
	return p.ProjectIDs
}

func (p *RetrievalRequest) GetContent() (v string) {
	return p.Content
}

func (p *RetrievalRequest) GetTopN() (v int32) {
	return p.TopN
}

func (p *RetrievalRequest) GetMatch() (v []*knowledgebase.Match) {
	return p.Match
}

var RetrievalRequest_HTTPRequest_DEFAULT *common.HTTPRequest

func (p *RetrievalRequest) GetHTTPRequest() (v *common.HTTPRequest) {
	if !p.IsSetHTTPRequest() {
		return RetrievalRequest_HTTPRequest_DEFAULT
	}
	return p.HTTPRequest
}

var RetrievalRequest_Base_DEFAULT *base.Base

func (p *RetrievalRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return RetrievalRequest_Base_DEFAULT
	}
	return p.Base
}
func (p *RetrievalRequest) SetWorkspaceID(val string) {
	p.WorkspaceID = val
}
func (p *RetrievalRequest) SetProjectIDs(val []string) {
	p.ProjectIDs = val
}
func (p *RetrievalRequest) SetContent(val string) {
	p.Content = val
}
func (p *RetrievalRequest) SetTopN(val int32) {
	p.TopN = val
}
func (p *RetrievalRequest) SetMatch(val []*knowledgebase.Match) {
	p.Match = val
}
func (p *RetrievalRequest) SetHTTPRequest(val *common.HTTPRequest) {
	p.HTTPRequest = val
}
func (p *RetrievalRequest) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_RetrievalRequest = map[int16]string{
	1:   "WorkspaceID",
	2:   "ProjectIDs",
	3:   "Content",
	4:   "TopN",
	5:   "Match",
	201: "HTTPRequest",
	255: "Base",
}

func (p *RetrievalRequest) IsSetHTTPRequest() bool {
	return p.HTTPRequest != nil
}

func (p *RetrievalRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *RetrievalRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetWorkspaceID bool = false
	var issetProjectIDs bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetWorkspaceID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetProjectIDs = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 201:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField201(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetWorkspaceID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetProjectIDs {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RetrievalRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_RetrievalRequest[fieldId]))
}

func (p *RetrievalRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.WorkspaceID = _field
	return nil
}
func (p *RetrievalRequest) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ProjectIDs = _field
	return nil
}
func (p *RetrievalRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Content = _field
	return nil
}
func (p *RetrievalRequest) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TopN = _field
	return nil
}
func (p *RetrievalRequest) ReadField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*knowledgebase.Match, 0, size)
	values := make([]knowledgebase.Match, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Match = _field
	return nil
}
func (p *RetrievalRequest) ReadField201(iprot thrift.TProtocol) error {
	_field := common.NewHTTPRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.HTTPRequest = _field
	return nil
}
func (p *RetrievalRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *RetrievalRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("RetrievalRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField201(oprot); err != nil {
			fieldId = 201
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RetrievalRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("WorkspaceID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.WorkspaceID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *RetrievalRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ProjectIDs", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.ProjectIDs)); err != nil {
		return err
	}
	for _, v := range p.ProjectIDs {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *RetrievalRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Content", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Content); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *RetrievalRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TopN", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.TopN); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *RetrievalRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Match", thrift.LIST, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Match)); err != nil {
		return err
	}
	for _, v := range p.Match {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *RetrievalRequest) writeField201(oprot thrift.TProtocol) (err error) {
	if p.IsSetHTTPRequest() {
		if err = oprot.WriteFieldBegin("HTTPRequest", thrift.STRUCT, 201); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.HTTPRequest.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 end error: ", p), err)
}
func (p *RetrievalRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *RetrievalRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RetrievalRequest(%+v)", *p)

}

func (p *RetrievalRequest) DeepEqual(ano *RetrievalRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.WorkspaceID) {
		return false
	}
	if !p.Field2DeepEqual(ano.ProjectIDs) {
		return false
	}
	if !p.Field3DeepEqual(ano.Content) {
		return false
	}
	if !p.Field4DeepEqual(ano.TopN) {
		return false
	}
	if !p.Field5DeepEqual(ano.Match) {
		return false
	}
	if !p.Field201DeepEqual(ano.HTTPRequest) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *RetrievalRequest) Field1DeepEqual(src string) bool {

	if strings.Compare(p.WorkspaceID, src) != 0 {
		return false
	}
	return true
}
func (p *RetrievalRequest) Field2DeepEqual(src []string) bool {

	if len(p.ProjectIDs) != len(src) {
		return false
	}
	for i, v := range p.ProjectIDs {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *RetrievalRequest) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Content, src) != 0 {
		return false
	}
	return true
}
func (p *RetrievalRequest) Field4DeepEqual(src int32) bool {

	if p.TopN != src {
		return false
	}
	return true
}
func (p *RetrievalRequest) Field5DeepEqual(src []*knowledgebase.Match) bool {

	if len(p.Match) != len(src) {
		return false
	}
	for i, v := range p.Match {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *RetrievalRequest) Field201DeepEqual(src *common.HTTPRequest) bool {

	if !p.HTTPRequest.DeepEqual(src) {
		return false
	}
	return true
}
func (p *RetrievalRequest) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type RetrievalResponse struct {
	Segments     []*Segment           `thrift:"Segments,1,required" frugal:"1,required,list<Segment>" json:"segments"`
	Code         *common.ErrorCode    `thrift:"Code,201,optional" frugal:"201,optional,ErrorCode" json:"code"`
	Message      *string              `thrift:"Message,202,optional" frugal:"202,optional,string" json:"message"`
	HTTPCode     *int32               `thrift:"HTTPCode,203,optional" frugal:"203,optional,i32" json:"HTTPCode,omitempty"`
	HTTPResponse *common.HTTPResponse `thrift:"HTTPResponse,204,optional" frugal:"204,optional,common.HTTPResponse" json:"HTTPResponse,omitempty"`
}

func NewRetrievalResponse() *RetrievalResponse {
	return &RetrievalResponse{}
}

func (p *RetrievalResponse) InitDefault() {
}

func (p *RetrievalResponse) GetSegments() (v []*Segment) {
	return p.Segments
}

var RetrievalResponse_Code_DEFAULT common.ErrorCode

func (p *RetrievalResponse) GetCode() (v common.ErrorCode) {
	if !p.IsSetCode() {
		return RetrievalResponse_Code_DEFAULT
	}
	return *p.Code
}

var RetrievalResponse_Message_DEFAULT string

func (p *RetrievalResponse) GetMessage() (v string) {
	if !p.IsSetMessage() {
		return RetrievalResponse_Message_DEFAULT
	}
	return *p.Message
}

var RetrievalResponse_HTTPCode_DEFAULT int32

func (p *RetrievalResponse) GetHTTPCode() (v int32) {
	if !p.IsSetHTTPCode() {
		return RetrievalResponse_HTTPCode_DEFAULT
	}
	return *p.HTTPCode
}

var RetrievalResponse_HTTPResponse_DEFAULT *common.HTTPResponse

func (p *RetrievalResponse) GetHTTPResponse() (v *common.HTTPResponse) {
	if !p.IsSetHTTPResponse() {
		return RetrievalResponse_HTTPResponse_DEFAULT
	}
	return p.HTTPResponse
}
func (p *RetrievalResponse) SetSegments(val []*Segment) {
	p.Segments = val
}
func (p *RetrievalResponse) SetCode(val *common.ErrorCode) {
	p.Code = val
}
func (p *RetrievalResponse) SetMessage(val *string) {
	p.Message = val
}
func (p *RetrievalResponse) SetHTTPCode(val *int32) {
	p.HTTPCode = val
}
func (p *RetrievalResponse) SetHTTPResponse(val *common.HTTPResponse) {
	p.HTTPResponse = val
}

var fieldIDToName_RetrievalResponse = map[int16]string{
	1:   "Segments",
	201: "Code",
	202: "Message",
	203: "HTTPCode",
	204: "HTTPResponse",
}

func (p *RetrievalResponse) IsSetCode() bool {
	return p.Code != nil
}

func (p *RetrievalResponse) IsSetMessage() bool {
	return p.Message != nil
}

func (p *RetrievalResponse) IsSetHTTPCode() bool {
	return p.HTTPCode != nil
}

func (p *RetrievalResponse) IsSetHTTPResponse() bool {
	return p.HTTPResponse != nil
}

func (p *RetrievalResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSegments bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSegments = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 201:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField201(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 202:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField202(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 203:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField203(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 204:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField204(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSegments {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RetrievalResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_RetrievalResponse[fieldId]))
}

func (p *RetrievalResponse) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*Segment, 0, size)
	values := make([]Segment, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Segments = _field
	return nil
}
func (p *RetrievalResponse) ReadField201(iprot thrift.TProtocol) error {

	var _field *common.ErrorCode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := common.ErrorCode(v)
		_field = &tmp
	}
	p.Code = _field
	return nil
}
func (p *RetrievalResponse) ReadField202(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Message = _field
	return nil
}
func (p *RetrievalResponse) ReadField203(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.HTTPCode = _field
	return nil
}
func (p *RetrievalResponse) ReadField204(iprot thrift.TProtocol) error {
	_field := common.NewHTTPResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.HTTPResponse = _field
	return nil
}

func (p *RetrievalResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("RetrievalResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField201(oprot); err != nil {
			fieldId = 201
			goto WriteFieldError
		}
		if err = p.writeField202(oprot); err != nil {
			fieldId = 202
			goto WriteFieldError
		}
		if err = p.writeField203(oprot); err != nil {
			fieldId = 203
			goto WriteFieldError
		}
		if err = p.writeField204(oprot); err != nil {
			fieldId = 204
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RetrievalResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Segments", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Segments)); err != nil {
		return err
	}
	for _, v := range p.Segments {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *RetrievalResponse) writeField201(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err = oprot.WriteFieldBegin("Code", thrift.I32, 201); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Code)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 end error: ", p), err)
}
func (p *RetrievalResponse) writeField202(oprot thrift.TProtocol) (err error) {
	if p.IsSetMessage() {
		if err = oprot.WriteFieldBegin("Message", thrift.STRING, 202); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Message); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 202 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 202 end error: ", p), err)
}
func (p *RetrievalResponse) writeField203(oprot thrift.TProtocol) (err error) {
	if p.IsSetHTTPCode() {
		if err = oprot.WriteFieldBegin("HTTPCode", thrift.I32, 203); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.HTTPCode); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 203 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 203 end error: ", p), err)
}
func (p *RetrievalResponse) writeField204(oprot thrift.TProtocol) (err error) {
	if p.IsSetHTTPResponse() {
		if err = oprot.WriteFieldBegin("HTTPResponse", thrift.STRUCT, 204); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.HTTPResponse.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 204 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 204 end error: ", p), err)
}

func (p *RetrievalResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RetrievalResponse(%+v)", *p)

}

func (p *RetrievalResponse) DeepEqual(ano *RetrievalResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Segments) {
		return false
	}
	if !p.Field201DeepEqual(ano.Code) {
		return false
	}
	if !p.Field202DeepEqual(ano.Message) {
		return false
	}
	if !p.Field203DeepEqual(ano.HTTPCode) {
		return false
	}
	if !p.Field204DeepEqual(ano.HTTPResponse) {
		return false
	}
	return true
}

func (p *RetrievalResponse) Field1DeepEqual(src []*Segment) bool {

	if len(p.Segments) != len(src) {
		return false
	}
	for i, v := range p.Segments {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *RetrievalResponse) Field201DeepEqual(src *common.ErrorCode) bool {

	if p.Code == src {
		return true
	} else if p.Code == nil || src == nil {
		return false
	}
	if *p.Code != *src {
		return false
	}
	return true
}
func (p *RetrievalResponse) Field202DeepEqual(src *string) bool {

	if p.Message == src {
		return true
	} else if p.Message == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Message, *src) != 0 {
		return false
	}
	return true
}
func (p *RetrievalResponse) Field203DeepEqual(src *int32) bool {

	if p.HTTPCode == src {
		return true
	} else if p.HTTPCode == nil || src == nil {
		return false
	}
	if *p.HTTPCode != *src {
		return false
	}
	return true
}
func (p *RetrievalResponse) Field204DeepEqual(src *common.HTTPResponse) bool {

	if !p.HTTPResponse.DeepEqual(src) {
		return false
	}
	return true
}

type Segment struct {
	Type                 knowledgebase.SegmentType           `thrift:"Type,1" frugal:"1,default,string" json:"type"`
	FileID               string                              `thrift:"FileID,2" frugal:"2,default,string" json:"file_id"`
	ProjectID            string                              `thrift:"ProjectID,3" frugal:"3,default,string" json:"project_id"`
	CodeSegment          *knowledgebase.CodeSegment          `thrift:"CodeSegment,10,optional" frugal:"10,optional,knowledgebase.CodeSegment" json:"code"`
	TextSegment          *knowledgebase.TextSegment          `thrift:"TextSegment,11,optional" frugal:"11,optional,knowledgebase.TextSegment" json:"text"`
	FolderSegment        *knowledgebase.FolderSegment        `thrift:"FolderSegment,12,optional" frugal:"12,optional,knowledgebase.FolderSegment" json:"folder"`
	FileSegment          *knowledgebase.FileSegment          `thrift:"FileSegment,13,optional" frugal:"13,optional,knowledgebase.FileSegment" json:"file"`
	FileTopLevelSegment  *knowledgebase.FileTopLevelSegment  `thrift:"FileTopLevelSegment,14,optional" frugal:"14,optional,knowledgebase.FileTopLevelSegment" json:"file_top_level"`
	ClassSegment         *knowledgebase.ClassSegment         `thrift:"ClassSegment,15,optional" frugal:"15,optional,knowledgebase.ClassSegment" json:"class"`
	ClassTopLevelSegment *knowledgebase.ClassTopLevelSegment `thrift:"ClassTopLevelSegment,16,optional" frugal:"16,optional,knowledgebase.ClassTopLevelSegment" json:"class_top_level"`
	MethodSegment        *knowledgebase.MethodSegment        `thrift:"MethodSegment,17,optional" frugal:"17,optional,knowledgebase.MethodSegment" json:"method"`
	CodeChunkSegment     *knowledgebase.CodeChunkSegment     `thrift:"CodeChunkSegment,18,optional" frugal:"18,optional,knowledgebase.CodeChunkSegment" json:"code_chunk"`
	RelationSegment      *knowledgebase.RelationSegment      `thrift:"RelationSegment,19,optional" frugal:"19,optional,knowledgebase.RelationSegment" json:"relation"`
	Score                float64                             `thrift:"Score,101" frugal:"101,default,double" json:"score"`
	Tags                 []*knowledgebase.Tag                `thrift:"Tags,102" frugal:"102,default,list<knowledgebase.Tag>" json:"tags,omitempty"`
}

func NewSegment() *Segment {
	return &Segment{}
}

func (p *Segment) InitDefault() {
}

func (p *Segment) GetType() (v knowledgebase.SegmentType) {
	return p.Type
}

func (p *Segment) GetFileID() (v string) {
	return p.FileID
}

func (p *Segment) GetProjectID() (v string) {
	return p.ProjectID
}

var Segment_CodeSegment_DEFAULT *knowledgebase.CodeSegment

func (p *Segment) GetCodeSegment() (v *knowledgebase.CodeSegment) {
	if !p.IsSetCodeSegment() {
		return Segment_CodeSegment_DEFAULT
	}
	return p.CodeSegment
}

var Segment_TextSegment_DEFAULT *knowledgebase.TextSegment

func (p *Segment) GetTextSegment() (v *knowledgebase.TextSegment) {
	if !p.IsSetTextSegment() {
		return Segment_TextSegment_DEFAULT
	}
	return p.TextSegment
}

var Segment_FolderSegment_DEFAULT *knowledgebase.FolderSegment

func (p *Segment) GetFolderSegment() (v *knowledgebase.FolderSegment) {
	if !p.IsSetFolderSegment() {
		return Segment_FolderSegment_DEFAULT
	}
	return p.FolderSegment
}

var Segment_FileSegment_DEFAULT *knowledgebase.FileSegment

func (p *Segment) GetFileSegment() (v *knowledgebase.FileSegment) {
	if !p.IsSetFileSegment() {
		return Segment_FileSegment_DEFAULT
	}
	return p.FileSegment
}

var Segment_FileTopLevelSegment_DEFAULT *knowledgebase.FileTopLevelSegment

func (p *Segment) GetFileTopLevelSegment() (v *knowledgebase.FileTopLevelSegment) {
	if !p.IsSetFileTopLevelSegment() {
		return Segment_FileTopLevelSegment_DEFAULT
	}
	return p.FileTopLevelSegment
}

var Segment_ClassSegment_DEFAULT *knowledgebase.ClassSegment

func (p *Segment) GetClassSegment() (v *knowledgebase.ClassSegment) {
	if !p.IsSetClassSegment() {
		return Segment_ClassSegment_DEFAULT
	}
	return p.ClassSegment
}

var Segment_ClassTopLevelSegment_DEFAULT *knowledgebase.ClassTopLevelSegment

func (p *Segment) GetClassTopLevelSegment() (v *knowledgebase.ClassTopLevelSegment) {
	if !p.IsSetClassTopLevelSegment() {
		return Segment_ClassTopLevelSegment_DEFAULT
	}
	return p.ClassTopLevelSegment
}

var Segment_MethodSegment_DEFAULT *knowledgebase.MethodSegment

func (p *Segment) GetMethodSegment() (v *knowledgebase.MethodSegment) {
	if !p.IsSetMethodSegment() {
		return Segment_MethodSegment_DEFAULT
	}
	return p.MethodSegment
}

var Segment_CodeChunkSegment_DEFAULT *knowledgebase.CodeChunkSegment

func (p *Segment) GetCodeChunkSegment() (v *knowledgebase.CodeChunkSegment) {
	if !p.IsSetCodeChunkSegment() {
		return Segment_CodeChunkSegment_DEFAULT
	}
	return p.CodeChunkSegment
}

var Segment_RelationSegment_DEFAULT *knowledgebase.RelationSegment

func (p *Segment) GetRelationSegment() (v *knowledgebase.RelationSegment) {
	if !p.IsSetRelationSegment() {
		return Segment_RelationSegment_DEFAULT
	}
	return p.RelationSegment
}

func (p *Segment) GetScore() (v float64) {
	return p.Score
}

func (p *Segment) GetTags() (v []*knowledgebase.Tag) {
	return p.Tags
}
func (p *Segment) SetType(val knowledgebase.SegmentType) {
	p.Type = val
}
func (p *Segment) SetFileID(val string) {
	p.FileID = val
}
func (p *Segment) SetProjectID(val string) {
	p.ProjectID = val
}
func (p *Segment) SetCodeSegment(val *knowledgebase.CodeSegment) {
	p.CodeSegment = val
}
func (p *Segment) SetTextSegment(val *knowledgebase.TextSegment) {
	p.TextSegment = val
}
func (p *Segment) SetFolderSegment(val *knowledgebase.FolderSegment) {
	p.FolderSegment = val
}
func (p *Segment) SetFileSegment(val *knowledgebase.FileSegment) {
	p.FileSegment = val
}
func (p *Segment) SetFileTopLevelSegment(val *knowledgebase.FileTopLevelSegment) {
	p.FileTopLevelSegment = val
}
func (p *Segment) SetClassSegment(val *knowledgebase.ClassSegment) {
	p.ClassSegment = val
}
func (p *Segment) SetClassTopLevelSegment(val *knowledgebase.ClassTopLevelSegment) {
	p.ClassTopLevelSegment = val
}
func (p *Segment) SetMethodSegment(val *knowledgebase.MethodSegment) {
	p.MethodSegment = val
}
func (p *Segment) SetCodeChunkSegment(val *knowledgebase.CodeChunkSegment) {
	p.CodeChunkSegment = val
}
func (p *Segment) SetRelationSegment(val *knowledgebase.RelationSegment) {
	p.RelationSegment = val
}
func (p *Segment) SetScore(val float64) {
	p.Score = val
}
func (p *Segment) SetTags(val []*knowledgebase.Tag) {
	p.Tags = val
}

var fieldIDToName_Segment = map[int16]string{
	1:   "Type",
	2:   "FileID",
	3:   "ProjectID",
	10:  "CodeSegment",
	11:  "TextSegment",
	12:  "FolderSegment",
	13:  "FileSegment",
	14:  "FileTopLevelSegment",
	15:  "ClassSegment",
	16:  "ClassTopLevelSegment",
	17:  "MethodSegment",
	18:  "CodeChunkSegment",
	19:  "RelationSegment",
	101: "Score",
	102: "Tags",
}

func (p *Segment) IsSetCodeSegment() bool {
	return p.CodeSegment != nil
}

func (p *Segment) IsSetTextSegment() bool {
	return p.TextSegment != nil
}

func (p *Segment) IsSetFolderSegment() bool {
	return p.FolderSegment != nil
}

func (p *Segment) IsSetFileSegment() bool {
	return p.FileSegment != nil
}

func (p *Segment) IsSetFileTopLevelSegment() bool {
	return p.FileTopLevelSegment != nil
}

func (p *Segment) IsSetClassSegment() bool {
	return p.ClassSegment != nil
}

func (p *Segment) IsSetClassTopLevelSegment() bool {
	return p.ClassTopLevelSegment != nil
}

func (p *Segment) IsSetMethodSegment() bool {
	return p.MethodSegment != nil
}

func (p *Segment) IsSetCodeChunkSegment() bool {
	return p.CodeChunkSegment != nil
}

func (p *Segment) IsSetRelationSegment() bool {
	return p.RelationSegment != nil
}

func (p *Segment) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 17:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField17(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 18:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField18(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 19:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField19(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 101:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField101(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 102:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField102(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Segment[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *Segment) ReadField1(iprot thrift.TProtocol) error {

	var _field knowledgebase.SegmentType
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Type = _field
	return nil
}
func (p *Segment) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FileID = _field
	return nil
}
func (p *Segment) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ProjectID = _field
	return nil
}
func (p *Segment) ReadField10(iprot thrift.TProtocol) error {
	_field := knowledgebase.NewCodeSegment()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.CodeSegment = _field
	return nil
}
func (p *Segment) ReadField11(iprot thrift.TProtocol) error {
	_field := knowledgebase.NewTextSegment()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.TextSegment = _field
	return nil
}
func (p *Segment) ReadField12(iprot thrift.TProtocol) error {
	_field := knowledgebase.NewFolderSegment()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.FolderSegment = _field
	return nil
}
func (p *Segment) ReadField13(iprot thrift.TProtocol) error {
	_field := knowledgebase.NewFileSegment()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.FileSegment = _field
	return nil
}
func (p *Segment) ReadField14(iprot thrift.TProtocol) error {
	_field := knowledgebase.NewFileTopLevelSegment()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.FileTopLevelSegment = _field
	return nil
}
func (p *Segment) ReadField15(iprot thrift.TProtocol) error {
	_field := knowledgebase.NewClassSegment()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ClassSegment = _field
	return nil
}
func (p *Segment) ReadField16(iprot thrift.TProtocol) error {
	_field := knowledgebase.NewClassTopLevelSegment()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ClassTopLevelSegment = _field
	return nil
}
func (p *Segment) ReadField17(iprot thrift.TProtocol) error {
	_field := knowledgebase.NewMethodSegment()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.MethodSegment = _field
	return nil
}
func (p *Segment) ReadField18(iprot thrift.TProtocol) error {
	_field := knowledgebase.NewCodeChunkSegment()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.CodeChunkSegment = _field
	return nil
}
func (p *Segment) ReadField19(iprot thrift.TProtocol) error {
	_field := knowledgebase.NewRelationSegment()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.RelationSegment = _field
	return nil
}
func (p *Segment) ReadField101(iprot thrift.TProtocol) error {

	var _field float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Score = _field
	return nil
}
func (p *Segment) ReadField102(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*knowledgebase.Tag, 0, size)
	values := make([]knowledgebase.Tag, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Tags = _field
	return nil
}

func (p *Segment) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("Segment"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
		if err = p.writeField17(oprot); err != nil {
			fieldId = 17
			goto WriteFieldError
		}
		if err = p.writeField18(oprot); err != nil {
			fieldId = 18
			goto WriteFieldError
		}
		if err = p.writeField19(oprot); err != nil {
			fieldId = 19
			goto WriteFieldError
		}
		if err = p.writeField101(oprot); err != nil {
			fieldId = 101
			goto WriteFieldError
		}
		if err = p.writeField102(oprot); err != nil {
			fieldId = 102
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Segment) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Type", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Type); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *Segment) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("FileID", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.FileID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *Segment) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ProjectID", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ProjectID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *Segment) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetCodeSegment() {
		if err = oprot.WriteFieldBegin("CodeSegment", thrift.STRUCT, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.CodeSegment.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *Segment) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetTextSegment() {
		if err = oprot.WriteFieldBegin("TextSegment", thrift.STRUCT, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.TextSegment.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *Segment) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetFolderSegment() {
		if err = oprot.WriteFieldBegin("FolderSegment", thrift.STRUCT, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.FolderSegment.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}
func (p *Segment) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetFileSegment() {
		if err = oprot.WriteFieldBegin("FileSegment", thrift.STRUCT, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.FileSegment.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}
func (p *Segment) writeField14(oprot thrift.TProtocol) (err error) {
	if p.IsSetFileTopLevelSegment() {
		if err = oprot.WriteFieldBegin("FileTopLevelSegment", thrift.STRUCT, 14); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.FileTopLevelSegment.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}
func (p *Segment) writeField15(oprot thrift.TProtocol) (err error) {
	if p.IsSetClassSegment() {
		if err = oprot.WriteFieldBegin("ClassSegment", thrift.STRUCT, 15); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.ClassSegment.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}
func (p *Segment) writeField16(oprot thrift.TProtocol) (err error) {
	if p.IsSetClassTopLevelSegment() {
		if err = oprot.WriteFieldBegin("ClassTopLevelSegment", thrift.STRUCT, 16); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.ClassTopLevelSegment.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}
func (p *Segment) writeField17(oprot thrift.TProtocol) (err error) {
	if p.IsSetMethodSegment() {
		if err = oprot.WriteFieldBegin("MethodSegment", thrift.STRUCT, 17); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.MethodSegment.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 end error: ", p), err)
}
func (p *Segment) writeField18(oprot thrift.TProtocol) (err error) {
	if p.IsSetCodeChunkSegment() {
		if err = oprot.WriteFieldBegin("CodeChunkSegment", thrift.STRUCT, 18); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.CodeChunkSegment.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 end error: ", p), err)
}
func (p *Segment) writeField19(oprot thrift.TProtocol) (err error) {
	if p.IsSetRelationSegment() {
		if err = oprot.WriteFieldBegin("RelationSegment", thrift.STRUCT, 19); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.RelationSegment.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 end error: ", p), err)
}
func (p *Segment) writeField101(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Score", thrift.DOUBLE, 101); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteDouble(p.Score); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 101 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 101 end error: ", p), err)
}
func (p *Segment) writeField102(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Tags", thrift.LIST, 102); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Tags)); err != nil {
		return err
	}
	for _, v := range p.Tags {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 102 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 102 end error: ", p), err)
}

func (p *Segment) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Segment(%+v)", *p)

}

func (p *Segment) DeepEqual(ano *Segment) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Type) {
		return false
	}
	if !p.Field2DeepEqual(ano.FileID) {
		return false
	}
	if !p.Field3DeepEqual(ano.ProjectID) {
		return false
	}
	if !p.Field10DeepEqual(ano.CodeSegment) {
		return false
	}
	if !p.Field11DeepEqual(ano.TextSegment) {
		return false
	}
	if !p.Field12DeepEqual(ano.FolderSegment) {
		return false
	}
	if !p.Field13DeepEqual(ano.FileSegment) {
		return false
	}
	if !p.Field14DeepEqual(ano.FileTopLevelSegment) {
		return false
	}
	if !p.Field15DeepEqual(ano.ClassSegment) {
		return false
	}
	if !p.Field16DeepEqual(ano.ClassTopLevelSegment) {
		return false
	}
	if !p.Field17DeepEqual(ano.MethodSegment) {
		return false
	}
	if !p.Field18DeepEqual(ano.CodeChunkSegment) {
		return false
	}
	if !p.Field19DeepEqual(ano.RelationSegment) {
		return false
	}
	if !p.Field101DeepEqual(ano.Score) {
		return false
	}
	if !p.Field102DeepEqual(ano.Tags) {
		return false
	}
	return true
}

func (p *Segment) Field1DeepEqual(src knowledgebase.SegmentType) bool {

	if strings.Compare(p.Type, src) != 0 {
		return false
	}
	return true
}
func (p *Segment) Field2DeepEqual(src string) bool {

	if strings.Compare(p.FileID, src) != 0 {
		return false
	}
	return true
}
func (p *Segment) Field3DeepEqual(src string) bool {

	if strings.Compare(p.ProjectID, src) != 0 {
		return false
	}
	return true
}
func (p *Segment) Field10DeepEqual(src *knowledgebase.CodeSegment) bool {

	if !p.CodeSegment.DeepEqual(src) {
		return false
	}
	return true
}
func (p *Segment) Field11DeepEqual(src *knowledgebase.TextSegment) bool {

	if !p.TextSegment.DeepEqual(src) {
		return false
	}
	return true
}
func (p *Segment) Field12DeepEqual(src *knowledgebase.FolderSegment) bool {

	if !p.FolderSegment.DeepEqual(src) {
		return false
	}
	return true
}
func (p *Segment) Field13DeepEqual(src *knowledgebase.FileSegment) bool {

	if !p.FileSegment.DeepEqual(src) {
		return false
	}
	return true
}
func (p *Segment) Field14DeepEqual(src *knowledgebase.FileTopLevelSegment) bool {

	if !p.FileTopLevelSegment.DeepEqual(src) {
		return false
	}
	return true
}
func (p *Segment) Field15DeepEqual(src *knowledgebase.ClassSegment) bool {

	if !p.ClassSegment.DeepEqual(src) {
		return false
	}
	return true
}
func (p *Segment) Field16DeepEqual(src *knowledgebase.ClassTopLevelSegment) bool {

	if !p.ClassTopLevelSegment.DeepEqual(src) {
		return false
	}
	return true
}
func (p *Segment) Field17DeepEqual(src *knowledgebase.MethodSegment) bool {

	if !p.MethodSegment.DeepEqual(src) {
		return false
	}
	return true
}
func (p *Segment) Field18DeepEqual(src *knowledgebase.CodeChunkSegment) bool {

	if !p.CodeChunkSegment.DeepEqual(src) {
		return false
	}
	return true
}
func (p *Segment) Field19DeepEqual(src *knowledgebase.RelationSegment) bool {

	if !p.RelationSegment.DeepEqual(src) {
		return false
	}
	return true
}
func (p *Segment) Field101DeepEqual(src float64) bool {

	if p.Score != src {
		return false
	}
	return true
}
func (p *Segment) Field102DeepEqual(src []*knowledgebase.Tag) bool {

	if len(p.Tags) != len(src) {
		return false
	}
	for i, v := range p.Tags {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type WriteKnowledgebaseFilesRequest struct {
	WorkspaceID  string              `thrift:"WorkspaceID,1" frugal:"1,default,string" json:"WorkspaceID"`
	ProjectFiles []*WorkspaceProject `thrift:"ProjectFiles,2" frugal:"2,default,list<WorkspaceProject>" json:"project_files"`
	HTTPRequest  *common.HTTPRequest `thrift:"HTTPRequest,201,optional" frugal:"201,optional,common.HTTPRequest" json:"HTTPRequest,omitempty"`
	Base         *base.Base          `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewWriteKnowledgebaseFilesRequest() *WriteKnowledgebaseFilesRequest {
	return &WriteKnowledgebaseFilesRequest{}
}

func (p *WriteKnowledgebaseFilesRequest) InitDefault() {
}

func (p *WriteKnowledgebaseFilesRequest) GetWorkspaceID() (v string) {
	return p.WorkspaceID
}

func (p *WriteKnowledgebaseFilesRequest) GetProjectFiles() (v []*WorkspaceProject) {
	return p.ProjectFiles
}

var WriteKnowledgebaseFilesRequest_HTTPRequest_DEFAULT *common.HTTPRequest

func (p *WriteKnowledgebaseFilesRequest) GetHTTPRequest() (v *common.HTTPRequest) {
	if !p.IsSetHTTPRequest() {
		return WriteKnowledgebaseFilesRequest_HTTPRequest_DEFAULT
	}
	return p.HTTPRequest
}

var WriteKnowledgebaseFilesRequest_Base_DEFAULT *base.Base

func (p *WriteKnowledgebaseFilesRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return WriteKnowledgebaseFilesRequest_Base_DEFAULT
	}
	return p.Base
}
func (p *WriteKnowledgebaseFilesRequest) SetWorkspaceID(val string) {
	p.WorkspaceID = val
}
func (p *WriteKnowledgebaseFilesRequest) SetProjectFiles(val []*WorkspaceProject) {
	p.ProjectFiles = val
}
func (p *WriteKnowledgebaseFilesRequest) SetHTTPRequest(val *common.HTTPRequest) {
	p.HTTPRequest = val
}
func (p *WriteKnowledgebaseFilesRequest) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_WriteKnowledgebaseFilesRequest = map[int16]string{
	1:   "WorkspaceID",
	2:   "ProjectFiles",
	201: "HTTPRequest",
	255: "Base",
}

func (p *WriteKnowledgebaseFilesRequest) IsSetHTTPRequest() bool {
	return p.HTTPRequest != nil
}

func (p *WriteKnowledgebaseFilesRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *WriteKnowledgebaseFilesRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 201:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField201(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_WriteKnowledgebaseFilesRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *WriteKnowledgebaseFilesRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.WorkspaceID = _field
	return nil
}
func (p *WriteKnowledgebaseFilesRequest) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*WorkspaceProject, 0, size)
	values := make([]WorkspaceProject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ProjectFiles = _field
	return nil
}
func (p *WriteKnowledgebaseFilesRequest) ReadField201(iprot thrift.TProtocol) error {
	_field := common.NewHTTPRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.HTTPRequest = _field
	return nil
}
func (p *WriteKnowledgebaseFilesRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *WriteKnowledgebaseFilesRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("WriteKnowledgebaseFilesRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField201(oprot); err != nil {
			fieldId = 201
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *WriteKnowledgebaseFilesRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("WorkspaceID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.WorkspaceID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *WriteKnowledgebaseFilesRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ProjectFiles", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ProjectFiles)); err != nil {
		return err
	}
	for _, v := range p.ProjectFiles {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *WriteKnowledgebaseFilesRequest) writeField201(oprot thrift.TProtocol) (err error) {
	if p.IsSetHTTPRequest() {
		if err = oprot.WriteFieldBegin("HTTPRequest", thrift.STRUCT, 201); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.HTTPRequest.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 end error: ", p), err)
}
func (p *WriteKnowledgebaseFilesRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *WriteKnowledgebaseFilesRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("WriteKnowledgebaseFilesRequest(%+v)", *p)

}

func (p *WriteKnowledgebaseFilesRequest) DeepEqual(ano *WriteKnowledgebaseFilesRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.WorkspaceID) {
		return false
	}
	if !p.Field2DeepEqual(ano.ProjectFiles) {
		return false
	}
	if !p.Field201DeepEqual(ano.HTTPRequest) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *WriteKnowledgebaseFilesRequest) Field1DeepEqual(src string) bool {

	if strings.Compare(p.WorkspaceID, src) != 0 {
		return false
	}
	return true
}
func (p *WriteKnowledgebaseFilesRequest) Field2DeepEqual(src []*WorkspaceProject) bool {

	if len(p.ProjectFiles) != len(src) {
		return false
	}
	for i, v := range p.ProjectFiles {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *WriteKnowledgebaseFilesRequest) Field201DeepEqual(src *common.HTTPRequest) bool {

	if !p.HTTPRequest.DeepEqual(src) {
		return false
	}
	return true
}
func (p *WriteKnowledgebaseFilesRequest) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type WriteKnowledgebaseFilesResponse struct {
	ProjectFiles []*WorkspaceProject  `thrift:"ProjectFiles,1" frugal:"1,default,list<WorkspaceProject>" json:"project_files"`
	Code         *common.ErrorCode    `thrift:"Code,201,optional" frugal:"201,optional,ErrorCode" json:"code"`
	Message      *string              `thrift:"Message,202,optional" frugal:"202,optional,string" json:"message"`
	HTTPCode     *int32               `thrift:"HTTPCode,203,optional" frugal:"203,optional,i32" json:"HTTPCode,omitempty"`
	HTTPResponse *common.HTTPResponse `thrift:"HTTPResponse,204,optional" frugal:"204,optional,common.HTTPResponse" json:"HTTPResponse,omitempty"`
}

func NewWriteKnowledgebaseFilesResponse() *WriteKnowledgebaseFilesResponse {
	return &WriteKnowledgebaseFilesResponse{}
}

func (p *WriteKnowledgebaseFilesResponse) InitDefault() {
}

func (p *WriteKnowledgebaseFilesResponse) GetProjectFiles() (v []*WorkspaceProject) {
	return p.ProjectFiles
}

var WriteKnowledgebaseFilesResponse_Code_DEFAULT common.ErrorCode

func (p *WriteKnowledgebaseFilesResponse) GetCode() (v common.ErrorCode) {
	if !p.IsSetCode() {
		return WriteKnowledgebaseFilesResponse_Code_DEFAULT
	}
	return *p.Code
}

var WriteKnowledgebaseFilesResponse_Message_DEFAULT string

func (p *WriteKnowledgebaseFilesResponse) GetMessage() (v string) {
	if !p.IsSetMessage() {
		return WriteKnowledgebaseFilesResponse_Message_DEFAULT
	}
	return *p.Message
}

var WriteKnowledgebaseFilesResponse_HTTPCode_DEFAULT int32

func (p *WriteKnowledgebaseFilesResponse) GetHTTPCode() (v int32) {
	if !p.IsSetHTTPCode() {
		return WriteKnowledgebaseFilesResponse_HTTPCode_DEFAULT
	}
	return *p.HTTPCode
}

var WriteKnowledgebaseFilesResponse_HTTPResponse_DEFAULT *common.HTTPResponse

func (p *WriteKnowledgebaseFilesResponse) GetHTTPResponse() (v *common.HTTPResponse) {
	if !p.IsSetHTTPResponse() {
		return WriteKnowledgebaseFilesResponse_HTTPResponse_DEFAULT
	}
	return p.HTTPResponse
}
func (p *WriteKnowledgebaseFilesResponse) SetProjectFiles(val []*WorkspaceProject) {
	p.ProjectFiles = val
}
func (p *WriteKnowledgebaseFilesResponse) SetCode(val *common.ErrorCode) {
	p.Code = val
}
func (p *WriteKnowledgebaseFilesResponse) SetMessage(val *string) {
	p.Message = val
}
func (p *WriteKnowledgebaseFilesResponse) SetHTTPCode(val *int32) {
	p.HTTPCode = val
}
func (p *WriteKnowledgebaseFilesResponse) SetHTTPResponse(val *common.HTTPResponse) {
	p.HTTPResponse = val
}

var fieldIDToName_WriteKnowledgebaseFilesResponse = map[int16]string{
	1:   "ProjectFiles",
	201: "Code",
	202: "Message",
	203: "HTTPCode",
	204: "HTTPResponse",
}

func (p *WriteKnowledgebaseFilesResponse) IsSetCode() bool {
	return p.Code != nil
}

func (p *WriteKnowledgebaseFilesResponse) IsSetMessage() bool {
	return p.Message != nil
}

func (p *WriteKnowledgebaseFilesResponse) IsSetHTTPCode() bool {
	return p.HTTPCode != nil
}

func (p *WriteKnowledgebaseFilesResponse) IsSetHTTPResponse() bool {
	return p.HTTPResponse != nil
}

func (p *WriteKnowledgebaseFilesResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 201:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField201(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 202:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField202(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 203:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField203(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 204:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField204(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_WriteKnowledgebaseFilesResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *WriteKnowledgebaseFilesResponse) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*WorkspaceProject, 0, size)
	values := make([]WorkspaceProject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ProjectFiles = _field
	return nil
}
func (p *WriteKnowledgebaseFilesResponse) ReadField201(iprot thrift.TProtocol) error {

	var _field *common.ErrorCode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := common.ErrorCode(v)
		_field = &tmp
	}
	p.Code = _field
	return nil
}
func (p *WriteKnowledgebaseFilesResponse) ReadField202(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Message = _field
	return nil
}
func (p *WriteKnowledgebaseFilesResponse) ReadField203(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.HTTPCode = _field
	return nil
}
func (p *WriteKnowledgebaseFilesResponse) ReadField204(iprot thrift.TProtocol) error {
	_field := common.NewHTTPResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.HTTPResponse = _field
	return nil
}

func (p *WriteKnowledgebaseFilesResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("WriteKnowledgebaseFilesResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField201(oprot); err != nil {
			fieldId = 201
			goto WriteFieldError
		}
		if err = p.writeField202(oprot); err != nil {
			fieldId = 202
			goto WriteFieldError
		}
		if err = p.writeField203(oprot); err != nil {
			fieldId = 203
			goto WriteFieldError
		}
		if err = p.writeField204(oprot); err != nil {
			fieldId = 204
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *WriteKnowledgebaseFilesResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ProjectFiles", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ProjectFiles)); err != nil {
		return err
	}
	for _, v := range p.ProjectFiles {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *WriteKnowledgebaseFilesResponse) writeField201(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err = oprot.WriteFieldBegin("Code", thrift.I32, 201); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Code)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 end error: ", p), err)
}
func (p *WriteKnowledgebaseFilesResponse) writeField202(oprot thrift.TProtocol) (err error) {
	if p.IsSetMessage() {
		if err = oprot.WriteFieldBegin("Message", thrift.STRING, 202); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Message); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 202 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 202 end error: ", p), err)
}
func (p *WriteKnowledgebaseFilesResponse) writeField203(oprot thrift.TProtocol) (err error) {
	if p.IsSetHTTPCode() {
		if err = oprot.WriteFieldBegin("HTTPCode", thrift.I32, 203); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.HTTPCode); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 203 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 203 end error: ", p), err)
}
func (p *WriteKnowledgebaseFilesResponse) writeField204(oprot thrift.TProtocol) (err error) {
	if p.IsSetHTTPResponse() {
		if err = oprot.WriteFieldBegin("HTTPResponse", thrift.STRUCT, 204); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.HTTPResponse.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 204 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 204 end error: ", p), err)
}

func (p *WriteKnowledgebaseFilesResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("WriteKnowledgebaseFilesResponse(%+v)", *p)

}

func (p *WriteKnowledgebaseFilesResponse) DeepEqual(ano *WriteKnowledgebaseFilesResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ProjectFiles) {
		return false
	}
	if !p.Field201DeepEqual(ano.Code) {
		return false
	}
	if !p.Field202DeepEqual(ano.Message) {
		return false
	}
	if !p.Field203DeepEqual(ano.HTTPCode) {
		return false
	}
	if !p.Field204DeepEqual(ano.HTTPResponse) {
		return false
	}
	return true
}

func (p *WriteKnowledgebaseFilesResponse) Field1DeepEqual(src []*WorkspaceProject) bool {

	if len(p.ProjectFiles) != len(src) {
		return false
	}
	for i, v := range p.ProjectFiles {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *WriteKnowledgebaseFilesResponse) Field201DeepEqual(src *common.ErrorCode) bool {

	if p.Code == src {
		return true
	} else if p.Code == nil || src == nil {
		return false
	}
	if *p.Code != *src {
		return false
	}
	return true
}
func (p *WriteKnowledgebaseFilesResponse) Field202DeepEqual(src *string) bool {

	if p.Message == src {
		return true
	} else if p.Message == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Message, *src) != 0 {
		return false
	}
	return true
}
func (p *WriteKnowledgebaseFilesResponse) Field203DeepEqual(src *int32) bool {

	if p.HTTPCode == src {
		return true
	} else if p.HTTPCode == nil || src == nil {
		return false
	}
	if *p.HTTPCode != *src {
		return false
	}
	return true
}
func (p *WriteKnowledgebaseFilesResponse) Field204DeepEqual(src *common.HTTPResponse) bool {

	if !p.HTTPResponse.DeepEqual(src) {
		return false
	}
	return true
}

type DeleteKnowledgebaseFilesRequest struct {
	WorkspaceID  string              `thrift:"WorkspaceID,1" frugal:"1,default,string" json:"WorkspaceID"`
	ProjectFiles []*WorkspaceProject `thrift:"ProjectFiles,2" frugal:"2,default,list<WorkspaceProject>" json:"project_files"`
	HTTPRequest  *common.HTTPRequest `thrift:"HTTPRequest,201,optional" frugal:"201,optional,common.HTTPRequest" json:"HTTPRequest,omitempty"`
	Base         *base.Base          `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewDeleteKnowledgebaseFilesRequest() *DeleteKnowledgebaseFilesRequest {
	return &DeleteKnowledgebaseFilesRequest{}
}

func (p *DeleteKnowledgebaseFilesRequest) InitDefault() {
}

func (p *DeleteKnowledgebaseFilesRequest) GetWorkspaceID() (v string) {
	return p.WorkspaceID
}

func (p *DeleteKnowledgebaseFilesRequest) GetProjectFiles() (v []*WorkspaceProject) {
	return p.ProjectFiles
}

var DeleteKnowledgebaseFilesRequest_HTTPRequest_DEFAULT *common.HTTPRequest

func (p *DeleteKnowledgebaseFilesRequest) GetHTTPRequest() (v *common.HTTPRequest) {
	if !p.IsSetHTTPRequest() {
		return DeleteKnowledgebaseFilesRequest_HTTPRequest_DEFAULT
	}
	return p.HTTPRequest
}

var DeleteKnowledgebaseFilesRequest_Base_DEFAULT *base.Base

func (p *DeleteKnowledgebaseFilesRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return DeleteKnowledgebaseFilesRequest_Base_DEFAULT
	}
	return p.Base
}
func (p *DeleteKnowledgebaseFilesRequest) SetWorkspaceID(val string) {
	p.WorkspaceID = val
}
func (p *DeleteKnowledgebaseFilesRequest) SetProjectFiles(val []*WorkspaceProject) {
	p.ProjectFiles = val
}
func (p *DeleteKnowledgebaseFilesRequest) SetHTTPRequest(val *common.HTTPRequest) {
	p.HTTPRequest = val
}
func (p *DeleteKnowledgebaseFilesRequest) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_DeleteKnowledgebaseFilesRequest = map[int16]string{
	1:   "WorkspaceID",
	2:   "ProjectFiles",
	201: "HTTPRequest",
	255: "Base",
}

func (p *DeleteKnowledgebaseFilesRequest) IsSetHTTPRequest() bool {
	return p.HTTPRequest != nil
}

func (p *DeleteKnowledgebaseFilesRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *DeleteKnowledgebaseFilesRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 201:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField201(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteKnowledgebaseFilesRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DeleteKnowledgebaseFilesRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.WorkspaceID = _field
	return nil
}
func (p *DeleteKnowledgebaseFilesRequest) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*WorkspaceProject, 0, size)
	values := make([]WorkspaceProject, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ProjectFiles = _field
	return nil
}
func (p *DeleteKnowledgebaseFilesRequest) ReadField201(iprot thrift.TProtocol) error {
	_field := common.NewHTTPRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.HTTPRequest = _field
	return nil
}
func (p *DeleteKnowledgebaseFilesRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *DeleteKnowledgebaseFilesRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteKnowledgebaseFilesRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField201(oprot); err != nil {
			fieldId = 201
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteKnowledgebaseFilesRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("WorkspaceID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.WorkspaceID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *DeleteKnowledgebaseFilesRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ProjectFiles", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ProjectFiles)); err != nil {
		return err
	}
	for _, v := range p.ProjectFiles {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *DeleteKnowledgebaseFilesRequest) writeField201(oprot thrift.TProtocol) (err error) {
	if p.IsSetHTTPRequest() {
		if err = oprot.WriteFieldBegin("HTTPRequest", thrift.STRUCT, 201); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.HTTPRequest.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 end error: ", p), err)
}
func (p *DeleteKnowledgebaseFilesRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *DeleteKnowledgebaseFilesRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteKnowledgebaseFilesRequest(%+v)", *p)

}

func (p *DeleteKnowledgebaseFilesRequest) DeepEqual(ano *DeleteKnowledgebaseFilesRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.WorkspaceID) {
		return false
	}
	if !p.Field2DeepEqual(ano.ProjectFiles) {
		return false
	}
	if !p.Field201DeepEqual(ano.HTTPRequest) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *DeleteKnowledgebaseFilesRequest) Field1DeepEqual(src string) bool {

	if strings.Compare(p.WorkspaceID, src) != 0 {
		return false
	}
	return true
}
func (p *DeleteKnowledgebaseFilesRequest) Field2DeepEqual(src []*WorkspaceProject) bool {

	if len(p.ProjectFiles) != len(src) {
		return false
	}
	for i, v := range p.ProjectFiles {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DeleteKnowledgebaseFilesRequest) Field201DeepEqual(src *common.HTTPRequest) bool {

	if !p.HTTPRequest.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DeleteKnowledgebaseFilesRequest) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type DeleteKnowledgebaseFilesResponse struct {
	Code         *common.ErrorCode    `thrift:"Code,201,optional" frugal:"201,optional,ErrorCode" json:"code"`
	Message      *string              `thrift:"Message,202,optional" frugal:"202,optional,string" json:"message"`
	HTTPCode     *int32               `thrift:"HTTPCode,203,optional" frugal:"203,optional,i32" json:"HTTPCode,omitempty"`
	HTTPResponse *common.HTTPResponse `thrift:"HTTPResponse,204,optional" frugal:"204,optional,common.HTTPResponse" json:"HTTPResponse,omitempty"`
}

func NewDeleteKnowledgebaseFilesResponse() *DeleteKnowledgebaseFilesResponse {
	return &DeleteKnowledgebaseFilesResponse{}
}

func (p *DeleteKnowledgebaseFilesResponse) InitDefault() {
}

var DeleteKnowledgebaseFilesResponse_Code_DEFAULT common.ErrorCode

func (p *DeleteKnowledgebaseFilesResponse) GetCode() (v common.ErrorCode) {
	if !p.IsSetCode() {
		return DeleteKnowledgebaseFilesResponse_Code_DEFAULT
	}
	return *p.Code
}

var DeleteKnowledgebaseFilesResponse_Message_DEFAULT string

func (p *DeleteKnowledgebaseFilesResponse) GetMessage() (v string) {
	if !p.IsSetMessage() {
		return DeleteKnowledgebaseFilesResponse_Message_DEFAULT
	}
	return *p.Message
}

var DeleteKnowledgebaseFilesResponse_HTTPCode_DEFAULT int32

func (p *DeleteKnowledgebaseFilesResponse) GetHTTPCode() (v int32) {
	if !p.IsSetHTTPCode() {
		return DeleteKnowledgebaseFilesResponse_HTTPCode_DEFAULT
	}
	return *p.HTTPCode
}

var DeleteKnowledgebaseFilesResponse_HTTPResponse_DEFAULT *common.HTTPResponse

func (p *DeleteKnowledgebaseFilesResponse) GetHTTPResponse() (v *common.HTTPResponse) {
	if !p.IsSetHTTPResponse() {
		return DeleteKnowledgebaseFilesResponse_HTTPResponse_DEFAULT
	}
	return p.HTTPResponse
}
func (p *DeleteKnowledgebaseFilesResponse) SetCode(val *common.ErrorCode) {
	p.Code = val
}
func (p *DeleteKnowledgebaseFilesResponse) SetMessage(val *string) {
	p.Message = val
}
func (p *DeleteKnowledgebaseFilesResponse) SetHTTPCode(val *int32) {
	p.HTTPCode = val
}
func (p *DeleteKnowledgebaseFilesResponse) SetHTTPResponse(val *common.HTTPResponse) {
	p.HTTPResponse = val
}

var fieldIDToName_DeleteKnowledgebaseFilesResponse = map[int16]string{
	201: "Code",
	202: "Message",
	203: "HTTPCode",
	204: "HTTPResponse",
}

func (p *DeleteKnowledgebaseFilesResponse) IsSetCode() bool {
	return p.Code != nil
}

func (p *DeleteKnowledgebaseFilesResponse) IsSetMessage() bool {
	return p.Message != nil
}

func (p *DeleteKnowledgebaseFilesResponse) IsSetHTTPCode() bool {
	return p.HTTPCode != nil
}

func (p *DeleteKnowledgebaseFilesResponse) IsSetHTTPResponse() bool {
	return p.HTTPResponse != nil
}

func (p *DeleteKnowledgebaseFilesResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 201:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField201(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 202:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField202(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 203:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField203(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 204:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField204(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteKnowledgebaseFilesResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DeleteKnowledgebaseFilesResponse) ReadField201(iprot thrift.TProtocol) error {

	var _field *common.ErrorCode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := common.ErrorCode(v)
		_field = &tmp
	}
	p.Code = _field
	return nil
}
func (p *DeleteKnowledgebaseFilesResponse) ReadField202(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Message = _field
	return nil
}
func (p *DeleteKnowledgebaseFilesResponse) ReadField203(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.HTTPCode = _field
	return nil
}
func (p *DeleteKnowledgebaseFilesResponse) ReadField204(iprot thrift.TProtocol) error {
	_field := common.NewHTTPResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.HTTPResponse = _field
	return nil
}

func (p *DeleteKnowledgebaseFilesResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteKnowledgebaseFilesResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField201(oprot); err != nil {
			fieldId = 201
			goto WriteFieldError
		}
		if err = p.writeField202(oprot); err != nil {
			fieldId = 202
			goto WriteFieldError
		}
		if err = p.writeField203(oprot); err != nil {
			fieldId = 203
			goto WriteFieldError
		}
		if err = p.writeField204(oprot); err != nil {
			fieldId = 204
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteKnowledgebaseFilesResponse) writeField201(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err = oprot.WriteFieldBegin("Code", thrift.I32, 201); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Code)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 end error: ", p), err)
}
func (p *DeleteKnowledgebaseFilesResponse) writeField202(oprot thrift.TProtocol) (err error) {
	if p.IsSetMessage() {
		if err = oprot.WriteFieldBegin("Message", thrift.STRING, 202); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Message); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 202 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 202 end error: ", p), err)
}
func (p *DeleteKnowledgebaseFilesResponse) writeField203(oprot thrift.TProtocol) (err error) {
	if p.IsSetHTTPCode() {
		if err = oprot.WriteFieldBegin("HTTPCode", thrift.I32, 203); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.HTTPCode); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 203 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 203 end error: ", p), err)
}
func (p *DeleteKnowledgebaseFilesResponse) writeField204(oprot thrift.TProtocol) (err error) {
	if p.IsSetHTTPResponse() {
		if err = oprot.WriteFieldBegin("HTTPResponse", thrift.STRUCT, 204); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.HTTPResponse.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 204 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 204 end error: ", p), err)
}

func (p *DeleteKnowledgebaseFilesResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteKnowledgebaseFilesResponse(%+v)", *p)

}

func (p *DeleteKnowledgebaseFilesResponse) DeepEqual(ano *DeleteKnowledgebaseFilesResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field201DeepEqual(ano.Code) {
		return false
	}
	if !p.Field202DeepEqual(ano.Message) {
		return false
	}
	if !p.Field203DeepEqual(ano.HTTPCode) {
		return false
	}
	if !p.Field204DeepEqual(ano.HTTPResponse) {
		return false
	}
	return true
}

func (p *DeleteKnowledgebaseFilesResponse) Field201DeepEqual(src *common.ErrorCode) bool {

	if p.Code == src {
		return true
	} else if p.Code == nil || src == nil {
		return false
	}
	if *p.Code != *src {
		return false
	}
	return true
}
func (p *DeleteKnowledgebaseFilesResponse) Field202DeepEqual(src *string) bool {

	if p.Message == src {
		return true
	} else if p.Message == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Message, *src) != 0 {
		return false
	}
	return true
}
func (p *DeleteKnowledgebaseFilesResponse) Field203DeepEqual(src *int32) bool {

	if p.HTTPCode == src {
		return true
	} else if p.HTTPCode == nil || src == nil {
		return false
	}
	if *p.HTTPCode != *src {
		return false
	}
	return true
}
func (p *DeleteKnowledgebaseFilesResponse) Field204DeepEqual(src *common.HTTPResponse) bool {

	if !p.HTTPResponse.DeepEqual(src) {
		return false
	}
	return true
}

type GetKnowledgebaseFilesRequest struct {
	WorkspaceID string              `thrift:"WorkspaceID,1" frugal:"1,default,string" json:"WorkspaceID"`
	ProjectID   string              `thrift:"ProjectID,2" frugal:"2,default,string" json:"project_id"`
	FileIDs     []string            `thrift:"FileIDs,3" frugal:"3,default,list<string>" json:"file_ids"`
	HTTPRequest *common.HTTPRequest `thrift:"HTTPRequest,201,optional" frugal:"201,optional,common.HTTPRequest" json:"HTTPRequest,omitempty"`
	Base        *base.Base          `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewGetKnowledgebaseFilesRequest() *GetKnowledgebaseFilesRequest {
	return &GetKnowledgebaseFilesRequest{}
}

func (p *GetKnowledgebaseFilesRequest) InitDefault() {
}

func (p *GetKnowledgebaseFilesRequest) GetWorkspaceID() (v string) {
	return p.WorkspaceID
}

func (p *GetKnowledgebaseFilesRequest) GetProjectID() (v string) {
	return p.ProjectID
}

func (p *GetKnowledgebaseFilesRequest) GetFileIDs() (v []string) {
	return p.FileIDs
}

var GetKnowledgebaseFilesRequest_HTTPRequest_DEFAULT *common.HTTPRequest

func (p *GetKnowledgebaseFilesRequest) GetHTTPRequest() (v *common.HTTPRequest) {
	if !p.IsSetHTTPRequest() {
		return GetKnowledgebaseFilesRequest_HTTPRequest_DEFAULT
	}
	return p.HTTPRequest
}

var GetKnowledgebaseFilesRequest_Base_DEFAULT *base.Base

func (p *GetKnowledgebaseFilesRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return GetKnowledgebaseFilesRequest_Base_DEFAULT
	}
	return p.Base
}
func (p *GetKnowledgebaseFilesRequest) SetWorkspaceID(val string) {
	p.WorkspaceID = val
}
func (p *GetKnowledgebaseFilesRequest) SetProjectID(val string) {
	p.ProjectID = val
}
func (p *GetKnowledgebaseFilesRequest) SetFileIDs(val []string) {
	p.FileIDs = val
}
func (p *GetKnowledgebaseFilesRequest) SetHTTPRequest(val *common.HTTPRequest) {
	p.HTTPRequest = val
}
func (p *GetKnowledgebaseFilesRequest) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_GetKnowledgebaseFilesRequest = map[int16]string{
	1:   "WorkspaceID",
	2:   "ProjectID",
	3:   "FileIDs",
	201: "HTTPRequest",
	255: "Base",
}

func (p *GetKnowledgebaseFilesRequest) IsSetHTTPRequest() bool {
	return p.HTTPRequest != nil
}

func (p *GetKnowledgebaseFilesRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *GetKnowledgebaseFilesRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 201:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField201(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetKnowledgebaseFilesRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetKnowledgebaseFilesRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.WorkspaceID = _field
	return nil
}
func (p *GetKnowledgebaseFilesRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ProjectID = _field
	return nil
}
func (p *GetKnowledgebaseFilesRequest) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.FileIDs = _field
	return nil
}
func (p *GetKnowledgebaseFilesRequest) ReadField201(iprot thrift.TProtocol) error {
	_field := common.NewHTTPRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.HTTPRequest = _field
	return nil
}
func (p *GetKnowledgebaseFilesRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *GetKnowledgebaseFilesRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetKnowledgebaseFilesRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField201(oprot); err != nil {
			fieldId = 201
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetKnowledgebaseFilesRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("WorkspaceID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.WorkspaceID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetKnowledgebaseFilesRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ProjectID", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ProjectID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *GetKnowledgebaseFilesRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("FileIDs", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.FileIDs)); err != nil {
		return err
	}
	for _, v := range p.FileIDs {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *GetKnowledgebaseFilesRequest) writeField201(oprot thrift.TProtocol) (err error) {
	if p.IsSetHTTPRequest() {
		if err = oprot.WriteFieldBegin("HTTPRequest", thrift.STRUCT, 201); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.HTTPRequest.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 end error: ", p), err)
}
func (p *GetKnowledgebaseFilesRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetKnowledgebaseFilesRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetKnowledgebaseFilesRequest(%+v)", *p)

}

func (p *GetKnowledgebaseFilesRequest) DeepEqual(ano *GetKnowledgebaseFilesRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.WorkspaceID) {
		return false
	}
	if !p.Field2DeepEqual(ano.ProjectID) {
		return false
	}
	if !p.Field3DeepEqual(ano.FileIDs) {
		return false
	}
	if !p.Field201DeepEqual(ano.HTTPRequest) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *GetKnowledgebaseFilesRequest) Field1DeepEqual(src string) bool {

	if strings.Compare(p.WorkspaceID, src) != 0 {
		return false
	}
	return true
}
func (p *GetKnowledgebaseFilesRequest) Field2DeepEqual(src string) bool {

	if strings.Compare(p.ProjectID, src) != 0 {
		return false
	}
	return true
}
func (p *GetKnowledgebaseFilesRequest) Field3DeepEqual(src []string) bool {

	if len(p.FileIDs) != len(src) {
		return false
	}
	for i, v := range p.FileIDs {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *GetKnowledgebaseFilesRequest) Field201DeepEqual(src *common.HTTPRequest) bool {

	if !p.HTTPRequest.DeepEqual(src) {
		return false
	}
	return true
}
func (p *GetKnowledgebaseFilesRequest) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type GetCachedFileRequest struct {
	FileID   string `thrift:"FileID,1" frugal:"1,default,string" json:"file_id"`
	UniqueID string `thrift:"UniqueID,2" frugal:"2,default,string" json:"unique_id"`
}

func NewGetCachedFileRequest() *GetCachedFileRequest {
	return &GetCachedFileRequest{}
}

func (p *GetCachedFileRequest) InitDefault() {
}

func (p *GetCachedFileRequest) GetFileID() (v string) {
	return p.FileID
}

func (p *GetCachedFileRequest) GetUniqueID() (v string) {
	return p.UniqueID
}
func (p *GetCachedFileRequest) SetFileID(val string) {
	p.FileID = val
}
func (p *GetCachedFileRequest) SetUniqueID(val string) {
	p.UniqueID = val
}

var fieldIDToName_GetCachedFileRequest = map[int16]string{
	1: "FileID",
	2: "UniqueID",
}

func (p *GetCachedFileRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetCachedFileRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetCachedFileRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FileID = _field
	return nil
}
func (p *GetCachedFileRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UniqueID = _field
	return nil
}

func (p *GetCachedFileRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetCachedFileRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetCachedFileRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("FileID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.FileID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetCachedFileRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UniqueID", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UniqueID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *GetCachedFileRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCachedFileRequest(%+v)", *p)

}

func (p *GetCachedFileRequest) DeepEqual(ano *GetCachedFileRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.FileID) {
		return false
	}
	if !p.Field2DeepEqual(ano.UniqueID) {
		return false
	}
	return true
}

func (p *GetCachedFileRequest) Field1DeepEqual(src string) bool {

	if strings.Compare(p.FileID, src) != 0 {
		return false
	}
	return true
}
func (p *GetCachedFileRequest) Field2DeepEqual(src string) bool {

	if strings.Compare(p.UniqueID, src) != 0 {
		return false
	}
	return true
}

type GetCachedFilesAndWriteRequest struct {
	Files       []*GetCachedFileRequest `thrift:"files,1" frugal:"1,default,list<GetCachedFileRequest>" json:"files"`
	WorkspaceID string                  `thrift:"WorkspaceID,2" frugal:"2,default,string" json:"WorkspaceID"`
	ProjectID   string                  `thrift:"ProjectID,3" frugal:"3,default,string" json:"project_id"`
	HTTPRequest *common.HTTPRequest     `thrift:"HTTPRequest,201,optional" frugal:"201,optional,common.HTTPRequest" json:"HTTPRequest,omitempty"`
	Base        *base.Base              `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewGetCachedFilesAndWriteRequest() *GetCachedFilesAndWriteRequest {
	return &GetCachedFilesAndWriteRequest{}
}

func (p *GetCachedFilesAndWriteRequest) InitDefault() {
}

func (p *GetCachedFilesAndWriteRequest) GetFiles() (v []*GetCachedFileRequest) {
	return p.Files
}

func (p *GetCachedFilesAndWriteRequest) GetWorkspaceID() (v string) {
	return p.WorkspaceID
}

func (p *GetCachedFilesAndWriteRequest) GetProjectID() (v string) {
	return p.ProjectID
}

var GetCachedFilesAndWriteRequest_HTTPRequest_DEFAULT *common.HTTPRequest

func (p *GetCachedFilesAndWriteRequest) GetHTTPRequest() (v *common.HTTPRequest) {
	if !p.IsSetHTTPRequest() {
		return GetCachedFilesAndWriteRequest_HTTPRequest_DEFAULT
	}
	return p.HTTPRequest
}

var GetCachedFilesAndWriteRequest_Base_DEFAULT *base.Base

func (p *GetCachedFilesAndWriteRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return GetCachedFilesAndWriteRequest_Base_DEFAULT
	}
	return p.Base
}
func (p *GetCachedFilesAndWriteRequest) SetFiles(val []*GetCachedFileRequest) {
	p.Files = val
}
func (p *GetCachedFilesAndWriteRequest) SetWorkspaceID(val string) {
	p.WorkspaceID = val
}
func (p *GetCachedFilesAndWriteRequest) SetProjectID(val string) {
	p.ProjectID = val
}
func (p *GetCachedFilesAndWriteRequest) SetHTTPRequest(val *common.HTTPRequest) {
	p.HTTPRequest = val
}
func (p *GetCachedFilesAndWriteRequest) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_GetCachedFilesAndWriteRequest = map[int16]string{
	1:   "files",
	2:   "WorkspaceID",
	3:   "ProjectID",
	201: "HTTPRequest",
	255: "Base",
}

func (p *GetCachedFilesAndWriteRequest) IsSetHTTPRequest() bool {
	return p.HTTPRequest != nil
}

func (p *GetCachedFilesAndWriteRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *GetCachedFilesAndWriteRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 201:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField201(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetCachedFilesAndWriteRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetCachedFilesAndWriteRequest) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*GetCachedFileRequest, 0, size)
	values := make([]GetCachedFileRequest, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Files = _field
	return nil
}
func (p *GetCachedFilesAndWriteRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.WorkspaceID = _field
	return nil
}
func (p *GetCachedFilesAndWriteRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ProjectID = _field
	return nil
}
func (p *GetCachedFilesAndWriteRequest) ReadField201(iprot thrift.TProtocol) error {
	_field := common.NewHTTPRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.HTTPRequest = _field
	return nil
}
func (p *GetCachedFilesAndWriteRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *GetCachedFilesAndWriteRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetCachedFilesAndWriteRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField201(oprot); err != nil {
			fieldId = 201
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetCachedFilesAndWriteRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("files", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Files)); err != nil {
		return err
	}
	for _, v := range p.Files {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetCachedFilesAndWriteRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("WorkspaceID", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.WorkspaceID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *GetCachedFilesAndWriteRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ProjectID", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ProjectID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *GetCachedFilesAndWriteRequest) writeField201(oprot thrift.TProtocol) (err error) {
	if p.IsSetHTTPRequest() {
		if err = oprot.WriteFieldBegin("HTTPRequest", thrift.STRUCT, 201); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.HTTPRequest.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 end error: ", p), err)
}
func (p *GetCachedFilesAndWriteRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetCachedFilesAndWriteRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCachedFilesAndWriteRequest(%+v)", *p)

}

func (p *GetCachedFilesAndWriteRequest) DeepEqual(ano *GetCachedFilesAndWriteRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Files) {
		return false
	}
	if !p.Field2DeepEqual(ano.WorkspaceID) {
		return false
	}
	if !p.Field3DeepEqual(ano.ProjectID) {
		return false
	}
	if !p.Field201DeepEqual(ano.HTTPRequest) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *GetCachedFilesAndWriteRequest) Field1DeepEqual(src []*GetCachedFileRequest) bool {

	if len(p.Files) != len(src) {
		return false
	}
	for i, v := range p.Files {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *GetCachedFilesAndWriteRequest) Field2DeepEqual(src string) bool {

	if strings.Compare(p.WorkspaceID, src) != 0 {
		return false
	}
	return true
}
func (p *GetCachedFilesAndWriteRequest) Field3DeepEqual(src string) bool {

	if strings.Compare(p.ProjectID, src) != 0 {
		return false
	}
	return true
}
func (p *GetCachedFilesAndWriteRequest) Field201DeepEqual(src *common.HTTPRequest) bool {

	if !p.HTTPRequest.DeepEqual(src) {
		return false
	}
	return true
}
func (p *GetCachedFilesAndWriteRequest) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type SplitFile struct {
	Content   string                  `thrift:"Content,1" frugal:"1,default,string" json:"content"`
	Path      string                  `thrift:"Path,2" frugal:"2,default,string" json:"path"`
	ChunkSize int32                   `thrift:"ChunkSize,3" frugal:"3,default,i32" json:"chunk_size"`
	ChunkType knowledgebase.ChunkType `thrift:"ChunkType,4" frugal:"4,default,string" json:"chunk_type"`
	Title     string                  `thrift:"Title,5" frugal:"5,default,string" json:"title"`
}

func NewSplitFile() *SplitFile {
	return &SplitFile{}
}

func (p *SplitFile) InitDefault() {
}

func (p *SplitFile) GetContent() (v string) {
	return p.Content
}

func (p *SplitFile) GetPath() (v string) {
	return p.Path
}

func (p *SplitFile) GetChunkSize() (v int32) {
	return p.ChunkSize
}

func (p *SplitFile) GetChunkType() (v knowledgebase.ChunkType) {
	return p.ChunkType
}

func (p *SplitFile) GetTitle() (v string) {
	return p.Title
}
func (p *SplitFile) SetContent(val string) {
	p.Content = val
}
func (p *SplitFile) SetPath(val string) {
	p.Path = val
}
func (p *SplitFile) SetChunkSize(val int32) {
	p.ChunkSize = val
}
func (p *SplitFile) SetChunkType(val knowledgebase.ChunkType) {
	p.ChunkType = val
}
func (p *SplitFile) SetTitle(val string) {
	p.Title = val
}

var fieldIDToName_SplitFile = map[int16]string{
	1: "Content",
	2: "Path",
	3: "ChunkSize",
	4: "ChunkType",
	5: "Title",
}

func (p *SplitFile) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SplitFile[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *SplitFile) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Content = _field
	return nil
}
func (p *SplitFile) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Path = _field
	return nil
}
func (p *SplitFile) ReadField3(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ChunkSize = _field
	return nil
}
func (p *SplitFile) ReadField4(iprot thrift.TProtocol) error {

	var _field knowledgebase.ChunkType
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ChunkType = _field
	return nil
}
func (p *SplitFile) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Title = _field
	return nil
}

func (p *SplitFile) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("SplitFile"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SplitFile) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Content", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Content); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *SplitFile) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Path", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Path); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *SplitFile) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ChunkSize", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.ChunkSize); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *SplitFile) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ChunkType", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ChunkType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *SplitFile) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Title", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Title); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *SplitFile) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SplitFile(%+v)", *p)

}

func (p *SplitFile) DeepEqual(ano *SplitFile) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Content) {
		return false
	}
	if !p.Field2DeepEqual(ano.Path) {
		return false
	}
	if !p.Field3DeepEqual(ano.ChunkSize) {
		return false
	}
	if !p.Field4DeepEqual(ano.ChunkType) {
		return false
	}
	if !p.Field5DeepEqual(ano.Title) {
		return false
	}
	return true
}

func (p *SplitFile) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Content, src) != 0 {
		return false
	}
	return true
}
func (p *SplitFile) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Path, src) != 0 {
		return false
	}
	return true
}
func (p *SplitFile) Field3DeepEqual(src int32) bool {

	if p.ChunkSize != src {
		return false
	}
	return true
}
func (p *SplitFile) Field4DeepEqual(src knowledgebase.ChunkType) bool {

	if strings.Compare(p.ChunkType, src) != 0 {
		return false
	}
	return true
}
func (p *SplitFile) Field5DeepEqual(src string) bool {

	if strings.Compare(p.Title, src) != 0 {
		return false
	}
	return true
}

type SplitFilesRequest struct {
	Files          []*SplitFile        `thrift:"Files,1" frugal:"1,default,list<SplitFile>" json:"files"`
	DatastoreName  string              `thrift:"DatastoreName,2" frugal:"2,default,string" json:"datastore_name"`
	ChunkingMethod *string             `thrift:"ChunkingMethod,3,optional" frugal:"3,optional,string" json:"chunking_method"`
	HTTPRequest    *common.HTTPRequest `thrift:"HTTPRequest,201,optional" frugal:"201,optional,common.HTTPRequest" json:"HTTPRequest,omitempty"`
	Base           *base.Base          `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewSplitFilesRequest() *SplitFilesRequest {
	return &SplitFilesRequest{}
}

func (p *SplitFilesRequest) InitDefault() {
}

func (p *SplitFilesRequest) GetFiles() (v []*SplitFile) {
	return p.Files
}

func (p *SplitFilesRequest) GetDatastoreName() (v string) {
	return p.DatastoreName
}

var SplitFilesRequest_ChunkingMethod_DEFAULT string

func (p *SplitFilesRequest) GetChunkingMethod() (v string) {
	if !p.IsSetChunkingMethod() {
		return SplitFilesRequest_ChunkingMethod_DEFAULT
	}
	return *p.ChunkingMethod
}

var SplitFilesRequest_HTTPRequest_DEFAULT *common.HTTPRequest

func (p *SplitFilesRequest) GetHTTPRequest() (v *common.HTTPRequest) {
	if !p.IsSetHTTPRequest() {
		return SplitFilesRequest_HTTPRequest_DEFAULT
	}
	return p.HTTPRequest
}

var SplitFilesRequest_Base_DEFAULT *base.Base

func (p *SplitFilesRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return SplitFilesRequest_Base_DEFAULT
	}
	return p.Base
}
func (p *SplitFilesRequest) SetFiles(val []*SplitFile) {
	p.Files = val
}
func (p *SplitFilesRequest) SetDatastoreName(val string) {
	p.DatastoreName = val
}
func (p *SplitFilesRequest) SetChunkingMethod(val *string) {
	p.ChunkingMethod = val
}
func (p *SplitFilesRequest) SetHTTPRequest(val *common.HTTPRequest) {
	p.HTTPRequest = val
}
func (p *SplitFilesRequest) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_SplitFilesRequest = map[int16]string{
	1:   "Files",
	2:   "DatastoreName",
	3:   "ChunkingMethod",
	201: "HTTPRequest",
	255: "Base",
}

func (p *SplitFilesRequest) IsSetChunkingMethod() bool {
	return p.ChunkingMethod != nil
}

func (p *SplitFilesRequest) IsSetHTTPRequest() bool {
	return p.HTTPRequest != nil
}

func (p *SplitFilesRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *SplitFilesRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 201:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField201(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SplitFilesRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *SplitFilesRequest) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*SplitFile, 0, size)
	values := make([]SplitFile, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Files = _field
	return nil
}
func (p *SplitFilesRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DatastoreName = _field
	return nil
}
func (p *SplitFilesRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ChunkingMethod = _field
	return nil
}
func (p *SplitFilesRequest) ReadField201(iprot thrift.TProtocol) error {
	_field := common.NewHTTPRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.HTTPRequest = _field
	return nil
}
func (p *SplitFilesRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *SplitFilesRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("SplitFilesRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField201(oprot); err != nil {
			fieldId = 201
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SplitFilesRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Files", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Files)); err != nil {
		return err
	}
	for _, v := range p.Files {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *SplitFilesRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DatastoreName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DatastoreName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *SplitFilesRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetChunkingMethod() {
		if err = oprot.WriteFieldBegin("ChunkingMethod", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ChunkingMethod); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *SplitFilesRequest) writeField201(oprot thrift.TProtocol) (err error) {
	if p.IsSetHTTPRequest() {
		if err = oprot.WriteFieldBegin("HTTPRequest", thrift.STRUCT, 201); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.HTTPRequest.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 end error: ", p), err)
}
func (p *SplitFilesRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *SplitFilesRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SplitFilesRequest(%+v)", *p)

}

func (p *SplitFilesRequest) DeepEqual(ano *SplitFilesRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Files) {
		return false
	}
	if !p.Field2DeepEqual(ano.DatastoreName) {
		return false
	}
	if !p.Field3DeepEqual(ano.ChunkingMethod) {
		return false
	}
	if !p.Field201DeepEqual(ano.HTTPRequest) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *SplitFilesRequest) Field1DeepEqual(src []*SplitFile) bool {

	if len(p.Files) != len(src) {
		return false
	}
	for i, v := range p.Files {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *SplitFilesRequest) Field2DeepEqual(src string) bool {

	if strings.Compare(p.DatastoreName, src) != 0 {
		return false
	}
	return true
}
func (p *SplitFilesRequest) Field3DeepEqual(src *string) bool {

	if p.ChunkingMethod == src {
		return true
	} else if p.ChunkingMethod == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ChunkingMethod, *src) != 0 {
		return false
	}
	return true
}
func (p *SplitFilesRequest) Field201DeepEqual(src *common.HTTPRequest) bool {

	if !p.HTTPRequest.DeepEqual(src) {
		return false
	}
	return true
}
func (p *SplitFilesRequest) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type SegmentWithEmbedding struct {
	EntityID          string               `thrift:"EntityID,1" frugal:"1,default,string" json:"entity_id"`
	SegmentUniqueID   string               `thrift:"SegmentUniqueID,2" frugal:"2,default,string" json:"segment_unique_id"`
	Segment           *Segment             `thrift:"Segment,3" frugal:"3,default,Segment" json:"segment"`
	IsAllEmbedding    bool                 `thrift:"IsAllEmbedding,4" frugal:"4,default,bool" json:"is_all_embedding"`
	EmbeddingContents []string             `thrift:"EmbeddingContents,5" frugal:"5,default,list<string>" json:"embedding_contents"`
	Embeddings        map[string][]float64 `thrift:"Embeddings,6" frugal:"6,default,map<string:list<double>>" json:"embeddings"`
}

func NewSegmentWithEmbedding() *SegmentWithEmbedding {
	return &SegmentWithEmbedding{}
}

func (p *SegmentWithEmbedding) InitDefault() {
}

func (p *SegmentWithEmbedding) GetEntityID() (v string) {
	return p.EntityID
}

func (p *SegmentWithEmbedding) GetSegmentUniqueID() (v string) {
	return p.SegmentUniqueID
}

var SegmentWithEmbedding_Segment_DEFAULT *Segment

func (p *SegmentWithEmbedding) GetSegment() (v *Segment) {
	if !p.IsSetSegment() {
		return SegmentWithEmbedding_Segment_DEFAULT
	}
	return p.Segment
}

func (p *SegmentWithEmbedding) GetIsAllEmbedding() (v bool) {
	return p.IsAllEmbedding
}

func (p *SegmentWithEmbedding) GetEmbeddingContents() (v []string) {
	return p.EmbeddingContents
}

func (p *SegmentWithEmbedding) GetEmbeddings() (v map[string][]float64) {
	return p.Embeddings
}
func (p *SegmentWithEmbedding) SetEntityID(val string) {
	p.EntityID = val
}
func (p *SegmentWithEmbedding) SetSegmentUniqueID(val string) {
	p.SegmentUniqueID = val
}
func (p *SegmentWithEmbedding) SetSegment(val *Segment) {
	p.Segment = val
}
func (p *SegmentWithEmbedding) SetIsAllEmbedding(val bool) {
	p.IsAllEmbedding = val
}
func (p *SegmentWithEmbedding) SetEmbeddingContents(val []string) {
	p.EmbeddingContents = val
}
func (p *SegmentWithEmbedding) SetEmbeddings(val map[string][]float64) {
	p.Embeddings = val
}

var fieldIDToName_SegmentWithEmbedding = map[int16]string{
	1: "EntityID",
	2: "SegmentUniqueID",
	3: "Segment",
	4: "IsAllEmbedding",
	5: "EmbeddingContents",
	6: "Embeddings",
}

func (p *SegmentWithEmbedding) IsSetSegment() bool {
	return p.Segment != nil
}

func (p *SegmentWithEmbedding) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SegmentWithEmbedding[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *SegmentWithEmbedding) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EntityID = _field
	return nil
}
func (p *SegmentWithEmbedding) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SegmentUniqueID = _field
	return nil
}
func (p *SegmentWithEmbedding) ReadField3(iprot thrift.TProtocol) error {
	_field := NewSegment()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Segment = _field
	return nil
}
func (p *SegmentWithEmbedding) ReadField4(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IsAllEmbedding = _field
	return nil
}
func (p *SegmentWithEmbedding) ReadField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.EmbeddingContents = _field
	return nil
}
func (p *SegmentWithEmbedding) ReadField6(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string][]float64, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}
		_, size, err := iprot.ReadListBegin()
		if err != nil {
			return err
		}
		_val := make([]float64, 0, size)
		for i := 0; i < size; i++ {

			var _elem float64
			if v, err := iprot.ReadDouble(); err != nil {
				return err
			} else {
				_elem = v
			}

			_val = append(_val, _elem)
		}
		if err := iprot.ReadListEnd(); err != nil {
			return err
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.Embeddings = _field
	return nil
}

func (p *SegmentWithEmbedding) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("SegmentWithEmbedding"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SegmentWithEmbedding) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EntityID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EntityID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *SegmentWithEmbedding) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SegmentUniqueID", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SegmentUniqueID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *SegmentWithEmbedding) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Segment", thrift.STRUCT, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Segment.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *SegmentWithEmbedding) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IsAllEmbedding", thrift.BOOL, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.IsAllEmbedding); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *SegmentWithEmbedding) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EmbeddingContents", thrift.LIST, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.EmbeddingContents)); err != nil {
		return err
	}
	for _, v := range p.EmbeddingContents {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *SegmentWithEmbedding) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Embeddings", thrift.MAP, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.LIST, len(p.Embeddings)); err != nil {
		return err
	}
	for k, v := range p.Embeddings {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteListBegin(thrift.DOUBLE, len(v)); err != nil {
			return err
		}
		for _, v := range v {
			if err := oprot.WriteDouble(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *SegmentWithEmbedding) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SegmentWithEmbedding(%+v)", *p)

}

func (p *SegmentWithEmbedding) DeepEqual(ano *SegmentWithEmbedding) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.EntityID) {
		return false
	}
	if !p.Field2DeepEqual(ano.SegmentUniqueID) {
		return false
	}
	if !p.Field3DeepEqual(ano.Segment) {
		return false
	}
	if !p.Field4DeepEqual(ano.IsAllEmbedding) {
		return false
	}
	if !p.Field5DeepEqual(ano.EmbeddingContents) {
		return false
	}
	if !p.Field6DeepEqual(ano.Embeddings) {
		return false
	}
	return true
}

func (p *SegmentWithEmbedding) Field1DeepEqual(src string) bool {

	if strings.Compare(p.EntityID, src) != 0 {
		return false
	}
	return true
}
func (p *SegmentWithEmbedding) Field2DeepEqual(src string) bool {

	if strings.Compare(p.SegmentUniqueID, src) != 0 {
		return false
	}
	return true
}
func (p *SegmentWithEmbedding) Field3DeepEqual(src *Segment) bool {

	if !p.Segment.DeepEqual(src) {
		return false
	}
	return true
}
func (p *SegmentWithEmbedding) Field4DeepEqual(src bool) bool {

	if p.IsAllEmbedding != src {
		return false
	}
	return true
}
func (p *SegmentWithEmbedding) Field5DeepEqual(src []string) bool {

	if len(p.EmbeddingContents) != len(src) {
		return false
	}
	for i, v := range p.EmbeddingContents {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *SegmentWithEmbedding) Field6DeepEqual(src map[string][]float64) bool {

	if len(p.Embeddings) != len(src) {
		return false
	}
	for k, v := range p.Embeddings {
		_src := src[k]
		if len(v) != len(_src) {
			return false
		}
		for i, v := range v {
			_src1 := _src[i]
			if v != _src1 {
				return false
			}
		}
	}
	return true
}

type SplitFileResult_ struct {
	SegmentsWithEmbedding map[string]*SegmentWithEmbedding `thrift:"SegmentsWithEmbedding,1" frugal:"1,default,map<string:SegmentWithEmbedding>" json:"segments_with_embedding"`
}

func NewSplitFileResult_() *SplitFileResult_ {
	return &SplitFileResult_{}
}

func (p *SplitFileResult_) InitDefault() {
}

func (p *SplitFileResult_) GetSegmentsWithEmbedding() (v map[string]*SegmentWithEmbedding) {
	return p.SegmentsWithEmbedding
}
func (p *SplitFileResult_) SetSegmentsWithEmbedding(val map[string]*SegmentWithEmbedding) {
	p.SegmentsWithEmbedding = val
}

var fieldIDToName_SplitFileResult_ = map[int16]string{
	1: "SegmentsWithEmbedding",
}

func (p *SplitFileResult_) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SplitFileResult_[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *SplitFileResult_) ReadField1(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]*SegmentWithEmbedding, size)
	values := make([]SegmentWithEmbedding, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		_val := &values[i]
		_val.InitDefault()
		if err := _val.Read(iprot); err != nil {
			return err
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.SegmentsWithEmbedding = _field
	return nil
}

func (p *SplitFileResult_) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("SplitFileResult"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SplitFileResult_) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SegmentsWithEmbedding", thrift.MAP, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRUCT, len(p.SegmentsWithEmbedding)); err != nil {
		return err
	}
	for k, v := range p.SegmentsWithEmbedding {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SplitFileResult_) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SplitFileResult_(%+v)", *p)

}

func (p *SplitFileResult_) DeepEqual(ano *SplitFileResult_) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SegmentsWithEmbedding) {
		return false
	}
	return true
}

func (p *SplitFileResult_) Field1DeepEqual(src map[string]*SegmentWithEmbedding) bool {

	if len(p.SegmentsWithEmbedding) != len(src) {
		return false
	}
	for k, v := range p.SegmentsWithEmbedding {
		_src := src[k]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type SplitFilesResponse struct {
	Results      map[string]*SplitFileResult_ `thrift:"Results,1" frugal:"1,default,map<string:SplitFileResult_>" json:"results"`
	FailedFiles  map[string]string            `thrift:"FailedFiles,2" frugal:"2,default,map<string:string>" json:"failed_files"`
	Code         *common.ErrorCode            `thrift:"Code,201,optional" frugal:"201,optional,ErrorCode" json:"code"`
	Message      *string                      `thrift:"Message,202,optional" frugal:"202,optional,string" json:"message"`
	HTTPCode     *int32                       `thrift:"HTTPCode,203,optional" frugal:"203,optional,i32" json:"HTTPCode,omitempty"`
	HTTPResponse *common.HTTPResponse         `thrift:"HTTPResponse,204,optional" frugal:"204,optional,common.HTTPResponse" json:"HTTPResponse,omitempty"`
}

func NewSplitFilesResponse() *SplitFilesResponse {
	return &SplitFilesResponse{}
}

func (p *SplitFilesResponse) InitDefault() {
}

func (p *SplitFilesResponse) GetResults() (v map[string]*SplitFileResult_) {
	return p.Results
}

func (p *SplitFilesResponse) GetFailedFiles() (v map[string]string) {
	return p.FailedFiles
}

var SplitFilesResponse_Code_DEFAULT common.ErrorCode

func (p *SplitFilesResponse) GetCode() (v common.ErrorCode) {
	if !p.IsSetCode() {
		return SplitFilesResponse_Code_DEFAULT
	}
	return *p.Code
}

var SplitFilesResponse_Message_DEFAULT string

func (p *SplitFilesResponse) GetMessage() (v string) {
	if !p.IsSetMessage() {
		return SplitFilesResponse_Message_DEFAULT
	}
	return *p.Message
}

var SplitFilesResponse_HTTPCode_DEFAULT int32

func (p *SplitFilesResponse) GetHTTPCode() (v int32) {
	if !p.IsSetHTTPCode() {
		return SplitFilesResponse_HTTPCode_DEFAULT
	}
	return *p.HTTPCode
}

var SplitFilesResponse_HTTPResponse_DEFAULT *common.HTTPResponse

func (p *SplitFilesResponse) GetHTTPResponse() (v *common.HTTPResponse) {
	if !p.IsSetHTTPResponse() {
		return SplitFilesResponse_HTTPResponse_DEFAULT
	}
	return p.HTTPResponse
}
func (p *SplitFilesResponse) SetResults(val map[string]*SplitFileResult_) {
	p.Results = val
}
func (p *SplitFilesResponse) SetFailedFiles(val map[string]string) {
	p.FailedFiles = val
}
func (p *SplitFilesResponse) SetCode(val *common.ErrorCode) {
	p.Code = val
}
func (p *SplitFilesResponse) SetMessage(val *string) {
	p.Message = val
}
func (p *SplitFilesResponse) SetHTTPCode(val *int32) {
	p.HTTPCode = val
}
func (p *SplitFilesResponse) SetHTTPResponse(val *common.HTTPResponse) {
	p.HTTPResponse = val
}

var fieldIDToName_SplitFilesResponse = map[int16]string{
	1:   "Results",
	2:   "FailedFiles",
	201: "Code",
	202: "Message",
	203: "HTTPCode",
	204: "HTTPResponse",
}

func (p *SplitFilesResponse) IsSetCode() bool {
	return p.Code != nil
}

func (p *SplitFilesResponse) IsSetMessage() bool {
	return p.Message != nil
}

func (p *SplitFilesResponse) IsSetHTTPCode() bool {
	return p.HTTPCode != nil
}

func (p *SplitFilesResponse) IsSetHTTPResponse() bool {
	return p.HTTPResponse != nil
}

func (p *SplitFilesResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 201:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField201(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 202:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField202(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 203:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField203(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 204:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField204(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SplitFilesResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *SplitFilesResponse) ReadField1(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]*SplitFileResult_, size)
	values := make([]SplitFileResult_, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		_val := &values[i]
		_val.InitDefault()
		if err := _val.Read(iprot); err != nil {
			return err
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.Results = _field
	return nil
}
func (p *SplitFilesResponse) ReadField2(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.FailedFiles = _field
	return nil
}
func (p *SplitFilesResponse) ReadField201(iprot thrift.TProtocol) error {

	var _field *common.ErrorCode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := common.ErrorCode(v)
		_field = &tmp
	}
	p.Code = _field
	return nil
}
func (p *SplitFilesResponse) ReadField202(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Message = _field
	return nil
}
func (p *SplitFilesResponse) ReadField203(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.HTTPCode = _field
	return nil
}
func (p *SplitFilesResponse) ReadField204(iprot thrift.TProtocol) error {
	_field := common.NewHTTPResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.HTTPResponse = _field
	return nil
}

func (p *SplitFilesResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("SplitFilesResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField201(oprot); err != nil {
			fieldId = 201
			goto WriteFieldError
		}
		if err = p.writeField202(oprot); err != nil {
			fieldId = 202
			goto WriteFieldError
		}
		if err = p.writeField203(oprot); err != nil {
			fieldId = 203
			goto WriteFieldError
		}
		if err = p.writeField204(oprot); err != nil {
			fieldId = 204
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SplitFilesResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Results", thrift.MAP, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRUCT, len(p.Results)); err != nil {
		return err
	}
	for k, v := range p.Results {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *SplitFilesResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("FailedFiles", thrift.MAP, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.FailedFiles)); err != nil {
		return err
	}
	for k, v := range p.FailedFiles {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *SplitFilesResponse) writeField201(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err = oprot.WriteFieldBegin("Code", thrift.I32, 201); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Code)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 end error: ", p), err)
}
func (p *SplitFilesResponse) writeField202(oprot thrift.TProtocol) (err error) {
	if p.IsSetMessage() {
		if err = oprot.WriteFieldBegin("Message", thrift.STRING, 202); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Message); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 202 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 202 end error: ", p), err)
}
func (p *SplitFilesResponse) writeField203(oprot thrift.TProtocol) (err error) {
	if p.IsSetHTTPCode() {
		if err = oprot.WriteFieldBegin("HTTPCode", thrift.I32, 203); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.HTTPCode); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 203 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 203 end error: ", p), err)
}
func (p *SplitFilesResponse) writeField204(oprot thrift.TProtocol) (err error) {
	if p.IsSetHTTPResponse() {
		if err = oprot.WriteFieldBegin("HTTPResponse", thrift.STRUCT, 204); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.HTTPResponse.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 204 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 204 end error: ", p), err)
}

func (p *SplitFilesResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SplitFilesResponse(%+v)", *p)

}

func (p *SplitFilesResponse) DeepEqual(ano *SplitFilesResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Results) {
		return false
	}
	if !p.Field2DeepEqual(ano.FailedFiles) {
		return false
	}
	if !p.Field201DeepEqual(ano.Code) {
		return false
	}
	if !p.Field202DeepEqual(ano.Message) {
		return false
	}
	if !p.Field203DeepEqual(ano.HTTPCode) {
		return false
	}
	if !p.Field204DeepEqual(ano.HTTPResponse) {
		return false
	}
	return true
}

func (p *SplitFilesResponse) Field1DeepEqual(src map[string]*SplitFileResult_) bool {

	if len(p.Results) != len(src) {
		return false
	}
	for k, v := range p.Results {
		_src := src[k]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *SplitFilesResponse) Field2DeepEqual(src map[string]string) bool {

	if len(p.FailedFiles) != len(src) {
		return false
	}
	for k, v := range p.FailedFiles {
		_src := src[k]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *SplitFilesResponse) Field201DeepEqual(src *common.ErrorCode) bool {

	if p.Code == src {
		return true
	} else if p.Code == nil || src == nil {
		return false
	}
	if *p.Code != *src {
		return false
	}
	return true
}
func (p *SplitFilesResponse) Field202DeepEqual(src *string) bool {

	if p.Message == src {
		return true
	} else if p.Message == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Message, *src) != 0 {
		return false
	}
	return true
}
func (p *SplitFilesResponse) Field203DeepEqual(src *int32) bool {

	if p.HTTPCode == src {
		return true
	} else if p.HTTPCode == nil || src == nil {
		return false
	}
	if *p.HTTPCode != *src {
		return false
	}
	return true
}
func (p *SplitFilesResponse) Field204DeepEqual(src *common.HTTPResponse) bool {

	if !p.HTTPResponse.DeepEqual(src) {
		return false
	}
	return true
}

type EmbeddingRequest struct {
	DatastoreName     string              `thrift:"DatastoreName,1" frugal:"1,default,string" json:"datastore_name"`
	EmbeddingContents []string            `thrift:"EmbeddingContents,2" frugal:"2,default,list<string>" json:"embedding_contents"`
	OfflineCluster    bool                `thrift:"OfflineCluster,3" frugal:"3,default,bool" json:"offline_cluster"`
	Extra             *string             `thrift:"Extra,4,optional" frugal:"4,optional,string" json:"extra"`
	HTTPRequest       *common.HTTPRequest `thrift:"HTTPRequest,201,optional" frugal:"201,optional,common.HTTPRequest" json:"HTTPRequest,omitempty"`
	Base              *base.Base          `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewEmbeddingRequest() *EmbeddingRequest {
	return &EmbeddingRequest{}
}

func (p *EmbeddingRequest) InitDefault() {
}

func (p *EmbeddingRequest) GetDatastoreName() (v string) {
	return p.DatastoreName
}

func (p *EmbeddingRequest) GetEmbeddingContents() (v []string) {
	return p.EmbeddingContents
}

func (p *EmbeddingRequest) GetOfflineCluster() (v bool) {
	return p.OfflineCluster
}

var EmbeddingRequest_Extra_DEFAULT string

func (p *EmbeddingRequest) GetExtra() (v string) {
	if !p.IsSetExtra() {
		return EmbeddingRequest_Extra_DEFAULT
	}
	return *p.Extra
}

var EmbeddingRequest_HTTPRequest_DEFAULT *common.HTTPRequest

func (p *EmbeddingRequest) GetHTTPRequest() (v *common.HTTPRequest) {
	if !p.IsSetHTTPRequest() {
		return EmbeddingRequest_HTTPRequest_DEFAULT
	}
	return p.HTTPRequest
}

var EmbeddingRequest_Base_DEFAULT *base.Base

func (p *EmbeddingRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return EmbeddingRequest_Base_DEFAULT
	}
	return p.Base
}
func (p *EmbeddingRequest) SetDatastoreName(val string) {
	p.DatastoreName = val
}
func (p *EmbeddingRequest) SetEmbeddingContents(val []string) {
	p.EmbeddingContents = val
}
func (p *EmbeddingRequest) SetOfflineCluster(val bool) {
	p.OfflineCluster = val
}
func (p *EmbeddingRequest) SetExtra(val *string) {
	p.Extra = val
}
func (p *EmbeddingRequest) SetHTTPRequest(val *common.HTTPRequest) {
	p.HTTPRequest = val
}
func (p *EmbeddingRequest) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_EmbeddingRequest = map[int16]string{
	1:   "DatastoreName",
	2:   "EmbeddingContents",
	3:   "OfflineCluster",
	4:   "Extra",
	201: "HTTPRequest",
	255: "Base",
}

func (p *EmbeddingRequest) IsSetExtra() bool {
	return p.Extra != nil
}

func (p *EmbeddingRequest) IsSetHTTPRequest() bool {
	return p.HTTPRequest != nil
}

func (p *EmbeddingRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *EmbeddingRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 201:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField201(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_EmbeddingRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *EmbeddingRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DatastoreName = _field
	return nil
}
func (p *EmbeddingRequest) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.EmbeddingContents = _field
	return nil
}
func (p *EmbeddingRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OfflineCluster = _field
	return nil
}
func (p *EmbeddingRequest) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Extra = _field
	return nil
}
func (p *EmbeddingRequest) ReadField201(iprot thrift.TProtocol) error {
	_field := common.NewHTTPRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.HTTPRequest = _field
	return nil
}
func (p *EmbeddingRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *EmbeddingRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("EmbeddingRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField201(oprot); err != nil {
			fieldId = 201
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *EmbeddingRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DatastoreName", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DatastoreName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *EmbeddingRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EmbeddingContents", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.EmbeddingContents)); err != nil {
		return err
	}
	for _, v := range p.EmbeddingContents {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *EmbeddingRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("OfflineCluster", thrift.BOOL, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.OfflineCluster); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *EmbeddingRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetExtra() {
		if err = oprot.WriteFieldBegin("Extra", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Extra); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *EmbeddingRequest) writeField201(oprot thrift.TProtocol) (err error) {
	if p.IsSetHTTPRequest() {
		if err = oprot.WriteFieldBegin("HTTPRequest", thrift.STRUCT, 201); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.HTTPRequest.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 end error: ", p), err)
}
func (p *EmbeddingRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *EmbeddingRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EmbeddingRequest(%+v)", *p)

}

func (p *EmbeddingRequest) DeepEqual(ano *EmbeddingRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.DatastoreName) {
		return false
	}
	if !p.Field2DeepEqual(ano.EmbeddingContents) {
		return false
	}
	if !p.Field3DeepEqual(ano.OfflineCluster) {
		return false
	}
	if !p.Field4DeepEqual(ano.Extra) {
		return false
	}
	if !p.Field201DeepEqual(ano.HTTPRequest) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *EmbeddingRequest) Field1DeepEqual(src string) bool {

	if strings.Compare(p.DatastoreName, src) != 0 {
		return false
	}
	return true
}
func (p *EmbeddingRequest) Field2DeepEqual(src []string) bool {

	if len(p.EmbeddingContents) != len(src) {
		return false
	}
	for i, v := range p.EmbeddingContents {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *EmbeddingRequest) Field3DeepEqual(src bool) bool {

	if p.OfflineCluster != src {
		return false
	}
	return true
}
func (p *EmbeddingRequest) Field4DeepEqual(src *string) bool {

	if p.Extra == src {
		return true
	} else if p.Extra == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Extra, *src) != 0 {
		return false
	}
	return true
}
func (p *EmbeddingRequest) Field201DeepEqual(src *common.HTTPRequest) bool {

	if !p.HTTPRequest.DeepEqual(src) {
		return false
	}
	return true
}
func (p *EmbeddingRequest) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type EmbeddingResponse struct {
	Embeddings     []*knowledgebase.Embedding `thrift:"Embeddings,1" frugal:"1,default,list<knowledgebase.Embedding>" json:"embeddings"`
	EmbeddingModel string                     `thrift:"EmbeddingModel,2" frugal:"2,default,string" json:"embedding_model"`
	Code           *common.ErrorCode          `thrift:"Code,201,optional" frugal:"201,optional,ErrorCode" json:"code"`
	Message        *string                    `thrift:"Message,202,optional" frugal:"202,optional,string" json:"message"`
	HTTPCode       *int32                     `thrift:"HTTPCode,203,optional" frugal:"203,optional,i32" json:"HTTPCode,omitempty"`
	HTTPResponse   *common.HTTPResponse       `thrift:"HTTPResponse,204,optional" frugal:"204,optional,common.HTTPResponse" json:"HTTPResponse,omitempty"`
}

func NewEmbeddingResponse() *EmbeddingResponse {
	return &EmbeddingResponse{}
}

func (p *EmbeddingResponse) InitDefault() {
}

func (p *EmbeddingResponse) GetEmbeddings() (v []*knowledgebase.Embedding) {
	return p.Embeddings
}

func (p *EmbeddingResponse) GetEmbeddingModel() (v string) {
	return p.EmbeddingModel
}

var EmbeddingResponse_Code_DEFAULT common.ErrorCode

func (p *EmbeddingResponse) GetCode() (v common.ErrorCode) {
	if !p.IsSetCode() {
		return EmbeddingResponse_Code_DEFAULT
	}
	return *p.Code
}

var EmbeddingResponse_Message_DEFAULT string

func (p *EmbeddingResponse) GetMessage() (v string) {
	if !p.IsSetMessage() {
		return EmbeddingResponse_Message_DEFAULT
	}
	return *p.Message
}

var EmbeddingResponse_HTTPCode_DEFAULT int32

func (p *EmbeddingResponse) GetHTTPCode() (v int32) {
	if !p.IsSetHTTPCode() {
		return EmbeddingResponse_HTTPCode_DEFAULT
	}
	return *p.HTTPCode
}

var EmbeddingResponse_HTTPResponse_DEFAULT *common.HTTPResponse

func (p *EmbeddingResponse) GetHTTPResponse() (v *common.HTTPResponse) {
	if !p.IsSetHTTPResponse() {
		return EmbeddingResponse_HTTPResponse_DEFAULT
	}
	return p.HTTPResponse
}
func (p *EmbeddingResponse) SetEmbeddings(val []*knowledgebase.Embedding) {
	p.Embeddings = val
}
func (p *EmbeddingResponse) SetEmbeddingModel(val string) {
	p.EmbeddingModel = val
}
func (p *EmbeddingResponse) SetCode(val *common.ErrorCode) {
	p.Code = val
}
func (p *EmbeddingResponse) SetMessage(val *string) {
	p.Message = val
}
func (p *EmbeddingResponse) SetHTTPCode(val *int32) {
	p.HTTPCode = val
}
func (p *EmbeddingResponse) SetHTTPResponse(val *common.HTTPResponse) {
	p.HTTPResponse = val
}

var fieldIDToName_EmbeddingResponse = map[int16]string{
	1:   "Embeddings",
	2:   "EmbeddingModel",
	201: "Code",
	202: "Message",
	203: "HTTPCode",
	204: "HTTPResponse",
}

func (p *EmbeddingResponse) IsSetCode() bool {
	return p.Code != nil
}

func (p *EmbeddingResponse) IsSetMessage() bool {
	return p.Message != nil
}

func (p *EmbeddingResponse) IsSetHTTPCode() bool {
	return p.HTTPCode != nil
}

func (p *EmbeddingResponse) IsSetHTTPResponse() bool {
	return p.HTTPResponse != nil
}

func (p *EmbeddingResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 201:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField201(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 202:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField202(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 203:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField203(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 204:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField204(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_EmbeddingResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *EmbeddingResponse) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*knowledgebase.Embedding, 0, size)
	values := make([]knowledgebase.Embedding, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Embeddings = _field
	return nil
}
func (p *EmbeddingResponse) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EmbeddingModel = _field
	return nil
}
func (p *EmbeddingResponse) ReadField201(iprot thrift.TProtocol) error {

	var _field *common.ErrorCode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := common.ErrorCode(v)
		_field = &tmp
	}
	p.Code = _field
	return nil
}
func (p *EmbeddingResponse) ReadField202(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Message = _field
	return nil
}
func (p *EmbeddingResponse) ReadField203(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.HTTPCode = _field
	return nil
}
func (p *EmbeddingResponse) ReadField204(iprot thrift.TProtocol) error {
	_field := common.NewHTTPResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.HTTPResponse = _field
	return nil
}

func (p *EmbeddingResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("EmbeddingResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField201(oprot); err != nil {
			fieldId = 201
			goto WriteFieldError
		}
		if err = p.writeField202(oprot); err != nil {
			fieldId = 202
			goto WriteFieldError
		}
		if err = p.writeField203(oprot); err != nil {
			fieldId = 203
			goto WriteFieldError
		}
		if err = p.writeField204(oprot); err != nil {
			fieldId = 204
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *EmbeddingResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Embeddings", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Embeddings)); err != nil {
		return err
	}
	for _, v := range p.Embeddings {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *EmbeddingResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EmbeddingModel", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EmbeddingModel); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *EmbeddingResponse) writeField201(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err = oprot.WriteFieldBegin("Code", thrift.I32, 201); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Code)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 end error: ", p), err)
}
func (p *EmbeddingResponse) writeField202(oprot thrift.TProtocol) (err error) {
	if p.IsSetMessage() {
		if err = oprot.WriteFieldBegin("Message", thrift.STRING, 202); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Message); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 202 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 202 end error: ", p), err)
}
func (p *EmbeddingResponse) writeField203(oprot thrift.TProtocol) (err error) {
	if p.IsSetHTTPCode() {
		if err = oprot.WriteFieldBegin("HTTPCode", thrift.I32, 203); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.HTTPCode); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 203 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 203 end error: ", p), err)
}
func (p *EmbeddingResponse) writeField204(oprot thrift.TProtocol) (err error) {
	if p.IsSetHTTPResponse() {
		if err = oprot.WriteFieldBegin("HTTPResponse", thrift.STRUCT, 204); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.HTTPResponse.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 204 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 204 end error: ", p), err)
}

func (p *EmbeddingResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EmbeddingResponse(%+v)", *p)

}

func (p *EmbeddingResponse) DeepEqual(ano *EmbeddingResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Embeddings) {
		return false
	}
	if !p.Field2DeepEqual(ano.EmbeddingModel) {
		return false
	}
	if !p.Field201DeepEqual(ano.Code) {
		return false
	}
	if !p.Field202DeepEqual(ano.Message) {
		return false
	}
	if !p.Field203DeepEqual(ano.HTTPCode) {
		return false
	}
	if !p.Field204DeepEqual(ano.HTTPResponse) {
		return false
	}
	return true
}

func (p *EmbeddingResponse) Field1DeepEqual(src []*knowledgebase.Embedding) bool {

	if len(p.Embeddings) != len(src) {
		return false
	}
	for i, v := range p.Embeddings {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *EmbeddingResponse) Field2DeepEqual(src string) bool {

	if strings.Compare(p.EmbeddingModel, src) != 0 {
		return false
	}
	return true
}
func (p *EmbeddingResponse) Field201DeepEqual(src *common.ErrorCode) bool {

	if p.Code == src {
		return true
	} else if p.Code == nil || src == nil {
		return false
	}
	if *p.Code != *src {
		return false
	}
	return true
}
func (p *EmbeddingResponse) Field202DeepEqual(src *string) bool {

	if p.Message == src {
		return true
	} else if p.Message == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Message, *src) != 0 {
		return false
	}
	return true
}
func (p *EmbeddingResponse) Field203DeepEqual(src *int32) bool {

	if p.HTTPCode == src {
		return true
	} else if p.HTTPCode == nil || src == nil {
		return false
	}
	if *p.HTTPCode != *src {
		return false
	}
	return true
}
func (p *EmbeddingResponse) Field204DeepEqual(src *common.HTTPResponse) bool {

	if !p.HTTPResponse.DeepEqual(src) {
		return false
	}
	return true
}
