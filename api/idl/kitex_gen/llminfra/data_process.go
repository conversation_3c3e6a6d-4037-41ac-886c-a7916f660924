// Code generated by thriftgo (0.3.19). DO NOT EDIT.

package llminfra

import (
	"database/sql"
	"database/sql/driver"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type DataProcessStageStatus int64

const (
	DataProcessStageStatus_Unkown    DataProcessStageStatus = 0
	DataProcessStageStatus_Success   DataProcessStageStatus = 1
	DataProcessStageStatus_Failed    DataProcessStageStatus = 2
	DataProcessStageStatus_Canceling DataProcessStageStatus = 3
	DataProcessStageStatus_Canceled  DataProcessStageStatus = 4
	DataProcessStageStatus_Created   DataProcessStageStatus = 5
	DataProcessStageStatus_Running   DataProcessStageStatus = 6
)

func (p DataProcessStageStatus) String() string {
	switch p {
	case DataProcessStageStatus_Unkown:
		return "Unkown"
	case DataProcessStageStatus_Success:
		return "Success"
	case DataProcessStageStatus_Failed:
		return "Failed"
	case DataProcessStageStatus_Canceling:
		return "Canceling"
	case DataProcessStageStatus_Canceled:
		return "Canceled"
	case DataProcessStageStatus_Created:
		return "Created"
	case DataProcessStageStatus_Running:
		return "Running"
	}
	return "<UNSET>"
}

func DataProcessStageStatusFromString(s string) (DataProcessStageStatus, error) {
	switch s {
	case "Unkown":
		return DataProcessStageStatus_Unkown, nil
	case "Success":
		return DataProcessStageStatus_Success, nil
	case "Failed":
		return DataProcessStageStatus_Failed, nil
	case "Canceling":
		return DataProcessStageStatus_Canceling, nil
	case "Canceled":
		return DataProcessStageStatus_Canceled, nil
	case "Created":
		return DataProcessStageStatus_Created, nil
	case "Running":
		return DataProcessStageStatus_Running, nil
	}
	return DataProcessStageStatus(0), fmt.Errorf("not a valid DataProcessStageStatus string")
}

func DataProcessStageStatusPtr(v DataProcessStageStatus) *DataProcessStageStatus { return &v }
func (p *DataProcessStageStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = DataProcessStageStatus(result.Int64)
	return
}

func (p *DataProcessStageStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type DatasetProcessStatus int64

const (
	DatasetProcessStatus_Unkown              DatasetProcessStatus = 0
	DatasetProcessStatus_Success             DatasetProcessStatus = 1
	DatasetProcessStatus_Failed              DatasetProcessStatus = 2
	DatasetProcessStatus_Canceling           DatasetProcessStatus = 3
	DatasetProcessStatus_Canceled            DatasetProcessStatus = 4
	DatasetProcessStatus_Created             DatasetProcessStatus = 5
	DatasetProcessStatus_InputDatasetCopying DatasetProcessStatus = 6
	DatasetProcessStatus_ReadyToProcess      DatasetProcessStatus = 7
	DatasetProcessStatus_Processing          DatasetProcessStatus = 8
)

func (p DatasetProcessStatus) String() string {
	switch p {
	case DatasetProcessStatus_Unkown:
		return "Unkown"
	case DatasetProcessStatus_Success:
		return "Success"
	case DatasetProcessStatus_Failed:
		return "Failed"
	case DatasetProcessStatus_Canceling:
		return "Canceling"
	case DatasetProcessStatus_Canceled:
		return "Canceled"
	case DatasetProcessStatus_Created:
		return "Created"
	case DatasetProcessStatus_InputDatasetCopying:
		return "InputDatasetCopying"
	case DatasetProcessStatus_ReadyToProcess:
		return "ReadyToProcess"
	case DatasetProcessStatus_Processing:
		return "Processing"
	}
	return "<UNSET>"
}

func DatasetProcessStatusFromString(s string) (DatasetProcessStatus, error) {
	switch s {
	case "Unkown":
		return DatasetProcessStatus_Unkown, nil
	case "Success":
		return DatasetProcessStatus_Success, nil
	case "Failed":
		return DatasetProcessStatus_Failed, nil
	case "Canceling":
		return DatasetProcessStatus_Canceling, nil
	case "Canceled":
		return DatasetProcessStatus_Canceled, nil
	case "Created":
		return DatasetProcessStatus_Created, nil
	case "InputDatasetCopying":
		return DatasetProcessStatus_InputDatasetCopying, nil
	case "ReadyToProcess":
		return DatasetProcessStatus_ReadyToProcess, nil
	case "Processing":
		return DatasetProcessStatus_Processing, nil
	}
	return DatasetProcessStatus(0), fmt.Errorf("not a valid DatasetProcessStatus string")
}

func DatasetProcessStatusPtr(v DatasetProcessStatus) *DatasetProcessStatus { return &v }
func (p *DatasetProcessStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = DatasetProcessStatus(result.Int64)
	return
}

func (p *DatasetProcessStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type DataProcessStepType int64

const (
	DataProcessStepType_Unkown          DataProcessStepType = 0
	DataProcessStepType_DataCleanByFaaS DataProcessStepType = 1
	DataProcessStepType_ModelInference  DataProcessStepType = 2
)

func (p DataProcessStepType) String() string {
	switch p {
	case DataProcessStepType_Unkown:
		return "Unkown"
	case DataProcessStepType_DataCleanByFaaS:
		return "DataCleanByFaaS"
	case DataProcessStepType_ModelInference:
		return "ModelInference"
	}
	return "<UNSET>"
}

func DataProcessStepTypeFromString(s string) (DataProcessStepType, error) {
	switch s {
	case "Unkown":
		return DataProcessStepType_Unkown, nil
	case "DataCleanByFaaS":
		return DataProcessStepType_DataCleanByFaaS, nil
	case "ModelInference":
		return DataProcessStepType_ModelInference, nil
	}
	return DataProcessStepType(0), fmt.Errorf("not a valid DataProcessStepType string")
}

func DataProcessStepTypePtr(v DataProcessStepType) *DataProcessStepType { return &v }
func (p *DataProcessStepType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = DataProcessStepType(result.Int64)
	return
}

func (p *DataProcessStepType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type DataProcessStage struct {
	ID          int64                   `thrift:"ID,1,required" frugal:"1,required,i64" json:"id"`
	CreatedAt   int64                   `thrift:"CreatedAt,2,required" frugal:"2,required,i64" json:"created_at"`
	UpdatedAt   int64                   `thrift:"UpdatedAt,3,required" frugal:"3,required,i64" json:"updated_at"`
	DeletedAt   *int64                  `thrift:"DeletedAt,4,optional" frugal:"4,optional,i64" json:"deleted_at,omitempty"`
	Name        string                  `thrift:"Name,5,required" frugal:"5,required,string" json:"name"`
	Desc        string                  `thrift:"desc,6,required" frugal:"6,required,string" json:"desc"`
	Input       *DataProcessStageInput  `thrift:"Input,7,required" frugal:"7,required,DataProcessStageInput" json:"input"`
	StageConfig *DataProcessStageConfig `thrift:"StageConfig,8,required" frugal:"8,required,DataProcessStageConfig" json:"stage_config"`
	Output      *DataProcessStageOutput `thrift:"Output,9,required" frugal:"9,required,DataProcessStageOutput" json:"output"`
	Status      DataProcessStageStatus  `thrift:"Status,10,required" frugal:"10,required,DataProcessStageStatus" json:"status"`
	IsSkipped   *bool                   `thrift:"IsSkipped,11,optional" frugal:"11,optional,bool" json:"is_skipped"`
	FailType    *FailType               `thrift:"FailType,12,optional" frugal:"12,optional,FailType" json:"fail_type"`
	FailMsg     *string                 `thrift:"FailMsg,13,optional" frugal:"13,optional,string" json:"fail_msg"`
	Creator     *string                 `thrift:"Creator,14,optional" frugal:"14,optional,string" json:"creator"`
	Updater     *string                 `thrift:"Updater,15,optional" frugal:"15,optional,string" json:"updater"`
	Tags        []*Tag                  `thrift:"Tags,16,optional" frugal:"16,optional,list<Tag>" json:"tags,omitempty"`
}

func NewDataProcessStage() *DataProcessStage {
	return &DataProcessStage{}
}

func (p *DataProcessStage) InitDefault() {
}

func (p *DataProcessStage) GetID() (v int64) {
	return p.ID
}

func (p *DataProcessStage) GetCreatedAt() (v int64) {
	return p.CreatedAt
}

func (p *DataProcessStage) GetUpdatedAt() (v int64) {
	return p.UpdatedAt
}

var DataProcessStage_DeletedAt_DEFAULT int64

func (p *DataProcessStage) GetDeletedAt() (v int64) {
	if !p.IsSetDeletedAt() {
		return DataProcessStage_DeletedAt_DEFAULT
	}
	return *p.DeletedAt
}

func (p *DataProcessStage) GetName() (v string) {
	return p.Name
}

func (p *DataProcessStage) GetDesc() (v string) {
	return p.Desc
}

var DataProcessStage_Input_DEFAULT *DataProcessStageInput

func (p *DataProcessStage) GetInput() (v *DataProcessStageInput) {
	if !p.IsSetInput() {
		return DataProcessStage_Input_DEFAULT
	}
	return p.Input
}

var DataProcessStage_StageConfig_DEFAULT *DataProcessStageConfig

func (p *DataProcessStage) GetStageConfig() (v *DataProcessStageConfig) {
	if !p.IsSetStageConfig() {
		return DataProcessStage_StageConfig_DEFAULT
	}
	return p.StageConfig
}

var DataProcessStage_Output_DEFAULT *DataProcessStageOutput

func (p *DataProcessStage) GetOutput() (v *DataProcessStageOutput) {
	if !p.IsSetOutput() {
		return DataProcessStage_Output_DEFAULT
	}
	return p.Output
}

func (p *DataProcessStage) GetStatus() (v DataProcessStageStatus) {
	return p.Status
}

var DataProcessStage_IsSkipped_DEFAULT bool

func (p *DataProcessStage) GetIsSkipped() (v bool) {
	if !p.IsSetIsSkipped() {
		return DataProcessStage_IsSkipped_DEFAULT
	}
	return *p.IsSkipped
}

var DataProcessStage_FailType_DEFAULT FailType

func (p *DataProcessStage) GetFailType() (v FailType) {
	if !p.IsSetFailType() {
		return DataProcessStage_FailType_DEFAULT
	}
	return *p.FailType
}

var DataProcessStage_FailMsg_DEFAULT string

func (p *DataProcessStage) GetFailMsg() (v string) {
	if !p.IsSetFailMsg() {
		return DataProcessStage_FailMsg_DEFAULT
	}
	return *p.FailMsg
}

var DataProcessStage_Creator_DEFAULT string

func (p *DataProcessStage) GetCreator() (v string) {
	if !p.IsSetCreator() {
		return DataProcessStage_Creator_DEFAULT
	}
	return *p.Creator
}

var DataProcessStage_Updater_DEFAULT string

func (p *DataProcessStage) GetUpdater() (v string) {
	if !p.IsSetUpdater() {
		return DataProcessStage_Updater_DEFAULT
	}
	return *p.Updater
}

var DataProcessStage_Tags_DEFAULT []*Tag

func (p *DataProcessStage) GetTags() (v []*Tag) {
	if !p.IsSetTags() {
		return DataProcessStage_Tags_DEFAULT
	}
	return p.Tags
}
func (p *DataProcessStage) SetID(val int64) {
	p.ID = val
}
func (p *DataProcessStage) SetCreatedAt(val int64) {
	p.CreatedAt = val
}
func (p *DataProcessStage) SetUpdatedAt(val int64) {
	p.UpdatedAt = val
}
func (p *DataProcessStage) SetDeletedAt(val *int64) {
	p.DeletedAt = val
}
func (p *DataProcessStage) SetName(val string) {
	p.Name = val
}
func (p *DataProcessStage) SetDesc(val string) {
	p.Desc = val
}
func (p *DataProcessStage) SetInput(val *DataProcessStageInput) {
	p.Input = val
}
func (p *DataProcessStage) SetStageConfig(val *DataProcessStageConfig) {
	p.StageConfig = val
}
func (p *DataProcessStage) SetOutput(val *DataProcessStageOutput) {
	p.Output = val
}
func (p *DataProcessStage) SetStatus(val DataProcessStageStatus) {
	p.Status = val
}
func (p *DataProcessStage) SetIsSkipped(val *bool) {
	p.IsSkipped = val
}
func (p *DataProcessStage) SetFailType(val *FailType) {
	p.FailType = val
}
func (p *DataProcessStage) SetFailMsg(val *string) {
	p.FailMsg = val
}
func (p *DataProcessStage) SetCreator(val *string) {
	p.Creator = val
}
func (p *DataProcessStage) SetUpdater(val *string) {
	p.Updater = val
}
func (p *DataProcessStage) SetTags(val []*Tag) {
	p.Tags = val
}

var fieldIDToName_DataProcessStage = map[int16]string{
	1:  "ID",
	2:  "CreatedAt",
	3:  "UpdatedAt",
	4:  "DeletedAt",
	5:  "Name",
	6:  "desc",
	7:  "Input",
	8:  "StageConfig",
	9:  "Output",
	10: "Status",
	11: "IsSkipped",
	12: "FailType",
	13: "FailMsg",
	14: "Creator",
	15: "Updater",
	16: "Tags",
}

func (p *DataProcessStage) IsSetDeletedAt() bool {
	return p.DeletedAt != nil
}

func (p *DataProcessStage) IsSetInput() bool {
	return p.Input != nil
}

func (p *DataProcessStage) IsSetStageConfig() bool {
	return p.StageConfig != nil
}

func (p *DataProcessStage) IsSetOutput() bool {
	return p.Output != nil
}

func (p *DataProcessStage) IsSetIsSkipped() bool {
	return p.IsSkipped != nil
}

func (p *DataProcessStage) IsSetFailType() bool {
	return p.FailType != nil
}

func (p *DataProcessStage) IsSetFailMsg() bool {
	return p.FailMsg != nil
}

func (p *DataProcessStage) IsSetCreator() bool {
	return p.Creator != nil
}

func (p *DataProcessStage) IsSetUpdater() bool {
	return p.Updater != nil
}

func (p *DataProcessStage) IsSetTags() bool {
	return p.Tags != nil
}

func (p *DataProcessStage) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetID bool = false
	var issetCreatedAt bool = false
	var issetUpdatedAt bool = false
	var issetName bool = false
	var issetDesc bool = false
	var issetInput bool = false
	var issetStageConfig bool = false
	var issetOutput bool = false
	var issetStatus bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreatedAt = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetUpdatedAt = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetDesc = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetInput = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetStageConfig = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
				issetOutput = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
				issetStatus = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetCreatedAt {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetUpdatedAt {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetName {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetDesc {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetInput {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetStageConfig {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetOutput {
		fieldId = 9
		goto RequiredFieldNotSetError
	}

	if !issetStatus {
		fieldId = 10
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DataProcessStage[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DataProcessStage[fieldId]))
}

func (p *DataProcessStage) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ID = _field
	return nil
}
func (p *DataProcessStage) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreatedAt = _field
	return nil
}
func (p *DataProcessStage) ReadField3(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UpdatedAt = _field
	return nil
}
func (p *DataProcessStage) ReadField4(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DeletedAt = _field
	return nil
}
func (p *DataProcessStage) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *DataProcessStage) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Desc = _field
	return nil
}
func (p *DataProcessStage) ReadField7(iprot thrift.TProtocol) error {
	_field := NewDataProcessStageInput()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Input = _field
	return nil
}
func (p *DataProcessStage) ReadField8(iprot thrift.TProtocol) error {
	_field := NewDataProcessStageConfig()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.StageConfig = _field
	return nil
}
func (p *DataProcessStage) ReadField9(iprot thrift.TProtocol) error {
	_field := NewDataProcessStageOutput()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Output = _field
	return nil
}
func (p *DataProcessStage) ReadField10(iprot thrift.TProtocol) error {

	var _field DataProcessStageStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = DataProcessStageStatus(v)
	}
	p.Status = _field
	return nil
}
func (p *DataProcessStage) ReadField11(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.IsSkipped = _field
	return nil
}
func (p *DataProcessStage) ReadField12(iprot thrift.TProtocol) error {

	var _field *FailType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := FailType(v)
		_field = &tmp
	}
	p.FailType = _field
	return nil
}
func (p *DataProcessStage) ReadField13(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FailMsg = _field
	return nil
}
func (p *DataProcessStage) ReadField14(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Creator = _field
	return nil
}
func (p *DataProcessStage) ReadField15(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Updater = _field
	return nil
}
func (p *DataProcessStage) ReadField16(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*Tag, 0, size)
	values := make([]Tag, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Tags = _field
	return nil
}

func (p *DataProcessStage) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DataProcessStage"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DataProcessStage) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ID", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *DataProcessStage) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreatedAt", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.CreatedAt); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *DataProcessStage) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UpdatedAt", thrift.I64, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.UpdatedAt); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *DataProcessStage) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetDeletedAt() {
		if err = oprot.WriteFieldBegin("DeletedAt", thrift.I64, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.DeletedAt); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *DataProcessStage) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Name", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *DataProcessStage) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("desc", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Desc); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *DataProcessStage) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Input", thrift.STRUCT, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Input.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *DataProcessStage) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StageConfig", thrift.STRUCT, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.StageConfig.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *DataProcessStage) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Output", thrift.STRUCT, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Output.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *DataProcessStage) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Status", thrift.I32, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *DataProcessStage) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetIsSkipped() {
		if err = oprot.WriteFieldBegin("IsSkipped", thrift.BOOL, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.IsSkipped); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *DataProcessStage) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetFailType() {
		if err = oprot.WriteFieldBegin("FailType", thrift.I32, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.FailType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}
func (p *DataProcessStage) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetFailMsg() {
		if err = oprot.WriteFieldBegin("FailMsg", thrift.STRING, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.FailMsg); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}
func (p *DataProcessStage) writeField14(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreator() {
		if err = oprot.WriteFieldBegin("Creator", thrift.STRING, 14); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Creator); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}
func (p *DataProcessStage) writeField15(oprot thrift.TProtocol) (err error) {
	if p.IsSetUpdater() {
		if err = oprot.WriteFieldBegin("Updater", thrift.STRING, 15); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Updater); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}
func (p *DataProcessStage) writeField16(oprot thrift.TProtocol) (err error) {
	if p.IsSetTags() {
		if err = oprot.WriteFieldBegin("Tags", thrift.LIST, 16); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Tags)); err != nil {
			return err
		}
		for _, v := range p.Tags {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}

func (p *DataProcessStage) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DataProcessStage(%+v)", *p)

}

func (p *DataProcessStage) DeepEqual(ano *DataProcessStage) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ID) {
		return false
	}
	if !p.Field2DeepEqual(ano.CreatedAt) {
		return false
	}
	if !p.Field3DeepEqual(ano.UpdatedAt) {
		return false
	}
	if !p.Field4DeepEqual(ano.DeletedAt) {
		return false
	}
	if !p.Field5DeepEqual(ano.Name) {
		return false
	}
	if !p.Field6DeepEqual(ano.Desc) {
		return false
	}
	if !p.Field7DeepEqual(ano.Input) {
		return false
	}
	if !p.Field8DeepEqual(ano.StageConfig) {
		return false
	}
	if !p.Field9DeepEqual(ano.Output) {
		return false
	}
	if !p.Field10DeepEqual(ano.Status) {
		return false
	}
	if !p.Field11DeepEqual(ano.IsSkipped) {
		return false
	}
	if !p.Field12DeepEqual(ano.FailType) {
		return false
	}
	if !p.Field13DeepEqual(ano.FailMsg) {
		return false
	}
	if !p.Field14DeepEqual(ano.Creator) {
		return false
	}
	if !p.Field15DeepEqual(ano.Updater) {
		return false
	}
	if !p.Field16DeepEqual(ano.Tags) {
		return false
	}
	return true
}

func (p *DataProcessStage) Field1DeepEqual(src int64) bool {

	if p.ID != src {
		return false
	}
	return true
}
func (p *DataProcessStage) Field2DeepEqual(src int64) bool {

	if p.CreatedAt != src {
		return false
	}
	return true
}
func (p *DataProcessStage) Field3DeepEqual(src int64) bool {

	if p.UpdatedAt != src {
		return false
	}
	return true
}
func (p *DataProcessStage) Field4DeepEqual(src *int64) bool {

	if p.DeletedAt == src {
		return true
	} else if p.DeletedAt == nil || src == nil {
		return false
	}
	if *p.DeletedAt != *src {
		return false
	}
	return true
}
func (p *DataProcessStage) Field5DeepEqual(src string) bool {

	if strings.Compare(p.Name, src) != 0 {
		return false
	}
	return true
}
func (p *DataProcessStage) Field6DeepEqual(src string) bool {

	if strings.Compare(p.Desc, src) != 0 {
		return false
	}
	return true
}
func (p *DataProcessStage) Field7DeepEqual(src *DataProcessStageInput) bool {

	if !p.Input.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DataProcessStage) Field8DeepEqual(src *DataProcessStageConfig) bool {

	if !p.StageConfig.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DataProcessStage) Field9DeepEqual(src *DataProcessStageOutput) bool {

	if !p.Output.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DataProcessStage) Field10DeepEqual(src DataProcessStageStatus) bool {

	if p.Status != src {
		return false
	}
	return true
}
func (p *DataProcessStage) Field11DeepEqual(src *bool) bool {

	if p.IsSkipped == src {
		return true
	} else if p.IsSkipped == nil || src == nil {
		return false
	}
	if *p.IsSkipped != *src {
		return false
	}
	return true
}
func (p *DataProcessStage) Field12DeepEqual(src *FailType) bool {

	if p.FailType == src {
		return true
	} else if p.FailType == nil || src == nil {
		return false
	}
	if *p.FailType != *src {
		return false
	}
	return true
}
func (p *DataProcessStage) Field13DeepEqual(src *string) bool {

	if p.FailMsg == src {
		return true
	} else if p.FailMsg == nil || src == nil {
		return false
	}
	if strings.Compare(*p.FailMsg, *src) != 0 {
		return false
	}
	return true
}
func (p *DataProcessStage) Field14DeepEqual(src *string) bool {

	if p.Creator == src {
		return true
	} else if p.Creator == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Creator, *src) != 0 {
		return false
	}
	return true
}
func (p *DataProcessStage) Field15DeepEqual(src *string) bool {

	if p.Updater == src {
		return true
	} else if p.Updater == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Updater, *src) != 0 {
		return false
	}
	return true
}
func (p *DataProcessStage) Field16DeepEqual(src []*Tag) bool {

	if len(p.Tags) != len(src) {
		return false
	}
	for i, v := range p.Tags {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DataProcessStageInput struct {
	Datasets []*DataProcessDataset `thrift:"Datasets,1,optional" frugal:"1,optional,list<DataProcessDataset>" json:"datasets,omitempty"`
}

func NewDataProcessStageInput() *DataProcessStageInput {
	return &DataProcessStageInput{}
}

func (p *DataProcessStageInput) InitDefault() {
}

var DataProcessStageInput_Datasets_DEFAULT []*DataProcessDataset

func (p *DataProcessStageInput) GetDatasets() (v []*DataProcessDataset) {
	if !p.IsSetDatasets() {
		return DataProcessStageInput_Datasets_DEFAULT
	}
	return p.Datasets
}
func (p *DataProcessStageInput) SetDatasets(val []*DataProcessDataset) {
	p.Datasets = val
}

var fieldIDToName_DataProcessStageInput = map[int16]string{
	1: "Datasets",
}

func (p *DataProcessStageInput) IsSetDatasets() bool {
	return p.Datasets != nil
}

func (p *DataProcessStageInput) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DataProcessStageInput[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DataProcessStageInput) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*DataProcessDataset, 0, size)
	values := make([]DataProcessDataset, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Datasets = _field
	return nil
}

func (p *DataProcessStageInput) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DataProcessStageInput"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DataProcessStageInput) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetDatasets() {
		if err = oprot.WriteFieldBegin("Datasets", thrift.LIST, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Datasets)); err != nil {
			return err
		}
		for _, v := range p.Datasets {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DataProcessStageInput) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DataProcessStageInput(%+v)", *p)

}

func (p *DataProcessStageInput) DeepEqual(ano *DataProcessStageInput) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Datasets) {
		return false
	}
	return true
}

func (p *DataProcessStageInput) Field1DeepEqual(src []*DataProcessDataset) bool {

	if len(p.Datasets) != len(src) {
		return false
	}
	for i, v := range p.Datasets {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DataProcessStageConfig struct {
	FilterRule *DataProcessFilterRule `thrift:"FilterRule,1,optional" frugal:"1,optional,DataProcessFilterRule" json:"filter_rule,omitempty"`
	Steps      []*DataProcessStep     `thrift:"Steps,2,optional" frugal:"2,optional,list<DataProcessStep>" json:"steps,omitempty"`
}

func NewDataProcessStageConfig() *DataProcessStageConfig {
	return &DataProcessStageConfig{}
}

func (p *DataProcessStageConfig) InitDefault() {
}

var DataProcessStageConfig_FilterRule_DEFAULT *DataProcessFilterRule

func (p *DataProcessStageConfig) GetFilterRule() (v *DataProcessFilterRule) {
	if !p.IsSetFilterRule() {
		return DataProcessStageConfig_FilterRule_DEFAULT
	}
	return p.FilterRule
}

var DataProcessStageConfig_Steps_DEFAULT []*DataProcessStep

func (p *DataProcessStageConfig) GetSteps() (v []*DataProcessStep) {
	if !p.IsSetSteps() {
		return DataProcessStageConfig_Steps_DEFAULT
	}
	return p.Steps
}
func (p *DataProcessStageConfig) SetFilterRule(val *DataProcessFilterRule) {
	p.FilterRule = val
}
func (p *DataProcessStageConfig) SetSteps(val []*DataProcessStep) {
	p.Steps = val
}

var fieldIDToName_DataProcessStageConfig = map[int16]string{
	1: "FilterRule",
	2: "Steps",
}

func (p *DataProcessStageConfig) IsSetFilterRule() bool {
	return p.FilterRule != nil
}

func (p *DataProcessStageConfig) IsSetSteps() bool {
	return p.Steps != nil
}

func (p *DataProcessStageConfig) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DataProcessStageConfig[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DataProcessStageConfig) ReadField1(iprot thrift.TProtocol) error {
	_field := NewDataProcessFilterRule()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.FilterRule = _field
	return nil
}
func (p *DataProcessStageConfig) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*DataProcessStep, 0, size)
	values := make([]DataProcessStep, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Steps = _field
	return nil
}

func (p *DataProcessStageConfig) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DataProcessStageConfig"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DataProcessStageConfig) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetFilterRule() {
		if err = oprot.WriteFieldBegin("FilterRule", thrift.STRUCT, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.FilterRule.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *DataProcessStageConfig) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetSteps() {
		if err = oprot.WriteFieldBegin("Steps", thrift.LIST, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Steps)); err != nil {
			return err
		}
		for _, v := range p.Steps {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DataProcessStageConfig) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DataProcessStageConfig(%+v)", *p)

}

func (p *DataProcessStageConfig) DeepEqual(ano *DataProcessStageConfig) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.FilterRule) {
		return false
	}
	if !p.Field2DeepEqual(ano.Steps) {
		return false
	}
	return true
}

func (p *DataProcessStageConfig) Field1DeepEqual(src *DataProcessFilterRule) bool {

	if !p.FilterRule.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DataProcessStageConfig) Field2DeepEqual(src []*DataProcessStep) bool {

	if len(p.Steps) != len(src) {
		return false
	}
	for i, v := range p.Steps {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DataProcessStageOutput struct {
	Datasets         []*DataProcessDataset       `thrift:"Datasets,1,required" frugal:"1,required,list<DataProcessDataset>" json:"datasets"`
	ProcessStepInfos [][]*DatasetProcessStepInfo `thrift:"ProcessStepInfos,2,required" frugal:"2,required,list<list<DatasetProcessStepInfo>>" json:"process_step_infos"`
}

func NewDataProcessStageOutput() *DataProcessStageOutput {
	return &DataProcessStageOutput{}
}

func (p *DataProcessStageOutput) InitDefault() {
}

func (p *DataProcessStageOutput) GetDatasets() (v []*DataProcessDataset) {
	return p.Datasets
}

func (p *DataProcessStageOutput) GetProcessStepInfos() (v [][]*DatasetProcessStepInfo) {
	return p.ProcessStepInfos
}
func (p *DataProcessStageOutput) SetDatasets(val []*DataProcessDataset) {
	p.Datasets = val
}
func (p *DataProcessStageOutput) SetProcessStepInfos(val [][]*DatasetProcessStepInfo) {
	p.ProcessStepInfos = val
}

var fieldIDToName_DataProcessStageOutput = map[int16]string{
	1: "Datasets",
	2: "ProcessStepInfos",
}

func (p *DataProcessStageOutput) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDatasets bool = false
	var issetProcessStepInfos bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDatasets = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetProcessStepInfos = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDatasets {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetProcessStepInfos {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DataProcessStageOutput[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DataProcessStageOutput[fieldId]))
}

func (p *DataProcessStageOutput) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*DataProcessDataset, 0, size)
	values := make([]DataProcessDataset, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Datasets = _field
	return nil
}
func (p *DataProcessStageOutput) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([][]*DatasetProcessStepInfo, 0, size)
	for i := 0; i < size; i++ {
		_, size, err := iprot.ReadListBegin()
		if err != nil {
			return err
		}
		_elem := make([]*DatasetProcessStepInfo, 0, size)
		values := make([]DatasetProcessStepInfo, size)
		for i := 0; i < size; i++ {
			_elem1 := &values[i]
			_elem1.InitDefault()

			if err := _elem1.Read(iprot); err != nil {
				return err
			}

			_elem = append(_elem, _elem1)
		}
		if err := iprot.ReadListEnd(); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ProcessStepInfos = _field
	return nil
}

func (p *DataProcessStageOutput) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DataProcessStageOutput"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DataProcessStageOutput) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Datasets", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Datasets)); err != nil {
		return err
	}
	for _, v := range p.Datasets {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *DataProcessStageOutput) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ProcessStepInfos", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.LIST, len(p.ProcessStepInfos)); err != nil {
		return err
	}
	for _, v := range p.ProcessStepInfos {
		if err := oprot.WriteListBegin(thrift.STRUCT, len(v)); err != nil {
			return err
		}
		for _, v := range v {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DataProcessStageOutput) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DataProcessStageOutput(%+v)", *p)

}

func (p *DataProcessStageOutput) DeepEqual(ano *DataProcessStageOutput) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Datasets) {
		return false
	}
	if !p.Field2DeepEqual(ano.ProcessStepInfos) {
		return false
	}
	return true
}

func (p *DataProcessStageOutput) Field1DeepEqual(src []*DataProcessDataset) bool {

	if len(p.Datasets) != len(src) {
		return false
	}
	for i, v := range p.Datasets {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DataProcessStageOutput) Field2DeepEqual(src [][]*DatasetProcessStepInfo) bool {

	if len(p.ProcessStepInfos) != len(src) {
		return false
	}
	for i, v := range p.ProcessStepInfos {
		_src := src[i]
		if len(v) != len(_src) {
			return false
		}
		for i, v := range v {
			_src1 := _src[i]
			if !v.DeepEqual(_src1) {
				return false
			}
		}
	}
	return true
}

type DatasetProcessStepInfo struct {
	StepName               string                   `thrift:"StepName,1,required" frugal:"1,required,string" json:"step_name"`
	Status                 DatasetProcessStatus     `thrift:"Status,2,required" frugal:"2,required,DatasetProcessStatus" json:"status"`
	OutputDataset          *DataProcessDataset      `thrift:"OutputDataset,3,optional" frugal:"3,optional,DataProcessDataset" json:"output_dataset"`
	ExecMsg                *string                  `thrift:"ExecMsg,4,optional" frugal:"4,optional,string" json:"exec_msg"`
	EvalSubTaskInfos       []*EvalSubTaskInfo       `thrift:"EvalSubTaskInfos,5,optional" frugal:"5,optional,list<EvalSubTaskInfo>" json:"eval_sub_task_infos"`
	DataCleanTaskExecInfos []*DataCleanTaskExecInfo `thrift:"DataCleanTaskExecInfos,6,optional" frugal:"6,optional,list<DataCleanTaskExecInfo>" json:"data_clean_task_exec_infos"`
	RawDataAddress         *string                  `thrift:"RawDataAddress,7,optional" frugal:"7,optional,string" json:"raw_data_address"`
}

func NewDatasetProcessStepInfo() *DatasetProcessStepInfo {
	return &DatasetProcessStepInfo{}
}

func (p *DatasetProcessStepInfo) InitDefault() {
}

func (p *DatasetProcessStepInfo) GetStepName() (v string) {
	return p.StepName
}

func (p *DatasetProcessStepInfo) GetStatus() (v DatasetProcessStatus) {
	return p.Status
}

var DatasetProcessStepInfo_OutputDataset_DEFAULT *DataProcessDataset

func (p *DatasetProcessStepInfo) GetOutputDataset() (v *DataProcessDataset) {
	if !p.IsSetOutputDataset() {
		return DatasetProcessStepInfo_OutputDataset_DEFAULT
	}
	return p.OutputDataset
}

var DatasetProcessStepInfo_ExecMsg_DEFAULT string

func (p *DatasetProcessStepInfo) GetExecMsg() (v string) {
	if !p.IsSetExecMsg() {
		return DatasetProcessStepInfo_ExecMsg_DEFAULT
	}
	return *p.ExecMsg
}

var DatasetProcessStepInfo_EvalSubTaskInfos_DEFAULT []*EvalSubTaskInfo

func (p *DatasetProcessStepInfo) GetEvalSubTaskInfos() (v []*EvalSubTaskInfo) {
	if !p.IsSetEvalSubTaskInfos() {
		return DatasetProcessStepInfo_EvalSubTaskInfos_DEFAULT
	}
	return p.EvalSubTaskInfos
}

var DatasetProcessStepInfo_DataCleanTaskExecInfos_DEFAULT []*DataCleanTaskExecInfo

func (p *DatasetProcessStepInfo) GetDataCleanTaskExecInfos() (v []*DataCleanTaskExecInfo) {
	if !p.IsSetDataCleanTaskExecInfos() {
		return DatasetProcessStepInfo_DataCleanTaskExecInfos_DEFAULT
	}
	return p.DataCleanTaskExecInfos
}

var DatasetProcessStepInfo_RawDataAddress_DEFAULT string

func (p *DatasetProcessStepInfo) GetRawDataAddress() (v string) {
	if !p.IsSetRawDataAddress() {
		return DatasetProcessStepInfo_RawDataAddress_DEFAULT
	}
	return *p.RawDataAddress
}
func (p *DatasetProcessStepInfo) SetStepName(val string) {
	p.StepName = val
}
func (p *DatasetProcessStepInfo) SetStatus(val DatasetProcessStatus) {
	p.Status = val
}
func (p *DatasetProcessStepInfo) SetOutputDataset(val *DataProcessDataset) {
	p.OutputDataset = val
}
func (p *DatasetProcessStepInfo) SetExecMsg(val *string) {
	p.ExecMsg = val
}
func (p *DatasetProcessStepInfo) SetEvalSubTaskInfos(val []*EvalSubTaskInfo) {
	p.EvalSubTaskInfos = val
}
func (p *DatasetProcessStepInfo) SetDataCleanTaskExecInfos(val []*DataCleanTaskExecInfo) {
	p.DataCleanTaskExecInfos = val
}
func (p *DatasetProcessStepInfo) SetRawDataAddress(val *string) {
	p.RawDataAddress = val
}

var fieldIDToName_DatasetProcessStepInfo = map[int16]string{
	1: "StepName",
	2: "Status",
	3: "OutputDataset",
	4: "ExecMsg",
	5: "EvalSubTaskInfos",
	6: "DataCleanTaskExecInfos",
	7: "RawDataAddress",
}

func (p *DatasetProcessStepInfo) IsSetOutputDataset() bool {
	return p.OutputDataset != nil
}

func (p *DatasetProcessStepInfo) IsSetExecMsg() bool {
	return p.ExecMsg != nil
}

func (p *DatasetProcessStepInfo) IsSetEvalSubTaskInfos() bool {
	return p.EvalSubTaskInfos != nil
}

func (p *DatasetProcessStepInfo) IsSetDataCleanTaskExecInfos() bool {
	return p.DataCleanTaskExecInfos != nil
}

func (p *DatasetProcessStepInfo) IsSetRawDataAddress() bool {
	return p.RawDataAddress != nil
}

func (p *DatasetProcessStepInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetStepName bool = false
	var issetStatus bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetStepName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetStatus = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetStepName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetStatus {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetProcessStepInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DatasetProcessStepInfo[fieldId]))
}

func (p *DatasetProcessStepInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StepName = _field
	return nil
}
func (p *DatasetProcessStepInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field DatasetProcessStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = DatasetProcessStatus(v)
	}
	p.Status = _field
	return nil
}
func (p *DatasetProcessStepInfo) ReadField3(iprot thrift.TProtocol) error {
	_field := NewDataProcessDataset()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.OutputDataset = _field
	return nil
}
func (p *DatasetProcessStepInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ExecMsg = _field
	return nil
}
func (p *DatasetProcessStepInfo) ReadField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*EvalSubTaskInfo, 0, size)
	values := make([]EvalSubTaskInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.EvalSubTaskInfos = _field
	return nil
}
func (p *DatasetProcessStepInfo) ReadField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*DataCleanTaskExecInfo, 0, size)
	values := make([]DataCleanTaskExecInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.DataCleanTaskExecInfos = _field
	return nil
}
func (p *DatasetProcessStepInfo) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RawDataAddress = _field
	return nil
}

func (p *DatasetProcessStepInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DatasetProcessStepInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetProcessStepInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StepName", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.StepName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *DatasetProcessStepInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Status", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *DatasetProcessStepInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetOutputDataset() {
		if err = oprot.WriteFieldBegin("OutputDataset", thrift.STRUCT, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.OutputDataset.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *DatasetProcessStepInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetExecMsg() {
		if err = oprot.WriteFieldBegin("ExecMsg", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ExecMsg); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *DatasetProcessStepInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetEvalSubTaskInfos() {
		if err = oprot.WriteFieldBegin("EvalSubTaskInfos", thrift.LIST, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.EvalSubTaskInfos)); err != nil {
			return err
		}
		for _, v := range p.EvalSubTaskInfos {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *DatasetProcessStepInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetDataCleanTaskExecInfos() {
		if err = oprot.WriteFieldBegin("DataCleanTaskExecInfos", thrift.LIST, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.DataCleanTaskExecInfos)); err != nil {
			return err
		}
		for _, v := range p.DataCleanTaskExecInfos {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *DatasetProcessStepInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetRawDataAddress() {
		if err = oprot.WriteFieldBegin("RawDataAddress", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RawDataAddress); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DatasetProcessStepInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetProcessStepInfo(%+v)", *p)

}

func (p *DatasetProcessStepInfo) DeepEqual(ano *DatasetProcessStepInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.StepName) {
		return false
	}
	if !p.Field2DeepEqual(ano.Status) {
		return false
	}
	if !p.Field3DeepEqual(ano.OutputDataset) {
		return false
	}
	if !p.Field4DeepEqual(ano.ExecMsg) {
		return false
	}
	if !p.Field5DeepEqual(ano.EvalSubTaskInfos) {
		return false
	}
	if !p.Field6DeepEqual(ano.DataCleanTaskExecInfos) {
		return false
	}
	if !p.Field7DeepEqual(ano.RawDataAddress) {
		return false
	}
	return true
}

func (p *DatasetProcessStepInfo) Field1DeepEqual(src string) bool {

	if strings.Compare(p.StepName, src) != 0 {
		return false
	}
	return true
}
func (p *DatasetProcessStepInfo) Field2DeepEqual(src DatasetProcessStatus) bool {

	if p.Status != src {
		return false
	}
	return true
}
func (p *DatasetProcessStepInfo) Field3DeepEqual(src *DataProcessDataset) bool {

	if !p.OutputDataset.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DatasetProcessStepInfo) Field4DeepEqual(src *string) bool {

	if p.ExecMsg == src {
		return true
	} else if p.ExecMsg == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ExecMsg, *src) != 0 {
		return false
	}
	return true
}
func (p *DatasetProcessStepInfo) Field5DeepEqual(src []*EvalSubTaskInfo) bool {

	if len(p.EvalSubTaskInfos) != len(src) {
		return false
	}
	for i, v := range p.EvalSubTaskInfos {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DatasetProcessStepInfo) Field6DeepEqual(src []*DataCleanTaskExecInfo) bool {

	if len(p.DataCleanTaskExecInfos) != len(src) {
		return false
	}
	for i, v := range p.DataCleanTaskExecInfos {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DatasetProcessStepInfo) Field7DeepEqual(src *string) bool {

	if p.RawDataAddress == src {
		return true
	} else if p.RawDataAddress == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RawDataAddress, *src) != 0 {
		return false
	}
	return true
}

type DataCleanTaskExecInfo struct {
	LogID    string `thrift:"LogID,1,required" frugal:"1,required,string" json:"log_id"`
	ExecInfo string `thrift:"ExecInfo,2,required" frugal:"2,required,string" json:"exec_info"`
}

func NewDataCleanTaskExecInfo() *DataCleanTaskExecInfo {
	return &DataCleanTaskExecInfo{}
}

func (p *DataCleanTaskExecInfo) InitDefault() {
}

func (p *DataCleanTaskExecInfo) GetLogID() (v string) {
	return p.LogID
}

func (p *DataCleanTaskExecInfo) GetExecInfo() (v string) {
	return p.ExecInfo
}
func (p *DataCleanTaskExecInfo) SetLogID(val string) {
	p.LogID = val
}
func (p *DataCleanTaskExecInfo) SetExecInfo(val string) {
	p.ExecInfo = val
}

var fieldIDToName_DataCleanTaskExecInfo = map[int16]string{
	1: "LogID",
	2: "ExecInfo",
}

func (p *DataCleanTaskExecInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetLogID bool = false
	var issetExecInfo bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetLogID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetExecInfo = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetLogID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetExecInfo {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DataCleanTaskExecInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DataCleanTaskExecInfo[fieldId]))
}

func (p *DataCleanTaskExecInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LogID = _field
	return nil
}
func (p *DataCleanTaskExecInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ExecInfo = _field
	return nil
}

func (p *DataCleanTaskExecInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DataCleanTaskExecInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DataCleanTaskExecInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LogID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.LogID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *DataCleanTaskExecInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ExecInfo", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ExecInfo); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DataCleanTaskExecInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DataCleanTaskExecInfo(%+v)", *p)

}

func (p *DataCleanTaskExecInfo) DeepEqual(ano *DataCleanTaskExecInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.LogID) {
		return false
	}
	if !p.Field2DeepEqual(ano.ExecInfo) {
		return false
	}
	return true
}

func (p *DataCleanTaskExecInfo) Field1DeepEqual(src string) bool {

	if strings.Compare(p.LogID, src) != 0 {
		return false
	}
	return true
}
func (p *DataCleanTaskExecInfo) Field2DeepEqual(src string) bool {

	if strings.Compare(p.ExecInfo, src) != 0 {
		return false
	}
	return true
}

type EvalSubTaskInfo struct {
	EvalTaskID     int64  `thrift:"EvalTaskID,1,required" frugal:"1,required,i64" json:"eval_task_id"`
	SubTaskID      int64  `thrift:"SubTaskID,2,required" frugal:"2,required,i64" json:"eval_sub_task_id"`
	TotalCaseNum   int64  `thrift:"TotalCaseNum,3,required" frugal:"3,required,i64" json:"total_case_num"`
	FinishCaseNum  int64  `thrift:"FinishCaseNum,4,required" frugal:"4,required,i64" json:"finish_case_num"`
	SuccessCaseNum *int64 `thrift:"SuccessCaseNum,5,optional" frugal:"5,optional,i64" json:"success_case_num"`
	FailCaseNum    *int64 `thrift:"FailCaseNum,6,optional" frugal:"6,optional,i64" json:"fail_case_num"`
}

func NewEvalSubTaskInfo() *EvalSubTaskInfo {
	return &EvalSubTaskInfo{}
}

func (p *EvalSubTaskInfo) InitDefault() {
}

func (p *EvalSubTaskInfo) GetEvalTaskID() (v int64) {
	return p.EvalTaskID
}

func (p *EvalSubTaskInfo) GetSubTaskID() (v int64) {
	return p.SubTaskID
}

func (p *EvalSubTaskInfo) GetTotalCaseNum() (v int64) {
	return p.TotalCaseNum
}

func (p *EvalSubTaskInfo) GetFinishCaseNum() (v int64) {
	return p.FinishCaseNum
}

var EvalSubTaskInfo_SuccessCaseNum_DEFAULT int64

func (p *EvalSubTaskInfo) GetSuccessCaseNum() (v int64) {
	if !p.IsSetSuccessCaseNum() {
		return EvalSubTaskInfo_SuccessCaseNum_DEFAULT
	}
	return *p.SuccessCaseNum
}

var EvalSubTaskInfo_FailCaseNum_DEFAULT int64

func (p *EvalSubTaskInfo) GetFailCaseNum() (v int64) {
	if !p.IsSetFailCaseNum() {
		return EvalSubTaskInfo_FailCaseNum_DEFAULT
	}
	return *p.FailCaseNum
}
func (p *EvalSubTaskInfo) SetEvalTaskID(val int64) {
	p.EvalTaskID = val
}
func (p *EvalSubTaskInfo) SetSubTaskID(val int64) {
	p.SubTaskID = val
}
func (p *EvalSubTaskInfo) SetTotalCaseNum(val int64) {
	p.TotalCaseNum = val
}
func (p *EvalSubTaskInfo) SetFinishCaseNum(val int64) {
	p.FinishCaseNum = val
}
func (p *EvalSubTaskInfo) SetSuccessCaseNum(val *int64) {
	p.SuccessCaseNum = val
}
func (p *EvalSubTaskInfo) SetFailCaseNum(val *int64) {
	p.FailCaseNum = val
}

var fieldIDToName_EvalSubTaskInfo = map[int16]string{
	1: "EvalTaskID",
	2: "SubTaskID",
	3: "TotalCaseNum",
	4: "FinishCaseNum",
	5: "SuccessCaseNum",
	6: "FailCaseNum",
}

func (p *EvalSubTaskInfo) IsSetSuccessCaseNum() bool {
	return p.SuccessCaseNum != nil
}

func (p *EvalSubTaskInfo) IsSetFailCaseNum() bool {
	return p.FailCaseNum != nil
}

func (p *EvalSubTaskInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetEvalTaskID bool = false
	var issetSubTaskID bool = false
	var issetTotalCaseNum bool = false
	var issetFinishCaseNum bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetEvalTaskID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetSubTaskID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotalCaseNum = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetFinishCaseNum = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetEvalTaskID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetSubTaskID {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetTotalCaseNum {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetFinishCaseNum {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_EvalSubTaskInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_EvalSubTaskInfo[fieldId]))
}

func (p *EvalSubTaskInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EvalTaskID = _field
	return nil
}
func (p *EvalSubTaskInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SubTaskID = _field
	return nil
}
func (p *EvalSubTaskInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TotalCaseNum = _field
	return nil
}
func (p *EvalSubTaskInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FinishCaseNum = _field
	return nil
}
func (p *EvalSubTaskInfo) ReadField5(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SuccessCaseNum = _field
	return nil
}
func (p *EvalSubTaskInfo) ReadField6(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FailCaseNum = _field
	return nil
}

func (p *EvalSubTaskInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("EvalSubTaskInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *EvalSubTaskInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EvalTaskID", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.EvalTaskID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *EvalSubTaskInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SubTaskID", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.SubTaskID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *EvalSubTaskInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TotalCaseNum", thrift.I64, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.TotalCaseNum); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *EvalSubTaskInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("FinishCaseNum", thrift.I64, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.FinishCaseNum); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *EvalSubTaskInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccessCaseNum() {
		if err = oprot.WriteFieldBegin("SuccessCaseNum", thrift.I64, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.SuccessCaseNum); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *EvalSubTaskInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetFailCaseNum() {
		if err = oprot.WriteFieldBegin("FailCaseNum", thrift.I64, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.FailCaseNum); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *EvalSubTaskInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EvalSubTaskInfo(%+v)", *p)

}

func (p *EvalSubTaskInfo) DeepEqual(ano *EvalSubTaskInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.EvalTaskID) {
		return false
	}
	if !p.Field2DeepEqual(ano.SubTaskID) {
		return false
	}
	if !p.Field3DeepEqual(ano.TotalCaseNum) {
		return false
	}
	if !p.Field4DeepEqual(ano.FinishCaseNum) {
		return false
	}
	if !p.Field5DeepEqual(ano.SuccessCaseNum) {
		return false
	}
	if !p.Field6DeepEqual(ano.FailCaseNum) {
		return false
	}
	return true
}

func (p *EvalSubTaskInfo) Field1DeepEqual(src int64) bool {

	if p.EvalTaskID != src {
		return false
	}
	return true
}
func (p *EvalSubTaskInfo) Field2DeepEqual(src int64) bool {

	if p.SubTaskID != src {
		return false
	}
	return true
}
func (p *EvalSubTaskInfo) Field3DeepEqual(src int64) bool {

	if p.TotalCaseNum != src {
		return false
	}
	return true
}
func (p *EvalSubTaskInfo) Field4DeepEqual(src int64) bool {

	if p.FinishCaseNum != src {
		return false
	}
	return true
}
func (p *EvalSubTaskInfo) Field5DeepEqual(src *int64) bool {

	if p.SuccessCaseNum == src {
		return true
	} else if p.SuccessCaseNum == nil || src == nil {
		return false
	}
	if *p.SuccessCaseNum != *src {
		return false
	}
	return true
}
func (p *EvalSubTaskInfo) Field6DeepEqual(src *int64) bool {

	if p.FailCaseNum == src {
		return true
	} else if p.FailCaseNum == nil || src == nil {
		return false
	}
	if *p.FailCaseNum != *src {
		return false
	}
	return true
}

type DataProcessDataset struct {
	DatasetType   DatasetType `thrift:"DatasetType,1,required" frugal:"1,required,DatasetType" json:"dataset_type"`
	DatasetUniqID int64       `thrift:"DatasetUniqID,2,required" frugal:"2,required,i64" json:"dataset_uniq_id"`
	MaxCaseNum    *int64      `thrift:"MaxCaseNum,3,optional" frugal:"3,optional,i64" json:"max_case_num"`
	DatasetName   *string     `thrift:"DatasetName,4,optional" frugal:"4,optional,string" json:"dataset_name"`
	TotalCaseNum  *int64      `thrift:"TotalCaseNum,5,optional" frugal:"5,optional,i64" json:"total_case_num"`
	FailCaseNum   *int64      `thrift:"FailCaseNum,6,optional" frugal:"6,optional,i64" json:"fail_case_num"`
}

func NewDataProcessDataset() *DataProcessDataset {
	return &DataProcessDataset{}
}

func (p *DataProcessDataset) InitDefault() {
}

func (p *DataProcessDataset) GetDatasetType() (v DatasetType) {
	return p.DatasetType
}

func (p *DataProcessDataset) GetDatasetUniqID() (v int64) {
	return p.DatasetUniqID
}

var DataProcessDataset_MaxCaseNum_DEFAULT int64

func (p *DataProcessDataset) GetMaxCaseNum() (v int64) {
	if !p.IsSetMaxCaseNum() {
		return DataProcessDataset_MaxCaseNum_DEFAULT
	}
	return *p.MaxCaseNum
}

var DataProcessDataset_DatasetName_DEFAULT string

func (p *DataProcessDataset) GetDatasetName() (v string) {
	if !p.IsSetDatasetName() {
		return DataProcessDataset_DatasetName_DEFAULT
	}
	return *p.DatasetName
}

var DataProcessDataset_TotalCaseNum_DEFAULT int64

func (p *DataProcessDataset) GetTotalCaseNum() (v int64) {
	if !p.IsSetTotalCaseNum() {
		return DataProcessDataset_TotalCaseNum_DEFAULT
	}
	return *p.TotalCaseNum
}

var DataProcessDataset_FailCaseNum_DEFAULT int64

func (p *DataProcessDataset) GetFailCaseNum() (v int64) {
	if !p.IsSetFailCaseNum() {
		return DataProcessDataset_FailCaseNum_DEFAULT
	}
	return *p.FailCaseNum
}
func (p *DataProcessDataset) SetDatasetType(val DatasetType) {
	p.DatasetType = val
}
func (p *DataProcessDataset) SetDatasetUniqID(val int64) {
	p.DatasetUniqID = val
}
func (p *DataProcessDataset) SetMaxCaseNum(val *int64) {
	p.MaxCaseNum = val
}
func (p *DataProcessDataset) SetDatasetName(val *string) {
	p.DatasetName = val
}
func (p *DataProcessDataset) SetTotalCaseNum(val *int64) {
	p.TotalCaseNum = val
}
func (p *DataProcessDataset) SetFailCaseNum(val *int64) {
	p.FailCaseNum = val
}

var fieldIDToName_DataProcessDataset = map[int16]string{
	1: "DatasetType",
	2: "DatasetUniqID",
	3: "MaxCaseNum",
	4: "DatasetName",
	5: "TotalCaseNum",
	6: "FailCaseNum",
}

func (p *DataProcessDataset) IsSetMaxCaseNum() bool {
	return p.MaxCaseNum != nil
}

func (p *DataProcessDataset) IsSetDatasetName() bool {
	return p.DatasetName != nil
}

func (p *DataProcessDataset) IsSetTotalCaseNum() bool {
	return p.TotalCaseNum != nil
}

func (p *DataProcessDataset) IsSetFailCaseNum() bool {
	return p.FailCaseNum != nil
}

func (p *DataProcessDataset) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDatasetType bool = false
	var issetDatasetUniqID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDatasetType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDatasetUniqID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDatasetType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDatasetUniqID {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DataProcessDataset[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DataProcessDataset[fieldId]))
}

func (p *DataProcessDataset) ReadField1(iprot thrift.TProtocol) error {

	var _field DatasetType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = DatasetType(v)
	}
	p.DatasetType = _field
	return nil
}
func (p *DataProcessDataset) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DatasetUniqID = _field
	return nil
}
func (p *DataProcessDataset) ReadField3(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.MaxCaseNum = _field
	return nil
}
func (p *DataProcessDataset) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DatasetName = _field
	return nil
}
func (p *DataProcessDataset) ReadField5(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TotalCaseNum = _field
	return nil
}
func (p *DataProcessDataset) ReadField6(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FailCaseNum = _field
	return nil
}

func (p *DataProcessDataset) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DataProcessDataset"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DataProcessDataset) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DatasetType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.DatasetType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *DataProcessDataset) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DatasetUniqID", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.DatasetUniqID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *DataProcessDataset) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetMaxCaseNum() {
		if err = oprot.WriteFieldBegin("MaxCaseNum", thrift.I64, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.MaxCaseNum); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *DataProcessDataset) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetDatasetName() {
		if err = oprot.WriteFieldBegin("DatasetName", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.DatasetName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *DataProcessDataset) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetTotalCaseNum() {
		if err = oprot.WriteFieldBegin("TotalCaseNum", thrift.I64, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.TotalCaseNum); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *DataProcessDataset) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetFailCaseNum() {
		if err = oprot.WriteFieldBegin("FailCaseNum", thrift.I64, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.FailCaseNum); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DataProcessDataset) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DataProcessDataset(%+v)", *p)

}

func (p *DataProcessDataset) DeepEqual(ano *DataProcessDataset) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.DatasetType) {
		return false
	}
	if !p.Field2DeepEqual(ano.DatasetUniqID) {
		return false
	}
	if !p.Field3DeepEqual(ano.MaxCaseNum) {
		return false
	}
	if !p.Field4DeepEqual(ano.DatasetName) {
		return false
	}
	if !p.Field5DeepEqual(ano.TotalCaseNum) {
		return false
	}
	if !p.Field6DeepEqual(ano.FailCaseNum) {
		return false
	}
	return true
}

func (p *DataProcessDataset) Field1DeepEqual(src DatasetType) bool {

	if p.DatasetType != src {
		return false
	}
	return true
}
func (p *DataProcessDataset) Field2DeepEqual(src int64) bool {

	if p.DatasetUniqID != src {
		return false
	}
	return true
}
func (p *DataProcessDataset) Field3DeepEqual(src *int64) bool {

	if p.MaxCaseNum == src {
		return true
	} else if p.MaxCaseNum == nil || src == nil {
		return false
	}
	if *p.MaxCaseNum != *src {
		return false
	}
	return true
}
func (p *DataProcessDataset) Field4DeepEqual(src *string) bool {

	if p.DatasetName == src {
		return true
	} else if p.DatasetName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.DatasetName, *src) != 0 {
		return false
	}
	return true
}
func (p *DataProcessDataset) Field5DeepEqual(src *int64) bool {

	if p.TotalCaseNum == src {
		return true
	} else if p.TotalCaseNum == nil || src == nil {
		return false
	}
	if *p.TotalCaseNum != *src {
		return false
	}
	return true
}
func (p *DataProcessDataset) Field6DeepEqual(src *int64) bool {

	if p.FailCaseNum == src {
		return true
	} else if p.FailCaseNum == nil || src == nil {
		return false
	}
	if *p.FailCaseNum != *src {
		return false
	}
	return true
}

type DataProcessFilterRule struct {
	FilterParams []*FilterParam   `thrift:"FilterParams,1,optional" frugal:"1,optional,list<FilterParam>" json:"filter_params"`
	Statuses     []AnalysisStatus `thrift:"Statuses,2,optional" frugal:"2,optional,list<AnalysisStatus>" json:"analysis_statuses"`
	Analysts     []string         `thrift:"Analysts,3,optional" frugal:"3,optional,list<string>" json:"analysts"`
}

func NewDataProcessFilterRule() *DataProcessFilterRule {
	return &DataProcessFilterRule{}
}

func (p *DataProcessFilterRule) InitDefault() {
}

var DataProcessFilterRule_FilterParams_DEFAULT []*FilterParam

func (p *DataProcessFilterRule) GetFilterParams() (v []*FilterParam) {
	if !p.IsSetFilterParams() {
		return DataProcessFilterRule_FilterParams_DEFAULT
	}
	return p.FilterParams
}

var DataProcessFilterRule_Statuses_DEFAULT []AnalysisStatus

func (p *DataProcessFilterRule) GetStatuses() (v []AnalysisStatus) {
	if !p.IsSetStatuses() {
		return DataProcessFilterRule_Statuses_DEFAULT
	}
	return p.Statuses
}

var DataProcessFilterRule_Analysts_DEFAULT []string

func (p *DataProcessFilterRule) GetAnalysts() (v []string) {
	if !p.IsSetAnalysts() {
		return DataProcessFilterRule_Analysts_DEFAULT
	}
	return p.Analysts
}
func (p *DataProcessFilterRule) SetFilterParams(val []*FilterParam) {
	p.FilterParams = val
}
func (p *DataProcessFilterRule) SetStatuses(val []AnalysisStatus) {
	p.Statuses = val
}
func (p *DataProcessFilterRule) SetAnalysts(val []string) {
	p.Analysts = val
}

var fieldIDToName_DataProcessFilterRule = map[int16]string{
	1: "FilterParams",
	2: "Statuses",
	3: "Analysts",
}

func (p *DataProcessFilterRule) IsSetFilterParams() bool {
	return p.FilterParams != nil
}

func (p *DataProcessFilterRule) IsSetStatuses() bool {
	return p.Statuses != nil
}

func (p *DataProcessFilterRule) IsSetAnalysts() bool {
	return p.Analysts != nil
}

func (p *DataProcessFilterRule) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DataProcessFilterRule[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DataProcessFilterRule) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*FilterParam, 0, size)
	values := make([]FilterParam, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.FilterParams = _field
	return nil
}
func (p *DataProcessFilterRule) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]AnalysisStatus, 0, size)
	for i := 0; i < size; i++ {

		var _elem AnalysisStatus
		if v, err := iprot.ReadI32(); err != nil {
			return err
		} else {
			_elem = AnalysisStatus(v)
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Statuses = _field
	return nil
}
func (p *DataProcessFilterRule) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Analysts = _field
	return nil
}

func (p *DataProcessFilterRule) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DataProcessFilterRule"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DataProcessFilterRule) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetFilterParams() {
		if err = oprot.WriteFieldBegin("FilterParams", thrift.LIST, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.FilterParams)); err != nil {
			return err
		}
		for _, v := range p.FilterParams {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *DataProcessFilterRule) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatuses() {
		if err = oprot.WriteFieldBegin("Statuses", thrift.LIST, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Statuses)); err != nil {
			return err
		}
		for _, v := range p.Statuses {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *DataProcessFilterRule) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetAnalysts() {
		if err = oprot.WriteFieldBegin("Analysts", thrift.LIST, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Analysts)); err != nil {
			return err
		}
		for _, v := range p.Analysts {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DataProcessFilterRule) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DataProcessFilterRule(%+v)", *p)

}

func (p *DataProcessFilterRule) DeepEqual(ano *DataProcessFilterRule) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.FilterParams) {
		return false
	}
	if !p.Field2DeepEqual(ano.Statuses) {
		return false
	}
	if !p.Field3DeepEqual(ano.Analysts) {
		return false
	}
	return true
}

func (p *DataProcessFilterRule) Field1DeepEqual(src []*FilterParam) bool {

	if len(p.FilterParams) != len(src) {
		return false
	}
	for i, v := range p.FilterParams {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DataProcessFilterRule) Field2DeepEqual(src []AnalysisStatus) bool {

	if len(p.Statuses) != len(src) {
		return false
	}
	for i, v := range p.Statuses {
		_src := src[i]
		if v != _src {
			return false
		}
	}
	return true
}
func (p *DataProcessFilterRule) Field3DeepEqual(src []string) bool {

	if len(p.Analysts) != len(src) {
		return false
	}
	for i, v := range p.Analysts {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}

type DataProcessStep struct {
	StepType       DataProcessStepType   `thrift:"StepType,1,required" frugal:"1,required,DataProcessStepType" json:"step_type"`
	Param          *DataProcessStepParam `thrift:"Param,2,required" frugal:"2,required,DataProcessStepParam" json:"param"`
	Name           string                `thrift:"Name,3,required" frugal:"3,required,string" json:"name"`
	Desc           *string               `thrift:"Desc,4,optional" frugal:"4,optional,string" json:"desc"`
	IsSkipped      *bool                 `thrift:"IsSkipped,5,optional" frugal:"5,optional,bool" json:"is_skipped"`
	StatisticParam *string               `thrift:"StatisticParam,6,optional" frugal:"6,optional,string" json:"statistic_param"`
}

func NewDataProcessStep() *DataProcessStep {
	return &DataProcessStep{}
}

func (p *DataProcessStep) InitDefault() {
}

func (p *DataProcessStep) GetStepType() (v DataProcessStepType) {
	return p.StepType
}

var DataProcessStep_Param_DEFAULT *DataProcessStepParam

func (p *DataProcessStep) GetParam() (v *DataProcessStepParam) {
	if !p.IsSetParam() {
		return DataProcessStep_Param_DEFAULT
	}
	return p.Param
}

func (p *DataProcessStep) GetName() (v string) {
	return p.Name
}

var DataProcessStep_Desc_DEFAULT string

func (p *DataProcessStep) GetDesc() (v string) {
	if !p.IsSetDesc() {
		return DataProcessStep_Desc_DEFAULT
	}
	return *p.Desc
}

var DataProcessStep_IsSkipped_DEFAULT bool

func (p *DataProcessStep) GetIsSkipped() (v bool) {
	if !p.IsSetIsSkipped() {
		return DataProcessStep_IsSkipped_DEFAULT
	}
	return *p.IsSkipped
}

var DataProcessStep_StatisticParam_DEFAULT string

func (p *DataProcessStep) GetStatisticParam() (v string) {
	if !p.IsSetStatisticParam() {
		return DataProcessStep_StatisticParam_DEFAULT
	}
	return *p.StatisticParam
}
func (p *DataProcessStep) SetStepType(val DataProcessStepType) {
	p.StepType = val
}
func (p *DataProcessStep) SetParam(val *DataProcessStepParam) {
	p.Param = val
}
func (p *DataProcessStep) SetName(val string) {
	p.Name = val
}
func (p *DataProcessStep) SetDesc(val *string) {
	p.Desc = val
}
func (p *DataProcessStep) SetIsSkipped(val *bool) {
	p.IsSkipped = val
}
func (p *DataProcessStep) SetStatisticParam(val *string) {
	p.StatisticParam = val
}

var fieldIDToName_DataProcessStep = map[int16]string{
	1: "StepType",
	2: "Param",
	3: "Name",
	4: "Desc",
	5: "IsSkipped",
	6: "StatisticParam",
}

func (p *DataProcessStep) IsSetParam() bool {
	return p.Param != nil
}

func (p *DataProcessStep) IsSetDesc() bool {
	return p.Desc != nil
}

func (p *DataProcessStep) IsSetIsSkipped() bool {
	return p.IsSkipped != nil
}

func (p *DataProcessStep) IsSetStatisticParam() bool {
	return p.StatisticParam != nil
}

func (p *DataProcessStep) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetStepType bool = false
	var issetParam bool = false
	var issetName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetStepType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetParam = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetStepType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetParam {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetName {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DataProcessStep[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DataProcessStep[fieldId]))
}

func (p *DataProcessStep) ReadField1(iprot thrift.TProtocol) error {

	var _field DataProcessStepType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = DataProcessStepType(v)
	}
	p.StepType = _field
	return nil
}
func (p *DataProcessStep) ReadField2(iprot thrift.TProtocol) error {
	_field := NewDataProcessStepParam()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Param = _field
	return nil
}
func (p *DataProcessStep) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *DataProcessStep) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Desc = _field
	return nil
}
func (p *DataProcessStep) ReadField5(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.IsSkipped = _field
	return nil
}
func (p *DataProcessStep) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.StatisticParam = _field
	return nil
}

func (p *DataProcessStep) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DataProcessStep"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DataProcessStep) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StepType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.StepType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *DataProcessStep) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Param", thrift.STRUCT, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Param.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *DataProcessStep) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Name", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *DataProcessStep) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetDesc() {
		if err = oprot.WriteFieldBegin("Desc", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Desc); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *DataProcessStep) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetIsSkipped() {
		if err = oprot.WriteFieldBegin("IsSkipped", thrift.BOOL, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.IsSkipped); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *DataProcessStep) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatisticParam() {
		if err = oprot.WriteFieldBegin("StatisticParam", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.StatisticParam); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DataProcessStep) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DataProcessStep(%+v)", *p)

}

func (p *DataProcessStep) DeepEqual(ano *DataProcessStep) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.StepType) {
		return false
	}
	if !p.Field2DeepEqual(ano.Param) {
		return false
	}
	if !p.Field3DeepEqual(ano.Name) {
		return false
	}
	if !p.Field4DeepEqual(ano.Desc) {
		return false
	}
	if !p.Field5DeepEqual(ano.IsSkipped) {
		return false
	}
	if !p.Field6DeepEqual(ano.StatisticParam) {
		return false
	}
	return true
}

func (p *DataProcessStep) Field1DeepEqual(src DataProcessStepType) bool {

	if p.StepType != src {
		return false
	}
	return true
}
func (p *DataProcessStep) Field2DeepEqual(src *DataProcessStepParam) bool {

	if !p.Param.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DataProcessStep) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Name, src) != 0 {
		return false
	}
	return true
}
func (p *DataProcessStep) Field4DeepEqual(src *string) bool {

	if p.Desc == src {
		return true
	} else if p.Desc == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Desc, *src) != 0 {
		return false
	}
	return true
}
func (p *DataProcessStep) Field5DeepEqual(src *bool) bool {

	if p.IsSkipped == src {
		return true
	} else if p.IsSkipped == nil || src == nil {
		return false
	}
	if *p.IsSkipped != *src {
		return false
	}
	return true
}
func (p *DataProcessStep) Field6DeepEqual(src *string) bool {

	if p.StatisticParam == src {
		return true
	} else if p.StatisticParam == nil || src == nil {
		return false
	}
	if strings.Compare(*p.StatisticParam, *src) != 0 {
		return false
	}
	return true
}

type DataProcessStepParam struct {
	DataCleanURL             *string           `thrift:"DataCleanURL,1,optional" frugal:"1,optional,string" json:"data_clean_url"`
	InferenceParams          []*InferenceParam `thrift:"InferenceParams,2,optional" frugal:"2,optional,list<InferenceParam>" json:"inference_params"`
	DataCleanUserParamMapStr *string           `thrift:"DataCleanUserParamMapStr,3,optional" frugal:"3,optional,string" json:"data_clean_user_param_map_str"`
	DataCleanType            *DataCleanType    `thrift:"DataCleanType,4,optional" frugal:"4,optional,DataCleanType" json:"data_clean_type"`
	BatchNumOfCaseClean      *int64            `thrift:"BatchNumOfCaseClean,5,optional" frugal:"5,optional,i64" json:"batch_num_of_case_clean"`
}

func NewDataProcessStepParam() *DataProcessStepParam {
	return &DataProcessStepParam{}
}

func (p *DataProcessStepParam) InitDefault() {
}

var DataProcessStepParam_DataCleanURL_DEFAULT string

func (p *DataProcessStepParam) GetDataCleanURL() (v string) {
	if !p.IsSetDataCleanURL() {
		return DataProcessStepParam_DataCleanURL_DEFAULT
	}
	return *p.DataCleanURL
}

var DataProcessStepParam_InferenceParams_DEFAULT []*InferenceParam

func (p *DataProcessStepParam) GetInferenceParams() (v []*InferenceParam) {
	if !p.IsSetInferenceParams() {
		return DataProcessStepParam_InferenceParams_DEFAULT
	}
	return p.InferenceParams
}

var DataProcessStepParam_DataCleanUserParamMapStr_DEFAULT string

func (p *DataProcessStepParam) GetDataCleanUserParamMapStr() (v string) {
	if !p.IsSetDataCleanUserParamMapStr() {
		return DataProcessStepParam_DataCleanUserParamMapStr_DEFAULT
	}
	return *p.DataCleanUserParamMapStr
}

var DataProcessStepParam_DataCleanType_DEFAULT DataCleanType

func (p *DataProcessStepParam) GetDataCleanType() (v DataCleanType) {
	if !p.IsSetDataCleanType() {
		return DataProcessStepParam_DataCleanType_DEFAULT
	}
	return *p.DataCleanType
}

var DataProcessStepParam_BatchNumOfCaseClean_DEFAULT int64

func (p *DataProcessStepParam) GetBatchNumOfCaseClean() (v int64) {
	if !p.IsSetBatchNumOfCaseClean() {
		return DataProcessStepParam_BatchNumOfCaseClean_DEFAULT
	}
	return *p.BatchNumOfCaseClean
}
func (p *DataProcessStepParam) SetDataCleanURL(val *string) {
	p.DataCleanURL = val
}
func (p *DataProcessStepParam) SetInferenceParams(val []*InferenceParam) {
	p.InferenceParams = val
}
func (p *DataProcessStepParam) SetDataCleanUserParamMapStr(val *string) {
	p.DataCleanUserParamMapStr = val
}
func (p *DataProcessStepParam) SetDataCleanType(val *DataCleanType) {
	p.DataCleanType = val
}
func (p *DataProcessStepParam) SetBatchNumOfCaseClean(val *int64) {
	p.BatchNumOfCaseClean = val
}

var fieldIDToName_DataProcessStepParam = map[int16]string{
	1: "DataCleanURL",
	2: "InferenceParams",
	3: "DataCleanUserParamMapStr",
	4: "DataCleanType",
	5: "BatchNumOfCaseClean",
}

func (p *DataProcessStepParam) IsSetDataCleanURL() bool {
	return p.DataCleanURL != nil
}

func (p *DataProcessStepParam) IsSetInferenceParams() bool {
	return p.InferenceParams != nil
}

func (p *DataProcessStepParam) IsSetDataCleanUserParamMapStr() bool {
	return p.DataCleanUserParamMapStr != nil
}

func (p *DataProcessStepParam) IsSetDataCleanType() bool {
	return p.DataCleanType != nil
}

func (p *DataProcessStepParam) IsSetBatchNumOfCaseClean() bool {
	return p.BatchNumOfCaseClean != nil
}

func (p *DataProcessStepParam) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DataProcessStepParam[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DataProcessStepParam) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DataCleanURL = _field
	return nil
}
func (p *DataProcessStepParam) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InferenceParam, 0, size)
	values := make([]InferenceParam, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.InferenceParams = _field
	return nil
}
func (p *DataProcessStepParam) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DataCleanUserParamMapStr = _field
	return nil
}
func (p *DataProcessStepParam) ReadField4(iprot thrift.TProtocol) error {

	var _field *DataCleanType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DataCleanType(v)
		_field = &tmp
	}
	p.DataCleanType = _field
	return nil
}
func (p *DataProcessStepParam) ReadField5(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BatchNumOfCaseClean = _field
	return nil
}

func (p *DataProcessStepParam) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DataProcessStepParam"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DataProcessStepParam) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetDataCleanURL() {
		if err = oprot.WriteFieldBegin("DataCleanURL", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.DataCleanURL); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *DataProcessStepParam) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetInferenceParams() {
		if err = oprot.WriteFieldBegin("InferenceParams", thrift.LIST, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.InferenceParams)); err != nil {
			return err
		}
		for _, v := range p.InferenceParams {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *DataProcessStepParam) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetDataCleanUserParamMapStr() {
		if err = oprot.WriteFieldBegin("DataCleanUserParamMapStr", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.DataCleanUserParamMapStr); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *DataProcessStepParam) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetDataCleanType() {
		if err = oprot.WriteFieldBegin("DataCleanType", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.DataCleanType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *DataProcessStepParam) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetBatchNumOfCaseClean() {
		if err = oprot.WriteFieldBegin("BatchNumOfCaseClean", thrift.I64, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.BatchNumOfCaseClean); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DataProcessStepParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DataProcessStepParam(%+v)", *p)

}

func (p *DataProcessStepParam) DeepEqual(ano *DataProcessStepParam) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.DataCleanURL) {
		return false
	}
	if !p.Field2DeepEqual(ano.InferenceParams) {
		return false
	}
	if !p.Field3DeepEqual(ano.DataCleanUserParamMapStr) {
		return false
	}
	if !p.Field4DeepEqual(ano.DataCleanType) {
		return false
	}
	if !p.Field5DeepEqual(ano.BatchNumOfCaseClean) {
		return false
	}
	return true
}

func (p *DataProcessStepParam) Field1DeepEqual(src *string) bool {

	if p.DataCleanURL == src {
		return true
	} else if p.DataCleanURL == nil || src == nil {
		return false
	}
	if strings.Compare(*p.DataCleanURL, *src) != 0 {
		return false
	}
	return true
}
func (p *DataProcessStepParam) Field2DeepEqual(src []*InferenceParam) bool {

	if len(p.InferenceParams) != len(src) {
		return false
	}
	for i, v := range p.InferenceParams {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DataProcessStepParam) Field3DeepEqual(src *string) bool {

	if p.DataCleanUserParamMapStr == src {
		return true
	} else if p.DataCleanUserParamMapStr == nil || src == nil {
		return false
	}
	if strings.Compare(*p.DataCleanUserParamMapStr, *src) != 0 {
		return false
	}
	return true
}
func (p *DataProcessStepParam) Field4DeepEqual(src *DataCleanType) bool {

	if p.DataCleanType == src {
		return true
	} else if p.DataCleanType == nil || src == nil {
		return false
	}
	if *p.DataCleanType != *src {
		return false
	}
	return true
}
func (p *DataProcessStepParam) Field5DeepEqual(src *int64) bool {

	if p.BatchNumOfCaseClean == src {
		return true
	} else if p.BatchNumOfCaseClean == nil || src == nil {
		return false
	}
	if *p.BatchNumOfCaseClean != *src {
		return false
	}
	return true
}
