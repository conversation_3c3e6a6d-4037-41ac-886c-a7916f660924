// Code generated by thriftgo (0.3.19). DO NOT EDIT.

package llminfra

import (
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/base"
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/common"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type ExecutionDetail struct {
	ExecutionID int64  `thrift:"ExecutionID,1,required" frugal:"1,required,i64" json:"execution_id"`
	Name        string `thrift:"Name,2,required" frugal:"2,required,string" json:"name"`
	Status      string `thrift:"Status,3,required" frugal:"3,required,string" json:"status"`
	Input       string `thrift:"Input,4,required" frugal:"4,required,string" json:"input"`
	Output      string `thrift:"Output,5,required" frugal:"5,required,string" json:"output"`
}

func NewExecutionDetail() *ExecutionDetail {
	return &ExecutionDetail{}
}

func (p *ExecutionDetail) InitDefault() {
}

func (p *ExecutionDetail) GetExecutionID() (v int64) {
	return p.ExecutionID
}

func (p *ExecutionDetail) GetName() (v string) {
	return p.Name
}

func (p *ExecutionDetail) GetStatus() (v string) {
	return p.Status
}

func (p *ExecutionDetail) GetInput() (v string) {
	return p.Input
}

func (p *ExecutionDetail) GetOutput() (v string) {
	return p.Output
}
func (p *ExecutionDetail) SetExecutionID(val int64) {
	p.ExecutionID = val
}
func (p *ExecutionDetail) SetName(val string) {
	p.Name = val
}
func (p *ExecutionDetail) SetStatus(val string) {
	p.Status = val
}
func (p *ExecutionDetail) SetInput(val string) {
	p.Input = val
}
func (p *ExecutionDetail) SetOutput(val string) {
	p.Output = val
}

var fieldIDToName_ExecutionDetail = map[int16]string{
	1: "ExecutionID",
	2: "Name",
	3: "Status",
	4: "Input",
	5: "Output",
}

func (p *ExecutionDetail) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetExecutionID bool = false
	var issetName bool = false
	var issetStatus bool = false
	var issetInput bool = false
	var issetOutput bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetExecutionID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetStatus = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetInput = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetOutput = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetExecutionID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetStatus {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetInput {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetOutput {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ExecutionDetail[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ExecutionDetail[fieldId]))
}

func (p *ExecutionDetail) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ExecutionID = _field
	return nil
}
func (p *ExecutionDetail) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *ExecutionDetail) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Status = _field
	return nil
}
func (p *ExecutionDetail) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Input = _field
	return nil
}
func (p *ExecutionDetail) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Output = _field
	return nil
}

func (p *ExecutionDetail) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ExecutionDetail"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ExecutionDetail) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ExecutionID", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ExecutionID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ExecutionDetail) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Name", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ExecutionDetail) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Status", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Status); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ExecutionDetail) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Input", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Input); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *ExecutionDetail) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Output", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Output); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ExecutionDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExecutionDetail(%+v)", *p)

}

func (p *ExecutionDetail) DeepEqual(ano *ExecutionDetail) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ExecutionID) {
		return false
	}
	if !p.Field2DeepEqual(ano.Name) {
		return false
	}
	if !p.Field3DeepEqual(ano.Status) {
		return false
	}
	if !p.Field4DeepEqual(ano.Input) {
		return false
	}
	if !p.Field5DeepEqual(ano.Output) {
		return false
	}
	return true
}

func (p *ExecutionDetail) Field1DeepEqual(src int64) bool {

	if p.ExecutionID != src {
		return false
	}
	return true
}
func (p *ExecutionDetail) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Name, src) != 0 {
		return false
	}
	return true
}
func (p *ExecutionDetail) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Status, src) != 0 {
		return false
	}
	return true
}
func (p *ExecutionDetail) Field4DeepEqual(src string) bool {

	if strings.Compare(p.Input, src) != 0 {
		return false
	}
	return true
}
func (p *ExecutionDetail) Field5DeepEqual(src string) bool {

	if strings.Compare(p.Output, src) != 0 {
		return false
	}
	return true
}

type TriggerPipelineStateMachineRequest struct {
	Name                   string              `thrift:"Name,1,required" frugal:"1,required,string" json:"name"`
	Input                  string              `thrift:"Input,2,required" frugal:"2,required,string" json:"input"`
	StateMachineID         *int64              `thrift:"StateMachineID,3,optional" frugal:"3,optional,i64" json:"state_machine_id,omitempty"`
	StateMachineDefinition *string             `thrift:"StateMachineDefinition,4,optional" frugal:"4,optional,string" json:"state_machine_definition,omitempty"`
	NotifyExecutionEvent   *bool               `thrift:"NotifyExecutionEvent,5,optional" frugal:"5,optional,bool" json:"notify_execution_event,omitempty"`
	NotifyStateEvent       *bool               `thrift:"NotifyStateEvent,6,optional" frugal:"6,optional,bool" json:"notify_state_event,omitempty"`
	WaitUntillFinished     *bool               `thrift:"WaitUntillFinished,7,optional" frugal:"7,optional,bool" json:"wait_untill_finished,omitempty"`
	UseTemporaryRevision   *bool               `thrift:"UseTemporaryRevision,8,optional" frugal:"8,optional,bool" json:"use_temporary_revision,omitempty"`
	RevisionNumber         *int64              `thrift:"RevisionNumber,9,optional" frugal:"9,optional,i64" json:"revision_number,omitempty"`
	HTTPRequest            *common.HTTPRequest `thrift:"HTTPRequest,201,optional" frugal:"201,optional,common.HTTPRequest" json:"HTTPRequest,omitempty"`
	Base                   *base.Base          `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewTriggerPipelineStateMachineRequest() *TriggerPipelineStateMachineRequest {
	return &TriggerPipelineStateMachineRequest{}
}

func (p *TriggerPipelineStateMachineRequest) InitDefault() {
}

func (p *TriggerPipelineStateMachineRequest) GetName() (v string) {
	return p.Name
}

func (p *TriggerPipelineStateMachineRequest) GetInput() (v string) {
	return p.Input
}

var TriggerPipelineStateMachineRequest_StateMachineID_DEFAULT int64

func (p *TriggerPipelineStateMachineRequest) GetStateMachineID() (v int64) {
	if !p.IsSetStateMachineID() {
		return TriggerPipelineStateMachineRequest_StateMachineID_DEFAULT
	}
	return *p.StateMachineID
}

var TriggerPipelineStateMachineRequest_StateMachineDefinition_DEFAULT string

func (p *TriggerPipelineStateMachineRequest) GetStateMachineDefinition() (v string) {
	if !p.IsSetStateMachineDefinition() {
		return TriggerPipelineStateMachineRequest_StateMachineDefinition_DEFAULT
	}
	return *p.StateMachineDefinition
}

var TriggerPipelineStateMachineRequest_NotifyExecutionEvent_DEFAULT bool

func (p *TriggerPipelineStateMachineRequest) GetNotifyExecutionEvent() (v bool) {
	if !p.IsSetNotifyExecutionEvent() {
		return TriggerPipelineStateMachineRequest_NotifyExecutionEvent_DEFAULT
	}
	return *p.NotifyExecutionEvent
}

var TriggerPipelineStateMachineRequest_NotifyStateEvent_DEFAULT bool

func (p *TriggerPipelineStateMachineRequest) GetNotifyStateEvent() (v bool) {
	if !p.IsSetNotifyStateEvent() {
		return TriggerPipelineStateMachineRequest_NotifyStateEvent_DEFAULT
	}
	return *p.NotifyStateEvent
}

var TriggerPipelineStateMachineRequest_WaitUntillFinished_DEFAULT bool

func (p *TriggerPipelineStateMachineRequest) GetWaitUntillFinished() (v bool) {
	if !p.IsSetWaitUntillFinished() {
		return TriggerPipelineStateMachineRequest_WaitUntillFinished_DEFAULT
	}
	return *p.WaitUntillFinished
}

var TriggerPipelineStateMachineRequest_UseTemporaryRevision_DEFAULT bool

func (p *TriggerPipelineStateMachineRequest) GetUseTemporaryRevision() (v bool) {
	if !p.IsSetUseTemporaryRevision() {
		return TriggerPipelineStateMachineRequest_UseTemporaryRevision_DEFAULT
	}
	return *p.UseTemporaryRevision
}

var TriggerPipelineStateMachineRequest_RevisionNumber_DEFAULT int64

func (p *TriggerPipelineStateMachineRequest) GetRevisionNumber() (v int64) {
	if !p.IsSetRevisionNumber() {
		return TriggerPipelineStateMachineRequest_RevisionNumber_DEFAULT
	}
	return *p.RevisionNumber
}

var TriggerPipelineStateMachineRequest_HTTPRequest_DEFAULT *common.HTTPRequest

func (p *TriggerPipelineStateMachineRequest) GetHTTPRequest() (v *common.HTTPRequest) {
	if !p.IsSetHTTPRequest() {
		return TriggerPipelineStateMachineRequest_HTTPRequest_DEFAULT
	}
	return p.HTTPRequest
}

var TriggerPipelineStateMachineRequest_Base_DEFAULT *base.Base

func (p *TriggerPipelineStateMachineRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return TriggerPipelineStateMachineRequest_Base_DEFAULT
	}
	return p.Base
}
func (p *TriggerPipelineStateMachineRequest) SetName(val string) {
	p.Name = val
}
func (p *TriggerPipelineStateMachineRequest) SetInput(val string) {
	p.Input = val
}
func (p *TriggerPipelineStateMachineRequest) SetStateMachineID(val *int64) {
	p.StateMachineID = val
}
func (p *TriggerPipelineStateMachineRequest) SetStateMachineDefinition(val *string) {
	p.StateMachineDefinition = val
}
func (p *TriggerPipelineStateMachineRequest) SetNotifyExecutionEvent(val *bool) {
	p.NotifyExecutionEvent = val
}
func (p *TriggerPipelineStateMachineRequest) SetNotifyStateEvent(val *bool) {
	p.NotifyStateEvent = val
}
func (p *TriggerPipelineStateMachineRequest) SetWaitUntillFinished(val *bool) {
	p.WaitUntillFinished = val
}
func (p *TriggerPipelineStateMachineRequest) SetUseTemporaryRevision(val *bool) {
	p.UseTemporaryRevision = val
}
func (p *TriggerPipelineStateMachineRequest) SetRevisionNumber(val *int64) {
	p.RevisionNumber = val
}
func (p *TriggerPipelineStateMachineRequest) SetHTTPRequest(val *common.HTTPRequest) {
	p.HTTPRequest = val
}
func (p *TriggerPipelineStateMachineRequest) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_TriggerPipelineStateMachineRequest = map[int16]string{
	1:   "Name",
	2:   "Input",
	3:   "StateMachineID",
	4:   "StateMachineDefinition",
	5:   "NotifyExecutionEvent",
	6:   "NotifyStateEvent",
	7:   "WaitUntillFinished",
	8:   "UseTemporaryRevision",
	9:   "RevisionNumber",
	201: "HTTPRequest",
	255: "Base",
}

func (p *TriggerPipelineStateMachineRequest) IsSetStateMachineID() bool {
	return p.StateMachineID != nil
}

func (p *TriggerPipelineStateMachineRequest) IsSetStateMachineDefinition() bool {
	return p.StateMachineDefinition != nil
}

func (p *TriggerPipelineStateMachineRequest) IsSetNotifyExecutionEvent() bool {
	return p.NotifyExecutionEvent != nil
}

func (p *TriggerPipelineStateMachineRequest) IsSetNotifyStateEvent() bool {
	return p.NotifyStateEvent != nil
}

func (p *TriggerPipelineStateMachineRequest) IsSetWaitUntillFinished() bool {
	return p.WaitUntillFinished != nil
}

func (p *TriggerPipelineStateMachineRequest) IsSetUseTemporaryRevision() bool {
	return p.UseTemporaryRevision != nil
}

func (p *TriggerPipelineStateMachineRequest) IsSetRevisionNumber() bool {
	return p.RevisionNumber != nil
}

func (p *TriggerPipelineStateMachineRequest) IsSetHTTPRequest() bool {
	return p.HTTPRequest != nil
}

func (p *TriggerPipelineStateMachineRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *TriggerPipelineStateMachineRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetName bool = false
	var issetInput bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInput = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 201:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField201(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInput {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TriggerPipelineStateMachineRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_TriggerPipelineStateMachineRequest[fieldId]))
}

func (p *TriggerPipelineStateMachineRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *TriggerPipelineStateMachineRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Input = _field
	return nil
}
func (p *TriggerPipelineStateMachineRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.StateMachineID = _field
	return nil
}
func (p *TriggerPipelineStateMachineRequest) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.StateMachineDefinition = _field
	return nil
}
func (p *TriggerPipelineStateMachineRequest) ReadField5(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.NotifyExecutionEvent = _field
	return nil
}
func (p *TriggerPipelineStateMachineRequest) ReadField6(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.NotifyStateEvent = _field
	return nil
}
func (p *TriggerPipelineStateMachineRequest) ReadField7(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.WaitUntillFinished = _field
	return nil
}
func (p *TriggerPipelineStateMachineRequest) ReadField8(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.UseTemporaryRevision = _field
	return nil
}
func (p *TriggerPipelineStateMachineRequest) ReadField9(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RevisionNumber = _field
	return nil
}
func (p *TriggerPipelineStateMachineRequest) ReadField201(iprot thrift.TProtocol) error {
	_field := common.NewHTTPRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.HTTPRequest = _field
	return nil
}
func (p *TriggerPipelineStateMachineRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *TriggerPipelineStateMachineRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("TriggerPipelineStateMachineRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField201(oprot); err != nil {
			fieldId = 201
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TriggerPipelineStateMachineRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Name", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *TriggerPipelineStateMachineRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Input", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Input); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *TriggerPipelineStateMachineRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetStateMachineID() {
		if err = oprot.WriteFieldBegin("StateMachineID", thrift.I64, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.StateMachineID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *TriggerPipelineStateMachineRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetStateMachineDefinition() {
		if err = oprot.WriteFieldBegin("StateMachineDefinition", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.StateMachineDefinition); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *TriggerPipelineStateMachineRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetNotifyExecutionEvent() {
		if err = oprot.WriteFieldBegin("NotifyExecutionEvent", thrift.BOOL, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.NotifyExecutionEvent); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *TriggerPipelineStateMachineRequest) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetNotifyStateEvent() {
		if err = oprot.WriteFieldBegin("NotifyStateEvent", thrift.BOOL, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.NotifyStateEvent); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *TriggerPipelineStateMachineRequest) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetWaitUntillFinished() {
		if err = oprot.WriteFieldBegin("WaitUntillFinished", thrift.BOOL, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.WaitUntillFinished); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *TriggerPipelineStateMachineRequest) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetUseTemporaryRevision() {
		if err = oprot.WriteFieldBegin("UseTemporaryRevision", thrift.BOOL, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.UseTemporaryRevision); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *TriggerPipelineStateMachineRequest) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetRevisionNumber() {
		if err = oprot.WriteFieldBegin("RevisionNumber", thrift.I64, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.RevisionNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *TriggerPipelineStateMachineRequest) writeField201(oprot thrift.TProtocol) (err error) {
	if p.IsSetHTTPRequest() {
		if err = oprot.WriteFieldBegin("HTTPRequest", thrift.STRUCT, 201); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.HTTPRequest.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 end error: ", p), err)
}
func (p *TriggerPipelineStateMachineRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *TriggerPipelineStateMachineRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TriggerPipelineStateMachineRequest(%+v)", *p)

}

func (p *TriggerPipelineStateMachineRequest) DeepEqual(ano *TriggerPipelineStateMachineRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Name) {
		return false
	}
	if !p.Field2DeepEqual(ano.Input) {
		return false
	}
	if !p.Field3DeepEqual(ano.StateMachineID) {
		return false
	}
	if !p.Field4DeepEqual(ano.StateMachineDefinition) {
		return false
	}
	if !p.Field5DeepEqual(ano.NotifyExecutionEvent) {
		return false
	}
	if !p.Field6DeepEqual(ano.NotifyStateEvent) {
		return false
	}
	if !p.Field7DeepEqual(ano.WaitUntillFinished) {
		return false
	}
	if !p.Field8DeepEqual(ano.UseTemporaryRevision) {
		return false
	}
	if !p.Field9DeepEqual(ano.RevisionNumber) {
		return false
	}
	if !p.Field201DeepEqual(ano.HTTPRequest) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *TriggerPipelineStateMachineRequest) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Name, src) != 0 {
		return false
	}
	return true
}
func (p *TriggerPipelineStateMachineRequest) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Input, src) != 0 {
		return false
	}
	return true
}
func (p *TriggerPipelineStateMachineRequest) Field3DeepEqual(src *int64) bool {

	if p.StateMachineID == src {
		return true
	} else if p.StateMachineID == nil || src == nil {
		return false
	}
	if *p.StateMachineID != *src {
		return false
	}
	return true
}
func (p *TriggerPipelineStateMachineRequest) Field4DeepEqual(src *string) bool {

	if p.StateMachineDefinition == src {
		return true
	} else if p.StateMachineDefinition == nil || src == nil {
		return false
	}
	if strings.Compare(*p.StateMachineDefinition, *src) != 0 {
		return false
	}
	return true
}
func (p *TriggerPipelineStateMachineRequest) Field5DeepEqual(src *bool) bool {

	if p.NotifyExecutionEvent == src {
		return true
	} else if p.NotifyExecutionEvent == nil || src == nil {
		return false
	}
	if *p.NotifyExecutionEvent != *src {
		return false
	}
	return true
}
func (p *TriggerPipelineStateMachineRequest) Field6DeepEqual(src *bool) bool {

	if p.NotifyStateEvent == src {
		return true
	} else if p.NotifyStateEvent == nil || src == nil {
		return false
	}
	if *p.NotifyStateEvent != *src {
		return false
	}
	return true
}
func (p *TriggerPipelineStateMachineRequest) Field7DeepEqual(src *bool) bool {

	if p.WaitUntillFinished == src {
		return true
	} else if p.WaitUntillFinished == nil || src == nil {
		return false
	}
	if *p.WaitUntillFinished != *src {
		return false
	}
	return true
}
func (p *TriggerPipelineStateMachineRequest) Field8DeepEqual(src *bool) bool {

	if p.UseTemporaryRevision == src {
		return true
	} else if p.UseTemporaryRevision == nil || src == nil {
		return false
	}
	if *p.UseTemporaryRevision != *src {
		return false
	}
	return true
}
func (p *TriggerPipelineStateMachineRequest) Field9DeepEqual(src *int64) bool {

	if p.RevisionNumber == src {
		return true
	} else if p.RevisionNumber == nil || src == nil {
		return false
	}
	if *p.RevisionNumber != *src {
		return false
	}
	return true
}
func (p *TriggerPipelineStateMachineRequest) Field201DeepEqual(src *common.HTTPRequest) bool {

	if !p.HTTPRequest.DeepEqual(src) {
		return false
	}
	return true
}
func (p *TriggerPipelineStateMachineRequest) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type TriggerPipelineStateMachineResponse struct {
	ExecutionDetail    *ExecutionDetail     `thrift:"ExecutionDetail,1,optional" frugal:"1,optional,ExecutionDetail" json:"execution_detail,omitempty"`
	Code               *common.ErrorCode    `thrift:"Code,201,optional" frugal:"201,optional,ErrorCode" json:"code,omitempty"`
	Message            *string              `thrift:"Message,202,optional" frugal:"202,optional,string" json:"message,omitempty"`
	HTTPCode           *int32               `thrift:"HTTPCode,203,optional" frugal:"203,optional,i32" json:"HTTPCode,omitempty"`
	HTTPResponse       *common.HTTPResponse `thrift:"HTTPResponse,204,optional" frugal:"204,optional,common.HTTPResponse" json:"HTTPResponse,omitempty"`
	MissingPermissions []*PermissionBinding `thrift:"MissingPermissions,205,optional" frugal:"205,optional,list<PermissionBinding>" json:"missing_permissions,omitempty"`
}

func NewTriggerPipelineStateMachineResponse() *TriggerPipelineStateMachineResponse {
	return &TriggerPipelineStateMachineResponse{}
}

func (p *TriggerPipelineStateMachineResponse) InitDefault() {
}

var TriggerPipelineStateMachineResponse_ExecutionDetail_DEFAULT *ExecutionDetail

func (p *TriggerPipelineStateMachineResponse) GetExecutionDetail() (v *ExecutionDetail) {
	if !p.IsSetExecutionDetail() {
		return TriggerPipelineStateMachineResponse_ExecutionDetail_DEFAULT
	}
	return p.ExecutionDetail
}

var TriggerPipelineStateMachineResponse_Code_DEFAULT common.ErrorCode

func (p *TriggerPipelineStateMachineResponse) GetCode() (v common.ErrorCode) {
	if !p.IsSetCode() {
		return TriggerPipelineStateMachineResponse_Code_DEFAULT
	}
	return *p.Code
}

var TriggerPipelineStateMachineResponse_Message_DEFAULT string

func (p *TriggerPipelineStateMachineResponse) GetMessage() (v string) {
	if !p.IsSetMessage() {
		return TriggerPipelineStateMachineResponse_Message_DEFAULT
	}
	return *p.Message
}

var TriggerPipelineStateMachineResponse_HTTPCode_DEFAULT int32

func (p *TriggerPipelineStateMachineResponse) GetHTTPCode() (v int32) {
	if !p.IsSetHTTPCode() {
		return TriggerPipelineStateMachineResponse_HTTPCode_DEFAULT
	}
	return *p.HTTPCode
}

var TriggerPipelineStateMachineResponse_HTTPResponse_DEFAULT *common.HTTPResponse

func (p *TriggerPipelineStateMachineResponse) GetHTTPResponse() (v *common.HTTPResponse) {
	if !p.IsSetHTTPResponse() {
		return TriggerPipelineStateMachineResponse_HTTPResponse_DEFAULT
	}
	return p.HTTPResponse
}

var TriggerPipelineStateMachineResponse_MissingPermissions_DEFAULT []*PermissionBinding

func (p *TriggerPipelineStateMachineResponse) GetMissingPermissions() (v []*PermissionBinding) {
	if !p.IsSetMissingPermissions() {
		return TriggerPipelineStateMachineResponse_MissingPermissions_DEFAULT
	}
	return p.MissingPermissions
}
func (p *TriggerPipelineStateMachineResponse) SetExecutionDetail(val *ExecutionDetail) {
	p.ExecutionDetail = val
}
func (p *TriggerPipelineStateMachineResponse) SetCode(val *common.ErrorCode) {
	p.Code = val
}
func (p *TriggerPipelineStateMachineResponse) SetMessage(val *string) {
	p.Message = val
}
func (p *TriggerPipelineStateMachineResponse) SetHTTPCode(val *int32) {
	p.HTTPCode = val
}
func (p *TriggerPipelineStateMachineResponse) SetHTTPResponse(val *common.HTTPResponse) {
	p.HTTPResponse = val
}
func (p *TriggerPipelineStateMachineResponse) SetMissingPermissions(val []*PermissionBinding) {
	p.MissingPermissions = val
}

var fieldIDToName_TriggerPipelineStateMachineResponse = map[int16]string{
	1:   "ExecutionDetail",
	201: "Code",
	202: "Message",
	203: "HTTPCode",
	204: "HTTPResponse",
	205: "MissingPermissions",
}

func (p *TriggerPipelineStateMachineResponse) IsSetExecutionDetail() bool {
	return p.ExecutionDetail != nil
}

func (p *TriggerPipelineStateMachineResponse) IsSetCode() bool {
	return p.Code != nil
}

func (p *TriggerPipelineStateMachineResponse) IsSetMessage() bool {
	return p.Message != nil
}

func (p *TriggerPipelineStateMachineResponse) IsSetHTTPCode() bool {
	return p.HTTPCode != nil
}

func (p *TriggerPipelineStateMachineResponse) IsSetHTTPResponse() bool {
	return p.HTTPResponse != nil
}

func (p *TriggerPipelineStateMachineResponse) IsSetMissingPermissions() bool {
	return p.MissingPermissions != nil
}

func (p *TriggerPipelineStateMachineResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 201:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField201(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 202:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField202(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 203:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField203(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 204:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField204(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 205:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField205(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TriggerPipelineStateMachineResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TriggerPipelineStateMachineResponse) ReadField1(iprot thrift.TProtocol) error {
	_field := NewExecutionDetail()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ExecutionDetail = _field
	return nil
}
func (p *TriggerPipelineStateMachineResponse) ReadField201(iprot thrift.TProtocol) error {

	var _field *common.ErrorCode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := common.ErrorCode(v)
		_field = &tmp
	}
	p.Code = _field
	return nil
}
func (p *TriggerPipelineStateMachineResponse) ReadField202(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Message = _field
	return nil
}
func (p *TriggerPipelineStateMachineResponse) ReadField203(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.HTTPCode = _field
	return nil
}
func (p *TriggerPipelineStateMachineResponse) ReadField204(iprot thrift.TProtocol) error {
	_field := common.NewHTTPResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.HTTPResponse = _field
	return nil
}
func (p *TriggerPipelineStateMachineResponse) ReadField205(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*PermissionBinding, 0, size)
	values := make([]PermissionBinding, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.MissingPermissions = _field
	return nil
}

func (p *TriggerPipelineStateMachineResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("TriggerPipelineStateMachineResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField201(oprot); err != nil {
			fieldId = 201
			goto WriteFieldError
		}
		if err = p.writeField202(oprot); err != nil {
			fieldId = 202
			goto WriteFieldError
		}
		if err = p.writeField203(oprot); err != nil {
			fieldId = 203
			goto WriteFieldError
		}
		if err = p.writeField204(oprot); err != nil {
			fieldId = 204
			goto WriteFieldError
		}
		if err = p.writeField205(oprot); err != nil {
			fieldId = 205
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TriggerPipelineStateMachineResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetExecutionDetail() {
		if err = oprot.WriteFieldBegin("ExecutionDetail", thrift.STRUCT, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.ExecutionDetail.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *TriggerPipelineStateMachineResponse) writeField201(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err = oprot.WriteFieldBegin("Code", thrift.I32, 201); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Code)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 end error: ", p), err)
}
func (p *TriggerPipelineStateMachineResponse) writeField202(oprot thrift.TProtocol) (err error) {
	if p.IsSetMessage() {
		if err = oprot.WriteFieldBegin("Message", thrift.STRING, 202); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Message); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 202 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 202 end error: ", p), err)
}
func (p *TriggerPipelineStateMachineResponse) writeField203(oprot thrift.TProtocol) (err error) {
	if p.IsSetHTTPCode() {
		if err = oprot.WriteFieldBegin("HTTPCode", thrift.I32, 203); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.HTTPCode); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 203 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 203 end error: ", p), err)
}
func (p *TriggerPipelineStateMachineResponse) writeField204(oprot thrift.TProtocol) (err error) {
	if p.IsSetHTTPResponse() {
		if err = oprot.WriteFieldBegin("HTTPResponse", thrift.STRUCT, 204); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.HTTPResponse.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 204 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 204 end error: ", p), err)
}
func (p *TriggerPipelineStateMachineResponse) writeField205(oprot thrift.TProtocol) (err error) {
	if p.IsSetMissingPermissions() {
		if err = oprot.WriteFieldBegin("MissingPermissions", thrift.LIST, 205); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.MissingPermissions)); err != nil {
			return err
		}
		for _, v := range p.MissingPermissions {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 205 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 205 end error: ", p), err)
}

func (p *TriggerPipelineStateMachineResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TriggerPipelineStateMachineResponse(%+v)", *p)

}

func (p *TriggerPipelineStateMachineResponse) DeepEqual(ano *TriggerPipelineStateMachineResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ExecutionDetail) {
		return false
	}
	if !p.Field201DeepEqual(ano.Code) {
		return false
	}
	if !p.Field202DeepEqual(ano.Message) {
		return false
	}
	if !p.Field203DeepEqual(ano.HTTPCode) {
		return false
	}
	if !p.Field204DeepEqual(ano.HTTPResponse) {
		return false
	}
	if !p.Field205DeepEqual(ano.MissingPermissions) {
		return false
	}
	return true
}

func (p *TriggerPipelineStateMachineResponse) Field1DeepEqual(src *ExecutionDetail) bool {

	if !p.ExecutionDetail.DeepEqual(src) {
		return false
	}
	return true
}
func (p *TriggerPipelineStateMachineResponse) Field201DeepEqual(src *common.ErrorCode) bool {

	if p.Code == src {
		return true
	} else if p.Code == nil || src == nil {
		return false
	}
	if *p.Code != *src {
		return false
	}
	return true
}
func (p *TriggerPipelineStateMachineResponse) Field202DeepEqual(src *string) bool {

	if p.Message == src {
		return true
	} else if p.Message == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Message, *src) != 0 {
		return false
	}
	return true
}
func (p *TriggerPipelineStateMachineResponse) Field203DeepEqual(src *int32) bool {

	if p.HTTPCode == src {
		return true
	} else if p.HTTPCode == nil || src == nil {
		return false
	}
	if *p.HTTPCode != *src {
		return false
	}
	return true
}
func (p *TriggerPipelineStateMachineResponse) Field204DeepEqual(src *common.HTTPResponse) bool {

	if !p.HTTPResponse.DeepEqual(src) {
		return false
	}
	return true
}
func (p *TriggerPipelineStateMachineResponse) Field205DeepEqual(src []*PermissionBinding) bool {

	if len(p.MissingPermissions) != len(src) {
		return false
	}
	for i, v := range p.MissingPermissions {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
