// Code generated by Kitex v1.17.2. DO NOT EDIT.
package bitsagentchatservice

import (
	bitsagent "code.byted.org/devgpt/kiwis/api/idl/kitex_gen/bitsagent"
	byted "code.byted.org/kite/kitex/byted"
	server "code.byted.org/kite/kitex/server"
)

// NewServer creates a server.Server with the given handler and options.
func NewServer(handler bitsagent.BitsAgentChatService, opts ...server.Option) server.Server {
	var options []server.Option

	options = append(options, byted.ServerSuite(serviceInfo()))

	options = append(options, opts...)
	options = append(options, server.WithCompatibleMiddlewareForUnary())

	svr := server.NewServer(options...)
	if err := svr.RegisterService(serviceInfo(), handler); err != nil {
		panic(err)
	}
	return svr
}

// NewServerWithBytedConfig creates a server.Server with the given handler and options.
func NewServerWithBytedConfig(handler bitsagent.BitsAgentChatService, config *byted.ServerConfig, opts ...server.Option) server.Server {
	var options []server.Option
	options = append(options, byted.ServerSuiteWithConfig(serviceInfo(), config))
	options = append(options, server.WithCompatibleMiddlewareForUnary())
	options = append(options, opts...)

	svr := server.NewServer(options...)
	if err := svr.RegisterService(serviceInfo(), handler); err != nil {
		panic(err)
	}
	return svr
}

func RegisterService(svr server.Server, handler bitsagent.BitsAgentChatService, opts ...server.RegisterOption) error {
	return svr.RegisterService(serviceInfo(), handler, opts...)
}
