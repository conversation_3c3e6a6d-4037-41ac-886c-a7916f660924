// Code generated by Kitex v1.12.2. DO NOT EDIT.

package generalllmservice

import (
	"context"

	byted "code.byted.org/kite/kitex/byted"
	client "code.byted.org/kite/kitex/client"
	callopt "code.byted.org/kite/kitex/client/callopt"

	general_rpc "code.byted.org/devgpt/kiwis/api/idl/kitex_gen/general_rpc"
)

// Client is designed to provide IDL-compatible methods with call-option parameter for kitex framework.
type Client interface {
	Chat(ctx context.Context, req *general_rpc.ChatRequest, callOptions ...callopt.Option) (r *general_rpc.ChatResponse, err error)
	Completion(ctx context.Context, req *general_rpc.CompletionRequest, callOptions ...callopt.Option) (r *general_rpc.CompletionResponse, err error)
	Embedding(ctx context.Context, req *general_rpc.EmbeddingRequest, callOptions ...callopt.Option) (r *general_rpc.EmbeddingResponse, err error)
}

// NewClient creates a client for the service defined in IDL.
func NewClient(destService string, opts ...client.Option) (Client, error) {
	var options []client.Option
	options = append(options, client.WithDestService(destService))

	config := byted.NewClientConfig()
	config.DestService = destService

	options = append(options, byted.ClientSuiteWithConfig(serviceInfo(), config))

	options = append(options, opts...)

	kc, err := client.NewClient(serviceInfo(), options...)
	if err != nil {
		return nil, err
	}
	return &kGeneralLLMServiceClient{
		kClient: newServiceClient(kc),
	}, nil
}

// MustNewClient creates a client for the service defined in IDL. It panics if any error occurs.
func MustNewClient(destService string, opts ...client.Option) Client {
	kc, err := NewClient(destService, opts...)
	if err != nil {
		panic(err)
	}
	return kc
}

type kGeneralLLMServiceClient struct {
	*kClient
}

func (p *kGeneralLLMServiceClient) Chat(ctx context.Context, req *general_rpc.ChatRequest, callOptions ...callopt.Option) (r *general_rpc.ChatResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.Chat(ctx, req)
}

func (p *kGeneralLLMServiceClient) Completion(ctx context.Context, req *general_rpc.CompletionRequest, callOptions ...callopt.Option) (r *general_rpc.CompletionResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.Completion(ctx, req)
}

func (p *kGeneralLLMServiceClient) Embedding(ctx context.Context, req *general_rpc.EmbeddingRequest, callOptions ...callopt.Option) (r *general_rpc.EmbeddingResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.Embedding(ctx, req)
}

// NewClientWithBytedConfig creates a client for the service defined in IDL.
func NewClientWithBytedConfig(destService string, config *byted.ClientConfig, opts ...client.Option) (Client, error) {
	if config == nil {
		config = byted.NewClientConfig()
	}
	config.DestService = destService

	var options []client.Option
	options = append(options, client.WithDestService(destService))

	options = append(options, byted.ClientSuiteWithConfig(serviceInfo(), config))
	options = append(options, opts...)
	kc, err := client.NewClient(serviceInfo(), options...)
	if err != nil {
		return nil, err
	}
	return &kGeneralLLMServiceClient{
		kClient: newServiceClient(kc),
	}, nil
}

// MustNewClientWithBytedConfig creates a client for the service defined in IDL. It panics if any error occurs.
func MustNewClientWithBytedConfig(destService string, config *byted.ClientConfig, opts ...client.Option) Client {
	kc, err := NewClientWithBytedConfig(destService, config, opts...)
	if err != nil {
		panic(err)
	}
	return kc
}
