package service

import (
	"context"

	chatentity "code.byted.org/devgpt/kiwis/copilotstack/entity"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/stream"
)

type UserGrowthService interface {
	IsUGActivityReq(ctx context.Context, opt UGActivityOption) bool
	ExecuteUserGrowthActivity(ctx context.Context, opt UGActivityOption) *stream.RecvChannel[chatentity.Event]
}

type UGActivityOption struct {
	AppID     string
	Username  string
	UserInput string
	TCCConfig config.ActivityTCCConfig
}
