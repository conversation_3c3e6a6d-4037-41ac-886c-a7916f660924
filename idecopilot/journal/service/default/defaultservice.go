package defaultjournalservice

import (
	"context"
	"reflect"

	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/tcc"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gorm.io/gorm"

	"code.byted.org/devgpt/kiwis/copilotstack/journal"
	"code.byted.org/devgpt/kiwis/idecopilot/journal/dal"
	"code.byted.org/devgpt/kiwis/idecopilot/journal/dal/po"
	"code.byted.org/devgpt/kiwis/idecopilot/journal/entity"
	"code.byted.org/devgpt/kiwis/idecopilot/journal/service"
	"code.byted.org/devgpt/kiwis/lib/util"
)

var _ journal.JournalService = &JournalService{}
var _ service.CodeCompletionJournalService = &JournalService{}
var _ service.PromptPipelineJournalService = &JournalService{}

type JournalService struct {
	DAO       dal.JournalDAO
	TCCConfig *tcc.GenericConfig[config.JournalTCCConfig]
}

func NewJournalService(DAO dal.JournalDAO, TCCConfig *tcc.GenericConfig[config.JournalTCCConfig]) (*JournalService, error) {
	s := &JournalService{DAO: DAO, TCCConfig: TCCConfig}
	return s, nil
}

func (j *JournalService) CreateLog(ctx context.Context, opt journal.CreateLogOption) (*journal.PromptCompletion, error) {
	if j.TCCConfig != nil && j.TCCConfig.GetPointer() != nil {
		disableTypes := j.TCCConfig.GetValue().PromptCompletionConfig.Disable
		if lo.Contains(disableTypes, opt.Type) {
			return &journal.PromptCompletion{
				ID:        -1,
				SessionID: opt.SessionId,
			}, nil
		}
	}
	res, err := j.DAO.CreatePromptCompletionLog(ctx, dal.CreatePromptCompletionLogOption{
		AppID:           opt.AppId,
		Username:        opt.Username,
		ModelName:       opt.ModelName,
		Status:          opt.Status,
		SessionId:       opt.SessionId,
		Prompt:          opt.Prompt,
		Type:            opt.Type,
		ContentRaw:      opt.ContentRaw,
		RequestMetadata: po.PromptCompletionRequestMetadata(opt.RequestMetadata),
		ContextVariable: po.PromptCompletionContextVariable(opt.ContextVariable),
	})

	if err != nil {
		return nil, errors.WithMessage(err, "failed to create db record")
	}

	return res, nil
}

func (j *JournalService) UpdateLog(ctx context.Context, opt journal.UpdateLogOption) (*journal.PromptCompletion, error) {
	log, err := j.DAO.GetPromptCompletionLogById(ctx, opt.ID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, errors.WithMessage(err, "failed to get db record")
	}

	// merge from two metadata
	requestMetadata := &journal.PromptCompletionRequestMetadata{
		Stream:              lo.Ternary(empty(opt.RequestMetadata.Stream), log.RequestMetadata.Stream, opt.RequestMetadata.Stream),
		MaxTokens:           lo.Ternary(empty(opt.RequestMetadata.MaxTokens), log.RequestMetadata.MaxTokens, opt.RequestMetadata.MaxTokens),
		Temperature:         lo.Ternary(empty(opt.RequestMetadata.Temperature), log.RequestMetadata.Temperature, opt.RequestMetadata.Temperature),
		TopP:                lo.Ternary(empty(opt.RequestMetadata.TopP), log.RequestMetadata.TopP, opt.RequestMetadata.TopP),
		TopK:                lo.Ternary(empty(opt.RequestMetadata.TopK), log.RequestMetadata.TopK, opt.RequestMetadata.TopK),
		N:                   lo.Ternary(empty(opt.RequestMetadata.N), log.RequestMetadata.N, opt.RequestMetadata.N),
		FrequencyPenalty:    lo.Ternary(empty(opt.RequestMetadata.FrequencyPenalty), log.RequestMetadata.FrequencyPenalty, opt.RequestMetadata.FrequencyPenalty),
		PresencePenalty:     lo.Ternary(empty(opt.RequestMetadata.PresencePenalty), log.RequestMetadata.PresencePenalty, opt.RequestMetadata.PresencePenalty),
		MinNewTokens:        lo.Ternary(empty(opt.RequestMetadata.MinNewTokens), log.RequestMetadata.MinNewTokens, opt.RequestMetadata.MinNewTokens),
		MaxPromptTokens:     lo.Ternary(empty(opt.RequestMetadata.MaxPromptTokens), log.RequestMetadata.MaxPromptTokens, opt.RequestMetadata.MaxPromptTokens),
		RepetitionPenalty:   lo.Ternary(empty(opt.RequestMetadata.RepetitionPenalty), log.RequestMetadata.RepetitionPenalty, opt.RequestMetadata.RepetitionPenalty),
		ThinkingConfig:      lo.Ternary(empty(opt.RequestMetadata.ThinkingConfig), log.RequestMetadata.ThinkingConfig, opt.RequestMetadata.ThinkingConfig),
		ID:                  lo.Ternary(empty(opt.RequestMetadata.ID), log.RequestMetadata.ID, opt.RequestMetadata.ID),
		Usage:               lo.Ternary(empty(opt.RequestMetadata.Usage), log.RequestMetadata.Usage, opt.RequestMetadata.Usage),
		Latency:             lo.Ternary(empty(opt.RequestMetadata.Latency), log.RequestMetadata.Latency, opt.RequestMetadata.Latency),
		FirstTokenLatency:   lo.Ternary(empty(opt.RequestMetadata.FirstTokenLatency), log.RequestMetadata.FirstTokenLatency, opt.RequestMetadata.FirstTokenLatency),
		Error:               lo.Ternary(empty(opt.RequestMetadata.Error), log.RequestMetadata.Error, opt.RequestMetadata.Error),
		FinishReason:        lo.Ternary(empty(opt.RequestMetadata.FinishReason), log.RequestMetadata.FinishReason, opt.RequestMetadata.FinishReason),
		LogID:               lo.Ternary(empty(opt.RequestMetadata.LogID), log.RequestMetadata.LogID, opt.RequestMetadata.LogID),
		UserInputTokens:     lo.Ternary(empty(opt.RequestMetadata.UserInputTokens), log.RequestMetadata.UserInputTokens, opt.RequestMetadata.UserInputTokens),
		FallbackModels:      lo.Ternary(empty(opt.RequestMetadata.FallbackModels), log.RequestMetadata.FallbackModels, opt.RequestMetadata.FallbackModels),
		RetriedTimes:        lo.Ternary(empty(opt.RequestMetadata.RetriedTimes), log.RequestMetadata.RetriedTimes, opt.RequestMetadata.RetriedTimes),
		IntentName:          log.RequestMetadata.IntentName,
		ProgrammingLanguage: log.RequestMetadata.ProgrammingLanguage,
		ContextVariables:    log.RequestMetadata.ContextVariables,
	}

	res, err := j.DAO.UpdatePromptCompletionLog(ctx, dal.UpdatePromptCompletionLogOption{
		ID:              opt.ID,
		AppID:           opt.AppId,
		Username:        opt.Username,
		ModelName:       opt.ModelName,
		Status:          opt.Status,
		SessionId:       opt.SessionId,
		Prompt:          opt.Prompt,
		Type:            opt.Type,
		ContentRaw:      opt.ContentRaw,
		RequestMetadata: requestMetadata,
	})

	if err != nil {
		return nil, errors.WithMessage(err, "failed to update db record")
	}

	return res, nil
}

func (j *JournalService) UpsertFeedback(ctx context.Context, opt journal.CreateFeedbackOption) (*journal.Feedback, error) {
	if util.ContainsPtr(opt.Type, journal.Copy, journal.Insert) {
		return j.DAO.CreateFeedback(ctx, dal.CreateFeedbackOption{
			Username:  opt.Username,
			SessionId: opt.SessionId,
			Type:      opt.Type,
			Content:   opt.Content,
		})
	}

	feedback, err := j.DAO.GetFeedback(ctx, opt.Username, opt.SessionId)
	// Create new feedback for copy and insert action.
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return j.DAO.CreateFeedback(ctx, dal.CreateFeedbackOption{
				Username:  opt.Username,
				SessionId: opt.SessionId,
				Type:      opt.Type,
				Content:   opt.Content,
			})
		}
		return nil, errors.WithMessage(err, "failed to get feedback")
	}

	// Update the existed feedback.
	feedback, err = j.DAO.UpdateFeedback(ctx, dal.UpdateFeedbackOption{
		FeedbackID: feedback.ID,
		Type:       opt.Type,
		Content:    opt.Content,
	})
	if errors.Is(err, dal.ErrNotUpdated) {
		return feedback, errors.WithMessage(journal.ErrFeedbackNotUpdated, "failed to update feedback")
	}

	return feedback, err
}

// empty returns true if the given value has the zero value for its type.
func empty(i interface{}) bool {
	g := reflect.ValueOf(i)
	if !g.IsValid() {
		return true
	}

	switch g.Kind() {
	default:
		return g.IsNil()
	case reflect.Array, reflect.Slice, reflect.Map, reflect.String:
		return g.Len() == 0
	case reflect.Bool:
		return !g.Bool()
	case reflect.Complex64, reflect.Complex128:
		return g.Complex() == 0
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return g.Int() == 0
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr:
		return g.Uint() == 0
	case reflect.Float32, reflect.Float64:
		return g.Float() == 0
	case reflect.Struct:
		return false
	}
}

func (j *JournalService) CreateCompletionLog(ctx context.Context, opt service.CreateCodeCompletionLogOption) (*entity.CodeCompletion, error) {
	res, err := j.DAO.CreateCodeCompletionLog(ctx, dal.CreateCodeCompletionLogOption{
		AppID:            opt.AppId,
		Username:         opt.Username,
		ModelName:        opt.ModelName,
		Status:           opt.Status,
		SessionId:        opt.SessionId,
		Prompt:           opt.Prompt,
		Suffix:           opt.Suffix,
		ContentRaw:       po.CodeCompletionContentRaw(opt.ContentRaw),
		ContentProcessed: po.CodeCompletionContentProcessed(opt.ContentProcessed),
		EnvMetadata:      po.CodeCompletionEnvMetadata(opt.EnvMetadata),
		RequestMetadata:  po.CodeCompletionRequestMetadata(opt.RequestMetadata),
		RequestTimestamp: opt.RequestTimestamp,
		IsEncrypted:      opt.IsEncrypted,
	})

	if err != nil {
		return nil, errors.WithMessage(err, "failed to create db record")
	}

	return res, nil
}

func (j *JournalService) UpdateCompletionLog(ctx context.Context, opt service.UpdateCodeCompletionLogOption) (*entity.CodeCompletion, error) {
	log, err := j.DAO.GetCodeCompletionLogById(ctx, opt.ID)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get db record")
	}

	// merge from two metadata
	requestMetadata := &entity.CodeCompletionRequestMetadata{
		Prefix:                lo.Ternary(empty(opt.RequestMetadata.Prefix), log.RequestMetadata.Prefix, opt.RequestMetadata.Prefix),
		ProgrammingLanguage:   lo.Ternary(empty(opt.RequestMetadata.ProgrammingLanguage), log.RequestMetadata.ProgrammingLanguage, opt.RequestMetadata.ProgrammingLanguage),
		OtherRequestInfo:      lo.Ternary(empty(opt.RequestMetadata.OtherRequestInfo), log.RequestMetadata.OtherRequestInfo, opt.RequestMetadata.OtherRequestInfo),
		PromptType:            lo.Ternary(empty(opt.RequestMetadata.PromptType), log.RequestMetadata.PromptType, opt.RequestMetadata.PromptType),
		ActualPrompt:          lo.Ternary(empty(opt.RequestMetadata.ActualPrompt), log.RequestMetadata.ActualPrompt, opt.RequestMetadata.ActualPrompt),
		Usage:                 lo.Ternary(empty(opt.RequestMetadata.Usage), log.RequestMetadata.Usage, opt.RequestMetadata.Usage),
		Latency:               lo.Ternary(empty(opt.RequestMetadata.Latency), log.RequestMetadata.Latency, opt.RequestMetadata.Latency),
		RPCPreprocessLatency:  lo.Ternary(empty(opt.RequestMetadata.RPCPreprocessLatency), log.RequestMetadata.RPCPreprocessLatency, opt.RequestMetadata.RPCPreprocessLatency),
		RPCPostprocessLatency: lo.Ternary(empty(opt.RequestMetadata.RPCPostprocessLatency), log.RequestMetadata.RPCPostprocessLatency, opt.RequestMetadata.RPCPostprocessLatency),
		BernardInferLatency:   lo.Ternary(empty(opt.RequestMetadata.BernardInferLatency), log.RequestMetadata.BernardInferLatency, opt.RequestMetadata.BernardInferLatency),
		EmptyReasons:          lo.Ternary(empty(opt.RequestMetadata.EmptyReasons), log.RequestMetadata.EmptyReasons, opt.RequestMetadata.EmptyReasons),
		Error:                 lo.Ternary(empty(opt.RequestMetadata.Error), log.RequestMetadata.Error, opt.RequestMetadata.Error),
	}

	res, err := j.DAO.UpdateCodeCompletionLog(ctx, dal.UpdateCodeCompletionLogOption{
		ID:               opt.ID,
		AppID:            opt.AppId,
		Username:         opt.Username,
		ModelName:        opt.ModelName,
		Status:           opt.Status,
		SessionId:        opt.SessionId,
		Prompt:           opt.Prompt,
		Suffix:           opt.Suffix,
		ContentRaw:       opt.ContentRaw,
		ContentProcessed: opt.ContentProcessed,
		EnvMetadata:      opt.EnvMetadata,
		RequestMetadata:  requestMetadata,
		RequestTimestamp: opt.RequestTimestamp,
		IsEncrypted:      opt.IsEncrypted,
	})

	if err != nil {
		return nil, errors.WithMessage(err, "failed to update db record")
	}

	return res, nil
}

func (j *JournalService) CreateCompletionMultiPointLog(ctx context.Context, opt service.CreateCompletionMultiPointLogOption) (*entity.CompletionMultiPoint, error) {
	res, err := j.DAO.CreateCompletionMultiPointLog(ctx, dal.CreateCompletionMultiPointLogOption{
		AppID:            opt.AppID,
		Username:         opt.Username,
		ModelName:        opt.ModelName,
		Status:           opt.Status,
		SessionID:        opt.SessionID,
		CursorEdit:       po.CompletionMultiPointCursorEdit(opt.CursorEdit),
		ExtraContext:     po.CompletionMultiPointExtraContext(opt.ExtraContext),
		Prompt:           opt.Prompt,
		ContentRaw:       po.CompletionMultiPointContentRaw(opt.ContentRaw),
		ContentProcessed: po.CompletionMultiPointContentProcessed(opt.ContentProcessed),
		EnvMetadata:      po.CompletionMultiPointEnvMetadata(opt.EnvMetadata),
		RequestMetadata:  po.CompletionMultiPointRequestMetadata(opt.RequestMetadata),
		RequestTimestamp: opt.RequestTimestamp,
		IsEncrypted:      opt.IsEncrypted,
	})

	if err != nil {
		return nil, errors.WithMessage(err, "failed to create db record")
	}

	return res, nil
}

func (j *JournalService) UpdateCompletionMultiPointLog(ctx context.Context, opt service.UpdateCompletionMultiPointLogOption) (*entity.CompletionMultiPoint, error) {
	log, err := j.DAO.GetCompletionMultiPointLogByID(ctx, opt.ID)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get db record")
	}

	// merge from two metadata
	requestMetadata := &entity.CompletionMultiPointRequestMetadata{
		ProgrammingLanguage:   lo.Ternary(empty(opt.RequestMetadata.ProgrammingLanguage), log.RequestMetadata.ProgrammingLanguage, opt.RequestMetadata.ProgrammingLanguage),
		TriggerMode:           lo.Ternary(empty(opt.RequestMetadata.TriggerMode), log.RequestMetadata.TriggerMode, opt.RequestMetadata.TriggerMode),
		ModelVersion:          lo.Ternary(empty(opt.RequestMetadata.ModelVersion), log.RequestMetadata.ModelVersion, opt.RequestMetadata.ModelVersion),
		OtherRequestInfo:      lo.Ternary(empty(opt.RequestMetadata.OtherRequestInfo), log.RequestMetadata.OtherRequestInfo, opt.RequestMetadata.OtherRequestInfo),
		OriginRequestMetadata: lo.Ternary(empty(opt.RequestMetadata.OriginRequestMetadata), log.RequestMetadata.OriginRequestMetadata, opt.RequestMetadata.OriginRequestMetadata),
		PromptType:            lo.Ternary(empty(opt.RequestMetadata.PromptType), log.RequestMetadata.PromptType, opt.RequestMetadata.PromptType),
		Usage:                 lo.Ternary(empty(opt.RequestMetadata.Usage), log.RequestMetadata.Usage, opt.RequestMetadata.Usage),
		Latency:               lo.Ternary(empty(opt.RequestMetadata.Latency), log.RequestMetadata.Latency, opt.RequestMetadata.Latency),
		RPCPreprocessLatency:  lo.Ternary(empty(opt.RequestMetadata.RPCPreprocessLatency), log.RequestMetadata.RPCPreprocessLatency, opt.RequestMetadata.RPCPreprocessLatency),
		RPCPostprocessLatency: lo.Ternary(empty(opt.RequestMetadata.RPCPostprocessLatency), log.RequestMetadata.RPCPostprocessLatency, opt.RequestMetadata.RPCPostprocessLatency),
		BernardInferLatency:   lo.Ternary(empty(opt.RequestMetadata.BernardInferLatency), log.RequestMetadata.BernardInferLatency, opt.RequestMetadata.BernardInferLatency),
		EmptyReasons:          lo.Ternary(empty(opt.RequestMetadata.EmptyReasons), log.RequestMetadata.EmptyReasons, opt.RequestMetadata.EmptyReasons),
		Error:                 lo.Ternary(empty(opt.RequestMetadata.Error), log.RequestMetadata.Error, opt.RequestMetadata.Error),
	}

	res, err := j.DAO.UpdateCompletionMultiPointLog(ctx, dal.UpdateCompletionMultiPointLogOption{
		ID:               opt.ID,
		AppID:            opt.AppID,
		Username:         opt.Username,
		ModelName:        opt.ModelName,
		Status:           opt.Status,
		SessionID:        opt.SessionID,
		CursorEdit:       opt.CursorEdit,
		ExtraContext:     opt.ExtraContext,
		Prompt:           opt.Prompt,
		ContentRaw:       opt.ContentRaw,
		ContentProcessed: opt.ContentProcessed,
		EnvMetadata:      opt.EnvMetadata,
		RequestMetadata:  requestMetadata,
		RequestTimestamp: opt.RequestTimestamp,
		IsEncrypted:      opt.IsEncrypted,
	})

	if err != nil {
		return nil, errors.WithMessage(err, "failed to update db record")
	}

	return res, nil
}

func (j *JournalService) CreatePromptPipeline(ctx context.Context, opt service.CreatePromptPipelineOption) (*entity.PromptPipeline, error) {
	res, err := j.DAO.CreatePromptPipeline(ctx, dal.CreatePromptPipelineOption{
		AppID:            opt.AppID,
		Username:         opt.Username,
		PipelineName:     string(opt.PipelineName),
		SessionID:        opt.SessionID,
		IntentName:       opt.IntentName,
		UserInput:        opt.UserInput,
		PromptComponents: po.PromptPipelinePromptComponents(opt.PromptComponents),
		Variables:        po.PromptPipelineContextVariables(opt.Variables),
		Metadata:         po.PromptPipelineMetadata(opt.Metadata),
	})

	if err != nil {
		return nil, errors.WithMessage(err, "failed to create db record")
	}

	return res, nil
}
