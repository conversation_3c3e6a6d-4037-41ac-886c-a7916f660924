package service

import (
	"time"

	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/gopkg/logs/v2"
)

// startCacheUpdate<PERSON>oop starts a background goroutine to update the cache periodically
func (s *documentRagService) startCacheUpdateLoop() {
	// Get update interval from config, default to 1 minute
	updateInterval := time.Minute

	// Add null check for tccParamsConfig
	if s.tccParamsConfig != nil && s.tccParamsConfig.GetPointer() != nil {
		tccUpdateInterval := s.tccParamsConfig.GetPointer().CacheUpdateIntervalMinutes
		if tccUpdateInterval > 0 {
			updateInterval = time.Duration(tccUpdateInterval) * time.Minute
		}
	} else {
		logs.Warn("[DocumentRagService.startCacheUpdateLoop] tccParamsConfig is nil, using default update interval")
	}

	// Start a goroutine with recover mechanism
	go func() {
		// Add recover mechanism to prevent goroutine from crashing
		defer func() {
			if r := recover(); r != nil {
				logs.Error("[DocumentRagService.startCacheUpdateLoop] goroutine panic recovered: %v", r)
				// Restart the goroutine after a short delay
				time.Sleep(time.Second)
				s.startCacheUpdateLoop()
			}
		}()

		ticker := time.NewTicker(updateInterval)
		defer ticker.Stop()

		// Update cache immediately when service starts
		s.updateCache()

		for {
			select {
			case <-ticker.C:
				s.updateCache()
			case <-s.stopChan:
				return
			}
		}
	}()
}

// updateCache updates the official document cache
func (s *documentRagService) updateCache() {
	// 增加错误重试机制
	maxRetries := 3
	for i := 0; i < maxRetries; i++ {
		if err := s.doUpdateCache(); err != nil {
			logs.Error("failed to update cache, retry %d: %v", i+1, err)
			time.Sleep(time.Second * time.Duration(i+1))
			continue
		}
		break
	}
}

// doUpdateCache updates the official document cache
func (s *documentRagService) doUpdateCache() error {
	// Add null check for tccOfficialDocsConfig
	if s.tccOfficialDocsConfig == nil || s.tccOfficialDocsConfig.GetPointer() == nil {
		logs.Warn("[DocumentRagService.doUpdateCache] tccOfficialDocsConfig is nil, skipping cache update")
		return nil
	}

	latestVersion := s.tccOfficialDocsConfig.GetPointer().OfficialDocSetsVersion
	logs.Info("[DocumentRagService.updateCache] Got latest version=%s", latestVersion)

	// Check if version needs update
	s.versionMutex.RLock()
	if latestVersion == s.cachedOfficialDocumentVersion {
		s.versionMutex.RUnlock()
		logs.Info("[DocumentRagService.updateCache] Cache is up to date, version=%s", latestVersion)
		return nil
	}
	s.versionMutex.RUnlock()

	// Need to update, get write lock
	s.versionMutex.Lock()
	// Double check to avoid updating by other goroutines
	if latestVersion == s.cachedOfficialDocumentVersion {
		logs.Info("[DocumentRagService.updateCache] Cache is up to date after double check, version=%s", latestVersion)
		s.versionMutex.Unlock()
		return nil
	}
	s.cachedOfficialDocumentVersion = latestVersion
	s.versionMutex.Unlock()

	// Update map
	s.mapMutex.Lock()
	defer s.mapMutex.Unlock()
	defer logs.Info("[DocumentRagService.updateCache] Updated cache map, before size=%d", len(s.cachedOfficialDocumentMap))
	s.cachedOfficialDocumentMap = make(map[string]config.DocumentRagOfficialSet)

	// Add null check for OfficialDocSets
	officialDocSets := s.tccOfficialDocsConfig.GetPointer().OfficialDocSets
	if officialDocSets == nil {
		logs.Warn("[DocumentRagService.doUpdateCache] OfficialDocSets is nil, cache map will be empty")
		return nil
	}

	for _, docSet := range officialDocSets {
		key := getDocumentKey(docSet.URL, docSet.Prefix)
		s.cachedOfficialDocumentMap[key] = docSet
		logs.Info("[DocumentRagService.updateCache] Added doc set to cache: tag=%s, entryPoint=%s, prefix=%s, version=%s", docSet.Tag, docSet.URL, docSet.Prefix, docSet.DocVersion)
	}
	logs.Info("[DocumentRagService.updateCache] Updated cache map, updated size=%d", len(s.cachedOfficialDocumentMap))
	return nil
}

// forEachOfficialDocumentSet iterates over the cached official document sets
func (s *documentRagService) forEachOfficialDocumentSet(fn func(docSet config.DocumentRagOfficialSet)) {
	s.mapMutex.RLock()
	defer s.mapMutex.RUnlock()

	for _, docSet := range s.cachedOfficialDocumentMap {
		fn(docSet)
	}
}

// Close gracefully shuts down the service
func (s *documentRagService) Close() {
	// Signal the background goroutine to stop
	close(s.stopChan)

	// Wait a bit to ensure the goroutine has stopped
	time.Sleep(100 * time.Millisecond)

	logs.Info("[DocumentRagService.Close] Service gracefully shut down")
}
