package handler

import (
	"context"

	"code.byted.org/devgpt/kiwis/copilotstack/common/auth/handler"
	"code.byted.org/devgpt/kiwis/lib/apierror"

	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/common"
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/documentrag"
	"code.byted.org/devgpt/kiwis/idecopilot/documentrag/entity"
	"code.byted.org/devgpt/kiwis/idecopilot/documentrag/service"
	"code.byted.org/devgpt/kiwis/lib/kitex"
	"code.byted.org/gopkg/logs"
	"github.com/pkg/errors"
)

// KitexHandler handles RPC requests for document rag operations
type KitexHandler struct {
	Service service.DocumentRagService
}

// NewKitexHandler creates a new instance of KitexHandler
func NewKitexHandler(service service.DocumentRagService) *KitexHandler {
	return &KitexHandler{
		Service: service,
	}
}

func (h *<PERSON><PERSON><PERSON>andler) CheckShouldUpdateOfficialDocuments(ctx context.Context,
	req *documentrag.CheckShouldUpdateOfficialDocumentsRequest) (*documentrag.CheckShouldUpdateOfficialDocumentsResponse, error) {
	if req.IdeProductName != nil {
		ctx = logs.CtxAddKVs(ctx, "ide_product_name", *req.IdeProductName)
	}
	if req.IdeVersion != nil {
		ctx = logs.CtxAddKVs(ctx, "ide_version", *req.IdeVersion)
	}
	if req.ExtensionVersion != nil {
		ctx = logs.CtxAddKVs(ctx, "extension_version", *req.ExtensionVersion)
	}

	account, ok := handler.GetAccount(ctx)
	if !ok {
		return nil, kitex.NewKitexError(common.ErrorCode_ErrNoAuth, errors.WithMessage(&apierror.ErrNoAuth, "failed to get account"))
	}
	ctx = logs.CtxAddKVs(ctx, "uid", account.Username)

	shouldUpdate, err := h.Service.CheckShouldUpdateOfficialDocuments(ctx, account.Username, req.GetVersion())
	if err != nil {
		logs.CtxError(ctx, "failed to get latest version: %v", err)
		return nil, kitex.NewKitexError(common.ErrorCode_ErrInternal, errors.WithMessage(err, "failed to get latest version"))
	}

	return &documentrag.CheckShouldUpdateOfficialDocumentsResponse{
		ShouldUpdate: shouldUpdate,
	}, nil
}

func (h *KitexHandler) LatestOfficialDocSets(ctx context.Context,
	req *documentrag.LatestOfficialDocSetsRequest) (*documentrag.LatestOfficialDocSetsResponse, error) {
	if req.IdeProductName != nil {
		ctx = logs.CtxAddKVs(ctx, "ide_product_name", *req.IdeProductName)
	}
	if req.IdeVersion != nil {
		ctx = logs.CtxAddKVs(ctx, "ide_version", *req.IdeVersion)
	}
	if req.ExtensionVersion != nil {
		ctx = logs.CtxAddKVs(ctx, "extension_version", *req.ExtensionVersion)
	}
	account, ok := handler.GetAccount(ctx)
	if !ok {
		return nil, kitex.NewKitexError(common.ErrorCode_ErrNoAuth, errors.WithMessage(&apierror.ErrNoAuth, "failed to get account"))
	}
	ctx = logs.CtxAddKVs(ctx, "uid", account.Username)

	sets, version, err := h.Service.LatestOfficialDocSets(ctx, account.Username)
	if err != nil {
		logs.CtxError(ctx, "failed to get document set list: %v", err)
		return nil, kitex.NewKitexError(common.ErrorCode_ErrInternal, errors.WithMessage(err, "failed to get document set list"))
	}

	return &documentrag.LatestOfficialDocSetsResponse{
		Version:         version,
		DocumentSetList: convertEntityDocumentSetListToKitex(sets),
	}, nil
}

func (h *KitexHandler) GetDocumentSetStatus(ctx context.Context, req *documentrag.GetDocumentSetStatusRequest) (*documentrag.GetDocumentSetStatusResponse, error) {
	if req.IdeProductName != nil {
		ctx = logs.CtxAddKVs(ctx, "ide_product_name", *req.IdeProductName)
	}
	if req.IdeVersion != nil {
		ctx = logs.CtxAddKVs(ctx, "ide_version", *req.IdeVersion)
	}
	if req.ExtensionVersion != nil {
		ctx = logs.CtxAddKVs(ctx, "extension_version", *req.ExtensionVersion)
	}

	account, ok := handler.GetAccount(ctx)
	if !ok {
		return nil, kitex.NewKitexError(common.ErrorCode_ErrNoAuth, errors.WithMessage(&apierror.ErrNoAuth, "failed to get account"))
	}
	ctx = logs.CtxAddKVs(ctx, "uid", account.Username)

	sets, detailedStatus, err := h.Service.GetDocumentSetStatus(ctx, account.Username, convertKitexDocumentSetListToEntity(req.GetDocumentSetList()), req.GetForceRefresh())
	if err != nil {
		logs.CtxError(ctx, "failed to get document set status: %v", err)
		return nil, kitex.NewKitexError(common.ErrorCode_ErrInternal, errors.WithMessage(err, "failed to get document set status"))
	}

	return &documentrag.GetDocumentSetStatusResponse{
		DocumentSetList:    convertEntityDocumentSetListToKitex(sets),
		DetailedStatusList: convertEntityDocumentSetListToDetailedStatusList(detailedStatus),
	}, nil
}

func (h *KitexHandler) IndexDocumentSet(ctx context.Context, req *documentrag.IndexDocumentSetRequest) (
	*documentrag.IndexDocumentSetResponse, error) {
	if req.IdeProductName != nil {
		ctx = logs.CtxAddKVs(ctx, "ide_product_name", *req.IdeProductName)
	}
	if req.IdeVersion != nil {
		ctx = logs.CtxAddKVs(ctx, "ide_version", *req.IdeVersion)
	}
	if req.ExtensionVersion != nil {
		ctx = logs.CtxAddKVs(ctx, "extension_version", *req.ExtensionVersion)
	}

	account, ok := handler.GetAccount(ctx)
	if !ok {
		return nil, kitex.NewKitexError(common.ErrorCode_ErrNoAuth, errors.WithMessage(&apierror.ErrNoAuth, "failed to get account"))
	}
	ctx = logs.CtxAddKVs(ctx, "uid", account.Username)

	indexTime, version, err := h.Service.IndexDocumentSet(ctx, account.Username, convertKitexDocumentSetToEntity(req.GetDocumentSet()), req.GetLastIndexTime())
	if err != nil {
		logs.CtxError(ctx, "failed to submit index document set: %v", err)
		return nil, kitex.NewKitexError(common.ErrorCode_ErrInternal, errors.WithMessage(err, "failed to index document set"))
	}

	return &documentrag.IndexDocumentSetResponse{
		IndexTime: indexTime,
		Version:   &version,
	}, nil
}

func (h *KitexHandler) SubscribeIndexDocumentSet(ctx context.Context, req *documentrag.SubscribeIndexDocumentSetRequest) (*documentrag.SubscribeIndexDocumentSetResponse, error) {
	account, ok := handler.GetAccount(ctx)
	if !ok {
		return nil, kitex.NewKitexError(common.ErrorCode_ErrNoAuth, errors.WithMessage(&apierror.ErrNoAuth, "failed to get account"))
	}
	ctx = logs.CtxAddKVs(ctx, "uid", account.Username)

	docEvent := &entity.DocumentEvent{
		EntryPoint: req.GetEntryPoint(),
		Prefix:     req.GetPrefix(),
		Version:    req.GetVersion(),
	}

	err := h.Service.SubscribeIndexDocumentSet(ctx, account.Username, docEvent)
	if err != nil {
		logs.CtxError(ctx, "failed to submit index document set: %v", err)
		return nil, kitex.NewKitexError(common.ErrorCode_ErrInternal, errors.WithMessage(err, "failed to index document set"))
	}

	return &documentrag.SubscribeIndexDocumentSetResponse{}, nil
}

func (h *KitexHandler) DeleteDocumentSet(ctx context.Context, req *documentrag.DeleteDocumentSetRequest) (*documentrag.
	DeleteDocumentSetResponse, error) {
	if req.IdeProductName != nil {
		ctx = logs.CtxAddKVs(ctx, "ide_product_name", *req.IdeProductName)
	}
	if req.IdeVersion != nil {
		ctx = logs.CtxAddKVs(ctx, "ide_version", *req.IdeVersion)
	}
	if req.ExtensionVersion != nil {
		ctx = logs.CtxAddKVs(ctx, "extension_version", *req.ExtensionVersion)
	}

	account, ok := handler.GetAccount(ctx)
	if !ok {
		return nil, kitex.NewKitexError(common.ErrorCode_ErrNoAuth, errors.WithMessage(&apierror.ErrNoAuth, "failed to get account"))
	}
	ctx = logs.CtxAddKVs(ctx, "uid", account.Username)

	version, err := h.Service.DeleteDocumentSet(ctx, account.Username, convertKitexDocumentSetToEntity(req.GetDocumentSet()))
	if err != nil {
		logs.CtxError(ctx, "failed to delete document set: %v", err)
		return nil, kitex.NewKitexError(common.ErrorCode_ErrInternal, errors.WithMessagef(err,
			"failed to delete document set"))
	}

	return &documentrag.DeleteDocumentSetResponse{
		Version: &version,
	}, nil
}

func (h *KitexHandler) RetrieveDocument(ctx context.Context, req *documentrag.RetrieveDocumentRequest) (*documentrag.
	RetrieveDocumentResponse,
	error) {
	if req.IdeProductName != nil {
		ctx = logs.CtxAddKVs(ctx, "ide_product_name", *req.IdeProductName)
	}
	if req.IdeVersion != nil {
		ctx = logs.CtxAddKVs(ctx, "ide_version", *req.IdeVersion)
	}
	if req.ExtensionVersion != nil {
		ctx = logs.CtxAddKVs(ctx, "extension_version", *req.ExtensionVersion)
	}

	account, ok := handler.GetAccount(ctx)
	if !ok {
		return nil, kitex.NewKitexError(common.ErrorCode_ErrNoAuth, errors.WithMessage(&apierror.ErrNoAuth, "failed to get account"))
	}
	ctx = logs.CtxAddKVs(ctx, "uid", account.Username)

	// Check required fields
	if req.DocumentSetList == nil || len(req.DocumentSetList) == 0 {
		return nil, errors.New("document set list is required")
	}
	if req.Query == "" {
		return nil, errors.New("query is required")
	}

	// Convert DocumentSet from thrift to entity
	documentSets := make([]*entity.DocumentSet, 0, len(req.DocumentSetList))
	for _, set := range req.DocumentSetList {
		documentSets = append(documentSets, &entity.DocumentSet{
			Tag:        set.GetTag(),
			Type:       set.GetType(),
			EntryPoint: set.GetEntryPoint(),
			Prefix:     set.GetPrefix(),
			Status:     set.GetStatus(),
			IndexTime:  set.GetIndexTime(),
			// No need to set version id: generated by service
		})
	}

	// Call service layer
	items, err := h.Service.Retrieve(ctx,
		account.Username,
		documentSets,
		req.GetQuery(),
		req.GetSelectedCodeContent(),
		req.GetQueryContext(),
	)
	if err != nil {
		return nil, err
	}

	// Convert RetrieveItem from entity to thrift
	itemList := make([]*documentrag.RetrieveDocumentItem, 0, len(items))
	for _, item := range items {
		title := item.Title
		url := item.URL
		content := item.RetrieveContent
		itemList = append(itemList, &documentrag.RetrieveDocumentItem{
			Title:   &title,
			Url:     &url,
			Content: &content,
			RelatedDoc: &documentrag.DocumentSet{
				Tag:        &item.RelatedDoc.Tag,
				Type:       &item.RelatedDoc.Type,
				EntryPoint: item.RelatedDoc.EntryPoint,
				Prefix:     item.RelatedDoc.Prefix,
				Status:     &item.RelatedDoc.Status,
				Version:    &item.RelatedDoc.Version,
			},
		})
	}

	return &documentrag.RetrieveDocumentResponse{
		ItemList: itemList,
	}, nil
}

func (h *KitexHandler) SubscribeUpdateDocumentSetStatus(ctx context.Context,
	req *documentrag.SubscribeUpdateDocumentSetStatusRequest) (*documentrag.SubscribeUpdateDocumentSetStatusResponse,
	error) {
	account, ok := handler.GetAccount(ctx)
	if !ok {
		return nil, kitex.NewKitexError(common.ErrorCode_ErrNoAuth, errors.WithMessage(&apierror.ErrNoAuth, "failed to get account"))
	}
	ctx = logs.CtxAddKVs(ctx, "uid", account.Username)

	docEvent := &entity.DocumentEvent{
		EntryPoint: req.EntryPoint,
		Prefix:     req.Prefix,
		Version:    req.Version,
	}

	err := h.Service.SubscribeUpdateDocumentSetStatus(ctx, account.Username, docEvent)
	if err != nil {
		logs.CtxError(ctx, "failed to subscribe get document set status: %v", err)
		return nil, kitex.NewKitexError(common.ErrorCode_ErrInternal, errors.WithMessage(err, "failed to subscribe get document set status"))
	}

	return &documentrag.SubscribeUpdateDocumentSetStatusResponse{}, nil
}

// Helper functions for converting between entity and kitex types
func convertKitexDocumentSetToEntity(set *documentrag.DocumentSet) *entity.DocumentSet {
	if set == nil {
		return nil
	}
	return &entity.DocumentSet{
		Tag:        set.GetTag(),
		Type:       set.GetType(),
		EntryPoint: set.GetEntryPoint(),
		Prefix:     set.GetPrefix(),
		IndexTime:  set.GetIndexTime(),
		Status:     set.GetStatus(),
		Version:    set.GetVersion(),
	}
}

func convertEntityDocumentSetToKitex(set *entity.DocumentSet) *documentrag.DocumentSet {
	if set == nil {
		return nil
	}
	return &documentrag.DocumentSet{
		Tag:        &set.Tag,
		Type:       &set.Type,
		EntryPoint: set.EntryPoint,
		Prefix:     set.Prefix,
		IndexTime:  &set.IndexTime,
		Status:     &set.Status,
		Version:    &set.Version,
	}
}

func convertKitexDocumentSetListToEntity(sets []*documentrag.DocumentSet) []*entity.DocumentSet {
	if sets == nil {
		return nil
	}
	result := make([]*entity.DocumentSet, 0, len(sets))
	for _, set := range sets {
		if entitySet := convertKitexDocumentSetToEntity(set); entitySet != nil {
			result = append(result, entitySet)
		}
	}
	return result
}

func convertEntityDocumentSetListToKitex(sets []*entity.DocumentSet) []*documentrag.DocumentSet {
	if sets == nil {
		return nil
	}
	result := make([]*documentrag.DocumentSet, 0, len(sets))
	for _, set := range sets {
		if kitexSet := convertEntityDocumentSetToKitex(set); kitexSet != nil {
			result = append(result, kitexSet)
		}
	}
	return result
}

func convertEntityDocumentSetListToDetailedStatusList(sets []*entity.DetailedDocumentSetStatus) []*documentrag.DetailedDocumentSetStatus {
	if sets == nil {
		return nil
	}

	result := make([]*documentrag.DetailedDocumentSetStatus, 0, len(sets))
	for _, set := range sets {
		result = append(result, &documentrag.DetailedDocumentSetStatus{
			DocumentSet:      convertEntityDocumentSetToKitex(&set.DocumentSet),
			DocumentPageList: convertEntityDocumentPageListToKitex(set.DocumentPages),
		})
	}

	return result
}

func convertEntityDocumentPageListToKitex(infos []*entity.DocumentPage) []*documentrag.DocumentPage {
	if infos == nil {
		return nil
	}
	result := make([]*documentrag.DocumentPage, 0, len(infos))
	for _, info := range infos {
		result = append(result, &documentrag.DocumentPage{
			Title:      &info.Title,
			Url:        &info.URL,
			Status:     &info.Status,
			ErrorMsg:   &info.ErrorMsg,
			UpdateTime: &info.UpdateTime,
		})
	}
	return result
}
