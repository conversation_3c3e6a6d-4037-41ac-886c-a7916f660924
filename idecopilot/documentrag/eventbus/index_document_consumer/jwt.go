package index_document_consumer

import (
	"fmt"
	"io"
	"net/http"
	"sync"

	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/gopkg/logs"
	"github.com/pkg/errors"
)

// JWTManager manages JWT tokens
type JWTManager struct {
	authURL   string
	tccConfig *config.DocumentRagTCCConfig
	cloudJwt  string
	mu        sync.RWMutex
}

// NewJWTManager creates a new JWTManager instance
func NewJWTManager(authURL string, tccConfig *config.DocumentRagTCCConfig) *JWTManager {
	return &JWTManager{
		authURL:   authURL,
		tccConfig: tccConfig,
	}
}

// GetJWT returns a valid JWT token
func (m *JWTManager) GetJWT() (string, error) {
	m.mu.RLock()
	jwt := m.cloudJwt
	m.mu.RUnlock()

	if jwt != "" {
		logs.Info("[DocumentRagIndexConsumer.GetJWT] using cached JWT token")
		return fmt.Sprintf("Byte-Cloud-JWT %s", jwt), nil
	}

	if err := m.updateJWT(); err != nil {
		logs.Error("[DocumentRagIndexConsumer.GetJWT] failed to update JWT token: %v", err)
		return "", err
	}

	m.mu.RLock()
	jwt = m.cloudJwt
	m.mu.RUnlock()

	return fmt.Sprintf("Byte-Cloud-JWT %s", jwt), nil
}

// updateJWT updates the JWT token
func (m *JWTManager) updateJWT() error {
	// Create request
	req, err := http.NewRequest("GET", m.authURL, nil)
	if err != nil {
		logs.Error("[DocumentRagIndexConsumer.updateJWT] failed to create request: %v", err)
		return err
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", m.tccConfig.SecretsConfig.GetPointer().DocumentRagEventBusSecret.ConsumerSecret.IAMSecret))

	// Send request
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		logs.Error("[DocumentRagIndexConsumer.updateJWT] failed to send request: %v", err)
		return err
	}
	defer resp.Body.Close()

	// Read response body for error messages
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logs.Error("[DocumentRagIndexConsumer.updateJWT] failed to read response body: %v", err)
		return err
	}

	// Check response status
	if resp.StatusCode != http.StatusOK {
		logs.Error("[DocumentRagIndexConsumer.updateJWT] request failed with status %d: %s", resp.StatusCode, string(body))
		return errors.Errorf("request failed with status %d", resp.StatusCode)
	}

	// Get JWT from header
	jwt := resp.Header.Get("x-jwt-token")
	if jwt == "" {
		logs.Error("[DocumentRagIndexConsumer.updateJWT] no JWT token in response header")
		return errors.New("no JWT token in response header")
	}

	// Update JWT
	m.mu.Lock()
	m.cloudJwt = jwt
	m.mu.Unlock()

	logs.Info("[DocumentRagIndexConsumer.updateJWT] successfully updated JWT token, jwt: %s", jwt)
	return nil
}
