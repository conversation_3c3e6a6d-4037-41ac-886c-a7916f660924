package testbed

import (
	"os"
	"path"
	"regexp"

	"code.byted.org/gopkg/logs/v2/log"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/idecopilot/dev/agentbench/entity"
	"code.byted.org/devgpt/kiwis/idecopilot/dev/agentbench/util"
)

// the execution enviroment of the instance
type Env struct {
	docker   *DockerManager
	evalLang string
	taskIns  *entity.Instance
	config   *entity.DockerConfig
	repoDir  string
}

func NewEnv(taskIns *entity.Instance, evalLang string, dockerConfig *entity.DockerConfig) *Env {
	env := Env{
		docker:   NewDockerManager(),
		evalLang: evalLang,
		taskIns:  taskIns,
		config:   dockerConfig,
		repoDir:  util.GetRepoDir(taskIns.Repo, taskIns.Version),
	}
	return &env
}

// get the URL of the current enviroment
func (e *Env) GetURL() string {
	return e.docker.URL
}

// start a docker enviroment
func (e *Env) Setup(logDir string, port string) error {
	dockerKey := util.GetDockerImageKey(e.taskIns, e.evalLang)
	if err := e.docker.Start(dockerKey, e.taskIns, e.config, logDir); err != nil {
		return errors.Wrapf(err, "cannot start a docker %s:%s", e.taskIns.Repo, e.taskIns.Version)
	}

	// start the local tool server
	// if _, err := e.docker.Exec("pip install python-lsp-server"); err != nil {
	// 	return errors.WithMessage(err, "cannot install the lsp server")
	// }
	if _, err := e.docker.Exec("nohup " + util.DockerAbsPath(e.taskIns.Repo, "tool_server") + " --port=" + port +
		" > " + util.DockerAbsPath(e.taskIns.Repo, "log", e.taskIns.InstanceID+"_toolcall.log") +
		" 2>&1 &"); err != nil {
		return errors.WithMessage(err, "cannot start the local tool server")
	}
	return nil
}

// reset the repo to base commit
func (e *Env) ResetCommit() error {
	_, err := e.docker.Exec("cd " + e.repoDir +
		" && git -c advice.detachedHead=false checkout " +
		e.taskIns.BaseCommit)
	if err != nil {
		return errors.Wrapf(err, "instance %s git checkout to base commit failed", e.taskIns.InstanceID)
	}

	switch e.taskIns.Repo {
	case "pytest-dev/pytest":
		if e.taskIns.Version == "5.4" {
			_, err := e.docker.ExecWithProxy("cd " + e.repoDir + " && " + util.GetCondaRun(e.taskIns.Repo, e.taskIns.Version) + " pip install -e .")
			if err != nil {
				return errors.Wrapf(err, "instance %s git preinstall failed", e.taskIns.InstanceID)
			}
		}
	case "pylint-dev/pylint":
		if e.taskIns.Version == "2.15" && e.taskIns.InstanceID != "pylint-dev__pylint-7080" {
			_, err := e.docker.ExecWithProxy("cd " + e.repoDir + " && " + util.GetCondaRun(e.taskIns.Repo, e.taskIns.Version) + " pip install -e .")
			if err != nil {
				return errors.Wrapf(err, "instance %s git preinstall failed", e.taskIns.InstanceID)
			}
		}
	case "sphinx-doc/sphinx":
		_, err := e.docker.Exec("cd " + e.repoDir + " && sed -i 's/pytest/pytest -rA/' tox.ini")
		if err != nil {
			return errors.Wrapf(err, "instance %s git preinstall failed", e.taskIns.InstanceID)
		}
	default:
	}
	return nil
}

// get the diff patch
func (e *Env) GetDiffPatch(genPatchDir *string) (string, error) {
	patchPath := util.GetPatchFileName(e.taskIns.InstanceID, "gen")
	patchAbsPath := util.DockerAbsPath(e.taskIns.Repo, patchPath)

	// get the current diff patch
	if _, err := e.docker.Exec("cd " + e.repoDir + " && git diff > " + patchAbsPath); err != nil {
		return "", errors.Wrapf(err, "cannot generate diff patch for instance %s", e.taskIns.InstanceID)
	} else {
		if err := e.docker.Copy(patchAbsPath, *genPatchDir, OUTDOCKER); err != nil {
			return "", errors.Wrapf(err, "generated patch %s cannot be copied", patchAbsPath)
		}
		return patchAbsPath, nil
	}
}

func (e *Env) GetPatchFromContent(dir string, content string) error {
	patchPath := util.GetPatchFileName(e.taskIns.InstanceID, "test")

	file, err := os.Create(path.Join(dir, patchPath))
	if err != nil {
		return err
	}
	defer file.Close()

	_, err = file.Write([]byte(content))
	if err != nil {
		return err
	}

	err = e.docker.Copy(path.Join(dir, patchPath), util.DockerAbsPath(e.taskIns.Repo, patchPath), INDOCKER)
	if err != nil {
		return err
	}
	return nil
}

// apply given patch to repo
func (e *Env) ApplyPatch(patchPath string) error {
	absPath := util.DockerAbsPath(e.taskIns.Repo, patchPath)
	if _, err := e.docker.Exec("cd " + e.repoDir + " && git apply -v " + absPath); err != nil {
		return errors.Wrapf(err, "cannot apply patch %s to instance %s", patchPath, e.taskIns.InstanceID)
	}
	return nil
}

// run unit tests of the repo
func (e *Env) RunTest(suffix string) error {
	e.docker.Exec("cd " + e.repoDir + " && " +
		util.GetCondaRun(e.taskIns.Repo, e.taskIns.Version) + " " + e.taskIns.TestCmd +
		" > " + util.GetTestLogName(e.taskIns, suffix) + " 2>&1")
	return nil
}

// extract the unit test tracback of the instnace
func (e *Env) ExtractTraceBack(logDir string, resDir string, suffix string) error {
	content, err := os.ReadFile(path.Join(logDir, e.taskIns.InstanceID+"_"+suffix+".log"))
	if err != nil {
		return err
	}
	log.V1.Info(string(content))
	traceBackReg := regexp.MustCompile(`(?s)Traceback (.*\n)*------------`)

	traceBacks := traceBackReg.FindAllStringSubmatch(string(content), -1)
	rawTraceBack := lo.Reduce(traceBacks, func(agg string, item []string, ind int) string {
		agg = agg + item[0]
		return agg
	}, "")
	log.V1.Info("The extracted traceback: %s", rawTraceBack)
	if len(rawTraceBack) > 0 {
		err = os.WriteFile(path.Join(resDir, e.taskIns.InstanceID+"_"+suffix+".log"), []byte(rawTraceBack), 0644)
		return err
	}
	return nil
}

// close the env
func (e *Env) Close() error {
	err := e.docker.Close()
	if err != nil {
		return err
	}
	return nil
}
