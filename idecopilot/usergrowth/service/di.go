package service

import (
	"go.uber.org/fx"

	"code.byted.org/devgpt/kiwis/idecopilot/usergrowth/service/security"
	"code.byted.org/devgpt/kiwis/lib/di"
)

var Module = fx.Options(
	fx.Provide(NewUserGrowthService),

	fx.Provide(fx.Annotate(NewLocker, fx.ParamTags(`name:"usergrowth_redis" optional:"true"`))),
	fx.Provide(di.Bind(new(Locker), new(Lock))),

	fx.Provide(NewRiskControlService),

	fx.Provide(security.NewSecurityService),
	fx.Provide(di.Bind(new(security.SecurityService), new(security.Service))),

	fx.Provide(fx.Annotate(NewUserGrowthRedisClient, fx.ParamTags(`name:"usergrowth_redis"`))),
)
