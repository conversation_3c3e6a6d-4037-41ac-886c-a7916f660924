package service

import (
	"context"
	"fmt"
	"testing"
	"time"

	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/port/redis"
	"github.com/alicebob/miniredis"
	"github.com/stretchr/testify/require"
)

func Test_calculateTTLToNextTargetHour(t *testing.T) {
	type args struct {
		currentTime time.Time
		targetHour  int
	}
	tests := []struct {
		name string
		args args
		want time.Duration
	}{
		{
			name: "9点 到 今天的10点",
			args: args{
				currentTime: time.Date(2023, time.October, 1, 9, 0, 0, 0, time.UTC),
				targetHour:  10,
			},
			want: 1 * time.Hour, // 今天的10点，即1小时
		},
		{
			name: "1点 到 今天2点",
			args: args{
				currentTime: time.Date(2023, time.October, 1, 1, 0, 0, 0, time.UTC),
				targetHour:  2,
			},
			want: 1 * time.Hour, // 今天2点，即1小时
		},
		{
			name: "11点 到 第二天的10点",
			args: args{
				currentTime: time.Date(2023, time.October, 1, 11, 0, 0, 0, time.UTC),
				targetHour:  10,
			},
			want: 23 * time.Hour, // 下一天的10点，即23小时
		},
		{
			name: "23点 到 第二天的10点",
			args: args{
				currentTime: time.Date(2023, time.October, 1, 23, 0, 0, 0, time.UTC),
				targetHour:  10,
			},
			want: 11 * time.Hour, // 下一天的10点，即11小时
		},
		{
			name: "1点 到 今天10点",
			args: args{
				currentTime: time.Date(2023, time.October, 1, 1, 0, 0, 0, time.UTC),
				targetHour:  10,
			},
			want: 9 * time.Hour,
		},
		{
			name: "13点 到 第二天10点",
			args: args{
				currentTime: time.Date(2023, time.October, 1, 13, 0, 0, 0, time.UTC),
				targetHour:  10,
			},
			want: 21 * time.Hour,
		},
		{
			name: "10点 到 第二天的10点",
			args: args{
				currentTime: time.Date(2023, time.October, 1, 10, 0, 0, 0, time.UTC),
				targetHour:  10,
			},
			want: 24 * time.Hour, // 第二天的10点，即24小时
		},
		{
			name: " 30号 10点 到 第二天的10点",
			args: args{
				currentTime: time.Date(2023, time.October, 30, 10, 0, 0, 0, time.UTC),
				targetHour:  10,
			},
			want: 24 * time.Hour, // 第二天的10点，即24小时
		},
		{
			name: "30号 13点 到 第二天10点",
			args: args{
				currentTime: time.Date(2023, time.October, 30, 13, 0, 0, 0, time.UTC),
				targetHour:  10,
			},
			want: 21 * time.Hour,
		},
		{
			name: "9点 到 第二天的0点",
			args: args{
				currentTime: time.Date(2024, time.October, 1, 9, 0, 0, 0, time.UTC),
				targetHour:  0,
			},
			want: 15 * time.Hour, // 今天的0点，即1小时
		},
		{
			name: "0点 到 第二天的0点",
			args: args{
				currentTime: time.Date(2024, time.October, 1, 0, 0, 0, 0, time.UTC),
				targetHour:  0,
			},
			want: 24 * time.Hour, // 今天的0点，即1小时
		},

		{
			name: "23点 到 第二天的0点",
			args: args{
				currentTime: time.Date(2024, time.October, 15, 23, 0, 0, 0, time.UTC),
				targetHour:  0,
			},
			want: 1 * time.Hour, // 今天的0点，即1小时
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := calculateTTLToNextTargetHour(tt.args.currentTime, tt.args.targetHour); got != tt.want {
				t.Errorf("calculateTTLToNextTargetHour() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestIncrUserQuotaAndGetUserCurrentQuota(t *testing.T) {
	s, err := miniredis.Run()
	// 不会保存修改修改记录
	require.NoError(t, err)
	defer s.Close()
	cli, _ := redis.NewBytedRedisClient(config.RedisConfig{
		PSM:  "",
		Addr: s.Addr(),
	})
	// testKey := "a::b::c" // user limit format
	fmt.Println(cli)
	handler := &UserGrowthRedisClient{
		Client: cli,
	}
	event_name := "2025_copartner"
	userID := "2821826283845780"
	// got, _ := handler.GetEventQualifiedUser(context.TODO(), event_name, userID)
	// fmt.Printf("%+v\n", got)
	err = handler.SetEventQualifiedUser(context.TODO(), event_name, userID)
	fmt.Printf("error: %v\n", err)
	got, err := handler.GetEventQualifiedUser(context.TODO(), event_name, userID)
	fmt.Printf("%+v\n", got)
	// s, err := miniredis.Run()
	// require.NoError(t, err)
	// defer s.Close()
	// cli, err := redis.NewBytedRedisClient(config.RedisConfig{
	// 	PSM:  "",
	// 	Addr: s.Addr(),
	// })

}
