package kitex_handler

import (
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/idecopilot"
	kbentity "code.byted.org/devgpt/kiwis/api/idl/kitex_gen/knowledgebase"
)

// convertXXXSegment convert entity.XXXSegment to knowledgebase.XXXSegment.
// entity.XXXSegment is generated by <PERSON><PERSON>, knowledgebase.XXXSegment is generated by Kitex.
// In the near future, they will be merged into one.

func convertSegment(projectID, fileID string, segment *kbentity.Segment) *idecopilot.Segment {
	return &idecopilot.Segment{
		Type:                 segment.Type,
		FileID:               fileID,
		ProjectID:            projectID,
		Tags:                 segment.Tags,
		CodeSegment:          segment.CodeSegment,
		TextSegment:          segment.TextSegment,
		FolderSegment:        segment.FolderSegment,
		FileSegment:          segment.FileSegment,
		FileTopLevelSegment:  segment.FileTopLevelSegment,
		ClassSegment:         segment.ClassSegment,
		ClassTopLevelSegment: segment.ClassTopLevelSegment,
		MethodSegment:        segment.MethodSegment,
		CodeChunkSegment:     segment.CodeChunkSegment,
		RelationSegment:      segment.RelationSegment,
	}
}

func convertSegmentWithEmbedding(fileID string, segmentWithEmbedding *kbentity.SegmentWithEmbedding) *idecopilot.SegmentWithEmbedding {
	return &idecopilot.SegmentWithEmbedding{
		EntityID:          segmentWithEmbedding.EntityID,
		SegmentUniqueID:   segmentWithEmbedding.SegmentUniqueID,
		Segment:           convertSegment("", fileID, segmentWithEmbedding.Segment),
		IsAllEmbedding:    segmentWithEmbedding.IsAllEmbedding,
		EmbeddingContents: segmentWithEmbedding.EmbeddingContents,
		Embeddings:        segmentWithEmbedding.Embeddings,
	}
}

func convertSplitFile(file *idecopilot.SplitFile) *kbentity.SplitFile {
	return &kbentity.SplitFile{
		Content:   file.Content,
		Path:      file.Path,
		ChunkSize: file.ChunkSize,
		ChunkType: file.ChunkType,
		Title:     file.Title,
	}
}
