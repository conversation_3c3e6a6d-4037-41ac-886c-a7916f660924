package handler

import (
	"context"
	"runtime/debug"
	"time"

	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/gopkg/logs/v2/log"
)

func (a *AILimitQueueHandler) monitorQueue(queue string, metricQueue string) {
	a.queues.Store(queue, QueueMonitorInfo{MetricQueueName: metricQueue})
}

type QueueMonitorInfo struct {
	MetricQueueName string
}

func (a *AILimitQueueHandler) startMonitor(timerLoop time.Duration) {
	if a == nil || !a.aiLimitQueueConfig.Enable { // 静态配置文件内没开启功能, 控制非全region发布
		return
	}
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.V1.CtxError(context.Background(), "panic in ai limit queue handler monitoring queue size goroutine: %+v, stacktrace: %s", r, string(debug.Stack()))
			}
		}()

		timer := time.NewTicker(timerLoop)
		defer timer.Stop()
		for range timer.C {
			a.queues.Range(func(key, val any) bool {
				ctx := context.Background()
				queueName := key.(string)
				info := val.(QueueMonitorInfo)
				size, err := a.QueueSize(ctx, queueName)
				if err == nil {
					_ = metrics.CM.AILimitQueueRawQueueSizeTag.WithTags(&metrics.RawQueueSizeTag{
						QueueName: info.MetricQueueName,
					}).Store(float64(size))
				} else {
					log.V1.CtxError(ctx, "get queue: %s request err: %+v", queueName, err)
				}
				return true
			})
		}
	}()
}
