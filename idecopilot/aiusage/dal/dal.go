package dal

import (
	"code.byted.org/devgpt/kiwis/idecopilot/aiusage/dal/po"
	dbclient "code.byted.org/devgpt/kiwis/port/db"
	"code.byted.org/gopkg/logs/v2/log"
	"context"
	"fmt"
	"github.com/go-sql-driver/mysql"
	"github.com/mattn/go-sqlite3"
	"github.com/pkg/errors"
	"gorm.io/gorm"
	"time"
)

type DAO struct {
	Cli dbclient.Client
}

func NewDAO(cli dbclient.Client) *DAO {
	return &DAO{cli}
}

func (d *DAO) CreateAndIncreaseAIUsage(ctx context.Context, record *po.AiUsageRecordPO, usage *po.AiUsagePO) error {
	db := d.Cli.NewRequest(ctx)
	err := db.Transaction(func(tx *gorm.DB) error {
		isInserted, err := d.CreateAIUsageRecord(db, record)
		if err != nil {
			return errors.Wrap(err, "failed to create ai-usage-record")
		}
		if !isInserted {
			log.V1.CtxWarn(ctx, "duplicate ai-usage-record, uuid: %s", record.EntitlementUuid)
			return nil
		}

		err = d.CreateAIUsage(db, usage)
		if err != nil {
			return errors.Wrap(err, "failed to create ai-usage")
		}

		err = d.IncrementUsage(db, usage.EntitlementUpdateID)
		if err != nil {
			return errors.Wrap(err, "failed to update ai-usage")
		}
		return nil
	})
	return err
}

func (d *DAO) CreateAIUsageRecord(db *gorm.DB, record *po.AiUsageRecordPO) (bool, error) {
	err := db.Create(record).Error
	if err != nil {
		if mysqlErr, ok := err.(*mysql.MySQLError); ok && mysqlErr.Number == 1062 {
			return false, nil
		}
		if sqliteErr, ok := err.(sqlite3.Error); ok && sqliteErr.Code == sqlite3.ErrConstraint {
			if sqliteErr.ExtendedCode == sqlite3.ErrConstraintUnique ||
				sqliteErr.ExtendedCode == sqlite3.ErrConstraintPrimaryKey {
				return false, nil
			}
		}
		return false, err
	}
	return true, nil
}

func (d *DAO) CreateAIUsage(db *gorm.DB, usage *po.AiUsagePO) error {
	result := db.Create(usage)
	if result.Error != nil {
		// 检查是否是唯一键冲突错误
		if mysqlErr, ok := result.Error.(*mysql.MySQLError); ok && mysqlErr.Number == 1062 {
			return nil
		}
		if sqliteErr, ok := result.Error.(sqlite3.Error); ok && sqliteErr.Code == sqlite3.ErrConstraint {
			if sqliteErr.ExtendedCode == sqlite3.ErrConstraintUnique ||
				sqliteErr.ExtendedCode == sqlite3.ErrConstraintPrimaryKey {
				return nil
			}
		}
		return result.Error
	}
	return nil
}

func GetEntitlementUpdateID(args ...interface{}) (string, error) {
	if len(args) != 5 {
		return "", errors.New("parameters not valid")
	}
	return fmt.Sprintf("%d_%d_%s_%s_%s", args...), nil
}

func (d *DAO) IncrementUsage(db *gorm.DB, entitlementUpdateID string) error {
	result := db.Model(&po.AiUsagePO{}).
		Where("entitlement_update_id = ?", entitlementUpdateID).
		Updates(map[string]interface{}{
			"usage":      gorm.Expr("`usage` + ?", 1),
			"updated_at": time.Now(),
		})

	if result.Error != nil {
		return result.Error
	}

	if result.RowsAffected == 0 {
		return errors.New("record not found")
	}

	return nil
}

type GetAIUsageOption struct {
	AppID              string
	UserID             string
	EntitlementID      int64
	EntitlementEndTime int64
	UsageType          string
}

func (d *DAO) GetAIUsage(ctx context.Context, opt GetAIUsageOption) (*po.AiUsagePO, error) {
	usage := &po.AiUsagePO{}
	uuid, err := GetEntitlementUpdateID(opt.EntitlementID, opt.EntitlementEndTime, opt.UsageType, opt.AppID, opt.UserID)
	if err != nil {
		return usage, err
	}
	if err = d.Cli.NewRequest(ctx).
		Where("entitlement_update_id = ?", uuid).
		Find(&usage).Error; err != nil {
		return usage, err
	}

	return usage, nil
}
