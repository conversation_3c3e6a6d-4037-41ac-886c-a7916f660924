package dal

import (
	"context"
	"testing"

	"github.com/samber/lo"
	"github.com/stretchr/testify/require"

	"code.byted.org/devgpt/kiwis/idecopilot/agent/dal/po"
	"code.byted.org/devgpt/kiwis/idecopilot/agent/entity"
	"code.byted.org/devgpt/kiwis/lib/cmp"
	"code.byted.org/devgpt/kiwis/port/db"
)

func TestMemoryDAO_CURD(t *testing.T) {
	ctx := context.Background()
	tests := []struct {
		name string
		f    func(t *testing.T, dao *DAO)
	}{
		{
			name: "curd",
			f: func(t *testing.T, dao *DAO) {
				_, err := dao.GetAgentMemoryByID(ctx, "1")
				require.Error(t, err)

				// create
				po1, err := dao.CreateAgentMemory(ctx, CreateAgentMemoryOption{
					ID:            "1",
					RunID:         "1",
					StepID:        "1",
					AgentID:       "1",
					Status:        "test",
					Content:       "",
					StructContent: make(map[string]any),
				})

				require.NoError(t, err)
				require.NotNil(t, po1)
				require.Equal(t, "1", po1.ID)
				require.Equal(t, "1", po1.RunID)
				require.Equal(t, "1", po1.StepID)
				require.Equal(t, "1", po1.AgentID)
				require.Equal(t, "test", po1.Status)
				require.Equal(t, "", po1.Content)

				po2, err := dao.GetAgentMemoryByID(ctx, "1")
				require.NoError(t, err)
				require.NoError(t, cmp.CompareStruct(po1, po2))

				// update
				po3, err := dao.UpdateAgentMemory(ctx, UpdateAgentMemoryOption{
					ID:      "1",
					Content: lo.ToPtr("2"),
					Status:  (*entity.MemoryStatus)(lo.ToPtr("test2")),
				})

				require.NoError(t, err)
				require.NotNil(t, po3)
				require.Equal(t, "1", po3.ID)
				require.Equal(t, "2", po3.Content)
				require.Equal(t, "test2", po3.Status)

				// list
				pos, err := dao.ListAgentMemory(ctx, ListAgentMemoryOption{
					RunID: lo.ToPtr("1"),
					Limit: 1,
				})
				require.NoError(t, err)
				require.Len(t, pos, 1)
				require.NoError(t, cmp.CompareStruct(po3, pos[0]))

			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cli, err := db.NewMockSQLiteClient(&po.AgentRunMemoryPO{})
			require.NoError(t, err)

			dao := NewDAO(cli)
			tt.f(t, dao)
		})
	}

}
