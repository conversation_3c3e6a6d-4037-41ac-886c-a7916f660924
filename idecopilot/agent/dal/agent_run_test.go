package dal

import (
	"context"
	"testing"

	"github.com/samber/lo"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"

	"code.byted.org/devgpt/kiwis/idecopilot/agent/dal/po"
	"code.byted.org/devgpt/kiwis/idecopilot/agent/entity"
	"code.byted.org/devgpt/kiwis/lib/cmp"
	"code.byted.org/devgpt/kiwis/port/db"
)

func TestTaskDAO_CURD(t *testing.T) {
	ctx := context.Background()
	tests := []struct {
		name string
		f    func(t *testing.T, dao *DAO)
	}{
		{
			name: "curd",
			f: func(t *testing.T, dao *DAO) {
				_, err := dao.GetAgentRunByID(ctx, "1")
				require.ErrorIs(t, err, gorm.ErrRecordNotFound)

				po1, err := dao.CreateAgentRun(ctx, CreateAgentRunOption{
					ID:      "1",
					AppID:   "1",
					Creator: "1",
					Handler: "1",
					Status:  "created",
					RequestMetadata: entity.AgentRunRequestMetadata{
						AgentID:   "1",
						SessionID: "1",
					},
				})
				require.NoError(t, err)
				require.NotNil(t, po1)
				require.Equal(t, "1", po1.ID)
				require.Equal(t, "1", po1.AppID)
				require.Equal(t, "1", po1.Creator)
				require.Equal(t, "1", po1.Handler)
				require.Equal(t, "created", po1.Status)
				require.Equal(t, "1", po1.RequestMetadata.AgentID)
				require.Equal(t, "1", po1.RequestMetadata.SessionID)

				po2, err := dao.GetAgentRunByID(ctx, "1")
				require.NoError(t, err)
				require.NoError(t, cmp.CompareStruct(po1, po2))

				po3, err := dao.UpdateAgentRun(ctx, UpdateAgentRunOption{
					ID:      "1",
					Handler: lo.ToPtr("2"),
					Status:  lo.ToPtr("in_progress"),
				})
				require.NoError(t, err)
				require.NotNil(t, po3)
				require.Equal(t, "1", po3.ID)
				require.Equal(t, "1", po3.AppID)
				require.Equal(t, "1", po3.Creator)
				require.Equal(t, "2", po3.Handler)
				require.Equal(t, "in_progress", po3.Status)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cli, err := db.NewMockSQLiteClient(&po.AgentRunPO{})
			require.NoError(t, err)

			dao := NewDAO(cli)
			tt.f(t, dao)
		})
	}

}
