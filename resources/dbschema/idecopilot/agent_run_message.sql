CREATE TABLE `agent_run_message`
(
    `id`             BIGINT UNSIGNED                                                       NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
    `uid`            VARCHAR(40)                                                           NOT NULL COMMENT 'unique string ID, usually UUID',
    `run_id`         VARCHAR(40)                                                           NOT NULL COMMENT 'agent run ID',
    `content`        TEXT                                                                  NULL COMMENT 'message content',
    `struct_content` JSON                                                                  NOT NULL COMMENT 'message struct content',
    `send_from`      VARCHAR(32)                                                           NOT NULL COMMENT 'message sender',
    `send_to`        VARCHAR(32)                                                           NOT NULL COMMENT 'message receiver',
    `created_at`     TIMESTAMP       DEFAULT CURRENT_TIMESTAMP                             NOT NULL COMMENT 'create timestamp',
    `updated_at`     TIMESTAMP       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT 'last update timestamp',
    `deleted_at`     BIGINT UNSIGNED DEFAULT 0                                             NOT NULL COMMENT 'deleted at',
    UNIQUE KEY `uk_uid` (`uid`),
    KEY `idx_run_id` (`run_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='agent run messages';
