CREATE TABLE prize_ticket (
                              `id`            BIGINT UNSIGNED                                                         NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT 'primary id',
                              `prize_id`      BIGINT UNSIGNED                                                         NOT NULL COMMENT '关联奖品信息',
                              `prize_redeem_id` BIGINT UNSIGNED                                                       DEFAULT NULL COMMENT '关联具体某次兑换信息',
                              `code`          TEXT                                                                    NOT NULL DEFAULT '' COMMENT '兑换码信息',
                              `type`          INT                                                                     NOT NULL COMMENT 'ticket 类型',
                              `value`         INT                                                                     NOT NULL DEFAULT 0 COMMENT '价值，默认单位元',
                              `description`   TEXT                                                                    NOT NULL DEFAULT '' COMMENT '描述信息',
                              `created_at`    TIMESTAMP       DEFAULT CURRENT_TIMESTAMP                               NOT NULL COMMENT 'create timestamp',
                              `updated_at`    TIMESTAMP       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP   NOT NULL COMMENT 'last update timestamp',
                              UNIQUE KEY `uk_prize_redeem_id` (`prize_redeem_id`),
                              KEY `idx_prize_id` (`prize_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='兑换券类型奖品信息';