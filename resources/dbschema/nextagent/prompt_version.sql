CREATE TABLE `next_prompt_version`
(
    `id`              BIGINT UNSIGNED                                                       NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
    `uid`             VARCHAR(40)                                                           NOT NULL COMMENT 'uniq id',
    `prompt_id`       VARCHAR(40)                                                           NOT NULL COMMENT 'prompt id',
    `creator`         VA<PERSON>HA<PERSON>(128)                                                          NOT NULL COMMENT 'creator username',
    `version`         INT                                                                   NULL COMMENT 'prompt id + version',
    `enabled`         INT                                                                   NOT NULL DEFAULT 0 COMMENT '对应 Prompt 版本的状态，1 开启 or 0 关闭',
    `description`     VARCHAR(256)                                                          NOT NULL COMMENT 'description',
    `variable`        JSON                                                                  NULL COMMENT '变量信息, map[string]string',
    `created_at`      TIMESTAMP       DEFAULT CURRENT_TIMESTAMP                             NOT NULL COMMENT 'create timestamp',
    `updated_at`      TIMESTAMP       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT 'last update timestamp',
    `deleted_at`      BIGINT UNSIGNED DEFAULT 0                                             NOT NULL COMMENT 'deleted at',
    UNIQUE KEY `uk_uid` (`uid`) COMMENT 'get prompt version by uid',
    UNIQUE KEY `uk_prompt_id_version` (`prompt_id`, `version`) COMMENT 'get prompt version by id and version',
    KEY `idx_prompt_id_enabled` (`prompt_id`, `enabled`) COMMENT 'get prompt version by id and enabled'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='agent prompt version';