CREATE TABLE `next_space` 
(
    `id`          BIGINT UNSIGNED   NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
    `uid`         VARCHAR(40)       NOT NULL COMMENT 'unique string ID, usually UUID',
    `name`        VA<PERSON><PERSON><PERSON>(255)      NOT NULL COMMENT 'space name',
    `name_en`     VARCHAR(255)      NOT NULL COMMENT 'space name',
    `description` TEXT              NOT NULL DEFAULT '' COMMENT 'space description',
    `creator`     VARCHAR(64)       NOT NULL COMMENT 'space creator',
    `type`        VARCHAR(32)       NOT NULL COMMENT 'space type',
    `status`      VARCHAR(64)       NOT NULL COMMENT 'space status',
    `created_at`  TIMESTAMP         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create timestamp',
    `updated_at`  TIMESTAMP         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'last update timestamp',
    `deleted_at`  BIGINT UNSIGNED   NOT NULL DEFAULT 0 COMMENT 'deleted at',
    UNIQUE KEY    `uk_uid` (`uid`) COMMENT 'unique key for uid',
    KEY           `idx_creator_created_at` (`creator`, `created_at`) COMMENT 'query space by creator and created at',
    KEY           `idx_status` (`status`) COMMENT 'query space by status'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 
  COLLATE = utf8mb4_general_ci
  COMMENT = 'space table';