# kitex logs.
/log
# kitex/hertz remote config.
conf/dumped_hertz_remote_config.json
conf/kitex_remote_config.json
# local test cmds.
/dev/local/test1.txt
/dev/apitest/http-client.private.env.json
/dev/apitest/*private*
/dev/apitest/requests_history
/dev/apitest/devgpt-test-play.http
/dev/apitest/devgpt-test-play-boe.http
# IDE config.
/.idea
# Compile dir.
/output
# Coverage output file.
c.out
.run/

.DS_Store

# dependence on external databases
port/bytedoc/bytedoc_dependent_test.go