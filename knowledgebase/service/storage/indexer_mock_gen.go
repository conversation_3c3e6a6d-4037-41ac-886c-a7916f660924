// Code generated by MockGen. DO NOT EDIT.
// Source: code.byted.org/devgpt/kiwis/knowledgebase/service/storage (interfaces: Indexer)
//
// Generated by this command:
//
//	mockgen -destination indexer_mock_gen.go -package storage -typed code.byted.org/devgpt/kiwis/knowledgebase/service/storage Indexer
//

// Package storage is a generated GoMock package.
package storage

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockIndexer is a mock of Indexer interface.
type MockIndexer struct {
	ctrl     *gomock.Controller
	recorder *MockIndexerMockRecorder
}

// MockIndexerMockRecorder is the mock recorder for MockIndexer.
type MockIndexerMockRecorder struct {
	mock *MockIndexer
}

// NewMockIndexer creates a new mock instance.
func NewMockIndexer(ctrl *gomock.Controller) *MockIndexer {
	mock := &MockIndexer{ctrl: ctrl}
	mock.recorder = &MockIndexerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIndexer) EXPECT() *MockIndexerMockRecorder {
	return m.recorder
}

// DeleteSegments mocks base method.
func (m *MockIndexer) DeleteSegments(arg0 context.Context, arg1 ...*DeleteSegmentOption) error {
	m.ctrl.T.Helper()
	varargs := []any{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteSegments", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteSegments indicates an expected call of DeleteSegments.
func (mr *MockIndexerMockRecorder) DeleteSegments(arg0 any, arg1 ...any) *MockIndexerDeleteSegmentsCall {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0}, arg1...)
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteSegments", reflect.TypeOf((*MockIndexer)(nil).DeleteSegments), varargs...)
	return &MockIndexerDeleteSegmentsCall{Call: call}
}

// MockIndexerDeleteSegmentsCall wrap *gomock.Call
type MockIndexerDeleteSegmentsCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockIndexerDeleteSegmentsCall) Return(arg0 error) *MockIndexerDeleteSegmentsCall {
	c.Call = c.Call.Return(arg0)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockIndexerDeleteSegmentsCall) Do(f func(context.Context, ...*DeleteSegmentOption) error) *MockIndexerDeleteSegmentsCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockIndexerDeleteSegmentsCall) DoAndReturn(f func(context.Context, ...*DeleteSegmentOption) error) *MockIndexerDeleteSegmentsCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// QuerySegments mocks base method.
func (m *MockIndexer) QuerySegments(arg0 context.Context, arg1 *QuerySegmentOption) ([]*QuerySegmentResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QuerySegments", arg0, arg1)
	ret0, _ := ret[0].([]*QuerySegmentResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QuerySegments indicates an expected call of QuerySegments.
func (mr *MockIndexerMockRecorder) QuerySegments(arg0, arg1 any) *MockIndexerQuerySegmentsCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QuerySegments", reflect.TypeOf((*MockIndexer)(nil).QuerySegments), arg0, arg1)
	return &MockIndexerQuerySegmentsCall{Call: call}
}

// MockIndexerQuerySegmentsCall wrap *gomock.Call
type MockIndexerQuerySegmentsCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockIndexerQuerySegmentsCall) Return(arg0 []*QuerySegmentResult, arg1 error) *MockIndexerQuerySegmentsCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockIndexerQuerySegmentsCall) Do(f func(context.Context, *QuerySegmentOption) ([]*QuerySegmentResult, error)) *MockIndexerQuerySegmentsCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockIndexerQuerySegmentsCall) DoAndReturn(f func(context.Context, *QuerySegmentOption) ([]*QuerySegmentResult, error)) *MockIndexerQuerySegmentsCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// SaveSegments mocks base method.
func (m *MockIndexer) SaveSegments(arg0 context.Context, arg1 ...*UpdateSegmentOption) error {
	m.ctrl.T.Helper()
	varargs := []any{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SaveSegments", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveSegments indicates an expected call of SaveSegments.
func (mr *MockIndexerMockRecorder) SaveSegments(arg0 any, arg1 ...any) *MockIndexerSaveSegmentsCall {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0}, arg1...)
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveSegments", reflect.TypeOf((*MockIndexer)(nil).SaveSegments), varargs...)
	return &MockIndexerSaveSegmentsCall{Call: call}
}

// MockIndexerSaveSegmentsCall wrap *gomock.Call
type MockIndexerSaveSegmentsCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockIndexerSaveSegmentsCall) Return(arg0 error) *MockIndexerSaveSegmentsCall {
	c.Call = c.Call.Return(arg0)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockIndexerSaveSegmentsCall) Do(f func(context.Context, ...*UpdateSegmentOption) error) *MockIndexerSaveSegmentsCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockIndexerSaveSegmentsCall) DoAndReturn(f func(context.Context, ...*UpdateSegmentOption) error) *MockIndexerSaveSegmentsCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}
