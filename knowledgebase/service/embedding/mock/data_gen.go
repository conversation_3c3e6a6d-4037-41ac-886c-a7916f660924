package storage

import (
	"hash/fnv"
	"math/rand"
)

// GenerateVector generates random []float332 by input string.
// The same input return the same []float32.
func GenerateVector(s string, dim int) []float32 {
	seed := hashString(s)
	r := rand.New(rand.NewSource(int64(seed)))
	vectors := make([]float32, 0, dim)
	for j := 0; j < dim; j++ {

		vectors = append(vectors, r.Float32())
	}
	return vectors
}

func hashString(s string) uint64 {
	h := fnv.New64a()
	_, _ = h.Write([]byte(s))
	return h.Sum64()
}
