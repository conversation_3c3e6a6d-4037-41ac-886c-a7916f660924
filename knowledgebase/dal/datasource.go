package dal

import (
	"context"

	"github.com/pkg/errors"
	"gorm.io/plugin/dbresolver"

	entity "code.byted.org/devgpt/kiwis/api/idl/kitex_gen/knowledgebase"
	"code.byted.org/devgpt/kiwis/port/db"
)

func (d *DAO) GetDatasource(ctx context.Context, id int64) (*DatasourcePO, error) {
	ds := &DatasourcePO{ID: id}
	err := d.cli.NewRequest(ctx).Take(ds).Error
	if err != nil {
		return nil, err
	}

	return ds, nil
}

func (d *DAO) GetDatasourceWithFallback(ctx context.Context, id int64) (*DatasourcePO, error) {
	ds := &DatasourcePO{ID: id}
	err := d.cli.NewRequest(ctx).Take(ds).Error
	if err != nil {
		if !db.IsRecordNotFoundError(err) {
			return nil, err
		}
		err := d.cli.NewRequest(ctx).Clauses(dbresolver.Write).Take(ds).Error
		if err != nil {
			return nil, err
		}
	}
	return ds, nil
}

type CreateDatasourceOption struct {
	DatasetID int64
}

func (d *DAO) CreateDatasource(ctx context.Context, opt *CreateDatasourceOption) (*DatasourcePO, error) {
	if opt.DatasetID <= 0 {
		return nil, errors.New("dataset id is required")
	}
	ds := &DatasourcePO{
		DatasetID: opt.DatasetID,
	}

	if err := d.cli.NewRequest(ctx).Save(ds).Error; err != nil {
		return nil, err
	}

	return ds, nil
}

type ListDatasourceOption struct {
	Offset int
	Limit  int
}

func (d *DAO) ListDatasource(ctx context.Context, opt ListDatasourceOption) ([]*DatasourcePO, error) {
	var ds []*DatasourcePO
	err := d.cli.NewRequest(ctx).Model(&DatasourcePO{}).
		Offset(opt.Offset).Limit(opt.Limit).
		Find(&ds).Error
	if err != nil {
		return nil, err
	}

	return ds, nil
}

func PackDatasource(po *DatasourcePO) *entity.Datasource {
	return &entity.Datasource{
		ID:        po.ID,
		DatasetID: po.DatasetID,
		CreatedAt: po.CreatedAt.Format(TimeFormat),
		UpdatedAt: po.UpdatedAt.Format(TimeFormat),
	}
}

func (d *DAO) GetDatasourceByDatasetID(ctx context.Context, dataSetID int64) ([]*DatasourcePO, error) {
	var ds []*DatasourcePO
	err := d.cli.NewRequest(ctx).Model(&DatasourcePO{}).Where("dataset_id = ?", dataSetID).Find(&ds).Error
	if err != nil {
		return nil, err
	}

	return ds, nil
}
