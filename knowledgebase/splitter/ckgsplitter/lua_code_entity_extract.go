package ckgsplitter

import (
	"context"
	"path"
	"path/filepath"
	"runtime/debug"
	"sort"
	"strings"

	"code.byted.org/gopkg/logs/v2/log"
	sitter "github.com/smacker/go-tree-sitter"

	entity "code.byted.org/devgpt/kiwis/api/idl/kitex_gen/knowledgebase"
	"code.byted.org/devgpt/kiwis/knowledgebase/splitter/util"
	"code.byted.org/devgpt/kiwis/lib/parser"
)

type LuaCodeEntity struct {
	UniCodeEntity
	idGen *util.SegmentIDGenerator
}

func NewLuaCodeEntityExtract(fileID string, filePath string, chunkSize int, root *parser.Node) *LuaCodeEntity {
	return &LuaCodeEntity{
		UniCodeEntity: UniCodeEntity{
			fileID:           fileID,
			filePath:         filePath,
			chunkSize:        chunkSize,
			root:             root,
			relations:        make([]entity.RelationSegment, 0),
			fileTopEntities:  make([]entity.FileTopLevelSegment, 0),
			classEntities:    make([]entity.ClassSegment, 0),
			classTopEntities: make([]entity.ClassTopLevelSegment, 0),
			methodEntities:   make([]entity.MethodSegment, 0),
			fileEntities:     make([]entity.FileSegment, 0),
		},
		idGen: util.NewSegmentIDGenerator(fileID),
	}
}

func (luaEntity *LuaCodeEntity) getCodeBlockEntity(ctx context.Context, cur *sitter.Node, outerClass *entity.ClassSegment) []*sitter.Node {
	topLevelNodes := make([]*sitter.Node, 0)
	for i := 0; i < int(cur.ChildCount()); i++ {
		child := cur.Child(i)
		if child.Type() == "function_statement" {
			luaEntity.getFunctionEntityInfo(ctx, child, outerClass)
		} else if child.Type() == "comment" {
			nodeAfterComment := child.NextSibling()
			if nodeAfterComment != nil && nodeAfterComment.Type() == "function_statement" {
				continue
			}
			topLevelNodes = append(topLevelNodes, child)
		} else {
			topLevelNodes = append(topLevelNodes, child)
		}
	}
	return topLevelNodes
}

func (luaEntity *LuaCodeEntity) GetCodeEntitiesAndRelations(ctx context.Context, cur *parser.Node) (*ChunkResult, error) {
	topLevelNodes := luaEntity.getCodeBlockEntity(ctx, cur.Node, nil)
	docStr := luaEntity.getFileTopLevelEntityInfo(ctx, topLevelNodes)
	luaEntity.getFileEntityInfo(cur, docStr)
	return &ChunkResult{
		Files:          luaEntity.fileEntities,
		FileTopLevels:  luaEntity.fileTopEntities,
		ClassTopLevels: luaEntity.classTopEntities,
		Class:          luaEntity.classEntities,
		Methods:        luaEntity.methodEntities,
		Relations:      luaEntity.relations,
	}, nil
}

func (luaEntity *LuaCodeEntity) getSimplifiedContent(curNode *sitter.Node) string {
	simplifiedContent := curNode.Content(luaEntity.root.Contents)
	type Offset struct {
		start, end uint32
	}
	var offsets []Offset

	visitFilter(curNode, func(node *sitter.Node) bool {
		if node.Type() == "function_body" {
			offsets = append(offsets, Offset{node.StartByte() - curNode.StartByte(), node.EndByte() - curNode.StartByte()})
			return false
		}
		return true
	})

	// 按结束偏移量降序排列偏移量对
	sort.Slice(offsets, func(i, j int) bool {
		return offsets[i].end > offsets[j].end
	})

	// 从后往前移除对应的子字符串
	for _, offset := range offsets {
		simplifiedContent = simplifiedContent[:offset.start] + blockContentFilling + simplifiedContent[offset.end:]
	}

	return simplifiedContent
}

func (luaEntity *LuaCodeEntity) getFileEntityInfo(node *parser.Node, docStr string) {
	lines := len(strings.Split(node.Content(luaEntity.root.Contents), "\n"))
	fileEntity := newFileEntity(luaEntity.idGen.GenerateFileSegmentID(), path.Base(luaEntity.filePath), luaEntity.filePath, 1, int32(lines))
	simplifiedContent := luaEntity.getSimplifiedContent(node.Node)
	fileEntity.Attributes.SimplifiedContent = &simplifiedContent
	if docStr != "" {
		fileEntity.Attributes.Comment = &docStr
	}
	luaEntity.fileEntities = append(luaEntity.fileEntities, *fileEntity)
}

func (luaEntity *LuaCodeEntity) createMethodEntity(ctx context.Context, id, name string, qualified string, node *sitter.Node, callSites []*sitter.Node) *entity.MethodSegment {
	newEntity := newMethodEntity(id, name, int32(node.StartPoint().Row+1), int32(node.EndPoint().Row+1), getNodeCodeChunksWithDefault(ctx, node.Content(luaEntity.root.Contents), entity.Lua, luaEntity.chunkSize), luaEntity.filePath)
	comment := luaEntity.getNodePrevComment(node)
	if comment != "" {
		newEntity.Attributes.Comment = &comment
	}
	signature := luaEntity.getMethodSignature(node, qualified)
	newEntity.Attributes.Signature = &signature

	simplifiedContent := luaEntity.getSimplifiedContent(node)
	newEntity.Attributes.SimplifiedContent = &simplifiedContent

	for _, e := range callSites {
		cName := luaEntity.getCallSiteName(e)
		if cName != "" {
			cgRelation := newRelation(newEntity.ID, newEntity.Name, "", cName, entity.CallerToCallee)
			startLine := int32(e.StartPoint().Row + 1)
			cgRelation.Attributes.StartLine = &startLine

			endLine := int32(e.EndPoint().Row + 1)
			cgRelation.Attributes.EndLine = &endLine
			luaEntity.relations = append(luaEntity.relations, *cgRelation)
		}
	}

	return newEntity
}

func (luaEntity *LuaCodeEntity) getMethodSignature(node *sitter.Node, qualified string) string {
	signature := qualified
	if qualified != "" {
		signature += "."
	}
	for i := 1; i < int(node.ChildCount()); i++ {
		child := node.Child(i)
		//  if parameter_list required, break until type== function_body
		if child.Type() == "function_body_paren" {
			break
		}
		signature += child.Content(luaEntity.root.Contents)
	}
	return signature
}

func (luaEntity *LuaCodeEntity) getCallSiteName(node *sitter.Node) string {

	callSiteName := ""
	for i := 0; i < int(node.ChildCount()); i++ {
		child := node.Child(i)
		if child.Type() == "function_call_paren" {
			break
		}
		callSiteName += child.Content(luaEntity.root.Contents)
	}
	return callSiteName
}

func (luaEntity *LuaCodeEntity) getFunctionEntityInfo(ctx context.Context, node *sitter.Node, classBelonged *entity.ClassSegment) {

	var name string
	var callSites []*sitter.Node
	_ = classBelonged

	if nameNode := node.ChildByFieldName("name"); nameNode != nil {
		name = nameNode.Content(luaEntity.root.Contents)
	} else {
		log.V1.CtxError(ctx, "get function name failed", string(debug.Stack()))
		return
	}

	pkgPath := filepath.Dir(luaEntity.filePath)
	if pkgPath == "" {
		log.V1.CtxError(ctx, "get function name failed", string(debug.Stack()))
		return
	}

	visit(node, func(node *sitter.Node) {
		if node.Type() == "function_call" {
			callSites = append(callSites, node)
		}
	})
	id := luaEntity.idGen.GenerateMethodSegmentID(pkgPath, "", name)
	newEntity := luaEntity.createMethodEntity(ctx, id, name, "", node, callSites)
	luaEntity.methodEntities = append(luaEntity.methodEntities, *newEntity)
	luaEntity.relations = append(luaEntity.relations, *newRelation(newEntity.ID, newEntity.Name, luaEntity.idGen.GenerateFileSegmentID(), path.Base(luaEntity.filePath), entity.MtdToFile))
}

func (luaEntity *LuaCodeEntity) getNodePrevComment(node *sitter.Node) string {
	if node.PrevSibling() != nil {
		if node.PrevSibling().Type() == "comment" && node.StartPoint().Row == node.PrevSibling().EndPoint().Row+1 {
			return node.PrevSibling().Content(luaEntity.root.Contents)
		}
	}
	return ""
}

func (luaEntity *LuaCodeEntity) getFileTopLevelEntityInfo(ctx context.Context, nodes []*sitter.Node) string {
	if len(nodes) == 0 {
		return ""
	}
	joinedStr := ""
	curLine := 0
	for _, node := range nodes {
		joinedStr += node.Content(luaEntity.root.Contents)
		if int(node.StartPoint().Row)+1 != curLine {
			if !strings.HasSuffix(joinedStr, "\n") {
				joinedStr += "\n"
			}
			curLine = int(node.StartPoint().Row + 1)
		}
	}

	if len(strings.TrimSpace(joinedStr)) == 0 {
		log.V1.CtxInfo(ctx, "file top level entity content is empty in %v", luaEntity.filePath)
		return ""
	}

	first := nodes[0]
	end := nodes[len(nodes)-1]

	fileTopLevelEntity := newFileTopLevelEntity(luaEntity.idGen, "", int32(first.StartPoint().Row+1), int32(end.EndPoint().Row+1), getNodeCodeChunksWithDefault(ctx, joinedStr, entity.Lua, luaEntity.chunkSize), luaEntity.filePath)
	luaEntity.fileTopEntities = append(luaEntity.fileTopEntities, *fileTopLevelEntity)
	luaEntity.relations = append(luaEntity.relations, *newRelation(fileTopLevelEntity.ID, fileTopLevelEntity.Name, luaEntity.fileID, luaEntity.filePath, entity.FileTopLevelToFile))
	return ""
}
