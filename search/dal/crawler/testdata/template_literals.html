<!doctype html><html lang="en-US" prefix="og: https://ogp.me/ns#"><head><meta charset="utf-8"/><meta name="viewport" content="width=device-width,initial-scale=1"/><link rel="icon" href="/favicon-48x48.cbbd161b.png"/><link rel="apple-touch-icon" href="/apple-touch-icon.6803c6f0.png"/><meta name="theme-color" content="#ffffff"/><link rel="manifest" href="/manifest.56b1cedc.json"/><link rel="search" type="application/opensearchdescription+xml" href="/opensearch.xml" title="MDN Web Docs"/><title>Template literals (Template strings) - JavaScript | MDN</title><link rel="alternate" title="Plantillas literales (plantillas de cadenas)" href="https://developer.mozilla.org/es/docs/Web/JavaScript/Reference/Template_literals" hreflang="es"/><link rel="alternate" title="Littéraux de gabarits" href="https://developer.mozilla.org/fr/docs/Web/JavaScript/Reference/Template_literals" hreflang="fr"/><link rel="alternate" title="テンプレートリテラル (テンプレート文字列)" href="https://developer.mozilla.org/ja/docs/Web/JavaScript/Reference/Template_literals" hreflang="ja"/><link rel="alternate" title="Template literals" href="https://developer.mozilla.org/ko/docs/Web/JavaScript/Reference/Template_literals" hreflang="ko"/><link rel="alternate" title="Template strings" href="https://developer.mozilla.org/pt-BR/docs/Web/JavaScript/Reference/Template_literals" hreflang="pt"/><link rel="alternate" title="Шаблонные строки" href="https://developer.mozilla.org/ru/docs/Web/JavaScript/Reference/Template_literals" hreflang="ru"/><link rel="alternate" title="模板字符串" href="https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Template_literals" hreflang="zh"/><link rel="alternate" title="樣板字面值" href="https://developer.mozilla.org/zh-TW/docs/Web/JavaScript/Reference/Template_literals" hreflang="zh-Hant"/><link rel="alternate" title="Template literals (Template strings)" href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Template_literals" hreflang="en"/><link rel="alternate" type="application/rss+xml" title="MDN Blog RSS Feed" href="https://developer.mozilla.org/en-US/blog/rss.xml" hreflang="en" /><meta name="robots" content="index, follow"><meta name="description" content="Template literals are literals delimited with backtick (`) characters, allowing for multi-line strings, string interpolation with embedded expressions, and special constructs called tagged templates."/><meta property="og:url" content="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Template_literals"/><meta property="og:title" content="Template literals (Template strings) - JavaScript | MDN"/><meta property="og:type" content="website"/><meta property="og:locale" content="en_US"/><meta property="og:description" content="Template literals are literals delimited with backtick (`) characters, allowing for multi-line strings, string interpolation with embedded expressions, and special constructs called tagged templates."/><meta property="og:image" content="https://developer.mozilla.org/mdn-social-share.cd6c4a5a.png"/><meta property="og:image:type" content="image/png"/><meta property="og:image:height" content="1080"/><meta property="og:image:width" content="1920"/><meta property="og:image:alt" content="The MDN Web Docs logo, featuring a blue accent color, displayed on a solid black background."/><meta property="og:site_name" content="MDN Web Docs"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:creator" content="MozDevNet"/><link rel="canonical" href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Template_literals"/><style media="print">.article-actions-container,.document-toc-container,.language-menu,.main-menu-toggle,.on-github,.page-footer,.place,.sidebar,.top-banner,.top-navigation-main,ul.prev-next{display:none!important}.main-page-content,.main-page-content pre{padding:2px}.main-page-content pre{border-left-width:2px}</style><script src="/static/js/ga.js" defer=""></script><script defer="defer" src="/static/js/main.0c497c21.js"></script><link href="/static/css/main.b0422016.css" rel="stylesheet"></head><body><script>if(document.body.addEventListener("load",(t=>{t.target.classList.contains("interactive")&&t.target.setAttribute("data-readystate","complete")}),{capture:!0}),window&&document.documentElement){const t={light:"#ffffff",dark:"#1b1b1b"};try{const e=window.localStorage.getItem("theme");e&&(document.documentElement.className=e,document.documentElement.style.backgroundColor=t[e])}catch(t){console.warn("Unable to read theme from localStorage",t)}}</script><div id="root"><ul id="nav-access" class="a11y-nav"><li><a id="skip-main" href="#content">Skip to main content</a></li><li><a id="skip-search" href="#top-nav-search-input">Skip to search</a></li><li><a id="skip-select-language" href="#languages-switcher-button">Skip to select language</a></li></ul><div class="page-wrapper  category-javascript document-page"><div class="top-banner loading"><section class="place top container"></section></div><div class="sticky-header-container"><header class="top-navigation 
      
      "><div class="container "><div class="top-navigation-wrap"><a href="/en-US/" class="logo" aria-label="MDN homepage"><svg id="mdn-docs-logo" xmlns="http://www.w3.org/2000/svg" x="0" y="0" viewBox="0 0 694.9 104.4" style="enable-background:new 0 0 694.9 104.4" xml:space="preserve" role="img"><title>MDN Web Docs</title><path d="M40.3 0 11.7 92.1H0L28.5 0h11.8zm10.4 0v92.1H40.3V0h10.4zM91 0 62.5 92.1H50.8L79.3 0H91zm10.4 0v92.1H91V0h10.4z" class="logo-m"></path><path d="M627.9 95.6h67v8.8h-67v-8.8z" class="logo-_"></path><path d="M367 42h-4l-10.7 30.8h-5.5l-10.8-26h-.4l-10.5 26h-5.2L308.7 42h-3.8v-5.6H323V42h-6.5l6.8 20.4h.4l10.3-26h4.7l11.2 26h.5l5.7-20.3h-6.2v-5.6H367V42zm34.9 20c-.4 3.2-2 5.9-4.7 8.2-2.8 2.3-6.5 3.4-11.3 3.4-5.4 0-9.7-1.6-13.1-4.7-3.3-3.2-5-7.7-5-13.7 0-5.7 1.6-10.3 4.7-14s7.4-5.5 12.9-5.5c5.1 0 9.1 1.6 11.9 4.7s4.3 6.9 4.3 11.3c0 1.5-.2 3-.5 4.7h-25.6c.3 7.7 4 11.6 10.9 11.6 2.9 0 5.1-.7 6.5-2 1.5-1.4 2.5-3 3-4.9l6 .9zM394 51.3c.2-2.4-.4-4.7-1.8-6.9s-3.8-3.3-7-3.3c-3.1 0-5.3 1-6.9 3-1.5 2-2.5 4.4-2.8 7.2H394zm51 2.4c0 5-1.3 9.5-4 13.7s-6.9 6.2-12.7 6.2c-6 0-10.3-2.2-12.7-6.7-.1.4-.2 1.4-.4 2.9s-.3 2.5-.4 2.9h-7.3c.3-1.7.6-3.5.8-5.3.3-1.8.4-3.7.4-5.5V22.3h-6v-5.6H416v27c1.1-2.2 2.7-4.1 4.7-5.7 2-1.6 4.8-2.4 8.4-2.4 4.6 0 8.4 1.6 11.4 4.7 3 3.2 4.5 7.6 4.5 13.4zm-7.7.6c0-4.2-1-7.4-3-9.5-2-2.2-4.4-3.3-7.4-3.3-3.4 0-6 1.2-8 3.7-1.9 2.4-2.9 5-3 7.7V57c0 3 1 5.6 3 7.7s4.5 3.1 7.6 3.1c3.6 0 6.3-1.3 8.1-3.9 1.8-2.7 2.7-5.9 2.7-9.6zm69.2 18.5h-13.2v-7.2c-1.2 2.2-2.8 4.1-4.9 5.6-2.1 1.6-4.8 2.4-8.3 2.4-4.8 0-8.7-1.6-11.6-4.9-2.9-3.2-4.3-7.7-4.3-13.3 0-5 1.3-9.6 4-13.7 2.6-4.1 6.9-6.2 12.8-6.2 5.7 0 9.8 2.2 12.3 6.5V22.3h-8.6v-5.6h15.8v50.6h6v5.5zM493.2 56v-4.4c-.1-3-1.2-5.5-3.2-7.3s-4.4-2.8-7.2-2.8c-3.6 0-6.3 1.3-8.2 3.9-1.9 2.6-2.8 5.8-2.8 9.6 0 4.1 1 7.3 3 9.5s4.5 3.3 7.4 3.3c3.2 0 5.8-1.3 7.8-3.8 2.1-2.6 3.1-5.3 3.2-8zm53.1-1.4c0 5.6-1.8 10.2-5.3 13.7s-8.2 5.3-13.9 5.3-10.1-1.7-13.4-5.1c-3.3-3.4-5-7.9-5-13.5 0-5.3 1.6-9.9 4.7-13.7 3.2-3.8 7.9-5.7 14.2-5.7s11 1.9 14.1 5.7c3 3.7 4.6 8.1 4.6 13.3zm-7.7-.2c0-4-1-7.2-3-9.5s-4.8-3.5-8.2-3.5c-3.6 0-6.4 1.2-8.3 3.7s-2.9 5.6-2.9 9.5c0 3.7.9 6.8 2.8 9.4 1.9 2.6 4.6 3.9 8.3 3.9 3.6 0 6.4-1.3 8.4-3.8 1.9-2.6 2.9-5.8 2.9-9.7zm45 5.8c-.4 3.2-1.9 6.3-4.4 9.1-2.5 2.9-6.4 4.3-11.8 4.3-5.2 0-9.4-1.6-12.6-4.8-3.2-3.2-4.8-7.7-4.8-13.7 0-5.5 1.6-10.1 4.7-13.9 3.2-3.8 7.6-5.7 13.2-5.7 2.3 0 4.6.3 6.7.8 2.2.5 4.2 1.5 6.2 2.9l1.5 9.5-5.9.7-1.3-6.1c-2.1-1.2-4.5-1.8-7.2-1.8-3.5 0-6.1 1.2-7.7 3.7-1.7 2.5-2.5 5.7-2.5 9.6 0 4.1.9 7.3 2.7 9.5 1.8 2.3 4.4 3.4 7.8 3.4 5.2 0 8.2-2.9 9.2-8.8l6.2 1.3zm34.7 1.9c0 3.6-1.5 6.5-4.6 8.5s-7 3-11.7 3c-5.7 0-10.6-1.2-14.6-3.6l1.2-8.8 5.7.6-.2 4.7c1.1.5 2.3.9 3.6 1.1s2.6.3 3.9.3c2.4 0 4.5-.4 6.5-1.3 1.9-.9 2.9-2.2 2.9-4.1 0-1.8-.8-3.1-2.3-3.8s-3.5-1.3-5.8-1.7-4.6-.9-6.9-1.4c-2.3-.6-4.2-1.6-5.7-2.9-1.6-1.4-2.3-3.5-2.3-6.3 0-4.1 1.5-6.9 4.6-8.5s6.4-2.4 9.9-2.4c2.6 0 5 .3 7.2.9 2.2.6 4.3 1.4 6.1 2.4l.8 8.8-5.8.7-.8-5.7c-2.3-1-4.7-1.6-7.2-1.6-2.1 0-3.7.4-5.1 1.1-1.3.8-2 2-2 3.8 0 1.7.8 2.9 2.3 3.6 1.5.7 3.4 1.2 5.7 1.6 2.2.4 4.5.8 6.7 1.4 2.2.6 4.1 1.6 5.7 3 1.4 1.6 2.2 3.7 2.2 6.6zM197.6 73.2h-17.1v-5.5h3.8V51.9c0-3.7-.7-6.3-2.1-7.9-1.4-1.6-3.3-2.3-5.7-2.3-3.2 0-5.6 1.1-7.2 3.4s-2.4 4.6-2.5 6.9v15.6h6v5.5h-17.1v-5.5h3.8V51.9c0-3.8-.7-6.4-2.1-7.9-1.4-1.5-3.3-2.3-5.6-2.3-3.2 0-5.5 1.1-7.2 3.3-1.6 2.2-2.4 4.5-2.5 6.9v15.8h6.9v5.5h-20.2v-5.5h6V42.4h-6.1v-5.6h13.4v6.4c1.2-2.1 2.7-3.8 4.7-5.2 2-1.3 4.4-2 7.3-2s5.3.7 7.5 2.1c2.2 1.4 3.7 3.5 4.5 6.4 1.1-2.5 2.7-4.5 4.9-6.1s4.8-2.4 7.9-2.4c3.5 0 6.5 1.1 8.9 3.3s3.7 5.6 3.7 10.2v18.2h6.1v5.5zm42.5 0h-13.2V66c-1.2 2.2-2.8 4.1-4.9 5.6-2.1 1.6-4.8 2.4-8.3 2.4-4.8 0-8.7-1.6-11.6-4.9-2.9-3.2-4.3-7.7-4.3-13.3 0-5 1.3-9.6 4-13.7 2.6-4.1 6.9-6.2 12.8-6.2s9.8 2.2 12.3 6.5V22.7h-8.6v-5.6h15.8v50.6h6v5.5zm-13.3-16.8V52c-.1-3-1.2-5.5-3.2-7.3s-4.4-2.8-7.2-2.8c-3.6 0-6.3 1.3-8.2 3.9-1.9 2.6-2.8 5.8-2.8 9.6 0 4.1 1 7.3 3 9.5s4.5 3.3 7.4 3.3c3.2 0 5.8-1.3 7.8-3.8 2.1-2.6 3.1-5.3 3.2-8zm61.5 16.8H269v-5.5h6V51.9c0-3.7-.7-6.3-2.2-7.9-1.4-1.6-3.4-2.3-5.7-2.3-3.1 0-5.6 1-7.4 3s-2.8 4.4-2.9 7v15.9h6v5.5h-19.3v-5.5h6V42.4h-6.2v-5.6h13.6V43c2.6-4.6 6.8-6.9 12.7-6.9 3.6 0 6.7 1.1 9.2 3.3s3.7 5.6 3.7 10.2v18.2h6v5.4h-.2z" class="logo-text"></path></svg></a><button title="Open main menu" type="button" class="button action has-icon main-menu-toggle" aria-haspopup="menu" aria-label="Open main menu" aria-expanded="false"><span class="button-wrap"><span class="icon icon-menu "></span><span class="visually-hidden">Open main menu</span></span></button></div><div class="top-navigation-main"><nav class="main-nav" aria-label="Main menu"><ul class="main-menu nojs"><li class="top-level-entry-container active"><button type="button" id="references-button" class="top-level-entry menu-toggle" aria-controls="references-menu" aria-expanded="false">References</button><a href="/en-US/docs/Web" class="top-level-entry">References</a><ul id="references-menu" class="submenu references hidden inline-submenu-lg" aria-labelledby="references-button"><li class="apis-link-container mobile-only "><a href="/en-US/docs/Web" class="submenu-item "><div class="submenu-icon"></div><div class="submenu-content-container"><div class="submenu-item-heading">Overview / Web Technology</div><p class="submenu-item-description">Web technology reference for developers</p></div></a></li><li class="html-link-container "><a href="/en-US/docs/Web/HTML" class="submenu-item "><div class="submenu-icon html"></div><div class="submenu-content-container"><div class="submenu-item-heading">HTML</div><p class="submenu-item-description">Structure of content on the web</p></div></a></li><li class="css-link-container "><a href="/en-US/docs/Web/CSS" class="submenu-item "><div class="submenu-icon css"></div><div class="submenu-content-container"><div class="submenu-item-heading">CSS</div><p class="submenu-item-description">Code used to describe document style</p></div></a></li><li class="javascript-link-container "><a href="/en-US/docs/Web/JavaScript" class="submenu-item "><div class="submenu-icon javascript"></div><div class="submenu-content-container"><div class="submenu-item-heading">JavaScript</div><p class="submenu-item-description">General-purpose scripting language</p></div></a></li><li class="http-link-container "><a href="/en-US/docs/Web/HTTP" class="submenu-item "><div class="submenu-icon http"></div><div class="submenu-content-container"><div class="submenu-item-heading">HTTP</div><p class="submenu-item-description">Protocol for transmitting web resources</p></div></a></li><li class="apis-link-container "><a href="/en-US/docs/Web/API" class="submenu-item "><div class="submenu-icon apis"></div><div class="submenu-content-container"><div class="submenu-item-heading">Web APIs</div><p class="submenu-item-description">Interfaces for building web applications</p></div></a></li><li class="apis-link-container "><a href="/en-US/docs/Mozilla/Add-ons/WebExtensions" class="submenu-item "><div class="submenu-icon"></div><div class="submenu-content-container"><div class="submenu-item-heading">Web Extensions</div><p class="submenu-item-description">Developing extensions for web browsers</p></div></a></li><li class="apis-link-container desktop-only "><a href="/en-US/docs/Web" class="submenu-item "><div class="submenu-icon"></div><div class="submenu-content-container"><div class="submenu-item-heading">Web Technology</div><p class="submenu-item-description">Web technology reference for developers</p></div></a></li></ul></li><li class="top-level-entry-container "><button type="button" id="guides-button" class="top-level-entry menu-toggle" aria-controls="guides-menu" aria-expanded="false">Guides</button><a href="/en-US/docs/Learn" class="top-level-entry">Guides</a><ul id="guides-menu" class="submenu guides hidden inline-submenu-lg" aria-labelledby="guides-button"><li class="apis-link-container mobile-only "><a href="/en-US/docs/Learn" class="submenu-item "><div class="submenu-icon learn"></div><div class="submenu-content-container"><div class="submenu-item-heading">Overview / MDN Learning Area</div><p class="submenu-item-description">Learn web development</p></div></a></li><li class="apis-link-container desktop-only "><a href="/en-US/docs/Learn" class="submenu-item "><div class="submenu-icon learn"></div><div class="submenu-content-container"><div class="submenu-item-heading">MDN Learning Area</div><p class="submenu-item-description">Learn web development</p></div></a></li><li class="html-link-container "><a href="/en-US/docs/Learn/HTML" class="submenu-item "><div class="submenu-icon html"></div><div class="submenu-content-container"><div class="submenu-item-heading">HTML</div><p class="submenu-item-description">Learn to structure web content with HTML</p></div></a></li><li class="css-link-container "><a href="/en-US/docs/Learn/CSS" class="submenu-item "><div class="submenu-icon css"></div><div class="submenu-content-container"><div class="submenu-item-heading">CSS</div><p class="submenu-item-description">Learn to style content using CSS</p></div></a></li><li class="javascript-link-container "><a href="/en-US/docs/Learn/JavaScript" class="submenu-item "><div class="submenu-icon javascript"></div><div class="submenu-content-container"><div class="submenu-item-heading">JavaScript</div><p class="submenu-item-description">Learn to run scripts in the browser</p></div></a></li><li class=" "><a href="/en-US/docs/Web/Accessibility" class="submenu-item "><div class="submenu-icon"></div><div class="submenu-content-container"><div class="submenu-item-heading">Accessibility</div><p class="submenu-item-description">Learn to make the web accessible to all</p></div></a></li></ul></li><li class="top-level-entry-container "><button type="button" id="mdn-plus-button" class="top-level-entry menu-toggle" aria-controls="mdn-plus-menu" aria-expanded="false">Plus</button><a href="/en-US/plus" class="top-level-entry">Plus</a><ul id="mdn-plus-menu" class="submenu mdn-plus hidden inline-submenu-lg" aria-labelledby="mdn-plus-button"><li class=" "><a href="/en-US/plus" class="submenu-item "><div class="submenu-icon"></div><div class="submenu-content-container"><div class="submenu-item-heading">Overview</div><p class="submenu-item-description">A customized MDN experience</p></div></a></li><li class=" "><a href="/en-US/plus/ai-help" class="submenu-item "><div class="submenu-icon"></div><div class="submenu-content-container"><div class="submenu-item-heading">AI Help (beta)</div><p class="submenu-item-description">Get real-time assistance and support</p></div></a></li><li class=" "><a href="/en-US/plus/updates" class="submenu-item "><div class="submenu-icon"></div><div class="submenu-content-container"><div class="submenu-item-heading">Updates</div><p class="submenu-item-description">All browser compatibility updates at a glance</p></div></a></li><li class=" "><a href="/en-US/plus/docs/features/overview" class="submenu-item "><div class="submenu-icon"></div><div class="submenu-content-container"><div class="submenu-item-heading">Documentation</div><p class="submenu-item-description">Learn how to use MDN Plus</p></div></a></li><li class=" "><a href="/en-US/plus/docs/faq" class="submenu-item "><div class="submenu-icon"></div><div class="submenu-content-container"><div class="submenu-item-heading">FAQ</div><p class="submenu-item-description">Frequently asked questions about MDN Plus</p></div></a></li></ul></li><li class="top-level-entry-container "><a class="top-level-entry menu-link" href="/en-US/blog/">Blog</a></li><li class="top-level-entry-container "><a class="top-level-entry menu-link" href="/en-US/play">Play</a></li><li class="top-level-entry-container "><a class="top-level-entry menu-link" href="/en-US/plus/ai-help">AI Help <sup class="new beta">Beta</sup></a></li></ul></nav><div class="header-search"><form action="/en-US/search" class="search-form search-widget" id="top-nav-search-form" role="search"><label id="top-nav-search-label" for="top-nav-search-input" class="visually-hidden">Search MDN</label><input aria-activedescendant="" aria-autocomplete="list" aria-controls="top-nav-search-menu" aria-expanded="false" aria-labelledby="top-nav-search-label" autoComplete="off" id="top-nav-search-input" role="combobox" type="search" class="search-input-field" name="q" placeholder="   " required="" value=""/><button type="button" class="button action has-icon clear-search-button"><span class="button-wrap"><span class="icon icon-cancel "></span><span class="visually-hidden">Clear search input</span></span></button><button type="submit" class="button action has-icon search-button"><span class="button-wrap"><span class="icon icon-search "></span><span class="visually-hidden">Search</span></span></button><div id="top-nav-search-menu" role="listbox" aria-labelledby="top-nav-search-label"></div></form></div><div class="theme-switcher-menu"><button type="button" class="button action has-icon theme-switcher-menu small" aria-haspopup="menu"><span class="button-wrap"><span class="icon icon-theme-os-default "></span>Theme</span></button></div><ul class="auth-container"><li><a href="/users/fxa/login/authenticate/?next=%2Fen-US%2Fdocs%2FWeb%2FJavaScript%2FReference%2FTemplate_literals" class="login-link" rel="nofollow">Log in</a></li><li><a href="/users/fxa/login/authenticate/?next=%2Fen-US%2Fdocs%2FWeb%2FJavaScript%2FReference%2FTemplate_literals" target="_self" class="button primary mdn-plus-subscribe-link"><span class="button-wrap">Sign up for free</span></a></li></ul></div></div></header><div class="article-actions-container"><div class="container"><button type="button" class="button action has-icon sidebar-button" aria-label="Expand sidebar" aria-expanded="false" aria-controls="sidebar-quicklinks"><span class="button-wrap"><span class="icon icon-sidebar "></span></span></button><nav class="breadcrumbs-container" aria-label="Breadcrumb"><ol typeof="BreadcrumbList" vocab="https://schema.org/" aria-label="breadcrumbs"><li property="itemListElement" typeof="ListItem"><a class="breadcrumb" property="item" typeof="WebPage" href="/en-US/docs/Web"><span property="name">References</span></a><meta property="position" content="1"/></li><li property="itemListElement" typeof="ListItem"><a class="breadcrumb" property="item" typeof="WebPage" href="/en-US/docs/Web/JavaScript"><span property="name">JavaScript</span></a><meta property="position" content="2"/></li><li property="itemListElement" typeof="ListItem"><a class="breadcrumb" property="item" typeof="WebPage" href="/en-US/docs/Web/JavaScript/Reference"><span property="name">Reference</span></a><meta property="position" content="3"/></li><li property="itemListElement" typeof="ListItem"><a class="breadcrumb-current-page" property="item" typeof="WebPage" href="/en-US/docs/Web/JavaScript/Reference/Template_literals"><span property="name">Template literals (Template strings)</span></a><meta property="position" content="4"/></li></ol></nav><div class="article-actions"><button type="button" class="button action has-icon article-actions-toggle" aria-label="Article actions"><span class="button-wrap"><span class="icon icon-ellipses "></span><span class="article-actions-dialog-heading">Article Actions</span></span></button><ul class="article-actions-entries"><li class="article-actions-entry"><div class="languages-switcher-menu open-on-focus-within"><button id="languages-switcher-button" type="button" class="button action small has-icon languages-switcher-menu" aria-haspopup="menu"><span class="button-wrap"><span class="icon icon-language "></span>English (US)</span></button></div></li></ul></div></div></div></div><div class="main-wrapper"><div class="sidebar-container"><aside id="sidebar-quicklinks" class="sidebar" data-macro="JsSidebar"><button type="button" class="button action backdrop" aria-label="Collapse sidebar"><span class="button-wrap"></span></button><nav aria-label="Related Topics" class="sidebar-inner"><header class="sidebar-actions"><section class="sidebar-filter-container"><div class="sidebar-filter "><label id="sidebar-filter-label" class="sidebar-filter-label" for="sidebar-filter-input"><span class="icon icon-filter"></span><span class="visually-hidden">Filter sidebar</span></label><input id="sidebar-filter-input" autoComplete="off" class="sidebar-filter-input-field false" type="text" placeholder="Filter" value=""/><button type="button" class="button action has-icon clear-sidebar-filter-button"><span class="button-wrap"><span class="icon icon-cancel "></span><span class="visually-hidden">Clear filter input</span></span></button></div></section></header><div class="sidebar-inner-nav"><div class="in-nav-toc"><div class="document-toc-container"><section class="document-toc"><header><h2 class="document-toc-heading">In this article</h2></header><ul class="document-toc-list"><li class="document-toc-item "><a class="document-toc-link" href="#syntax">Syntax</a></li><li class="document-toc-item "><a class="document-toc-link" href="#description">Description</a></li><li class="document-toc-item "><a class="document-toc-link" href="#specifications">Specifications</a></li><li class="document-toc-item "><a class="document-toc-link" href="#browser_compatibility">Browser compatibility</a></li><li class="document-toc-item "><a class="document-toc-link" href="#see_also">See also</a></li></ul></section></div></div><div class="sidebar-body">
 <ol>
  <li><a href="/en-US/docs/Web/JavaScript"><strong>JavaScript</strong></a></li>
  <li><a href="/en-US/docs/Web/JavaScript/Tutorials"><strong>Tutorials</strong></a></li>
  <li class="toggle">
    <details>
      <summary>Complete beginners</summary>
      <ol>
        <li><a href="/en-US/docs/Learn/Getting_started_with_the_web/JavaScript_basics">JavaScript basics</a></li>
        <li><a href="/en-US/docs/Learn/JavaScript/First_steps">JavaScript first steps</a></li>
        <li><a href="/en-US/docs/Learn/JavaScript/Building_blocks">JavaScript building blocks</a></li>
        <li><a href="/en-US/docs/Learn/JavaScript/Objects">Introducing JavaScript objects</a></li>
      </ol>
    </details>
  </li>
  <li class="toggle">
    <details>
      <summary>JavaScript Guide</summary>
      <ol>
          <li><a href="/en-US/docs/Web/JavaScript/Guide/Introduction">Introduction</a></li>
          <li><a href="/en-US/docs/Web/JavaScript/Guide/Grammar_and_types">Grammar and types</a></li>
          <li><a href="/en-US/docs/Web/JavaScript/Guide/Control_flow_and_error_handling">Control flow and error handling</a></li>
          <li><a href="/en-US/docs/Web/JavaScript/Guide/Loops_and_iteration">Loops and iteration</a></li>
          <li><a href="/en-US/docs/Web/JavaScript/Guide/Functions">Functions</a></li>
          <li><a href="/en-US/docs/Web/JavaScript/Guide/Expressions_and_operators">Expressions and operators</a></li>
          <li><a href="/en-US/docs/Web/JavaScript/Guide/Numbers_and_dates">Numbers and dates</a></li>
          <li><a href="/en-US/docs/Web/JavaScript/Guide/Text_formatting">Text formatting</a></li>
          <li><a href="/en-US/docs/Web/JavaScript/Guide/Regular_expressions">Regular expressions</a></li>
          <li><a href="/en-US/docs/Web/JavaScript/Guide/Indexed_collections">Indexed collections</a></li>
          <li><a href="/en-US/docs/Web/JavaScript/Guide/Keyed_collections">Keyed collections</a></li>
          <li><a href="/en-US/docs/Web/JavaScript/Guide/Working_with_objects">Working with objects</a></li>
          <li><a href="/en-US/docs/Web/JavaScript/Guide/Using_classes">Using classes</a></li>
          <li><a href="/en-US/docs/Web/JavaScript/Guide/Using_promises">Using promises</a></li>
          <li><a href="/en-US/docs/Web/JavaScript/Guide/Typed_arrays">JavaScript typed arrays</a></li>
          <li><a href="/en-US/docs/Web/JavaScript/Guide/Iterators_and_generators">Iterators and generators</a></li>
          <li><a href="/en-US/docs/Web/JavaScript/Guide/Meta_programming">Meta programming</a></li>
          <li><a href="/en-US/docs/Web/JavaScript/Guide/Modules">JavaScript modules</a></li>
        </ol>
    </details>
  </li>
  <li class="toggle">
    <details>
      <summary>Intermediate</summary>
      <ol>
        <li><a href="/en-US/docs/Learn/Tools_and_testing/Client-side_JavaScript_frameworks">Client-side JavaScript frameworks</a></li>
        <li><a href="/en-US/docs/Learn/JavaScript/Client-side_web_APIs">Client-side web APIs</a></li>
        <li><a href="/en-US/docs/Web/JavaScript/Language_overview">Language overview</a></li>
        <li><a href="/en-US/docs/Web/JavaScript/Data_structures">JavaScript data structures</a></li>
        <li><a href="/en-US/docs/Web/JavaScript/Equality_comparisons_and_sameness">Equality comparisons and sameness</a></li>
        <li><a href="/en-US/docs/Web/JavaScript/Enumerability_and_ownership_of_properties">Enumerability and ownership of properties</a></li>
        <li><a href="/en-US/docs/Web/JavaScript/Closures">Closures</a></li>
      </ol>
    </details>
  </li>
  <li class="toggle">
    <details>
      <summary>Advanced</summary>
      <ol>
        <li><a href="/en-US/docs/Web/JavaScript/Inheritance_and_the_prototype_chain">Inheritance and the prototype chain</a></li>
        <li><a href="/en-US/docs/Web/JavaScript/Memory_management">Memory Management</a></li>
        <li><a href="/en-US/docs/Web/JavaScript/Event_loop">Concurrency model and Event Loop</a></li>
      </ol>
    </details>
  </li>
  <li><strong><a href="/en-US/docs/Web/JavaScript/Reference">References</a></strong></li>
  <li class="toggle">
    <details>
       <summary>Built-in objects</summary>
        <ol><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects">Overview</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/AggregateError">AggregateError</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/ArrayBuffer">ArrayBuffer</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/AsyncFunction">AsyncFunction</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/AsyncGenerator">AsyncGenerator</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/AsyncGeneratorFunction">AsyncGeneratorFunction</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/AsyncIterator">AsyncIterator</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Atomics">Atomics</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/BigInt">BigInt</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/BigInt64Array">BigInt64Array</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/BigUint64Array">BigUint64Array</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">Boolean</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/DataView">DataView</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date">Date</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/decodeURI">decodeURI()</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/decodeURIComponent">decodeURIComponent()</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/encodeURI">encodeURI()</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/encodeURIComponent">encodeURIComponent()</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error">Error</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/escape">escape()</a><abbr class="icon icon-deprecated" title="Deprecated. Not for use in new websites.">
  <span class="visually-hidden">Deprecated</span>
</abbr></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/eval">eval()</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/EvalError">EvalError</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/FinalizationRegistry">FinalizationRegistry</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Float32Array">Float32Array</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Float64Array">Float64Array</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function">Function</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Generator">Generator</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/GeneratorFunction">GeneratorFunction</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/globalThis">globalThis</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Infinity">Infinity</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Int16Array">Int16Array</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Int32Array">Int32Array</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Int8Array">Int8Array</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/InternalError">InternalError</a><abbr class="icon icon-nonstandard" title="Non-standard. Check cross-browser support before using.">
    <span class="visually-hidden">Non-standard</span>
</abbr></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl">Intl</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/isFinite">isFinite()</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/isNaN">isNaN()</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Iterator">Iterator</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON">JSON</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Map">Map</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Math">Math</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/NaN">NaN</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">Number</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">Object</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/parseFloat">parseFloat()</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/parseInt">parseInt()</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Proxy">Proxy</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/RangeError">RangeError</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/ReferenceError">ReferenceError</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Reflect">Reflect</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/RegExp">RegExp</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Set">Set</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/SharedArrayBuffer">SharedArrayBuffer</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">String</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Symbol">Symbol</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/SyntaxError">SyntaxError</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/TypedArray">TypedArray</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/TypeError">TypeError</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Uint16Array">Uint16Array</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Uint32Array">Uint32Array</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Uint8Array">Uint8Array</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Uint8ClampedArray">Uint8ClampedArray</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/undefined">undefined</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/unescape">unescape()</a><abbr class="icon icon-deprecated" title="Deprecated. Not for use in new websites.">
  <span class="visually-hidden">Deprecated</span>
</abbr></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/URIError">URIError</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/WeakMap">WeakMap</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/WeakRef">WeakRef</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/WeakSet">WeakSet</a></li></ol>
    </details>
  </li>
  <li class="toggle">
    <details>
      <summary>Expressions &amp; operators</summary>
      <ol><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators">Overview</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Addition">Addition (+)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Addition_assignment">Addition assignment (+=)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Assignment">Assignment (=)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/async_function">async function expression</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/async_function*">async function* expression</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/await">await</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Bitwise_AND">Bitwise AND (&amp;)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Bitwise_AND_assignment">Bitwise AND assignment (&amp;=)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Bitwise_NOT">Bitwise NOT (~)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Bitwise_OR">Bitwise OR (|)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Bitwise_OR_assignment">Bitwise OR assignment (|=)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Bitwise_XOR">Bitwise XOR (^)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Bitwise_XOR_assignment">Bitwise XOR assignment (^=)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/class">class expression</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Comma_operator">Comma operator (,)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Conditional_operator">Conditional (ternary) operator</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Decrement">Decrement (--)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/delete">delete</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Destructuring_assignment">Destructuring assignment</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Division">Division (/)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Division_assignment">Division assignment (/=)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Equality">Equality (==)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Exponentiation">Exponentiation (**)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Exponentiation_assignment">Exponentiation assignment (**=)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/function">function expression</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/function*">function* expression</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Greater_than">Greater than (&gt;)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Greater_than_or_equal">Greater than or equal (&gt;=)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Grouping">Grouping operator ( )</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/import.meta">import.meta</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/import">import()</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/in">in</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Increment">Increment (++)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Inequality">Inequality (!=)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/instanceof">instanceof</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Left_shift">Left shift (&lt;&lt;)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Left_shift_assignment">Left shift assignment (&lt;&lt;=)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Less_than">Less than (&lt;)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Less_than_or_equal">Less than or equal (&lt;=)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Logical_AND">Logical AND (&amp;&amp;)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Logical_AND_assignment">Logical AND assignment (&amp;&amp;=)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Logical_NOT">Logical NOT (!)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Logical_OR">Logical OR (||)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Logical_OR_assignment">Logical OR assignment (||=)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Multiplication">Multiplication (*)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Multiplication_assignment">Multiplication assignment (*=)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/new">new</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/new.target">new.target</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/null">null</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Nullish_coalescing_assignment">Nullish coalescing assignment (??=)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Nullish_coalescing">Nullish coalescing operator (??)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Object_initializer">Object initializer</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Operator_precedence">Operator precedence</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Optional_chaining">Optional chaining (?.)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Property_accessors">Property accessors</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Remainder">Remainder (%)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Remainder_assignment">Remainder assignment (%=)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Right_shift">Right shift (&gt;&gt;)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Right_shift_assignment">Right shift assignment (&gt;&gt;=)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Spread_syntax">Spread syntax (...)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Strict_equality">Strict equality (===)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Strict_inequality">Strict inequality (!==)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Subtraction">Subtraction (-)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Subtraction_assignment">Subtraction assignment (-=)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/super">super</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/this">this</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/typeof">typeof</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Unary_negation">Unary negation (-)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Unary_plus">Unary plus (+)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Unsigned_right_shift">Unsigned right shift (&gt;&gt;&gt;)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/Unsigned_right_shift_assignment">Unsigned right shift assignment (&gt;&gt;&gt;=)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/void">void operator</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/yield">yield</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Operators/yield*">yield*</a></li></ol>
    </details>
  </li>
  <li class="toggle">
    <details>
      <summary>Statements &amp; declarations</summary>
      <ol><li><a href="/en-US/docs/Web/JavaScript/Reference/Statements">Overview</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Statements/async_function">async function</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Statements/async_function*">async function*</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Statements/block">Block statement</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Statements/break">break</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Statements/class">class</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Statements/const">const</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Statements/continue">continue</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Statements/debugger">debugger</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Statements/do...while">do...while</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Statements/Empty">Empty statement</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Statements/export">export</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Statements/Expression_statement">Expression statement</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Statements/for">for</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Statements/for-await...of">for await...of</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Statements/for...in">for...in</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Statements/for...of">for...of</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Statements/function">function</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Statements/function*">function*</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Statements/if...else">if...else</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Statements/import">import</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Statements/label">Labeled statement</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Statements/let">let</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Statements/return">return</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Statements/switch">switch</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Statements/throw">throw</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Statements/try...catch">try...catch</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Statements/var">var</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Statements/while">while</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Statements/with">with</a><abbr class="icon icon-deprecated" title="Deprecated. Not for use in new websites.">
  <span class="visually-hidden">Deprecated</span>
</abbr></li></ol>
    </details>
  </li>
  <li class="toggle">
    <details>
      <summary>Functions</summary>
      <ol><li><a href="/en-US/docs/Web/JavaScript/Reference/Functions">Overview</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Functions/Arrow_functions">Arrow function expressions</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Functions/Default_parameters">Default parameters</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Functions/get">get</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Functions/Method_definitions">Method definitions</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Functions/rest_parameters">Rest parameters</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Functions/set">set</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Functions/arguments">The arguments object</a></li></ol>
    </details>
  </li>
  <li class="toggle">
    <details>
      <summary>Classes</summary>
      <ol><li><a href="/en-US/docs/Web/JavaScript/Reference/Classes">Overview</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Classes/constructor">constructor</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Classes/extends">extends</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Classes/Private_properties">Private properties</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Classes/Public_class_fields">Public class fields</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Classes/static">static</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Classes/Static_initialization_blocks">Static initialization blocks</a></li></ol>
    </details>
  </li>
  <li class="toggle">
    <details>
      <summary>Regular expressions</summary>
      <ol><li><a href="/en-US/docs/Web/JavaScript/Reference/Regular_expressions">Overview</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Regular_expressions/Backreference">Backreference: \1, \2</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Regular_expressions/Capturing_group">Capturing group: (...)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Regular_expressions/Character_class_escape">Character class escape: \d, \D, \w, \W, \s, \S</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Regular_expressions/Character_class">Character class: [...], [^...]</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Regular_expressions/Character_escape">Character escape: \n, \u{...}</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Regular_expressions/Disjunction">Disjunction: |</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Regular_expressions/Input_boundary_assertion">Input boundary assertion: ^, $</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Regular_expressions/Literal_character">Literal character: a, b</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Regular_expressions/Lookahead_assertion">Lookahead assertion: (?=...), (?!...)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Regular_expressions/Lookbehind_assertion">Lookbehind assertion: (?&lt;=...), (?&lt;!...)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Regular_expressions/Named_backreference">Named backreference: \k&lt;name&gt;</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Regular_expressions/Named_capturing_group">Named capturing group: (?&lt;name&gt;...)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Regular_expressions/Non-capturing_group">Non-capturing group: (?:...)</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Regular_expressions/Quantifier">Quantifier: *, +, ?, {n}, {n,}, {n,m}</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Regular_expressions/Unicode_character_class_escape">Unicode character class escape: \p{...}, \P{...}</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Regular_expressions/Wildcard">Wildcard: .</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Regular_expressions/Word_boundary_assertion">Word boundary assertion: \b, \B</a></li></ol>
    </details>
  </li>
  <li class="toggle">
    <details>
      <summary>Errors</summary>
      <ol><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors">Overview</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Property_access_denied">Error: Permission denied to access property "x"</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Too_much_recursion">InternalError: too much recursion</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Not_a_valid_code_point">RangeError: argument is not a valid code point</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/BigInt_division_by_zero">RangeError: BigInt division by zero</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/BigInt_negative_exponent">RangeError: BigInt negative exponent</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Invalid_array_length">RangeError: invalid array length</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Invalid_date">RangeError: invalid date</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Precision_range">RangeError: precision is out of range</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Bad_radix">RangeError: radix must be an integer</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Resulting_string_too_large">RangeError: repeat count must be less than infinity</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Negative_repetition_count">RangeError: repeat count must be non-negative</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Cant_be_converted_to_BigInt_because_it_isnt_an_integer">RangeError: x can't be converted to BigInt because it isn't an integer</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Not_defined">ReferenceError: "x" is not defined</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Undeclared_var">ReferenceError: assignment to undeclared variable "x"</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Cant_access_lexical_declaration_before_init">ReferenceError: can't access lexical declaration 'X' before initialization</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Deprecated_caller_or_arguments_usage">ReferenceError: deprecated caller or arguments usage</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Undefined_prop">ReferenceError: reference to undefined property "x"</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Deprecated_octal">SyntaxError: "0"-prefixed octal literals and octal escape seq. are deprecated</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Strict_non_simple_params">SyntaxError: "use strict" not allowed in function with non-simple parameters</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Reserved_identifier">SyntaxError: "x" is a reserved identifier</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Invalid_for-of_initializer">SyntaxError: a declaration in the head of a for-of loop can't have an initializer</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Delete_in_strict_mode">SyntaxError: applying the 'delete' operator to an unqualified name is deprecated</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Bad_await">SyntaxError: await is only valid in async functions, async generators and modules</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Cant_use_nullish_coalescing_unparenthesized">SyntaxError: cannot use `??` unparenthesized within `||` and `&amp;&amp;` expressions</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Bad_continue">SyntaxError: continue must be inside loop</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Invalid_for-in_initializer">SyntaxError: for-in loop head declarations may not have initializers</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Unnamed_function_statement">SyntaxError: function statement requires a name</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Either_be_both_static_or_non-static">SyntaxError: getter and setter for private name #x should either be both static or non-static</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Identifier_after_number">SyntaxError: identifier starts immediately after numeric literal</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Illegal_character">SyntaxError: illegal character</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Invalid_assignment_left-hand_side">SyntaxError: invalid assignment left-hand side</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Invalid_BigInt_syntax">SyntaxError: invalid BigInt syntax</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Bad_regexp_flag">SyntaxError: invalid regular expression flag "x"</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/JSON_bad_parse">SyntaxError: JSON.parse: bad parsing</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Label_not_found">SyntaxError: label not found</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Missing_semicolon_before_statement">SyntaxError: missing ; before statement</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Missing_colon_after_property_id">SyntaxError: missing : after property id</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Missing_parenthesis_after_argument_list">SyntaxError: missing ) after argument list</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Missing_parenthesis_after_condition">SyntaxError: missing ) after condition</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Missing_bracket_after_list">SyntaxError: missing ] after element list</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Missing_curly_after_function_body">SyntaxError: missing } after function body</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Missing_curly_after_property_list">SyntaxError: missing } after property list</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Missing_initializer_in_const">SyntaxError: missing = in const declaration</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Missing_formal_parameter">SyntaxError: missing formal parameter</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Missing_name_after_dot_operator">SyntaxError: missing name after . operator</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/No_variable_name">SyntaxError: missing variable name</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Redeclared_parameter">SyntaxError: redeclaration of formal parameter "x"</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Bad_return">SyntaxError: return not in function</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Equal_as_assign">SyntaxError: test for equality (==) mistyped as assignment (=)?</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Hash_outside_class">SyntaxError: Unexpected '#' used outside of class body</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Unexpected_token">SyntaxError: Unexpected token</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Bad_break">SyntaxError: unlabeled break must be inside loop or switch</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Unparenthesized_unary_expr_lhs_exponentiation">SyntaxError: unparenthesized unary expression can't appear on the left-hand side of '**'</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Unterminated_string_literal">SyntaxError: unterminated string literal</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Deprecated_source_map_pragma">SyntaxError: Using //@ to indicate sourceURL pragmas is deprecated. Use //# instead</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/is_not_iterable">TypeError: 'x' is not iterable</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/No_properties">TypeError: "x" has no properties</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Unexpected_type">TypeError: "x" is (not) "y"</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Not_a_constructor">TypeError: "x" is not a constructor</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Not_a_function">TypeError: "x" is not a function</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/No_non-null_object">TypeError: "x" is not a non-null object</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Read-only">TypeError: "x" is read-only</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Cant_assign_to_property">TypeError: can't assign to property "x" on "y": not an object</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Cant_convert_BigInt_to_number">TypeError: can't convert BigInt to number</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Cant_convert_x_to_BigInt">TypeError: can't convert x to BigInt</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Cant_define_property_object_not_extensible">TypeError: can't define property "x": "obj" is not extensible</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Non_configurable_array_element">TypeError: can't delete non-configurable array element</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Cant_redefine_property">TypeError: can't redefine non-configurable property "x"</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/in_operator_no_object">TypeError: cannot use 'in' operator to search for 'x' in 'y'</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Cyclic_object_value">TypeError: cyclic object value</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/invalid_right_hand_side_instanceof_operand">TypeError: invalid 'instanceof' operand 'x'</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Array_sort_argument">TypeError: invalid Array.prototype.sort argument</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Invalid_const_assignment">TypeError: invalid assignment to const "x"</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/More_arguments_needed">TypeError: More arguments needed</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Cant_delete">TypeError: property "x" is non-configurable and can't be deleted</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Reduce_of_empty_array_with_no_initial_value">TypeError: Reduce of empty array with no initial value</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Getter_only">TypeError: setting getter-only property "x"</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Called_on_incompatible_type">TypeError: X.prototype.y called on incompatible type</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Malformed_URI">URIError: malformed URI sequence</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Already_has_pragma">Warning: -file- is being assigned a //# sourceMappingURL, but already has one</a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Errors/Stmt_after_return">Warning: unreachable code after return statement</a></li></ol>
    </details>
  </li>
  <li class="toggle">
    <details open="">
      <summary>Misc</summary>
      <ol>
        <li><a href="/en-US/docs/Web/JavaScript/JavaScript_technologies_overview">JavaScript technologies overview</a></li>
        <li><a href="/en-US/docs/Web/JavaScript/Reference/Lexical_grammar">Lexical grammar</a></li>
        <li><a href="/en-US/docs/Web/JavaScript/Reference/Iteration_protocols">Iteration protocols</a></li>
        <li><a href="/en-US/docs/Web/JavaScript/Reference/Strict_mode">Strict mode</a></li>
        <li><em><a href="/en-US/docs/Web/JavaScript/Reference/Template_literals" aria-current="page">Template literals</a></em></li>
        <li><a href="/en-US/docs/Web/JavaScript/Reference/Trailing_commas">Trailing commas</a></li>
        <li><a href="/en-US/docs/Web/JavaScript/Reference/Deprecated_and_obsolete_features">Deprecated features</a></li>
      </ol>
    </details>
  </li>
 </ol>
</div></div><section class="place side"></section></nav></aside><div class="toc-container"><aside class="toc"><nav><div class="document-toc-container"><section class="document-toc"><header><h2 class="document-toc-heading">In this article</h2></header><ul class="document-toc-list"><li class="document-toc-item "><a class="document-toc-link" href="#syntax">Syntax</a></li><li class="document-toc-item "><a class="document-toc-link" href="#description">Description</a></li><li class="document-toc-item "><a class="document-toc-link" href="#specifications">Specifications</a></li><li class="document-toc-item "><a class="document-toc-link" href="#browser_compatibility">Browser compatibility</a></li><li class="document-toc-item "><a class="document-toc-link" href="#see_also">See also</a></li></ul></section></div></nav></aside><section class="place side"></section></div></div><main id="content" class="main-content  "><article class="main-page-content" lang="en-US"><header><h1>Template literals (Template strings)</h1></header><div class="section-content"><p><strong>Template literals</strong> are literals delimited with backtick (<code>`</code>) characters, allowing for <a href="#multi-line_strings">multi-line strings</a>, <a href="#string_interpolation">string interpolation</a> with embedded expressions, and special constructs called <a href="#tagged_templates">tagged templates</a>.</p>
<p>Template literals are sometimes informally called <em>template strings</em>, because they are used most commonly for <a href="#string_interpolation">string interpolation</a> (to create strings by doing substitution of placeholders). However, a tagged template literal may not result in a string; it can be used with a custom <a href="#tagged_templates">tag function</a> to perform whatever operations you want on the different parts of the template literal.</p></div><section aria-labelledby="syntax"><h2 id="syntax"><a href="#syntax">Syntax</a></h2><div class="section-content"><div class="code-example"><div class="example-header"><span class="language-name">js</span></div><pre class="brush: js notranslate" data-signature="POt0oT1wqiF1sZUcuWLr1d+VvVei8hg3WH0cXOunTrk="><code><span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">string text</span><span class="token template-punctuation string">`</span></span>

<span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">string text line 1
 string text line 2</span><span class="token template-punctuation string">`</span></span>

<span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">string text </span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span>expression<span class="token interpolation-punctuation punctuation">}</span></span><span class="token string"> string text</span><span class="token template-punctuation string">`</span></span>

tagFunction<span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">string text </span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span>expression<span class="token interpolation-punctuation punctuation">}</span></span><span class="token string"> string text</span><span class="token template-punctuation string">`</span></span>
</code></pre></div></div></section><section aria-labelledby="parameters"><h3 id="parameters"><a href="#parameters">Parameters</a></h3><div class="section-content"><dl>
  <dt id="string_text"><a href="#string_text"><code>string text</code></a></dt>
  <dd>
    <p>The string text that will become part of the template literal. Almost all characters are allowed literally, including <a href="/en-US/docs/Web/JavaScript/Reference/Lexical_grammar#line_terminators">line breaks</a> and other <a href="/en-US/docs/Web/JavaScript/Reference/Lexical_grammar#white_space">whitespace characters</a>. However, invalid escape sequences will cause a syntax error, unless a <a href="#tagged_templates_and_escape_sequences">tag function</a> is used.</p>
  </dd>
  <dt id="expression"><a href="#expression"><code>expression</code></a></dt>
  <dd>
    <p>An expression to be inserted in the current position, whose value is converted to a string or passed to <code>tagFunction</code>.</p>
  </dd>
  <dt id="tagfunction"><a href="#tagfunction"><code>tagFunction</code></a></dt>
  <dd>
    <p>If specified, it will be called with the template strings array and substitution expressions, and the return value becomes the value of the template literal. See <a href="#tagged_templates">tagged templates</a>.</p>
  </dd>
</dl></div></section><section aria-labelledby="description"><h2 id="description"><a href="#description">Description</a></h2><div class="section-content"><p>Template literals are enclosed by backtick (<code>`</code>) characters instead of double or single quotes.</p>
<p>Along with having normal strings, template literals can also contain other parts called <em>placeholders</em>, which are embedded expressions delimited by a dollar sign and curly braces: <code>${expression}</code>. The strings and placeholders get passed to a function — either a default function, or a function you supply. The default function (when you don't supply your own) just performs <a href="#string_interpolation">string interpolation</a> to do substitution of the placeholders and then concatenate the parts into a single string.</p>
<p>To supply a function of your own, precede the template literal with a function name; the result is called a <a href="#tagged_templates"><strong>tagged template</strong></a>. In that case, the template literal is passed to your tag function, where you can then perform whatever operations you want on the different parts of the template literal.</p>
<p>To escape a backtick in a template literal, put a backslash (<code>\</code>) before the backtick.</p>
<div class="code-example"><div class="example-header"><span class="language-name">js</span></div><pre class="brush: js notranslate" data-signature="/vzBpScYQ01/11UvRE6qmWGitZYE0i7M5sSdE0zn98M="><code><span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">\`</span><span class="token template-punctuation string">`</span></span> <span class="token operator">===</span> <span class="token string">"`"</span><span class="token punctuation">;</span> <span class="token comment">// true</span>
</code></pre></div>
<p>Dollar signs can be escaped as well to prevent interpolation.</p>
<div class="code-example"><div class="example-header"><span class="language-name">js</span></div><pre class="brush: js notranslate" data-signature="dnT3NDUaIR0TlrDwH5bCgCdUf8m8Gnl3bBrq9/mcqsw="><code><span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">\${1}</span><span class="token template-punctuation string">`</span></span> <span class="token operator">===</span> <span class="token string">"${1}"</span><span class="token punctuation">;</span> <span class="token comment">// true</span>
</code></pre></div></div></section><section aria-labelledby="multi-line_strings"><h3 id="multi-line_strings"><a href="#multi-line_strings">Multi-line strings</a></h3><div class="section-content"><p>Any newline characters inserted in the source are part of the template literal.</p>
<p>Using normal strings, you would have to use the following syntax in order to get multi-line strings:</p>
<div class="code-example"><div class="example-header"><span class="language-name">js</span></div><pre class="brush: js notranslate" data-signature="hJzfJT08wCoBDENM7tv/dGUC1aqdCUVPSjSnyVHdS7w="><code>console<span class="token punctuation">.</span><span class="token function">log</span><span class="token punctuation">(</span><span class="token string">"string text line 1\n"</span> <span class="token operator">+</span> <span class="token string">"string text line 2"</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token comment">// "string text line 1</span>
<span class="token comment">// string text line 2"</span>
</code></pre></div>
<p>Using template literals, you can do the same with this:</p>
<div class="code-example"><div class="example-header"><span class="language-name">js</span></div><pre class="brush: js notranslate" data-signature="0weQlKv+or/JxXSkYX3Tsdpxe4Jvqi8O5+v3H08JkOM="><code>console<span class="token punctuation">.</span><span class="token function">log</span><span class="token punctuation">(</span><span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">string text line 1
string text line 2</span><span class="token template-punctuation string">`</span></span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token comment">// "string text line 1</span>
<span class="token comment">// string text line 2"</span>
</code></pre></div></div></section><section aria-labelledby="string_interpolation"><h3 id="string_interpolation"><a href="#string_interpolation">String interpolation</a></h3><div class="section-content"><p>Without template literals, when you want to combine output from expressions with strings, you'd <a href="/en-US/docs/Learn/JavaScript/First_steps/Strings#concatenation_using">concatenate them</a> using the <a href="/en-US/docs/Web/JavaScript/Reference/Operators/Addition">addition operator</a> <code>+</code>:</p>
<div class="code-example"><div class="example-header"><span class="language-name">js</span></div><pre class="brush: js notranslate" data-signature="7cwIc8Sw9EWtXtKeGqrhMhZpJ9LJzPQIak6LG1oeD2s="><code><span class="token keyword">const</span> a <span class="token operator">=</span> <span class="token number">5</span><span class="token punctuation">;</span>
<span class="token keyword">const</span> b <span class="token operator">=</span> <span class="token number">10</span><span class="token punctuation">;</span>
console<span class="token punctuation">.</span><span class="token function">log</span><span class="token punctuation">(</span><span class="token string">"Fifteen is "</span> <span class="token operator">+</span> <span class="token punctuation">(</span>a <span class="token operator">+</span> b<span class="token punctuation">)</span> <span class="token operator">+</span> <span class="token string">" and\nnot "</span> <span class="token operator">+</span> <span class="token punctuation">(</span><span class="token number">2</span> <span class="token operator">*</span> a <span class="token operator">+</span> b<span class="token punctuation">)</span> <span class="token operator">+</span> <span class="token string">"."</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token comment">// "Fifteen is 15 and</span>
<span class="token comment">// not 20."</span>
</code></pre></div>
<p>That can be hard to read – especially when you have multiple expressions.</p>
<p>With template literals, you can avoid the concatenation operator — and improve the readability of your code — by using placeholders of the form <code>${expression}</code> to perform substitutions for embedded expressions:</p>
<div class="code-example"><div class="example-header"><span class="language-name">js</span></div><pre class="brush: js notranslate" data-signature="e7UYB1fA2jSkh6LarUKqLqaJhoiefzvB3JCYeP38SJs="><code><span class="token keyword">const</span> a <span class="token operator">=</span> <span class="token number">5</span><span class="token punctuation">;</span>
<span class="token keyword">const</span> b <span class="token operator">=</span> <span class="token number">10</span><span class="token punctuation">;</span>
console<span class="token punctuation">.</span><span class="token function">log</span><span class="token punctuation">(</span><span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">Fifteen is </span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span>a <span class="token operator">+</span> b<span class="token interpolation-punctuation punctuation">}</span></span><span class="token string"> and
not </span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span><span class="token number">2</span> <span class="token operator">*</span> a <span class="token operator">+</span> b<span class="token interpolation-punctuation punctuation">}</span></span><span class="token string">.</span><span class="token template-punctuation string">`</span></span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token comment">// "Fifteen is 15 and</span>
<span class="token comment">// not 20."</span>
</code></pre></div>
<p>Note that there's a mild difference between the two syntaxes. Template literals <a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/String#string_coercion">coerce their expressions directly to strings</a>, while addition coerces its operands to primitives first. For more information, see the reference page for the <a href="/en-US/docs/Web/JavaScript/Reference/Operators/Addition"><code>+</code> operator</a>.</p></div></section><section aria-labelledby="nesting_templates"><h3 id="nesting_templates"><a href="#nesting_templates">Nesting templates</a></h3><div class="section-content"><p>In certain cases, nesting a template is the easiest (and perhaps more readable) way to have configurable strings. Within a backtick-delimited template, it is simple to allow inner backticks by using them inside an <code>${expression}</code> placeholder within the template.</p>
<p>For example, without template literals, if you wanted to return a certain value based on a particular condition, you could do something like the following:</p>
<div class="code-example"><div class="example-header"><span class="language-name">js</span></div><pre class="brush: js example-bad notranslate" data-signature="7Ay65lWwVeaV0IV3DMi6G527iizW2CPLyM8S/xfIqwo="><code><span class="token keyword">let</span> classes <span class="token operator">=</span> <span class="token string">"header"</span><span class="token punctuation">;</span>
classes <span class="token operator">+=</span> <span class="token function">isLargeScreen</span><span class="token punctuation">(</span><span class="token punctuation">)</span>
  <span class="token operator">?</span> <span class="token string">""</span>
  <span class="token operator">:</span> item<span class="token punctuation">.</span>isCollapsed
    <span class="token operator">?</span> <span class="token string">" icon-expander"</span>
    <span class="token operator">:</span> <span class="token string">" icon-collapser"</span><span class="token punctuation">;</span>
</code></pre></div>
<p>With a template literal but without nesting, you could do this:</p>
<div class="code-example"><div class="example-header"><span class="language-name">js</span></div><pre class="brush: js example-bad notranslate" data-signature="egv7yBcsx4mSQXdkQWJFWMxtrjfEPq5NgLRaeYyQJ84="><code><span class="token keyword">const</span> classes <span class="token operator">=</span> <span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">header </span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span>
  <span class="token function">isLargeScreen</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">?</span> <span class="token string">""</span> <span class="token operator">:</span> item<span class="token punctuation">.</span>isCollapsed <span class="token operator">?</span> <span class="token string">"icon-expander"</span> <span class="token operator">:</span> <span class="token string">"icon-collapser"</span>
<span class="token interpolation-punctuation punctuation">}</span></span><span class="token template-punctuation string">`</span></span><span class="token punctuation">;</span>
</code></pre></div>
<p>With nesting of template literals, you can do this:</p>
<div class="code-example"><div class="example-header"><span class="language-name">js</span></div><pre class="brush: js example-good notranslate" data-signature="AaQlC2hhFzFU/m8NEknSyhRVBk/ylwqZyiPSgPseae8="><code><span class="token keyword">const</span> classes <span class="token operator">=</span> <span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">header </span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span>
  <span class="token function">isLargeScreen</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">?</span> <span class="token string">""</span> <span class="token operator">:</span> <span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">icon-</span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span>item<span class="token punctuation">.</span>isCollapsed <span class="token operator">?</span> <span class="token string">"expander"</span> <span class="token operator">:</span> <span class="token string">"collapser"</span><span class="token interpolation-punctuation punctuation">}</span></span><span class="token template-punctuation string">`</span></span>
<span class="token interpolation-punctuation punctuation">}</span></span><span class="token template-punctuation string">`</span></span><span class="token punctuation">;</span>
</code></pre></div></div></section><section aria-labelledby="tagged_templates"><h3 id="tagged_templates"><a href="#tagged_templates">Tagged templates</a></h3><div class="section-content"><p>A more advanced form of template literals are <em>tagged</em> templates.</p>
<p>Tags allow you to parse template literals with a function. The first argument of a tag function contains an array of string values. The remaining arguments are related to the expressions.</p>
<p>The tag function can then perform whatever operations on these arguments you wish, and return the manipulated string. (Alternatively, it can return something completely different, as described in one of the following examples.)</p>
<p>The name of the function used for the tag can be whatever you want.</p>
<div class="code-example"><div class="example-header"><span class="language-name">js</span></div><pre class="brush: js notranslate" data-signature="rJnqQ6QdUcOXtW4dxXbx3Yi+y+icFw+jA5uX8Bx9Lrk="><code><span class="token keyword">const</span> person <span class="token operator">=</span> <span class="token string">"Mike"</span><span class="token punctuation">;</span>
<span class="token keyword">const</span> age <span class="token operator">=</span> <span class="token number">28</span><span class="token punctuation">;</span>

<span class="token keyword">function</span> <span class="token function">myTag</span><span class="token punctuation">(</span><span class="token parameter">strings<span class="token punctuation">,</span> personExp<span class="token punctuation">,</span> ageExp</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
  <span class="token keyword">const</span> str0 <span class="token operator">=</span> strings<span class="token punctuation">[</span><span class="token number">0</span><span class="token punctuation">]</span><span class="token punctuation">;</span> <span class="token comment">// "That "</span>
  <span class="token keyword">const</span> str1 <span class="token operator">=</span> strings<span class="token punctuation">[</span><span class="token number">1</span><span class="token punctuation">]</span><span class="token punctuation">;</span> <span class="token comment">// " is a "</span>
  <span class="token keyword">const</span> str2 <span class="token operator">=</span> strings<span class="token punctuation">[</span><span class="token number">2</span><span class="token punctuation">]</span><span class="token punctuation">;</span> <span class="token comment">// "."</span>

  <span class="token keyword">const</span> ageStr <span class="token operator">=</span> ageExp <span class="token operator">&lt;</span> <span class="token number">100</span> <span class="token operator">?</span> <span class="token string">"youngster"</span> <span class="token operator">:</span> <span class="token string">"centenarian"</span><span class="token punctuation">;</span>

  <span class="token comment">// We can even return a string built using a template literal</span>
  <span class="token keyword">return</span> <span class="token template-string"><span class="token template-punctuation string">`</span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span>str0<span class="token interpolation-punctuation punctuation">}</span></span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span>personExp<span class="token interpolation-punctuation punctuation">}</span></span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span>str1<span class="token interpolation-punctuation punctuation">}</span></span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span>ageStr<span class="token interpolation-punctuation punctuation">}</span></span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span>str2<span class="token interpolation-punctuation punctuation">}</span></span><span class="token template-punctuation string">`</span></span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<span class="token keyword">const</span> output <span class="token operator">=</span> myTag<span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">That </span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span>person<span class="token interpolation-punctuation punctuation">}</span></span><span class="token string"> is a </span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span>age<span class="token interpolation-punctuation punctuation">}</span></span><span class="token string">.</span><span class="token template-punctuation string">`</span></span><span class="token punctuation">;</span>

console<span class="token punctuation">.</span><span class="token function">log</span><span class="token punctuation">(</span>output<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token comment">// That Mike is a youngster.</span>
</code></pre></div>
<p>The tag does not have to be a plain identifier. You can use any expression with <a href="/en-US/docs/Web/JavaScript/Reference/Operators/Operator_precedence#table">precedence</a> greater than 16, which includes <a href="/en-US/docs/Web/JavaScript/Reference/Operators/Property_accessors">property access</a>, function call, <a href="/en-US/docs/Web/JavaScript/Reference/Operators/new">new expression</a>, or even another tagged template literal.</p>
<div class="code-example"><div class="example-header"><span class="language-name">js</span></div><pre class="brush: js notranslate" data-signature="NsE2ioiknovNCQvHSn0tMd40JH50/BQgYPFvvURxwsk="><code>console<span class="token punctuation">.</span>log<span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">Hello</span><span class="token template-punctuation string">`</span></span><span class="token punctuation">;</span> <span class="token comment">// [ 'Hello' ]</span>
console<span class="token punctuation">.</span><span class="token function">log</span><span class="token punctuation">.</span><span class="token function">bind</span><span class="token punctuation">(</span><span class="token number">1</span><span class="token punctuation">,</span> <span class="token number">2</span><span class="token punctuation">)</span><span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">Hello</span><span class="token template-punctuation string">`</span></span><span class="token punctuation">;</span> <span class="token comment">// 2 [ 'Hello' ]</span>
<span class="token keyword">new</span> <span class="token class-name">Function</span><span class="token punctuation">(</span><span class="token string">"console.log(arguments)"</span><span class="token punctuation">)</span><span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">Hello</span><span class="token template-punctuation string">`</span></span><span class="token punctuation">;</span> <span class="token comment">// [Arguments] { '0': [ 'Hello' ] }</span>

<span class="token keyword">function</span> <span class="token function">recursive</span><span class="token punctuation">(</span><span class="token parameter">strings<span class="token punctuation">,</span> <span class="token operator">...</span>values</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
  console<span class="token punctuation">.</span><span class="token function">log</span><span class="token punctuation">(</span>strings<span class="token punctuation">,</span> values<span class="token punctuation">)</span><span class="token punctuation">;</span>
  <span class="token keyword">return</span> recursive<span class="token punctuation">;</span>
<span class="token punctuation">}</span>
recursive<span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">Hello</span><span class="token template-punctuation string">`</span></span><span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">World</span><span class="token template-punctuation string">`</span></span><span class="token punctuation">;</span>
<span class="token comment">// [ 'Hello' ] []</span>
<span class="token comment">// [ 'World' ] []</span>
</code></pre></div>
<p>While technically permitted by the syntax, <em>untagged</em> template literals are strings and will throw a <a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/TypeError"><code>TypeError</code></a> when chained.</p>
<div class="code-example"><div class="example-header"><span class="language-name">js</span></div><pre class="brush: js notranslate" data-signature="7aSgEmaTPjIFNq4q7YlCBNjX79DoYRIpErNvjfr69i4="><code>console<span class="token punctuation">.</span><span class="token function">log</span><span class="token punctuation">(</span><span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">Hello</span><span class="token template-punctuation string">`</span></span><span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">World</span><span class="token template-punctuation string">`</span></span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// TypeError: "Hello" is not a function</span>
</code></pre></div>
<p>The only exception is optional chaining, which will throw a syntax error.</p>
<div class="code-example"><div class="example-header"><span class="language-name">js</span></div><pre class="brush: js example-bad notranslate" data-signature="155+w9vUm9SUHasN7hCo6sSuvslpbN9bcqFMoMbeGw8="><code>console<span class="token punctuation">.</span>log<span class="token operator">?.</span><span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">Hello</span><span class="token template-punctuation string">`</span></span><span class="token punctuation">;</span> <span class="token comment">// SyntaxError: Invalid tagged template on optional chain</span>
console<span class="token operator">?.</span>log<span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">Hello</span><span class="token template-punctuation string">`</span></span><span class="token punctuation">;</span> <span class="token comment">// SyntaxError: Invalid tagged template on optional chain</span>
</code></pre></div>
<p>Note that these two expressions are still parsable. This means they would not be subject to <a href="/en-US/docs/Web/JavaScript/Reference/Lexical_grammar#automatic_semicolon_insertion">automatic semicolon insertion</a>, which will only insert semicolons to fix code that's otherwise unparsable.</p>
<div class="code-example"><div class="example-header"><span class="language-name">js</span></div><pre class="brush: js example-bad notranslate" data-signature="sParPuiRD1VdazbSB3EYVrWdz5CD+OUtioWrHz+U5RI="><code><span class="token comment">// Still a syntax error</span>
<span class="token keyword">const</span> a <span class="token operator">=</span> console<span class="token operator">?.</span>log
<span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">Hello</span><span class="token template-punctuation string">`</span></span>
</code></pre></div>
<p>Tag functions don't even need to return a string!</p>
<div class="code-example"><div class="example-header"><span class="language-name">js</span></div><pre class="brush: js notranslate" data-signature="tta4Vn7I3PhB+KUUKOr0DRlCxv4XDXwFS2kWmKuGi4U="><code><span class="token keyword">function</span> <span class="token function">template</span><span class="token punctuation">(</span><span class="token parameter">strings<span class="token punctuation">,</span> <span class="token operator">...</span>keys</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
  <span class="token keyword">return</span> <span class="token punctuation">(</span><span class="token parameter"><span class="token operator">...</span>values</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token punctuation">{</span>
    <span class="token keyword">const</span> dict <span class="token operator">=</span> values<span class="token punctuation">[</span>values<span class="token punctuation">.</span>length <span class="token operator">-</span> <span class="token number">1</span><span class="token punctuation">]</span> <span class="token operator">||</span> <span class="token punctuation">{</span><span class="token punctuation">}</span><span class="token punctuation">;</span>
    <span class="token keyword">const</span> result <span class="token operator">=</span> <span class="token punctuation">[</span>strings<span class="token punctuation">[</span><span class="token number">0</span><span class="token punctuation">]</span><span class="token punctuation">]</span><span class="token punctuation">;</span>
    keys<span class="token punctuation">.</span><span class="token function">forEach</span><span class="token punctuation">(</span><span class="token punctuation">(</span><span class="token parameter">key<span class="token punctuation">,</span> i</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token punctuation">{</span>
      <span class="token keyword">const</span> value <span class="token operator">=</span> Number<span class="token punctuation">.</span><span class="token function">isInteger</span><span class="token punctuation">(</span>key<span class="token punctuation">)</span> <span class="token operator">?</span> values<span class="token punctuation">[</span>key<span class="token punctuation">]</span> <span class="token operator">:</span> dict<span class="token punctuation">[</span>key<span class="token punctuation">]</span><span class="token punctuation">;</span>
      result<span class="token punctuation">.</span><span class="token function">push</span><span class="token punctuation">(</span>value<span class="token punctuation">,</span> strings<span class="token punctuation">[</span>i <span class="token operator">+</span> <span class="token number">1</span><span class="token punctuation">]</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword">return</span> result<span class="token punctuation">.</span><span class="token function">join</span><span class="token punctuation">(</span><span class="token string">""</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
  <span class="token punctuation">}</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<span class="token keyword">const</span> t1Closure <span class="token operator">=</span> template<span class="token template-string"><span class="token template-punctuation string">`</span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span><span class="token number">0</span><span class="token interpolation-punctuation punctuation">}</span></span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span><span class="token number">1</span><span class="token interpolation-punctuation punctuation">}</span></span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span><span class="token number">0</span><span class="token interpolation-punctuation punctuation">}</span></span><span class="token string">!</span><span class="token template-punctuation string">`</span></span><span class="token punctuation">;</span>
<span class="token comment">// const t1Closure = template(["","","","!"],0,1,0);</span>
<span class="token function">t1Closure</span><span class="token punctuation">(</span><span class="token string">"Y"</span><span class="token punctuation">,</span> <span class="token string">"A"</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// "YAY!"</span>

<span class="token keyword">const</span> t2Closure <span class="token operator">=</span> template<span class="token template-string"><span class="token template-punctuation string">`</span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span><span class="token number">0</span><span class="token interpolation-punctuation punctuation">}</span></span><span class="token string"> </span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span><span class="token string">"foo"</span><span class="token interpolation-punctuation punctuation">}</span></span><span class="token string">!</span><span class="token template-punctuation string">`</span></span><span class="token punctuation">;</span>
<span class="token comment">// const t2Closure = template([""," ","!"],0,"foo");</span>
<span class="token function">t2Closure</span><span class="token punctuation">(</span><span class="token string">"Hello"</span><span class="token punctuation">,</span> <span class="token punctuation">{</span> <span class="token literal-property property">foo</span><span class="token operator">:</span> <span class="token string">"World"</span> <span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// "Hello World!"</span>

<span class="token keyword">const</span> t3Closure <span class="token operator">=</span> template<span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">I'm </span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span><span class="token string">"name"</span><span class="token interpolation-punctuation punctuation">}</span></span><span class="token string">. I'm almost </span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span><span class="token string">"age"</span><span class="token interpolation-punctuation punctuation">}</span></span><span class="token string"> years old.</span><span class="token template-punctuation string">`</span></span><span class="token punctuation">;</span>
<span class="token comment">// const t3Closure = template(["I'm ", ". I'm almost ", " years old."], "name", "age");</span>
<span class="token function">t3Closure</span><span class="token punctuation">(</span><span class="token string">"foo"</span><span class="token punctuation">,</span> <span class="token punctuation">{</span> <span class="token literal-property property">name</span><span class="token operator">:</span> <span class="token string">"MDN"</span><span class="token punctuation">,</span> <span class="token literal-property property">age</span><span class="token operator">:</span> <span class="token number">30</span> <span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// "I'm MDN. I'm almost 30 years old."</span>
<span class="token function">t3Closure</span><span class="token punctuation">(</span><span class="token punctuation">{</span> <span class="token literal-property property">name</span><span class="token operator">:</span> <span class="token string">"MDN"</span><span class="token punctuation">,</span> <span class="token literal-property property">age</span><span class="token operator">:</span> <span class="token number">30</span> <span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// "I'm MDN. I'm almost 30 years old."</span>
</code></pre></div>
<p>The first argument received by the tag function is an array of strings. For any template literal, its length is equal to the number of substitutions (occurrences of <code>${…}</code>) plus one, and is therefore always non-empty.</p>
<p>For any particular tagged template literal expression, the tag function will always be called with the exact same literal array, no matter how many times the literal is evaluated.</p>
<div class="code-example"><div class="example-header"><span class="language-name">js</span></div><pre class="brush: js notranslate" data-signature="R0Wr23NGuRTBoku6cSHZ/XNZ2oZAdoqm3df2x/QPGco="><code><span class="token keyword">const</span> callHistory <span class="token operator">=</span> <span class="token punctuation">[</span><span class="token punctuation">]</span><span class="token punctuation">;</span>

<span class="token keyword">function</span> <span class="token function">tag</span><span class="token punctuation">(</span><span class="token parameter">strings<span class="token punctuation">,</span> <span class="token operator">...</span>values</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
  callHistory<span class="token punctuation">.</span><span class="token function">push</span><span class="token punctuation">(</span>strings<span class="token punctuation">)</span><span class="token punctuation">;</span>
  <span class="token comment">// Return a freshly made object</span>
  <span class="token keyword">return</span> <span class="token punctuation">{</span><span class="token punctuation">}</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<span class="token keyword">function</span> <span class="token function">evaluateLiteral</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
  <span class="token keyword">return</span> tag<span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">Hello, </span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span><span class="token string">"world"</span><span class="token interpolation-punctuation punctuation">}</span></span><span class="token string">!</span><span class="token template-punctuation string">`</span></span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>

console<span class="token punctuation">.</span><span class="token function">log</span><span class="token punctuation">(</span><span class="token function">evaluateLiteral</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">===</span> <span class="token function">evaluateLiteral</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// false; each time `tag` is called, it returns a new object</span>
console<span class="token punctuation">.</span><span class="token function">log</span><span class="token punctuation">(</span>callHistory<span class="token punctuation">[</span><span class="token number">0</span><span class="token punctuation">]</span> <span class="token operator">===</span> callHistory<span class="token punctuation">[</span><span class="token number">1</span><span class="token punctuation">]</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// true; all evaluations of the same tagged literal would pass in the same strings array</span>
</code></pre></div>
<p>This allows the tag to cache the result based on the identity of its first argument. To further ensure the array value's stability, the first argument and its <a href="#raw_strings"><code>raw</code> property</a> are both <a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/isFrozen">frozen</a>, so you can't mutate them in any way.</p></div></section><section aria-labelledby="raw_strings"><h3 id="raw_strings"><a href="#raw_strings">Raw strings</a></h3><div class="section-content"><p>The special <code>raw</code> property, available on the first argument to the tag function, allows you to access the raw strings as they were entered, without processing <a href="/en-US/docs/Web/JavaScript/Guide/Grammar_and_types#using_special_characters_in_strings">escape sequences</a>.</p>
<div class="code-example"><div class="example-header"><span class="language-name">js</span></div><pre class="brush: js notranslate" data-signature="8LxMf/dxd5eQsNjZ+JH3EEBC2G3NlW32B7+Gd+XoG7M="><code><span class="token keyword">function</span> <span class="token function">tag</span><span class="token punctuation">(</span><span class="token parameter">strings</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
  console<span class="token punctuation">.</span><span class="token function">log</span><span class="token punctuation">(</span>strings<span class="token punctuation">.</span>raw<span class="token punctuation">[</span><span class="token number">0</span><span class="token punctuation">]</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>

tag<span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">string text line 1 \n string text line 2</span><span class="token template-punctuation string">`</span></span><span class="token punctuation">;</span>
<span class="token comment">// Logs "string text line 1 \n string text line 2" ,</span>
<span class="token comment">// including the two characters '\' and 'n'</span>
</code></pre></div>
<p>In addition, the <a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/raw"><code>String.raw()</code></a> method exists to create raw strings just like the default template function and string concatenation would create.</p>
<div class="code-example"><div class="example-header"><span class="language-name">js</span></div><pre class="brush: js notranslate" data-signature="BbUr/VVYlNhbtiRvrOeLQMOQ3Hd9DO91tHQqmWstlGU="><code><span class="token keyword">const</span> str <span class="token operator">=</span> String<span class="token punctuation">.</span>raw<span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">Hi\n</span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span><span class="token number">2</span> <span class="token operator">+</span> <span class="token number">3</span><span class="token interpolation-punctuation punctuation">}</span></span><span class="token string">!</span><span class="token template-punctuation string">`</span></span><span class="token punctuation">;</span>
<span class="token comment">// "Hi\\n5!"</span>

str<span class="token punctuation">.</span>length<span class="token punctuation">;</span>
<span class="token comment">// 6</span>

Array<span class="token punctuation">.</span><span class="token function">from</span><span class="token punctuation">(</span>str<span class="token punctuation">)</span><span class="token punctuation">.</span><span class="token function">join</span><span class="token punctuation">(</span><span class="token string">","</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token comment">// "H,i,\\,n,5,!"</span>
</code></pre></div>
<p><code>String.raw</code> functions like an "identity" tag if the literal doesn't contain any escape sequences. In case you want an actual identity tag that always works as if the literal is untagged, you can make a custom function that passes the "cooked" (i.e. escape sequences are processed) literal array to <code>String.raw</code>, pretending they are raw strings.</p>
<div class="code-example"><div class="example-header"><span class="language-name">js</span></div><pre class="brush: js notranslate" data-signature="tttx9I9r0BnPK/M2r9GFDQX3mlN9EKaRpNndAwmah+o="><code><span class="token keyword">const</span> <span class="token function-variable function">identity</span> <span class="token operator">=</span> <span class="token punctuation">(</span><span class="token parameter">strings<span class="token punctuation">,</span> <span class="token operator">...</span>values</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span>
  String<span class="token punctuation">.</span><span class="token function">raw</span><span class="token punctuation">(</span><span class="token punctuation">{</span> <span class="token literal-property property">raw</span><span class="token operator">:</span> strings <span class="token punctuation">}</span><span class="token punctuation">,</span> <span class="token operator">...</span>values<span class="token punctuation">)</span><span class="token punctuation">;</span>
console<span class="token punctuation">.</span><span class="token function">log</span><span class="token punctuation">(</span>identity<span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">Hi\n</span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span><span class="token number">2</span> <span class="token operator">+</span> <span class="token number">3</span><span class="token interpolation-punctuation punctuation">}</span></span><span class="token string">!</span><span class="token template-punctuation string">`</span></span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token comment">// Hi</span>
<span class="token comment">// 5!</span>
</code></pre></div>
<p>This is useful for many tools which give special treatment to literals tagged by a particular name.</p>
<div class="code-example"><div class="example-header"><span class="language-name">js</span></div><pre class="brush: js notranslate" data-signature="EOgB9+Kd/2A8i46IZrX2nhtPjOfWS07EO4D69wc4Vnw="><code><span class="token keyword">const</span> <span class="token function-variable function">html</span> <span class="token operator">=</span> <span class="token punctuation">(</span><span class="token parameter">strings<span class="token punctuation">,</span> <span class="token operator">...</span>values</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> String<span class="token punctuation">.</span><span class="token function">raw</span><span class="token punctuation">(</span><span class="token punctuation">{</span> <span class="token literal-property property">raw</span><span class="token operator">:</span> strings <span class="token punctuation">}</span><span class="token punctuation">,</span> <span class="token operator">...</span>values<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token comment">// Some formatters will format this literal's content as HTML</span>
<span class="token keyword">const</span> doc <span class="token operator">=</span> html<span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">&lt;!doctype html&gt;
  &lt;html lang="en-US"&gt;
    &lt;head&gt;
      &lt;title&gt;Hello&lt;/title&gt;
    &lt;/head&gt;
    &lt;body&gt;
      &lt;h1&gt;Hello world!&lt;/h1&gt;
    &lt;/body&gt;
  &lt;/html&gt;</span><span class="token template-punctuation string">`</span></span><span class="token punctuation">;</span>
</code></pre></div></div></section><section aria-labelledby="tagged_templates_and_escape_sequences"><h3 id="tagged_templates_and_escape_sequences"><a href="#tagged_templates_and_escape_sequences">Tagged templates and escape sequences</a></h3><div class="section-content"><p>In normal template literals, <a href="/en-US/docs/Web/JavaScript/Reference/Lexical_grammar#escape_sequences">the escape sequences in string literals</a> are all allowed. Any other non-well-formed escape sequence is a syntax error. This includes:</p>
<ul>
  <li><code>\</code> followed by any decimal digit other than <code>0</code>, or <code>\0</code> followed by a decimal digit; for example <code>\9</code> and <code>\07</code> (which is a <a href="/en-US/docs/Web/JavaScript/Reference/Deprecated_and_obsolete_features#escape_sequences">deprecated syntax</a>)</li>
  <li><code>\x</code> followed by fewer than two hex digits (including none); for example <code>\xz</code></li>
  <li><code>\u</code> not followed by <code>{</code> and followed by fewer than four hex digits (including none); for example <code>\uz</code></li>
  <li><code>\u{}</code> enclosing an invalid Unicode code point — it contains a non-hex digit, or its value is greater than <code>10FFFF</code>; for example <code>\u{110000}</code> and <code>\u{z}</code></li>
</ul>
<div class="notecard note" id="sect1">
  <p><strong>Note:</strong> <code>\</code> followed by other characters, while they may be useless since nothing is escaped, are not syntax errors.</p>
</div>
<p>However, this is problematic for tagged templates, which, in addition to the "cooked" literal, also have access to the raw literals (escape sequences are preserved as-is).</p>
<p>Tagged templates should allow the embedding of languages (for example <a href="https://en.wikipedia.org/wiki/Domain-specific_language" class="external" target="_blank">DSLs</a>, or <a href="https://en.wikipedia.org/wiki/LaTeX" class="external" target="_blank">LaTeX</a>), where other escapes sequences are common. Therefore, the syntax restriction of well-formed escape sequences is removed from tagged templates.</p>
<div class="code-example"><div class="example-header"><span class="language-name">js</span></div><pre class="brush: js notranslate" data-signature="pqpnq90MG+vK3rKAWLWYdNFZIkgWIWOf927Zs27uIk0="><code>latex<span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">\unicode</span><span class="token template-punctuation string">`</span></span><span class="token punctuation">;</span>
<span class="token comment">// Throws in older ECMAScript versions (ES2016 and earlier)</span>
<span class="token comment">// SyntaxError: malformed Unicode character escape sequence</span>
</code></pre></div>
<p>However, illegal escape sequences must still be represented in the "cooked" representation. They will show up as <a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/undefined"><code>undefined</code></a> element in the "cooked" array:</p>
<div class="code-example"><div class="example-header"><span class="language-name">js</span></div><pre class="brush: js notranslate" data-signature="oHdySCkouCSuxxdUaiQCyL2xl3MPcjqXFMJnm3W0r5Q="><code><span class="token keyword">function</span> <span class="token function">latex</span><span class="token punctuation">(</span><span class="token parameter">str</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
  <span class="token keyword">return</span> <span class="token punctuation">{</span> <span class="token literal-property property">cooked</span><span class="token operator">:</span> str<span class="token punctuation">[</span><span class="token number">0</span><span class="token punctuation">]</span><span class="token punctuation">,</span> <span class="token literal-property property">raw</span><span class="token operator">:</span> str<span class="token punctuation">.</span>raw<span class="token punctuation">[</span><span class="token number">0</span><span class="token punctuation">]</span> <span class="token punctuation">}</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>

latex<span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">\unicode</span><span class="token template-punctuation string">`</span></span><span class="token punctuation">;</span>

<span class="token comment">// { cooked: undefined, raw: "\\unicode" }</span>
</code></pre></div>
<p>Note that the escape-sequence restriction is only dropped from <em>tagged</em> templates, but not from <em>untagged</em> template literals:</p>
<div class="code-example"><div class="example-header"><span class="language-name">js</span></div><pre class="brush: js example-bad notranslate" data-signature="l/2X9FCw/Hd/PqTYZ74IYiAYYUffWFkzLdU0YVY03tE="><code><span class="token keyword">const</span> bad <span class="token operator">=</span> <span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">bad escape sequence: \unicode</span><span class="token template-punctuation string">`</span></span><span class="token punctuation">;</span>
</code></pre></div></div></section><h2 id="specifications"><a href="#specifications">Specifications</a></h2><table class="standard-table"><thead><tr><th scope="col">Specification</th></tr></thead><tbody><tr><td><a href="https://tc39.es/ecma262/multipage/ecmascript-language-expressions.html#sec-template-literals">ECMAScript Language Specification<!-- --> <br/><small># <!-- -->sec-template-literals</small></a></td></tr></tbody></table><h2 id="browser_compatibility"><a href="#browser_compatibility">Browser compatibility</a></h2><p>BCD tables only load in the browser<noscript> <!-- -->with JavaScript enabled. Enable JavaScript to view data.</noscript></p><section aria-labelledby="see_also"><h2 id="see_also"><a href="#see_also">See also</a></h2><div class="section-content"><ul>
  <li><a href="/en-US/docs/Web/JavaScript/Guide/Text_formatting">Text formatting</a> guide</li>
  <li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/String"><code>String</code></a></li>
  <li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/raw"><code>String.raw()</code></a></li>
  <li><a href="/en-US/docs/Web/JavaScript/Reference/Lexical_grammar">Lexical grammar</a></li>
  <li><a href="https://hacks.mozilla.org/2015/05/es6-in-depth-template-strings-2/" class="external" target="_blank">ES6 in Depth: Template strings</a> on hacks.mozilla.org (2015)</li>
</ul></div></section><aside class="metadata"><div class="metadata-content-container"><div id="on-github" class="on-github"><h3>Found a content problem with this page?</h3><ul><li><a href="https://github.com/mdn/content/edit/main/files/en-us/web/javascript/reference/template_literals/index.md" title="This will take you to GitHub, where you&#x27;ll need to sign in first." target="_blank" rel="noopener noreferrer">Edit the page on GitHub</a>.</li><li><a href="https://github.com/mdn/content/issues/new?template=page-report.yml&amp;mdn-url=https%3A%2F%2Fdeveloper.mozilla.org%2Fen-US%2Fdocs%2FWeb%2FJavaScript%2FReference%2FTemplate_literals&amp;metadata=%3C%21--+Do+not+make+changes+below+this+line+--%3E%0A%3Cdetails%3E%0A%3Csummary%3EPage+report+details%3C%2Fsummary%3E%0A%0A*+Folder%3A+%60en-us%2Fweb%2Fjavascript%2Freference%2Ftemplate_literals%60%0A*+MDN+URL%3A+https%3A%2F%2Fdeveloper.mozilla.org%2Fen-US%2Fdocs%2FWeb%2FJavaScript%2FReference%2FTemplate_literals%0A*+GitHub+URL%3A+https%3A%2F%2Fgithub.com%2Fmdn%2Fcontent%2Fblob%2Fmain%2Ffiles%2Fen-us%2Fweb%2Fjavascript%2Freference%2Ftemplate_literals%2Findex.md%0A*+Last+commit%3A+https%3A%2F%2Fgithub.com%2Fmdn%2Fcontent%2Fcommit%2F0922a6ed8c2c4d365d1952a0cd3ddc5f79b61a3a%0A*+Document+last+modified%3A+2023-12-03T00%3A03%3A52.000Z%0A%0A%3C%2Fdetails%3E" title="This will take you to GitHub to file a new issue." target="_blank" rel="noopener noreferrer">Report the content issue</a>.</li><li><a href="https://github.com/mdn/content/blob/main/files/en-us/web/javascript/reference/template_literals/index.md?plain=1" title="Folder: en-us/web/javascript/reference/template_literals (Opens in a new tab)" target="_blank" rel="noopener noreferrer">View the source on GitHub</a>.</li></ul>Want to get more involved?<!-- --> <a href="https://github.com/mdn/content/blob/main/CONTRIBUTING.md" title="This will take you to our contribution guidelines on GitHub." target="_blank" rel="noopener noreferrer">Learn how to contribute</a>.</div><p class="last-modified-date">This page was last modified on<!-- --> <time dateTime="2023-12-03T00:03:52.000Z">Dec 3, 2023</time> by<!-- --> <a href="/en-US/docs/Web/JavaScript/Reference/Template_literals/contributors.txt">MDN contributors</a>.</p></div></aside></article></main></div></div><footer id="nav-footer" class="page-footer"><div class="page-footer-grid"><div class="page-footer-logo-col"><a href="/" class="mdn-footer-logo" aria-label="MDN homepage"><svg width="48" height="17" viewBox="0 0 48 17" fill="none" xmlns="http://www.w3.org/2000/svg"><title id="mdn-footer-logo-svg">MDN logo</title><path d="M20.04 16.512H15.504V10.416C15.504 9.488 15.344 8.824 15.024 8.424C14.72 8.024 14.264 7.824 13.656 7.824C12.92 7.824 12.384 8.064 12.048 8.544C11.728 9.024 11.568 9.64 11.568 10.392V14.184H13.008V16.512H8.472V10.416C8.472 9.488 8.312 8.824 7.992 8.424C7.688 8.024 7.232 7.824 6.624 7.824C5.872 7.824 5.336 8.064 5.016 8.544C4.696 9.024 4.536 9.64 4.536 10.392V14.184H6.6V16.512H0V14.184H1.44V8.04H0.024V5.688H4.536V7.32C5.224 6.088 6.32 5.472 7.824 5.472C8.608 5.472 9.328 5.664 9.984 6.048C10.64 6.432 11.096 7.016 11.352 7.8C11.992 6.248 13.168 5.472 14.88 5.472C15.856 5.472 16.72 5.776 17.472 6.384C18.224 6.992 18.6 7.936 18.6 9.216V14.184H20.04V16.512Z" fill="currentColor"></path><path d="M33.6714 16.512H29.1354V14.496C28.8314 15.12 28.3834 15.656 27.7914 16.104C27.1994 16.536 26.4154 16.752 25.4394 16.752C24.0154 16.752 22.8954 16.264 22.0794 15.288C21.2634 14.312 20.8554 12.984 20.8554 11.304C20.8554 9.688 21.2554 8.312 22.0554 7.176C22.8554 6.04 24.0634 5.472 25.6794 5.472C26.5594 5.472 27.2794 5.648 27.8394 6C28.3994 6.352 28.8314 6.8 29.1354 7.344V2.352H26.9754V0H32.2314V14.184H33.6714V16.512ZM29.1354 11.04V10.776C29.1354 9.88 28.8954 9.184 28.4154 8.688C27.9514 8.176 27.3674 7.92 26.6634 7.92C25.9754 7.92 25.3674 8.176 24.8394 8.688C24.3274 9.2 24.0714 10.008 24.0714 11.112C24.0714 12.152 24.3114 12.944 24.7914 13.488C25.2714 14.032 25.8394 14.304 26.4954 14.304C27.3114 14.304 27.9514 13.96 28.4154 13.272C28.8954 12.584 29.1354 11.84 29.1354 11.04Z" fill="currentColor"></path><path d="M47.9589 16.512H41.9829V14.184H43.4229V10.416C43.4229 9.488 43.2629 8.824 42.9429 8.424C42.6389 8.024 42.1829 7.824 41.5749 7.824C40.8389 7.824 40.2709 8.056 39.8709 8.52C39.4709 8.968 39.2629 9.56 39.2469 10.296V14.184H40.6869V16.512H34.7109V14.184H36.1509V8.04H34.5909V5.688H39.2469V7.344C39.9669 6.096 41.1269 5.472 42.7269 5.472C43.7509 5.472 44.6389 5.776 45.3909 6.384C46.1429 6.992 46.5189 7.936 46.5189 9.216V14.184H47.9589V16.512Z" fill="currentColor"></path></svg></a><p>Your blueprint for a better internet.</p><ul class="social-icons"><li><a class="icon icon-mastodon" href="https://mozilla.social/@mdn" target="_blank" rel="me noopener noreferrer"><span class="visually-hidden">MDN on Mastodon</span></a></li><li><a class="icon icon-twitter" href="https://twitter.com/mozdevnet" target="_blank" rel="noopener noreferrer"><span class="visually-hidden">MDN on Twitter</span></a></li><li><a class="icon icon-github-mark-small" href="https://github.com/mdn/" target="_blank" rel="noopener noreferrer"><span class="visually-hidden">MDN on GitHub</span></a></li><li><a class="icon icon-feed" href="/en-US/blog/rss.xml" target="_blank"><span class="visually-hidden">MDN Blog RSS Feed</span></a></li></ul></div><div class="page-footer-nav-col-1"><h2 class="footer-nav-heading">MDN</h2><ul class="footer-nav-list"><li class="footer-nav-item"><a href="/en-US/about">About</a></li><li class="footer-nav-item"><a href="/en-US/blog/">Blog</a></li><li class="footer-nav-item"><a href="https://www.mozilla.org/en-US/careers/listings/?team=ProdOps" target="_blank" rel="noopener noreferrer">Careers</a></li><li class="footer-nav-item"><a href="/en-US/advertising">Advertise with us</a></li></ul></div><div class="page-footer-nav-col-2"><h2 class="footer-nav-heading">Support</h2><ul class="footer-nav-list"><li class="footer-nav-item"><a class="footer-nav-link" href="https://support.mozilla.org/products/mdn-plus">Product help</a></li><li class="footer-nav-item"><a class="footer-nav-link" href="/en-US/docs/MDN/Community/Issues">Report an issue</a></li></ul></div><div class="page-footer-nav-col-3"><h2 class="footer-nav-heading">Our communities</h2><ul class="footer-nav-list"><li class="footer-nav-item"><a class="footer-nav-link" href="/en-US/community">MDN Community</a></li><li class="footer-nav-item"><a class="footer-nav-link" href="https://discourse.mozilla.org/c/mdn/236" target="_blank" rel="noopener noreferrer">MDN Forum</a></li><li class="footer-nav-item"><a class="footer-nav-link" href="/discord" target="_blank" rel="noopener noreferrer">MDN Chat</a></li></ul></div><div class="page-footer-nav-col-4"><h2 class="footer-nav-heading">Developers</h2><ul class="footer-nav-list"><li class="footer-nav-item"><a class="footer-nav-link" href="/en-US/docs/Web">Web Technologies</a></li><li class="footer-nav-item"><a class="footer-nav-link" href="/en-US/docs/Learn">Learn Web Development</a></li><li class="footer-nav-item"><a class="footer-nav-link" href="/en-US/plus">MDN Plus</a></li><li class="footer-nav-item"><a href="https://hacks.mozilla.org/" target="_blank" rel="noopener noreferrer">Hacks Blog</a></li></ul></div><div class="page-footer-moz"><a href="https://www.mozilla.org/" class="footer-moz-logo-link" target="_blank" rel="noopener noreferrer"><svg width="112" height="32" fill="none" xmlns="http://www.w3.org/2000/svg"><title id="mozilla-footer-logo-svg">Mozilla logo</title><path d="M41.753 14.218c-2.048 0-3.324 1.522-3.324 4.157 0 2.423 1.119 4.286 3.29 4.286 2.082 0 3.447-1.678 3.447-4.347 0-2.826-1.522-4.096-3.413-4.096Zm54.89 7.044c0 .901.437 1.618 1.645 1.618 1.427 0 2.949-1.024 3.044-3.352-.649-.095-1.365-.185-2.02-.185-1.426-.005-2.668.397-2.668 1.92Z" fill="currentColor"></path><path d="M0 0v32h111.908V0H0Zm32.56 25.426h-5.87v-7.884c0-2.423-.806-3.352-2.39-3.352-1.924 0-2.702 1.365-2.702 3.324v4.868h1.864v3.044h-5.864v-7.884c0-2.423-.806-3.352-2.39-3.352-1.924 0-2.702 1.365-2.702 3.324v4.868h2.669v3.044H6.642v-3.044h1.863v-7.918H6.642V11.42h5.864v2.11c.839-1.489 2.3-2.39 4.252-2.39 2.02 0 3.878.963 4.566 3.01.778-1.862 2.361-3.01 4.566-3.01 2.512 0 4.812 1.522 4.812 4.84v6.402h1.863v3.044h-.005Zm9.036.307c-4.314 0-7.296-2.635-7.296-7.106 0-4.096 2.484-7.481 7.514-7.481s7.481 3.38 7.481 7.29c0 4.472-3.228 7.297-7.699 7.297Zm22.578-.307H51.942l-.403-2.11 7.7-8.846h-4.376l-.621 2.17-2.888-.313.498-4.907h12.294l.313 2.11-7.767 8.852h4.533l.654-2.172 3.167.308-.872 4.908Zm7.99 0h-4.191v-5.03h4.19v5.03Zm0-8.976h-4.191v-5.03h4.19v5.03Zm2.618 8.976 6.054-21.358h3.945l-6.054 21.358h-3.945Zm8.136 0 6.048-21.358h3.945l-6.054 21.358h-3.939Zm21.486.307c-1.863 0-2.887-1.085-3.072-2.792-.805 1.427-2.232 2.792-4.498 2.792-2.02 0-4.314-1.085-4.314-4.006 0-3.447 3.323-4.253 6.518-4.253.778 0 1.584.034 2.3.124v-.465c0-1.427-.034-3.133-2.3-3.133-.84 0-1.488.061-2.143.402l-.453 1.578-3.195-.34.549-3.224c2.45-.996 3.692-1.27 5.992-1.27 3.01 0 5.556 1.55 5.556 4.75v6.083c0 .805.314 1.085.963 1.085.184 0 .375-.034.587-.095l.034 2.11a5.432 5.432 0 0 1-2.524.654Z" fill="currentColor"></path></svg></a><ul class="footer-moz-list"><li class="footer-moz-item"><a href="https://www.mozilla.org/privacy/websites/" class="footer-moz-link" target="_blank" rel="noopener noreferrer">Website Privacy Notice</a></li><li class="footer-moz-item"><a href="https://www.mozilla.org/privacy/websites/#cookies" class="footer-moz-link" target="_blank" rel="noopener noreferrer">Cookies</a></li><li class="footer-moz-item"><a href="https://www.mozilla.org/about/legal/terms/mozilla" class="footer-moz-link" target="_blank" rel="noopener noreferrer">Legal</a></li><li class="footer-moz-item"><a href="https://www.mozilla.org/about/governance/policies/participation/" class="footer-moz-link" target="_blank" rel="noopener noreferrer">Community Participation Guidelines</a></li></ul></div><div class="page-footer-legal"><p id="license" class="page-footer-legal-text">Visit<!-- --> <a href="https://www.mozilla.org" target="_blank" rel="noopener noreferrer">Mozilla Corporation’s</a> <!-- -->not-for-profit parent, the<!-- --> <a target="_blank" rel="noopener noreferrer" href="https://foundation.mozilla.org/">Mozilla Foundation</a>.<br/>Portions of this content are ©1998–<!-- -->2024<!-- --> by individual mozilla.org contributors. Content available under<!-- --> <a href="/en-US/docs/MDN/Writing_guidelines/Attrib_copyright_license">a Creative Commons license</a>.</p></div></div></footer></div><script type="application/json" id="hydration">{"doc":{"isMarkdown":true,"isTranslated":false,"isActive":true,"flaws":{},"title":"Template literals (Template strings)","mdn_url":"/en-US/docs/Web/JavaScript/Reference/Template_literals","locale":"en-US","native":"English (US)","browserCompat":["javascript.grammar.template_literals"],"sidebarHTML":"\n <ol>\n  <li><a href=\"/en-US/docs/Web/JavaScript\"><strong>JavaScript</strong></a></li>\n  <li><a href=\"/en-US/docs/Web/JavaScript/Tutorials\"><strong>Tutorials</strong></a></li>\n  <li class=\"toggle\">\n    <details>\n      <summary>Complete beginners</summary>\n      <ol>\n        <li><a href=\"/en-US/docs/Learn/Getting_started_with_the_web/JavaScript_basics\">JavaScript basics</a></li>\n        <li><a href=\"/en-US/docs/Learn/JavaScript/First_steps\">JavaScript first steps</a></li>\n        <li><a href=\"/en-US/docs/Learn/JavaScript/Building_blocks\">JavaScript building blocks</a></li>\n        <li><a href=\"/en-US/docs/Learn/JavaScript/Objects\">Introducing JavaScript objects</a></li>\n      </ol>\n    </details>\n  </li>\n  <li class=\"toggle\">\n    <details>\n      <summary>JavaScript Guide</summary>\n      <ol>\n          <li><a href=\"/en-US/docs/Web/JavaScript/Guide/Introduction\">Introduction</a></li>\n          <li><a href=\"/en-US/docs/Web/JavaScript/Guide/Grammar_and_types\">Grammar and types</a></li>\n          <li><a href=\"/en-US/docs/Web/JavaScript/Guide/Control_flow_and_error_handling\">Control flow and error handling</a></li>\n          <li><a href=\"/en-US/docs/Web/JavaScript/Guide/Loops_and_iteration\">Loops and iteration</a></li>\n          <li><a href=\"/en-US/docs/Web/JavaScript/Guide/Functions\">Functions</a></li>\n          <li><a href=\"/en-US/docs/Web/JavaScript/Guide/Expressions_and_operators\">Expressions and operators</a></li>\n          <li><a href=\"/en-US/docs/Web/JavaScript/Guide/Numbers_and_dates\">Numbers and dates</a></li>\n          <li><a href=\"/en-US/docs/Web/JavaScript/Guide/Text_formatting\">Text formatting</a></li>\n          <li><a href=\"/en-US/docs/Web/JavaScript/Guide/Regular_expressions\">Regular expressions</a></li>\n          <li><a href=\"/en-US/docs/Web/JavaScript/Guide/Indexed_collections\">Indexed collections</a></li>\n          <li><a href=\"/en-US/docs/Web/JavaScript/Guide/Keyed_collections\">Keyed collections</a></li>\n          <li><a href=\"/en-US/docs/Web/JavaScript/Guide/Working_with_objects\">Working with objects</a></li>\n          <li><a href=\"/en-US/docs/Web/JavaScript/Guide/Using_classes\">Using classes</a></li>\n          <li><a href=\"/en-US/docs/Web/JavaScript/Guide/Using_promises\">Using promises</a></li>\n          <li><a href=\"/en-US/docs/Web/JavaScript/Guide/Typed_arrays\">JavaScript typed arrays</a></li>\n          <li><a href=\"/en-US/docs/Web/JavaScript/Guide/Iterators_and_generators\">Iterators and generators</a></li>\n          <li><a href=\"/en-US/docs/Web/JavaScript/Guide/Meta_programming\">Meta programming</a></li>\n          <li><a href=\"/en-US/docs/Web/JavaScript/Guide/Modules\">JavaScript modules</a></li>\n        </ol>\n    </details>\n  </li>\n  <li class=\"toggle\">\n    <details>\n      <summary>Intermediate</summary>\n      <ol>\n        <li><a href=\"/en-US/docs/Learn/Tools_and_testing/Client-side_JavaScript_frameworks\">Client-side JavaScript frameworks</a></li>\n        <li><a href=\"/en-US/docs/Learn/JavaScript/Client-side_web_APIs\">Client-side web APIs</a></li>\n        <li><a href=\"/en-US/docs/Web/JavaScript/Language_overview\">Language overview</a></li>\n        <li><a href=\"/en-US/docs/Web/JavaScript/Data_structures\">JavaScript data structures</a></li>\n        <li><a href=\"/en-US/docs/Web/JavaScript/Equality_comparisons_and_sameness\">Equality comparisons and sameness</a></li>\n        <li><a href=\"/en-US/docs/Web/JavaScript/Enumerability_and_ownership_of_properties\">Enumerability and ownership of properties</a></li>\n        <li><a href=\"/en-US/docs/Web/JavaScript/Closures\">Closures</a></li>\n      </ol>\n    </details>\n  </li>\n  <li class=\"toggle\">\n    <details>\n      <summary>Advanced</summary>\n      <ol>\n        <li><a href=\"/en-US/docs/Web/JavaScript/Inheritance_and_the_prototype_chain\">Inheritance and the prototype chain</a></li>\n        <li><a href=\"/en-US/docs/Web/JavaScript/Memory_management\">Memory Management</a></li>\n        <li><a href=\"/en-US/docs/Web/JavaScript/Event_loop\">Concurrency model and Event Loop</a></li>\n      </ol>\n    </details>\n  </li>\n  <li><strong><a href=\"/en-US/docs/Web/JavaScript/Reference\">References</a></strong></li>\n  <li class=\"toggle\">\n    <details>\n       <summary>Built-in objects</summary>\n        <ol><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects\">Overview</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/AggregateError\">AggregateError</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array\">Array</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/ArrayBuffer\">ArrayBuffer</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/AsyncFunction\">AsyncFunction</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/AsyncGenerator\">AsyncGenerator</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/AsyncGeneratorFunction\">AsyncGeneratorFunction</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/AsyncIterator\">AsyncIterator</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Atomics\">Atomics</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/BigInt\">BigInt</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/BigInt64Array\">BigInt64Array</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/BigUint64Array\">BigUint64Array</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean\">Boolean</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/DataView\">DataView</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date\">Date</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/decodeURI\">decodeURI()</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/decodeURIComponent\">decodeURIComponent()</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/encodeURI\">encodeURI()</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/encodeURIComponent\">encodeURIComponent()</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error\">Error</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/escape\">escape()</a><abbr class=\"icon icon-deprecated\" title=\"Deprecated. Not for use in new websites.\">\n  <span class=\"visually-hidden\">Deprecated</span>\n</abbr></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/eval\">eval()</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/EvalError\">EvalError</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/FinalizationRegistry\">FinalizationRegistry</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Float32Array\">Float32Array</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Float64Array\">Float64Array</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function\">Function</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Generator\">Generator</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/GeneratorFunction\">GeneratorFunction</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/globalThis\">globalThis</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Infinity\">Infinity</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Int16Array\">Int16Array</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Int32Array\">Int32Array</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Int8Array\">Int8Array</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/InternalError\">InternalError</a><abbr class=\"icon icon-nonstandard\" title=\"Non-standard. Check cross-browser support before using.\">\n    <span class=\"visually-hidden\">Non-standard</span>\n</abbr></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl\">Intl</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/isFinite\">isFinite()</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/isNaN\">isNaN()</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Iterator\">Iterator</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON\">JSON</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Map\">Map</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Math\">Math</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/NaN\">NaN</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number\">Number</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object\">Object</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/parseFloat\">parseFloat()</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/parseInt\">parseInt()</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise\">Promise</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Proxy\">Proxy</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/RangeError\">RangeError</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/ReferenceError\">ReferenceError</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Reflect\">Reflect</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/RegExp\">RegExp</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Set\">Set</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/SharedArrayBuffer\">SharedArrayBuffer</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/String\">String</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Symbol\">Symbol</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/SyntaxError\">SyntaxError</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/TypedArray\">TypedArray</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/TypeError\">TypeError</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Uint16Array\">Uint16Array</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Uint32Array\">Uint32Array</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Uint8Array\">Uint8Array</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Uint8ClampedArray\">Uint8ClampedArray</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/undefined\">undefined</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/unescape\">unescape()</a><abbr class=\"icon icon-deprecated\" title=\"Deprecated. Not for use in new websites.\">\n  <span class=\"visually-hidden\">Deprecated</span>\n</abbr></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/URIError\">URIError</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/WeakMap\">WeakMap</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/WeakRef\">WeakRef</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/WeakSet\">WeakSet</a></li></ol>\n    </details>\n  </li>\n  <li class=\"toggle\">\n    <details>\n      <summary>Expressions &amp; operators</summary>\n      <ol><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators\">Overview</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Addition\">Addition (+)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Addition_assignment\">Addition assignment (+=)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Assignment\">Assignment (=)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/async_function\">async function expression</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/async_function*\">async function* expression</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/await\">await</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Bitwise_AND\">Bitwise AND (&amp;)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Bitwise_AND_assignment\">Bitwise AND assignment (&amp;=)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Bitwise_NOT\">Bitwise NOT (~)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Bitwise_OR\">Bitwise OR (|)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Bitwise_OR_assignment\">Bitwise OR assignment (|=)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Bitwise_XOR\">Bitwise XOR (^)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Bitwise_XOR_assignment\">Bitwise XOR assignment (^=)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/class\">class expression</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Comma_operator\">Comma operator (,)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Conditional_operator\">Conditional (ternary) operator</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Decrement\">Decrement (--)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/delete\">delete</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Destructuring_assignment\">Destructuring assignment</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Division\">Division (/)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Division_assignment\">Division assignment (/=)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Equality\">Equality (==)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Exponentiation\">Exponentiation (**)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Exponentiation_assignment\">Exponentiation assignment (**=)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/function\">function expression</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/function*\">function* expression</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Greater_than\">Greater than (&gt;)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Greater_than_or_equal\">Greater than or equal (&gt;=)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Grouping\">Grouping operator ( )</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/import.meta\">import.meta</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/import\">import()</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/in\">in</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Increment\">Increment (++)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Inequality\">Inequality (!=)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/instanceof\">instanceof</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Left_shift\">Left shift (&lt;&lt;)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Left_shift_assignment\">Left shift assignment (&lt;&lt;=)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Less_than\">Less than (&lt;)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Less_than_or_equal\">Less than or equal (&lt;=)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Logical_AND\">Logical AND (&amp;&amp;)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Logical_AND_assignment\">Logical AND assignment (&amp;&amp;=)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Logical_NOT\">Logical NOT (!)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Logical_OR\">Logical OR (||)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Logical_OR_assignment\">Logical OR assignment (||=)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Multiplication\">Multiplication (*)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Multiplication_assignment\">Multiplication assignment (*=)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/new\">new</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/new.target\">new.target</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/null\">null</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Nullish_coalescing_assignment\">Nullish coalescing assignment (??=)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Nullish_coalescing\">Nullish coalescing operator (??)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Object_initializer\">Object initializer</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Operator_precedence\">Operator precedence</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Optional_chaining\">Optional chaining (?.)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Property_accessors\">Property accessors</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Remainder\">Remainder (%)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Remainder_assignment\">Remainder assignment (%=)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Right_shift\">Right shift (&gt;&gt;)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Right_shift_assignment\">Right shift assignment (&gt;&gt;=)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Spread_syntax\">Spread syntax (...)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Strict_equality\">Strict equality (===)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Strict_inequality\">Strict inequality (!==)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Subtraction\">Subtraction (-)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Subtraction_assignment\">Subtraction assignment (-=)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/super\">super</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/this\">this</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/typeof\">typeof</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Unary_negation\">Unary negation (-)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Unary_plus\">Unary plus (+)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Unsigned_right_shift\">Unsigned right shift (&gt;&gt;&gt;)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Unsigned_right_shift_assignment\">Unsigned right shift assignment (&gt;&gt;&gt;=)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/void\">void operator</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/yield\">yield</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/yield*\">yield*</a></li></ol>\n    </details>\n  </li>\n  <li class=\"toggle\">\n    <details>\n      <summary>Statements &amp; declarations</summary>\n      <ol><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Statements\">Overview</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Statements/async_function\">async function</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Statements/async_function*\">async function*</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Statements/block\">Block statement</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Statements/break\">break</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Statements/class\">class</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Statements/const\">const</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Statements/continue\">continue</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Statements/debugger\">debugger</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Statements/do...while\">do...while</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Statements/Empty\">Empty statement</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Statements/export\">export</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Statements/Expression_statement\">Expression statement</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Statements/for\">for</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Statements/for-await...of\">for await...of</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Statements/for...in\">for...in</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Statements/for...of\">for...of</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Statements/function\">function</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Statements/function*\">function*</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Statements/if...else\">if...else</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Statements/import\">import</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Statements/label\">Labeled statement</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Statements/let\">let</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Statements/return\">return</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Statements/switch\">switch</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Statements/throw\">throw</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Statements/try...catch\">try...catch</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Statements/var\">var</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Statements/while\">while</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Statements/with\">with</a><abbr class=\"icon icon-deprecated\" title=\"Deprecated. Not for use in new websites.\">\n  <span class=\"visually-hidden\">Deprecated</span>\n</abbr></li></ol>\n    </details>\n  </li>\n  <li class=\"toggle\">\n    <details>\n      <summary>Functions</summary>\n      <ol><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Functions\">Overview</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Functions/Arrow_functions\">Arrow function expressions</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Functions/Default_parameters\">Default parameters</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Functions/get\">get</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Functions/Method_definitions\">Method definitions</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Functions/rest_parameters\">Rest parameters</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Functions/set\">set</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Functions/arguments\">The arguments object</a></li></ol>\n    </details>\n  </li>\n  <li class=\"toggle\">\n    <details>\n      <summary>Classes</summary>\n      <ol><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Classes\">Overview</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Classes/constructor\">constructor</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Classes/extends\">extends</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Classes/Private_properties\">Private properties</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Classes/Public_class_fields\">Public class fields</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Classes/static\">static</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Classes/Static_initialization_blocks\">Static initialization blocks</a></li></ol>\n    </details>\n  </li>\n  <li class=\"toggle\">\n    <details>\n      <summary>Regular expressions</summary>\n      <ol><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Regular_expressions\">Overview</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Regular_expressions/Backreference\">Backreference: \\1, \\2</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Regular_expressions/Capturing_group\">Capturing group: (...)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Regular_expressions/Character_class_escape\">Character class escape: \\d, \\D, \\w, \\W, \\s, \\S</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Regular_expressions/Character_class\">Character class: [...], [^...]</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Regular_expressions/Character_escape\">Character escape: \\n, \\u{...}</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Regular_expressions/Disjunction\">Disjunction: |</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Regular_expressions/Input_boundary_assertion\">Input boundary assertion: ^, $</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Regular_expressions/Literal_character\">Literal character: a, b</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Regular_expressions/Lookahead_assertion\">Lookahead assertion: (?=...), (?!...)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Regular_expressions/Lookbehind_assertion\">Lookbehind assertion: (?&lt;=...), (?&lt;!...)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Regular_expressions/Named_backreference\">Named backreference: \\k&lt;name&gt;</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Regular_expressions/Named_capturing_group\">Named capturing group: (?&lt;name&gt;...)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Regular_expressions/Non-capturing_group\">Non-capturing group: (?:...)</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Regular_expressions/Quantifier\">Quantifier: *, +, ?, {n}, {n,}, {n,m}</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Regular_expressions/Unicode_character_class_escape\">Unicode character class escape: \\p{...}, \\P{...}</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Regular_expressions/Wildcard\">Wildcard: .</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Regular_expressions/Word_boundary_assertion\">Word boundary assertion: \\b, \\B</a></li></ol>\n    </details>\n  </li>\n  <li class=\"toggle\">\n    <details>\n      <summary>Errors</summary>\n      <ol><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors\">Overview</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Property_access_denied\">Error: Permission denied to access property \"x\"</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Too_much_recursion\">InternalError: too much recursion</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Not_a_valid_code_point\">RangeError: argument is not a valid code point</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/BigInt_division_by_zero\">RangeError: BigInt division by zero</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/BigInt_negative_exponent\">RangeError: BigInt negative exponent</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Invalid_array_length\">RangeError: invalid array length</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Invalid_date\">RangeError: invalid date</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Precision_range\">RangeError: precision is out of range</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Bad_radix\">RangeError: radix must be an integer</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Resulting_string_too_large\">RangeError: repeat count must be less than infinity</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Negative_repetition_count\">RangeError: repeat count must be non-negative</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Cant_be_converted_to_BigInt_because_it_isnt_an_integer\">RangeError: x can't be converted to BigInt because it isn't an integer</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Not_defined\">ReferenceError: \"x\" is not defined</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Undeclared_var\">ReferenceError: assignment to undeclared variable \"x\"</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Cant_access_lexical_declaration_before_init\">ReferenceError: can't access lexical declaration 'X' before initialization</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Deprecated_caller_or_arguments_usage\">ReferenceError: deprecated caller or arguments usage</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Undefined_prop\">ReferenceError: reference to undefined property \"x\"</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Deprecated_octal\">SyntaxError: \"0\"-prefixed octal literals and octal escape seq. are deprecated</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Strict_non_simple_params\">SyntaxError: \"use strict\" not allowed in function with non-simple parameters</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Reserved_identifier\">SyntaxError: \"x\" is a reserved identifier</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Invalid_for-of_initializer\">SyntaxError: a declaration in the head of a for-of loop can't have an initializer</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Delete_in_strict_mode\">SyntaxError: applying the 'delete' operator to an unqualified name is deprecated</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Bad_await\">SyntaxError: await is only valid in async functions, async generators and modules</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Cant_use_nullish_coalescing_unparenthesized\">SyntaxError: cannot use `??` unparenthesized within `||` and `&amp;&amp;` expressions</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Bad_continue\">SyntaxError: continue must be inside loop</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Invalid_for-in_initializer\">SyntaxError: for-in loop head declarations may not have initializers</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Unnamed_function_statement\">SyntaxError: function statement requires a name</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Either_be_both_static_or_non-static\">SyntaxError: getter and setter for private name #x should either be both static or non-static</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Identifier_after_number\">SyntaxError: identifier starts immediately after numeric literal</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Illegal_character\">SyntaxError: illegal character</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Invalid_assignment_left-hand_side\">SyntaxError: invalid assignment left-hand side</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Invalid_BigInt_syntax\">SyntaxError: invalid BigInt syntax</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Bad_regexp_flag\">SyntaxError: invalid regular expression flag \"x\"</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/JSON_bad_parse\">SyntaxError: JSON.parse: bad parsing</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Label_not_found\">SyntaxError: label not found</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Missing_semicolon_before_statement\">SyntaxError: missing ; before statement</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Missing_colon_after_property_id\">SyntaxError: missing : after property id</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Missing_parenthesis_after_argument_list\">SyntaxError: missing ) after argument list</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Missing_parenthesis_after_condition\">SyntaxError: missing ) after condition</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Missing_bracket_after_list\">SyntaxError: missing ] after element list</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Missing_curly_after_function_body\">SyntaxError: missing } after function body</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Missing_curly_after_property_list\">SyntaxError: missing } after property list</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Missing_initializer_in_const\">SyntaxError: missing = in const declaration</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Missing_formal_parameter\">SyntaxError: missing formal parameter</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Missing_name_after_dot_operator\">SyntaxError: missing name after . operator</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/No_variable_name\">SyntaxError: missing variable name</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Redeclared_parameter\">SyntaxError: redeclaration of formal parameter \"x\"</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Bad_return\">SyntaxError: return not in function</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Equal_as_assign\">SyntaxError: test for equality (==) mistyped as assignment (=)?</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Hash_outside_class\">SyntaxError: Unexpected '#' used outside of class body</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Unexpected_token\">SyntaxError: Unexpected token</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Bad_break\">SyntaxError: unlabeled break must be inside loop or switch</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Unparenthesized_unary_expr_lhs_exponentiation\">SyntaxError: unparenthesized unary expression can't appear on the left-hand side of '**'</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Unterminated_string_literal\">SyntaxError: unterminated string literal</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Deprecated_source_map_pragma\">SyntaxError: Using //@ to indicate sourceURL pragmas is deprecated. Use //# instead</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/is_not_iterable\">TypeError: 'x' is not iterable</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/No_properties\">TypeError: \"x\" has no properties</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Unexpected_type\">TypeError: \"x\" is (not) \"y\"</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Not_a_constructor\">TypeError: \"x\" is not a constructor</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Not_a_function\">TypeError: \"x\" is not a function</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/No_non-null_object\">TypeError: \"x\" is not a non-null object</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Read-only\">TypeError: \"x\" is read-only</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Cant_assign_to_property\">TypeError: can't assign to property \"x\" on \"y\": not an object</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Cant_convert_BigInt_to_number\">TypeError: can't convert BigInt to number</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Cant_convert_x_to_BigInt\">TypeError: can't convert x to BigInt</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Cant_define_property_object_not_extensible\">TypeError: can't define property \"x\": \"obj\" is not extensible</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Non_configurable_array_element\">TypeError: can't delete non-configurable array element</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Cant_redefine_property\">TypeError: can't redefine non-configurable property \"x\"</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/in_operator_no_object\">TypeError: cannot use 'in' operator to search for 'x' in 'y'</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Cyclic_object_value\">TypeError: cyclic object value</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/invalid_right_hand_side_instanceof_operand\">TypeError: invalid 'instanceof' operand 'x'</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Array_sort_argument\">TypeError: invalid Array.prototype.sort argument</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Invalid_const_assignment\">TypeError: invalid assignment to const \"x\"</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/More_arguments_needed\">TypeError: More arguments needed</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Cant_delete\">TypeError: property \"x\" is non-configurable and can't be deleted</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Reduce_of_empty_array_with_no_initial_value\">TypeError: Reduce of empty array with no initial value</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Getter_only\">TypeError: setting getter-only property \"x\"</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Called_on_incompatible_type\">TypeError: X.prototype.y called on incompatible type</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Malformed_URI\">URIError: malformed URI sequence</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Already_has_pragma\">Warning: -file- is being assigned a //# sourceMappingURL, but already has one</a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Errors/Stmt_after_return\">Warning: unreachable code after return statement</a></li></ol>\n    </details>\n  </li>\n  <li class=\"toggle\">\n    <details open=\"\">\n      <summary>Misc</summary>\n      <ol>\n        <li><a href=\"/en-US/docs/Web/JavaScript/JavaScript_technologies_overview\">JavaScript technologies overview</a></li>\n        <li><a href=\"/en-US/docs/Web/JavaScript/Reference/Lexical_grammar\">Lexical grammar</a></li>\n        <li><a href=\"/en-US/docs/Web/JavaScript/Reference/Iteration_protocols\">Iteration protocols</a></li>\n        <li><a href=\"/en-US/docs/Web/JavaScript/Reference/Strict_mode\">Strict mode</a></li>\n        <li><em><a href=\"/en-US/docs/Web/JavaScript/Reference/Template_literals\" aria-current=\"page\">Template literals</a></em></li>\n        <li><a href=\"/en-US/docs/Web/JavaScript/Reference/Trailing_commas\">Trailing commas</a></li>\n        <li><a href=\"/en-US/docs/Web/JavaScript/Reference/Deprecated_and_obsolete_features\">Deprecated features</a></li>\n      </ol>\n    </details>\n  </li>\n </ol>\n","sidebarMacro":"JsSidebar","body":[{"type":"prose","value":{"id":null,"title":null,"isH3":false,"content":"<p><strong>Template literals</strong> are literals delimited with backtick (<code>`</code>) characters, allowing for <a href=\"#multi-line_strings\">multi-line strings</a>, <a href=\"#string_interpolation\">string interpolation</a> with embedded expressions, and special constructs called <a href=\"#tagged_templates\">tagged templates</a>.</p>\n<p>Template literals are sometimes informally called <em>template strings</em>, because they are used most commonly for <a href=\"#string_interpolation\">string interpolation</a> (to create strings by doing substitution of placeholders). However, a tagged template literal may not result in a string; it can be used with a custom <a href=\"#tagged_templates\">tag function</a> to perform whatever operations you want on the different parts of the template literal.</p>"}},{"type":"prose","value":{"id":"syntax","title":"Syntax","isH3":false,"content":"<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">js</span></div><pre class=\"brush: js notranslate\" data-signature=\"POt0oT1wqiF1sZUcuWLr1d+VvVei8hg3WH0cXOunTrk=\"><code><span class=\"token template-string\"><span class=\"token template-punctuation string\">`</span><span class=\"token string\">string text</span><span class=\"token template-punctuation string\">`</span></span>\n\n<span class=\"token template-string\"><span class=\"token template-punctuation string\">`</span><span class=\"token string\">string text line 1\n string text line 2</span><span class=\"token template-punctuation string\">`</span></span>\n\n<span class=\"token template-string\"><span class=\"token template-punctuation string\">`</span><span class=\"token string\">string text </span><span class=\"token interpolation\"><span class=\"token interpolation-punctuation punctuation\">${</span>expression<span class=\"token interpolation-punctuation punctuation\">}</span></span><span class=\"token string\"> string text</span><span class=\"token template-punctuation string\">`</span></span>\n\ntagFunction<span class=\"token template-string\"><span class=\"token template-punctuation string\">`</span><span class=\"token string\">string text </span><span class=\"token interpolation\"><span class=\"token interpolation-punctuation punctuation\">${</span>expression<span class=\"token interpolation-punctuation punctuation\">}</span></span><span class=\"token string\"> string text</span><span class=\"token template-punctuation string\">`</span></span>\n</code></pre></div>"}},{"type":"prose","value":{"id":"parameters","title":"Parameters","isH3":true,"content":"<dl>\n  <dt id=\"string_text\"><a href=\"#string_text\"><code>string text</code></a></dt>\n  <dd>\n    <p>The string text that will become part of the template literal. Almost all characters are allowed literally, including <a href=\"/en-US/docs/Web/JavaScript/Reference/Lexical_grammar#line_terminators\">line breaks</a> and other <a href=\"/en-US/docs/Web/JavaScript/Reference/Lexical_grammar#white_space\">whitespace characters</a>. However, invalid escape sequences will cause a syntax error, unless a <a href=\"#tagged_templates_and_escape_sequences\">tag function</a> is used.</p>\n  </dd>\n  <dt id=\"expression\"><a href=\"#expression\"><code>expression</code></a></dt>\n  <dd>\n    <p>An expression to be inserted in the current position, whose value is converted to a string or passed to <code>tagFunction</code>.</p>\n  </dd>\n  <dt id=\"tagfunction\"><a href=\"#tagfunction\"><code>tagFunction</code></a></dt>\n  <dd>\n    <p>If specified, it will be called with the template strings array and substitution expressions, and the return value becomes the value of the template literal. See <a href=\"#tagged_templates\">tagged templates</a>.</p>\n  </dd>\n</dl>"}},{"type":"prose","value":{"id":"description","title":"Description","isH3":false,"content":"<p>Template literals are enclosed by backtick (<code>`</code>) characters instead of double or single quotes.</p>\n<p>Along with having normal strings, template literals can also contain other parts called <em>placeholders</em>, which are embedded expressions delimited by a dollar sign and curly braces: <code>${expression}</code>. The strings and placeholders get passed to a function — either a default function, or a function you supply. The default function (when you don't supply your own) just performs <a href=\"#string_interpolation\">string interpolation</a> to do substitution of the placeholders and then concatenate the parts into a single string.</p>\n<p>To supply a function of your own, precede the template literal with a function name; the result is called a <a href=\"#tagged_templates\"><strong>tagged template</strong></a>. In that case, the template literal is passed to your tag function, where you can then perform whatever operations you want on the different parts of the template literal.</p>\n<p>To escape a backtick in a template literal, put a backslash (<code>\\</code>) before the backtick.</p>\n<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">js</span></div><pre class=\"brush: js notranslate\" data-signature=\"/vzBpScYQ01/11UvRE6qmWGitZYE0i7M5sSdE0zn98M=\"><code><span class=\"token template-string\"><span class=\"token template-punctuation string\">`</span><span class=\"token string\">\\`</span><span class=\"token template-punctuation string\">`</span></span> <span class=\"token operator\">===</span> <span class=\"token string\">\"`\"</span><span class=\"token punctuation\">;</span> <span class=\"token comment\">// true</span>\n</code></pre></div>\n<p>Dollar signs can be escaped as well to prevent interpolation.</p>\n<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">js</span></div><pre class=\"brush: js notranslate\" data-signature=\"dnT3NDUaIR0TlrDwH5bCgCdUf8m8Gnl3bBrq9/mcqsw=\"><code><span class=\"token template-string\"><span class=\"token template-punctuation string\">`</span><span class=\"token string\">\\${1}</span><span class=\"token template-punctuation string\">`</span></span> <span class=\"token operator\">===</span> <span class=\"token string\">\"${1}\"</span><span class=\"token punctuation\">;</span> <span class=\"token comment\">// true</span>\n</code></pre></div>"}},{"type":"prose","value":{"id":"multi-line_strings","title":"Multi-line strings","isH3":true,"content":"<p>Any newline characters inserted in the source are part of the template literal.</p>\n<p>Using normal strings, you would have to use the following syntax in order to get multi-line strings:</p>\n<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">js</span></div><pre class=\"brush: js notranslate\" data-signature=\"hJzfJT08wCoBDENM7tv/dGUC1aqdCUVPSjSnyVHdS7w=\"><code>console<span class=\"token punctuation\">.</span><span class=\"token function\">log</span><span class=\"token punctuation\">(</span><span class=\"token string\">\"string text line 1\\n\"</span> <span class=\"token operator\">+</span> <span class=\"token string\">\"string text line 2\"</span><span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\n<span class=\"token comment\">// \"string text line 1</span>\n<span class=\"token comment\">// string text line 2\"</span>\n</code></pre></div>\n<p>Using template literals, you can do the same with this:</p>\n<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">js</span></div><pre class=\"brush: js notranslate\" data-signature=\"0weQlKv+or/JxXSkYX3Tsdpxe4Jvqi8O5+v3H08JkOM=\"><code>console<span class=\"token punctuation\">.</span><span class=\"token function\">log</span><span class=\"token punctuation\">(</span><span class=\"token template-string\"><span class=\"token template-punctuation string\">`</span><span class=\"token string\">string text line 1\nstring text line 2</span><span class=\"token template-punctuation string\">`</span></span><span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\n<span class=\"token comment\">// \"string text line 1</span>\n<span class=\"token comment\">// string text line 2\"</span>\n</code></pre></div>"}},{"type":"prose","value":{"id":"string_interpolation","title":"String interpolation","isH3":true,"content":"<p>Without template literals, when you want to combine output from expressions with strings, you'd <a href=\"/en-US/docs/Learn/JavaScript/First_steps/Strings#concatenation_using\">concatenate them</a> using the <a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Addition\">addition operator</a> <code>+</code>:</p>\n<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">js</span></div><pre class=\"brush: js notranslate\" data-signature=\"7cwIc8Sw9EWtXtKeGqrhMhZpJ9LJzPQIak6LG1oeD2s=\"><code><span class=\"token keyword\">const</span> a <span class=\"token operator\">=</span> <span class=\"token number\">5</span><span class=\"token punctuation\">;</span>\n<span class=\"token keyword\">const</span> b <span class=\"token operator\">=</span> <span class=\"token number\">10</span><span class=\"token punctuation\">;</span>\nconsole<span class=\"token punctuation\">.</span><span class=\"token function\">log</span><span class=\"token punctuation\">(</span><span class=\"token string\">\"Fifteen is \"</span> <span class=\"token operator\">+</span> <span class=\"token punctuation\">(</span>a <span class=\"token operator\">+</span> b<span class=\"token punctuation\">)</span> <span class=\"token operator\">+</span> <span class=\"token string\">\" and\\nnot \"</span> <span class=\"token operator\">+</span> <span class=\"token punctuation\">(</span><span class=\"token number\">2</span> <span class=\"token operator\">*</span> a <span class=\"token operator\">+</span> b<span class=\"token punctuation\">)</span> <span class=\"token operator\">+</span> <span class=\"token string\">\".\"</span><span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\n<span class=\"token comment\">// \"Fifteen is 15 and</span>\n<span class=\"token comment\">// not 20.\"</span>\n</code></pre></div>\n<p>That can be hard to read – especially when you have multiple expressions.</p>\n<p>With template literals, you can avoid the concatenation operator — and improve the readability of your code — by using placeholders of the form <code>${expression}</code> to perform substitutions for embedded expressions:</p>\n<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">js</span></div><pre class=\"brush: js notranslate\" data-signature=\"e7UYB1fA2jSkh6LarUKqLqaJhoiefzvB3JCYeP38SJs=\"><code><span class=\"token keyword\">const</span> a <span class=\"token operator\">=</span> <span class=\"token number\">5</span><span class=\"token punctuation\">;</span>\n<span class=\"token keyword\">const</span> b <span class=\"token operator\">=</span> <span class=\"token number\">10</span><span class=\"token punctuation\">;</span>\nconsole<span class=\"token punctuation\">.</span><span class=\"token function\">log</span><span class=\"token punctuation\">(</span><span class=\"token template-string\"><span class=\"token template-punctuation string\">`</span><span class=\"token string\">Fifteen is </span><span class=\"token interpolation\"><span class=\"token interpolation-punctuation punctuation\">${</span>a <span class=\"token operator\">+</span> b<span class=\"token interpolation-punctuation punctuation\">}</span></span><span class=\"token string\"> and\nnot </span><span class=\"token interpolation\"><span class=\"token interpolation-punctuation punctuation\">${</span><span class=\"token number\">2</span> <span class=\"token operator\">*</span> a <span class=\"token operator\">+</span> b<span class=\"token interpolation-punctuation punctuation\">}</span></span><span class=\"token string\">.</span><span class=\"token template-punctuation string\">`</span></span><span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\n<span class=\"token comment\">// \"Fifteen is 15 and</span>\n<span class=\"token comment\">// not 20.\"</span>\n</code></pre></div>\n<p>Note that there's a mild difference between the two syntaxes. Template literals <a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/String#string_coercion\">coerce their expressions directly to strings</a>, while addition coerces its operands to primitives first. For more information, see the reference page for the <a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Addition\"><code>+</code> operator</a>.</p>"}},{"type":"prose","value":{"id":"nesting_templates","title":"Nesting templates","isH3":true,"content":"<p>In certain cases, nesting a template is the easiest (and perhaps more readable) way to have configurable strings. Within a backtick-delimited template, it is simple to allow inner backticks by using them inside an <code>${expression}</code> placeholder within the template.</p>\n<p>For example, without template literals, if you wanted to return a certain value based on a particular condition, you could do something like the following:</p>\n<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">js</span></div><pre class=\"brush: js example-bad notranslate\" data-signature=\"7Ay65lWwVeaV0IV3DMi6G527iizW2CPLyM8S/xfIqwo=\"><code><span class=\"token keyword\">let</span> classes <span class=\"token operator\">=</span> <span class=\"token string\">\"header\"</span><span class=\"token punctuation\">;</span>\nclasses <span class=\"token operator\">+=</span> <span class=\"token function\">isLargeScreen</span><span class=\"token punctuation\">(</span><span class=\"token punctuation\">)</span>\n  <span class=\"token operator\">?</span> <span class=\"token string\">\"\"</span>\n  <span class=\"token operator\">:</span> item<span class=\"token punctuation\">.</span>isCollapsed\n    <span class=\"token operator\">?</span> <span class=\"token string\">\" icon-expander\"</span>\n    <span class=\"token operator\">:</span> <span class=\"token string\">\" icon-collapser\"</span><span class=\"token punctuation\">;</span>\n</code></pre></div>\n<p>With a template literal but without nesting, you could do this:</p>\n<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">js</span></div><pre class=\"brush: js example-bad notranslate\" data-signature=\"egv7yBcsx4mSQXdkQWJFWMxtrjfEPq5NgLRaeYyQJ84=\"><code><span class=\"token keyword\">const</span> classes <span class=\"token operator\">=</span> <span class=\"token template-string\"><span class=\"token template-punctuation string\">`</span><span class=\"token string\">header </span><span class=\"token interpolation\"><span class=\"token interpolation-punctuation punctuation\">${</span>\n  <span class=\"token function\">isLargeScreen</span><span class=\"token punctuation\">(</span><span class=\"token punctuation\">)</span> <span class=\"token operator\">?</span> <span class=\"token string\">\"\"</span> <span class=\"token operator\">:</span> item<span class=\"token punctuation\">.</span>isCollapsed <span class=\"token operator\">?</span> <span class=\"token string\">\"icon-expander\"</span> <span class=\"token operator\">:</span> <span class=\"token string\">\"icon-collapser\"</span>\n<span class=\"token interpolation-punctuation punctuation\">}</span></span><span class=\"token template-punctuation string\">`</span></span><span class=\"token punctuation\">;</span>\n</code></pre></div>\n<p>With nesting of template literals, you can do this:</p>\n<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">js</span></div><pre class=\"brush: js example-good notranslate\" data-signature=\"AaQlC2hhFzFU/m8NEknSyhRVBk/ylwqZyiPSgPseae8=\"><code><span class=\"token keyword\">const</span> classes <span class=\"token operator\">=</span> <span class=\"token template-string\"><span class=\"token template-punctuation string\">`</span><span class=\"token string\">header </span><span class=\"token interpolation\"><span class=\"token interpolation-punctuation punctuation\">${</span>\n  <span class=\"token function\">isLargeScreen</span><span class=\"token punctuation\">(</span><span class=\"token punctuation\">)</span> <span class=\"token operator\">?</span> <span class=\"token string\">\"\"</span> <span class=\"token operator\">:</span> <span class=\"token template-string\"><span class=\"token template-punctuation string\">`</span><span class=\"token string\">icon-</span><span class=\"token interpolation\"><span class=\"token interpolation-punctuation punctuation\">${</span>item<span class=\"token punctuation\">.</span>isCollapsed <span class=\"token operator\">?</span> <span class=\"token string\">\"expander\"</span> <span class=\"token operator\">:</span> <span class=\"token string\">\"collapser\"</span><span class=\"token interpolation-punctuation punctuation\">}</span></span><span class=\"token template-punctuation string\">`</span></span>\n<span class=\"token interpolation-punctuation punctuation\">}</span></span><span class=\"token template-punctuation string\">`</span></span><span class=\"token punctuation\">;</span>\n</code></pre></div>"}},{"type":"prose","value":{"id":"tagged_templates","title":"Tagged templates","isH3":true,"content":"<p>A more advanced form of template literals are <em>tagged</em> templates.</p>\n<p>Tags allow you to parse template literals with a function. The first argument of a tag function contains an array of string values. The remaining arguments are related to the expressions.</p>\n<p>The tag function can then perform whatever operations on these arguments you wish, and return the manipulated string. (Alternatively, it can return something completely different, as described in one of the following examples.)</p>\n<p>The name of the function used for the tag can be whatever you want.</p>\n<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">js</span></div><pre class=\"brush: js notranslate\" data-signature=\"rJnqQ6QdUcOXtW4dxXbx3Yi+y+icFw+jA5uX8Bx9Lrk=\"><code><span class=\"token keyword\">const</span> person <span class=\"token operator\">=</span> <span class=\"token string\">\"Mike\"</span><span class=\"token punctuation\">;</span>\n<span class=\"token keyword\">const</span> age <span class=\"token operator\">=</span> <span class=\"token number\">28</span><span class=\"token punctuation\">;</span>\n\n<span class=\"token keyword\">function</span> <span class=\"token function\">myTag</span><span class=\"token punctuation\">(</span><span class=\"token parameter\">strings<span class=\"token punctuation\">,</span> personExp<span class=\"token punctuation\">,</span> ageExp</span><span class=\"token punctuation\">)</span> <span class=\"token punctuation\">{</span>\n  <span class=\"token keyword\">const</span> str0 <span class=\"token operator\">=</span> strings<span class=\"token punctuation\">[</span><span class=\"token number\">0</span><span class=\"token punctuation\">]</span><span class=\"token punctuation\">;</span> <span class=\"token comment\">// \"That \"</span>\n  <span class=\"token keyword\">const</span> str1 <span class=\"token operator\">=</span> strings<span class=\"token punctuation\">[</span><span class=\"token number\">1</span><span class=\"token punctuation\">]</span><span class=\"token punctuation\">;</span> <span class=\"token comment\">// \" is a \"</span>\n  <span class=\"token keyword\">const</span> str2 <span class=\"token operator\">=</span> strings<span class=\"token punctuation\">[</span><span class=\"token number\">2</span><span class=\"token punctuation\">]</span><span class=\"token punctuation\">;</span> <span class=\"token comment\">// \".\"</span>\n\n  <span class=\"token keyword\">const</span> ageStr <span class=\"token operator\">=</span> ageExp <span class=\"token operator\">&lt;</span> <span class=\"token number\">100</span> <span class=\"token operator\">?</span> <span class=\"token string\">\"youngster\"</span> <span class=\"token operator\">:</span> <span class=\"token string\">\"centenarian\"</span><span class=\"token punctuation\">;</span>\n\n  <span class=\"token comment\">// We can even return a string built using a template literal</span>\n  <span class=\"token keyword\">return</span> <span class=\"token template-string\"><span class=\"token template-punctuation string\">`</span><span class=\"token interpolation\"><span class=\"token interpolation-punctuation punctuation\">${</span>str0<span class=\"token interpolation-punctuation punctuation\">}</span></span><span class=\"token interpolation\"><span class=\"token interpolation-punctuation punctuation\">${</span>personExp<span class=\"token interpolation-punctuation punctuation\">}</span></span><span class=\"token interpolation\"><span class=\"token interpolation-punctuation punctuation\">${</span>str1<span class=\"token interpolation-punctuation punctuation\">}</span></span><span class=\"token interpolation\"><span class=\"token interpolation-punctuation punctuation\">${</span>ageStr<span class=\"token interpolation-punctuation punctuation\">}</span></span><span class=\"token interpolation\"><span class=\"token interpolation-punctuation punctuation\">${</span>str2<span class=\"token interpolation-punctuation punctuation\">}</span></span><span class=\"token template-punctuation string\">`</span></span><span class=\"token punctuation\">;</span>\n<span class=\"token punctuation\">}</span>\n\n<span class=\"token keyword\">const</span> output <span class=\"token operator\">=</span> myTag<span class=\"token template-string\"><span class=\"token template-punctuation string\">`</span><span class=\"token string\">That </span><span class=\"token interpolation\"><span class=\"token interpolation-punctuation punctuation\">${</span>person<span class=\"token interpolation-punctuation punctuation\">}</span></span><span class=\"token string\"> is a </span><span class=\"token interpolation\"><span class=\"token interpolation-punctuation punctuation\">${</span>age<span class=\"token interpolation-punctuation punctuation\">}</span></span><span class=\"token string\">.</span><span class=\"token template-punctuation string\">`</span></span><span class=\"token punctuation\">;</span>\n\nconsole<span class=\"token punctuation\">.</span><span class=\"token function\">log</span><span class=\"token punctuation\">(</span>output<span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\n<span class=\"token comment\">// That Mike is a youngster.</span>\n</code></pre></div>\n<p>The tag does not have to be a plain identifier. You can use any expression with <a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Operator_precedence#table\">precedence</a> greater than 16, which includes <a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/Property_accessors\">property access</a>, function call, <a href=\"/en-US/docs/Web/JavaScript/Reference/Operators/new\">new expression</a>, or even another tagged template literal.</p>\n<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">js</span></div><pre class=\"brush: js notranslate\" data-signature=\"NsE2ioiknovNCQvHSn0tMd40JH50/BQgYPFvvURxwsk=\"><code>console<span class=\"token punctuation\">.</span>log<span class=\"token template-string\"><span class=\"token template-punctuation string\">`</span><span class=\"token string\">Hello</span><span class=\"token template-punctuation string\">`</span></span><span class=\"token punctuation\">;</span> <span class=\"token comment\">// [ 'Hello' ]</span>\nconsole<span class=\"token punctuation\">.</span><span class=\"token function\">log</span><span class=\"token punctuation\">.</span><span class=\"token function\">bind</span><span class=\"token punctuation\">(</span><span class=\"token number\">1</span><span class=\"token punctuation\">,</span> <span class=\"token number\">2</span><span class=\"token punctuation\">)</span><span class=\"token template-string\"><span class=\"token template-punctuation string\">`</span><span class=\"token string\">Hello</span><span class=\"token template-punctuation string\">`</span></span><span class=\"token punctuation\">;</span> <span class=\"token comment\">// 2 [ 'Hello' ]</span>\n<span class=\"token keyword\">new</span> <span class=\"token class-name\">Function</span><span class=\"token punctuation\">(</span><span class=\"token string\">\"console.log(arguments)\"</span><span class=\"token punctuation\">)</span><span class=\"token template-string\"><span class=\"token template-punctuation string\">`</span><span class=\"token string\">Hello</span><span class=\"token template-punctuation string\">`</span></span><span class=\"token punctuation\">;</span> <span class=\"token comment\">// [Arguments] { '0': [ 'Hello' ] }</span>\n\n<span class=\"token keyword\">function</span> <span class=\"token function\">recursive</span><span class=\"token punctuation\">(</span><span class=\"token parameter\">strings<span class=\"token punctuation\">,</span> <span class=\"token operator\">...</span>values</span><span class=\"token punctuation\">)</span> <span class=\"token punctuation\">{</span>\n  console<span class=\"token punctuation\">.</span><span class=\"token function\">log</span><span class=\"token punctuation\">(</span>strings<span class=\"token punctuation\">,</span> values<span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\n  <span class=\"token keyword\">return</span> recursive<span class=\"token punctuation\">;</span>\n<span class=\"token punctuation\">}</span>\nrecursive<span class=\"token template-string\"><span class=\"token template-punctuation string\">`</span><span class=\"token string\">Hello</span><span class=\"token template-punctuation string\">`</span></span><span class=\"token template-string\"><span class=\"token template-punctuation string\">`</span><span class=\"token string\">World</span><span class=\"token template-punctuation string\">`</span></span><span class=\"token punctuation\">;</span>\n<span class=\"token comment\">// [ 'Hello' ] []</span>\n<span class=\"token comment\">// [ 'World' ] []</span>\n</code></pre></div>\n<p>While technically permitted by the syntax, <em>untagged</em> template literals are strings and will throw a <a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/TypeError\"><code>TypeError</code></a> when chained.</p>\n<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">js</span></div><pre class=\"brush: js notranslate\" data-signature=\"7aSgEmaTPjIFNq4q7YlCBNjX79DoYRIpErNvjfr69i4=\"><code>console<span class=\"token punctuation\">.</span><span class=\"token function\">log</span><span class=\"token punctuation\">(</span><span class=\"token template-string\"><span class=\"token template-punctuation string\">`</span><span class=\"token string\">Hello</span><span class=\"token template-punctuation string\">`</span></span><span class=\"token template-string\"><span class=\"token template-punctuation string\">`</span><span class=\"token string\">World</span><span class=\"token template-punctuation string\">`</span></span><span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span> <span class=\"token comment\">// TypeError: \"Hello\" is not a function</span>\n</code></pre></div>\n<p>The only exception is optional chaining, which will throw a syntax error.</p>\n<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">js</span></div><pre class=\"brush: js example-bad notranslate\" data-signature=\"155+w9vUm9SUHasN7hCo6sSuvslpbN9bcqFMoMbeGw8=\"><code>console<span class=\"token punctuation\">.</span>log<span class=\"token operator\">?.</span><span class=\"token template-string\"><span class=\"token template-punctuation string\">`</span><span class=\"token string\">Hello</span><span class=\"token template-punctuation string\">`</span></span><span class=\"token punctuation\">;</span> <span class=\"token comment\">// SyntaxError: Invalid tagged template on optional chain</span>\nconsole<span class=\"token operator\">?.</span>log<span class=\"token template-string\"><span class=\"token template-punctuation string\">`</span><span class=\"token string\">Hello</span><span class=\"token template-punctuation string\">`</span></span><span class=\"token punctuation\">;</span> <span class=\"token comment\">// SyntaxError: Invalid tagged template on optional chain</span>\n</code></pre></div>\n<p>Note that these two expressions are still parsable. This means they would not be subject to <a href=\"/en-US/docs/Web/JavaScript/Reference/Lexical_grammar#automatic_semicolon_insertion\">automatic semicolon insertion</a>, which will only insert semicolons to fix code that's otherwise unparsable.</p>\n<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">js</span></div><pre class=\"brush: js example-bad notranslate\" data-signature=\"sParPuiRD1VdazbSB3EYVrWdz5CD+OUtioWrHz+U5RI=\"><code><span class=\"token comment\">// Still a syntax error</span>\n<span class=\"token keyword\">const</span> a <span class=\"token operator\">=</span> console<span class=\"token operator\">?.</span>log\n<span class=\"token template-string\"><span class=\"token template-punctuation string\">`</span><span class=\"token string\">Hello</span><span class=\"token template-punctuation string\">`</span></span>\n</code></pre></div>\n<p>Tag functions don't even need to return a string!</p>\n<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">js</span></div><pre class=\"brush: js notranslate\" data-signature=\"tta4Vn7I3PhB+KUUKOr0DRlCxv4XDXwFS2kWmKuGi4U=\"><code><span class=\"token keyword\">function</span> <span class=\"token function\">template</span><span class=\"token punctuation\">(</span><span class=\"token parameter\">strings<span class=\"token punctuation\">,</span> <span class=\"token operator\">...</span>keys</span><span class=\"token punctuation\">)</span> <span class=\"token punctuation\">{</span>\n  <span class=\"token keyword\">return</span> <span class=\"token punctuation\">(</span><span class=\"token parameter\"><span class=\"token operator\">...</span>values</span><span class=\"token punctuation\">)</span> <span class=\"token operator\">=&gt;</span> <span class=\"token punctuation\">{</span>\n    <span class=\"token keyword\">const</span> dict <span class=\"token operator\">=</span> values<span class=\"token punctuation\">[</span>values<span class=\"token punctuation\">.</span>length <span class=\"token operator\">-</span> <span class=\"token number\">1</span><span class=\"token punctuation\">]</span> <span class=\"token operator\">||</span> <span class=\"token punctuation\">{</span><span class=\"token punctuation\">}</span><span class=\"token punctuation\">;</span>\n    <span class=\"token keyword\">const</span> result <span class=\"token operator\">=</span> <span class=\"token punctuation\">[</span>strings<span class=\"token punctuation\">[</span><span class=\"token number\">0</span><span class=\"token punctuation\">]</span><span class=\"token punctuation\">]</span><span class=\"token punctuation\">;</span>\n    keys<span class=\"token punctuation\">.</span><span class=\"token function\">forEach</span><span class=\"token punctuation\">(</span><span class=\"token punctuation\">(</span><span class=\"token parameter\">key<span class=\"token punctuation\">,</span> i</span><span class=\"token punctuation\">)</span> <span class=\"token operator\">=&gt;</span> <span class=\"token punctuation\">{</span>\n      <span class=\"token keyword\">const</span> value <span class=\"token operator\">=</span> Number<span class=\"token punctuation\">.</span><span class=\"token function\">isInteger</span><span class=\"token punctuation\">(</span>key<span class=\"token punctuation\">)</span> <span class=\"token operator\">?</span> values<span class=\"token punctuation\">[</span>key<span class=\"token punctuation\">]</span> <span class=\"token operator\">:</span> dict<span class=\"token punctuation\">[</span>key<span class=\"token punctuation\">]</span><span class=\"token punctuation\">;</span>\n      result<span class=\"token punctuation\">.</span><span class=\"token function\">push</span><span class=\"token punctuation\">(</span>value<span class=\"token punctuation\">,</span> strings<span class=\"token punctuation\">[</span>i <span class=\"token operator\">+</span> <span class=\"token number\">1</span><span class=\"token punctuation\">]</span><span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\n    <span class=\"token punctuation\">}</span><span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\n    <span class=\"token keyword\">return</span> result<span class=\"token punctuation\">.</span><span class=\"token function\">join</span><span class=\"token punctuation\">(</span><span class=\"token string\">\"\"</span><span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\n  <span class=\"token punctuation\">}</span><span class=\"token punctuation\">;</span>\n<span class=\"token punctuation\">}</span>\n\n<span class=\"token keyword\">const</span> t1Closure <span class=\"token operator\">=</span> template<span class=\"token template-string\"><span class=\"token template-punctuation string\">`</span><span class=\"token interpolation\"><span class=\"token interpolation-punctuation punctuation\">${</span><span class=\"token number\">0</span><span class=\"token interpolation-punctuation punctuation\">}</span></span><span class=\"token interpolation\"><span class=\"token interpolation-punctuation punctuation\">${</span><span class=\"token number\">1</span><span class=\"token interpolation-punctuation punctuation\">}</span></span><span class=\"token interpolation\"><span class=\"token interpolation-punctuation punctuation\">${</span><span class=\"token number\">0</span><span class=\"token interpolation-punctuation punctuation\">}</span></span><span class=\"token string\">!</span><span class=\"token template-punctuation string\">`</span></span><span class=\"token punctuation\">;</span>\n<span class=\"token comment\">// const t1Closure = template([\"\",\"\",\"\",\"!\"],0,1,0);</span>\n<span class=\"token function\">t1Closure</span><span class=\"token punctuation\">(</span><span class=\"token string\">\"Y\"</span><span class=\"token punctuation\">,</span> <span class=\"token string\">\"A\"</span><span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span> <span class=\"token comment\">// \"YAY!\"</span>\n\n<span class=\"token keyword\">const</span> t2Closure <span class=\"token operator\">=</span> template<span class=\"token template-string\"><span class=\"token template-punctuation string\">`</span><span class=\"token interpolation\"><span class=\"token interpolation-punctuation punctuation\">${</span><span class=\"token number\">0</span><span class=\"token interpolation-punctuation punctuation\">}</span></span><span class=\"token string\"> </span><span class=\"token interpolation\"><span class=\"token interpolation-punctuation punctuation\">${</span><span class=\"token string\">\"foo\"</span><span class=\"token interpolation-punctuation punctuation\">}</span></span><span class=\"token string\">!</span><span class=\"token template-punctuation string\">`</span></span><span class=\"token punctuation\">;</span>\n<span class=\"token comment\">// const t2Closure = template([\"\",\" \",\"!\"],0,\"foo\");</span>\n<span class=\"token function\">t2Closure</span><span class=\"token punctuation\">(</span><span class=\"token string\">\"Hello\"</span><span class=\"token punctuation\">,</span> <span class=\"token punctuation\">{</span> <span class=\"token literal-property property\">foo</span><span class=\"token operator\">:</span> <span class=\"token string\">\"World\"</span> <span class=\"token punctuation\">}</span><span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span> <span class=\"token comment\">// \"Hello World!\"</span>\n\n<span class=\"token keyword\">const</span> t3Closure <span class=\"token operator\">=</span> template<span class=\"token template-string\"><span class=\"token template-punctuation string\">`</span><span class=\"token string\">I'm </span><span class=\"token interpolation\"><span class=\"token interpolation-punctuation punctuation\">${</span><span class=\"token string\">\"name\"</span><span class=\"token interpolation-punctuation punctuation\">}</span></span><span class=\"token string\">. I'm almost </span><span class=\"token interpolation\"><span class=\"token interpolation-punctuation punctuation\">${</span><span class=\"token string\">\"age\"</span><span class=\"token interpolation-punctuation punctuation\">}</span></span><span class=\"token string\"> years old.</span><span class=\"token template-punctuation string\">`</span></span><span class=\"token punctuation\">;</span>\n<span class=\"token comment\">// const t3Closure = template([\"I'm \", \". I'm almost \", \" years old.\"], \"name\", \"age\");</span>\n<span class=\"token function\">t3Closure</span><span class=\"token punctuation\">(</span><span class=\"token string\">\"foo\"</span><span class=\"token punctuation\">,</span> <span class=\"token punctuation\">{</span> <span class=\"token literal-property property\">name</span><span class=\"token operator\">:</span> <span class=\"token string\">\"MDN\"</span><span class=\"token punctuation\">,</span> <span class=\"token literal-property property\">age</span><span class=\"token operator\">:</span> <span class=\"token number\">30</span> <span class=\"token punctuation\">}</span><span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span> <span class=\"token comment\">// \"I'm MDN. I'm almost 30 years old.\"</span>\n<span class=\"token function\">t3Closure</span><span class=\"token punctuation\">(</span><span class=\"token punctuation\">{</span> <span class=\"token literal-property property\">name</span><span class=\"token operator\">:</span> <span class=\"token string\">\"MDN\"</span><span class=\"token punctuation\">,</span> <span class=\"token literal-property property\">age</span><span class=\"token operator\">:</span> <span class=\"token number\">30</span> <span class=\"token punctuation\">}</span><span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span> <span class=\"token comment\">// \"I'm MDN. I'm almost 30 years old.\"</span>\n</code></pre></div>\n<p>The first argument received by the tag function is an array of strings. For any template literal, its length is equal to the number of substitutions (occurrences of <code>${…}</code>) plus one, and is therefore always non-empty.</p>\n<p>For any particular tagged template literal expression, the tag function will always be called with the exact same literal array, no matter how many times the literal is evaluated.</p>\n<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">js</span></div><pre class=\"brush: js notranslate\" data-signature=\"R0Wr23NGuRTBoku6cSHZ/XNZ2oZAdoqm3df2x/QPGco=\"><code><span class=\"token keyword\">const</span> callHistory <span class=\"token operator\">=</span> <span class=\"token punctuation\">[</span><span class=\"token punctuation\">]</span><span class=\"token punctuation\">;</span>\n\n<span class=\"token keyword\">function</span> <span class=\"token function\">tag</span><span class=\"token punctuation\">(</span><span class=\"token parameter\">strings<span class=\"token punctuation\">,</span> <span class=\"token operator\">...</span>values</span><span class=\"token punctuation\">)</span> <span class=\"token punctuation\">{</span>\n  callHistory<span class=\"token punctuation\">.</span><span class=\"token function\">push</span><span class=\"token punctuation\">(</span>strings<span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\n  <span class=\"token comment\">// Return a freshly made object</span>\n  <span class=\"token keyword\">return</span> <span class=\"token punctuation\">{</span><span class=\"token punctuation\">}</span><span class=\"token punctuation\">;</span>\n<span class=\"token punctuation\">}</span>\n\n<span class=\"token keyword\">function</span> <span class=\"token function\">evaluateLiteral</span><span class=\"token punctuation\">(</span><span class=\"token punctuation\">)</span> <span class=\"token punctuation\">{</span>\n  <span class=\"token keyword\">return</span> tag<span class=\"token template-string\"><span class=\"token template-punctuation string\">`</span><span class=\"token string\">Hello, </span><span class=\"token interpolation\"><span class=\"token interpolation-punctuation punctuation\">${</span><span class=\"token string\">\"world\"</span><span class=\"token interpolation-punctuation punctuation\">}</span></span><span class=\"token string\">!</span><span class=\"token template-punctuation string\">`</span></span><span class=\"token punctuation\">;</span>\n<span class=\"token punctuation\">}</span>\n\nconsole<span class=\"token punctuation\">.</span><span class=\"token function\">log</span><span class=\"token punctuation\">(</span><span class=\"token function\">evaluateLiteral</span><span class=\"token punctuation\">(</span><span class=\"token punctuation\">)</span> <span class=\"token operator\">===</span> <span class=\"token function\">evaluateLiteral</span><span class=\"token punctuation\">(</span><span class=\"token punctuation\">)</span><span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span> <span class=\"token comment\">// false; each time `tag` is called, it returns a new object</span>\nconsole<span class=\"token punctuation\">.</span><span class=\"token function\">log</span><span class=\"token punctuation\">(</span>callHistory<span class=\"token punctuation\">[</span><span class=\"token number\">0</span><span class=\"token punctuation\">]</span> <span class=\"token operator\">===</span> callHistory<span class=\"token punctuation\">[</span><span class=\"token number\">1</span><span class=\"token punctuation\">]</span><span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span> <span class=\"token comment\">// true; all evaluations of the same tagged literal would pass in the same strings array</span>\n</code></pre></div>\n<p>This allows the tag to cache the result based on the identity of its first argument. To further ensure the array value's stability, the first argument and its <a href=\"#raw_strings\"><code>raw</code> property</a> are both <a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/isFrozen\">frozen</a>, so you can't mutate them in any way.</p>"}},{"type":"prose","value":{"id":"raw_strings","title":"Raw strings","isH3":true,"content":"<p>The special <code>raw</code> property, available on the first argument to the tag function, allows you to access the raw strings as they were entered, without processing <a href=\"/en-US/docs/Web/JavaScript/Guide/Grammar_and_types#using_special_characters_in_strings\">escape sequences</a>.</p>\n<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">js</span></div><pre class=\"brush: js notranslate\" data-signature=\"8LxMf/dxd5eQsNjZ+JH3EEBC2G3NlW32B7+Gd+XoG7M=\"><code><span class=\"token keyword\">function</span> <span class=\"token function\">tag</span><span class=\"token punctuation\">(</span><span class=\"token parameter\">strings</span><span class=\"token punctuation\">)</span> <span class=\"token punctuation\">{</span>\n  console<span class=\"token punctuation\">.</span><span class=\"token function\">log</span><span class=\"token punctuation\">(</span>strings<span class=\"token punctuation\">.</span>raw<span class=\"token punctuation\">[</span><span class=\"token number\">0</span><span class=\"token punctuation\">]</span><span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\n<span class=\"token punctuation\">}</span>\n\ntag<span class=\"token template-string\"><span class=\"token template-punctuation string\">`</span><span class=\"token string\">string text line 1 \\n string text line 2</span><span class=\"token template-punctuation string\">`</span></span><span class=\"token punctuation\">;</span>\n<span class=\"token comment\">// Logs \"string text line 1 \\n string text line 2\" ,</span>\n<span class=\"token comment\">// including the two characters '\\' and 'n'</span>\n</code></pre></div>\n<p>In addition, the <a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/raw\"><code>String.raw()</code></a> method exists to create raw strings just like the default template function and string concatenation would create.</p>\n<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">js</span></div><pre class=\"brush: js notranslate\" data-signature=\"BbUr/VVYlNhbtiRvrOeLQMOQ3Hd9DO91tHQqmWstlGU=\"><code><span class=\"token keyword\">const</span> str <span class=\"token operator\">=</span> String<span class=\"token punctuation\">.</span>raw<span class=\"token template-string\"><span class=\"token template-punctuation string\">`</span><span class=\"token string\">Hi\\n</span><span class=\"token interpolation\"><span class=\"token interpolation-punctuation punctuation\">${</span><span class=\"token number\">2</span> <span class=\"token operator\">+</span> <span class=\"token number\">3</span><span class=\"token interpolation-punctuation punctuation\">}</span></span><span class=\"token string\">!</span><span class=\"token template-punctuation string\">`</span></span><span class=\"token punctuation\">;</span>\n<span class=\"token comment\">// \"Hi\\\\n5!\"</span>\n\nstr<span class=\"token punctuation\">.</span>length<span class=\"token punctuation\">;</span>\n<span class=\"token comment\">// 6</span>\n\nArray<span class=\"token punctuation\">.</span><span class=\"token function\">from</span><span class=\"token punctuation\">(</span>str<span class=\"token punctuation\">)</span><span class=\"token punctuation\">.</span><span class=\"token function\">join</span><span class=\"token punctuation\">(</span><span class=\"token string\">\",\"</span><span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\n<span class=\"token comment\">// \"H,i,\\\\,n,5,!\"</span>\n</code></pre></div>\n<p><code>String.raw</code> functions like an \"identity\" tag if the literal doesn't contain any escape sequences. In case you want an actual identity tag that always works as if the literal is untagged, you can make a custom function that passes the \"cooked\" (i.e. escape sequences are processed) literal array to <code>String.raw</code>, pretending they are raw strings.</p>\n<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">js</span></div><pre class=\"brush: js notranslate\" data-signature=\"tttx9I9r0BnPK/M2r9GFDQX3mlN9EKaRpNndAwmah+o=\"><code><span class=\"token keyword\">const</span> <span class=\"token function-variable function\">identity</span> <span class=\"token operator\">=</span> <span class=\"token punctuation\">(</span><span class=\"token parameter\">strings<span class=\"token punctuation\">,</span> <span class=\"token operator\">...</span>values</span><span class=\"token punctuation\">)</span> <span class=\"token operator\">=&gt;</span>\n  String<span class=\"token punctuation\">.</span><span class=\"token function\">raw</span><span class=\"token punctuation\">(</span><span class=\"token punctuation\">{</span> <span class=\"token literal-property property\">raw</span><span class=\"token operator\">:</span> strings <span class=\"token punctuation\">}</span><span class=\"token punctuation\">,</span> <span class=\"token operator\">...</span>values<span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\nconsole<span class=\"token punctuation\">.</span><span class=\"token function\">log</span><span class=\"token punctuation\">(</span>identity<span class=\"token template-string\"><span class=\"token template-punctuation string\">`</span><span class=\"token string\">Hi\\n</span><span class=\"token interpolation\"><span class=\"token interpolation-punctuation punctuation\">${</span><span class=\"token number\">2</span> <span class=\"token operator\">+</span> <span class=\"token number\">3</span><span class=\"token interpolation-punctuation punctuation\">}</span></span><span class=\"token string\">!</span><span class=\"token template-punctuation string\">`</span></span><span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\n<span class=\"token comment\">// Hi</span>\n<span class=\"token comment\">// 5!</span>\n</code></pre></div>\n<p>This is useful for many tools which give special treatment to literals tagged by a particular name.</p>\n<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">js</span></div><pre class=\"brush: js notranslate\" data-signature=\"EOgB9+Kd/2A8i46IZrX2nhtPjOfWS07EO4D69wc4Vnw=\"><code><span class=\"token keyword\">const</span> <span class=\"token function-variable function\">html</span> <span class=\"token operator\">=</span> <span class=\"token punctuation\">(</span><span class=\"token parameter\">strings<span class=\"token punctuation\">,</span> <span class=\"token operator\">...</span>values</span><span class=\"token punctuation\">)</span> <span class=\"token operator\">=&gt;</span> String<span class=\"token punctuation\">.</span><span class=\"token function\">raw</span><span class=\"token punctuation\">(</span><span class=\"token punctuation\">{</span> <span class=\"token literal-property property\">raw</span><span class=\"token operator\">:</span> strings <span class=\"token punctuation\">}</span><span class=\"token punctuation\">,</span> <span class=\"token operator\">...</span>values<span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\n<span class=\"token comment\">// Some formatters will format this literal's content as HTML</span>\n<span class=\"token keyword\">const</span> doc <span class=\"token operator\">=</span> html<span class=\"token template-string\"><span class=\"token template-punctuation string\">`</span><span class=\"token string\">&lt;!doctype html&gt;\n  &lt;html lang=\"en-US\"&gt;\n    &lt;head&gt;\n      &lt;title&gt;Hello&lt;/title&gt;\n    &lt;/head&gt;\n    &lt;body&gt;\n      &lt;h1&gt;Hello world!&lt;/h1&gt;\n    &lt;/body&gt;\n  &lt;/html&gt;</span><span class=\"token template-punctuation string\">`</span></span><span class=\"token punctuation\">;</span>\n</code></pre></div>"}},{"type":"prose","value":{"id":"tagged_templates_and_escape_sequences","title":"Tagged templates and escape sequences","isH3":true,"content":"<p>In normal template literals, <a href=\"/en-US/docs/Web/JavaScript/Reference/Lexical_grammar#escape_sequences\">the escape sequences in string literals</a> are all allowed. Any other non-well-formed escape sequence is a syntax error. This includes:</p>\n<ul>\n  <li><code>\\</code> followed by any decimal digit other than <code>0</code>, or <code>\\0</code> followed by a decimal digit; for example <code>\\9</code> and <code>\\07</code> (which is a <a href=\"/en-US/docs/Web/JavaScript/Reference/Deprecated_and_obsolete_features#escape_sequences\">deprecated syntax</a>)</li>\n  <li><code>\\x</code> followed by fewer than two hex digits (including none); for example <code>\\xz</code></li>\n  <li><code>\\u</code> not followed by <code>{</code> and followed by fewer than four hex digits (including none); for example <code>\\uz</code></li>\n  <li><code>\\u{}</code> enclosing an invalid Unicode code point — it contains a non-hex digit, or its value is greater than <code>10FFFF</code>; for example <code>\\u{110000}</code> and <code>\\u{z}</code></li>\n</ul>\n<div class=\"notecard note\" id=\"sect1\">\n  <p><strong>Note:</strong> <code>\\</code> followed by other characters, while they may be useless since nothing is escaped, are not syntax errors.</p>\n</div>\n<p>However, this is problematic for tagged templates, which, in addition to the \"cooked\" literal, also have access to the raw literals (escape sequences are preserved as-is).</p>\n<p>Tagged templates should allow the embedding of languages (for example <a href=\"https://en.wikipedia.org/wiki/Domain-specific_language\" class=\"external\" target=\"_blank\">DSLs</a>, or <a href=\"https://en.wikipedia.org/wiki/LaTeX\" class=\"external\" target=\"_blank\">LaTeX</a>), where other escapes sequences are common. Therefore, the syntax restriction of well-formed escape sequences is removed from tagged templates.</p>\n<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">js</span></div><pre class=\"brush: js notranslate\" data-signature=\"pqpnq90MG+vK3rKAWLWYdNFZIkgWIWOf927Zs27uIk0=\"><code>latex<span class=\"token template-string\"><span class=\"token template-punctuation string\">`</span><span class=\"token string\">\\unicode</span><span class=\"token template-punctuation string\">`</span></span><span class=\"token punctuation\">;</span>\n<span class=\"token comment\">// Throws in older ECMAScript versions (ES2016 and earlier)</span>\n<span class=\"token comment\">// SyntaxError: malformed Unicode character escape sequence</span>\n</code></pre></div>\n<p>However, illegal escape sequences must still be represented in the \"cooked\" representation. They will show up as <a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/undefined\"><code>undefined</code></a> element in the \"cooked\" array:</p>\n<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">js</span></div><pre class=\"brush: js notranslate\" data-signature=\"oHdySCkouCSuxxdUaiQCyL2xl3MPcjqXFMJnm3W0r5Q=\"><code><span class=\"token keyword\">function</span> <span class=\"token function\">latex</span><span class=\"token punctuation\">(</span><span class=\"token parameter\">str</span><span class=\"token punctuation\">)</span> <span class=\"token punctuation\">{</span>\n  <span class=\"token keyword\">return</span> <span class=\"token punctuation\">{</span> <span class=\"token literal-property property\">cooked</span><span class=\"token operator\">:</span> str<span class=\"token punctuation\">[</span><span class=\"token number\">0</span><span class=\"token punctuation\">]</span><span class=\"token punctuation\">,</span> <span class=\"token literal-property property\">raw</span><span class=\"token operator\">:</span> str<span class=\"token punctuation\">.</span>raw<span class=\"token punctuation\">[</span><span class=\"token number\">0</span><span class=\"token punctuation\">]</span> <span class=\"token punctuation\">}</span><span class=\"token punctuation\">;</span>\n<span class=\"token punctuation\">}</span>\n\nlatex<span class=\"token template-string\"><span class=\"token template-punctuation string\">`</span><span class=\"token string\">\\unicode</span><span class=\"token template-punctuation string\">`</span></span><span class=\"token punctuation\">;</span>\n\n<span class=\"token comment\">// { cooked: undefined, raw: \"\\\\unicode\" }</span>\n</code></pre></div>\n<p>Note that the escape-sequence restriction is only dropped from <em>tagged</em> templates, but not from <em>untagged</em> template literals:</p>\n<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">js</span></div><pre class=\"brush: js example-bad notranslate\" data-signature=\"l/2X9FCw/Hd/PqTYZ74IYiAYYUffWFkzLdU0YVY03tE=\"><code><span class=\"token keyword\">const</span> bad <span class=\"token operator\">=</span> <span class=\"token template-string\"><span class=\"token template-punctuation string\">`</span><span class=\"token string\">bad escape sequence: \\unicode</span><span class=\"token template-punctuation string\">`</span></span><span class=\"token punctuation\">;</span>\n</code></pre></div>"}},{"type":"specifications","value":{"title":"Specifications","id":"specifications","isH3":false,"specifications":[{"bcdSpecificationURL":"https://tc39.es/ecma262/multipage/ecmascript-language-expressions.html#sec-template-literals","title":"ECMAScript Language Specification"}],"query":"javascript.grammar.template_literals"}},{"type":"browser_compatibility","value":{"title":"Browser compatibility","id":"browser_compatibility","isH3":false,"query":"javascript.grammar.template_literals"}},{"type":"prose","value":{"id":"see_also","title":"See also","isH3":false,"content":"<ul>\n  <li><a href=\"/en-US/docs/Web/JavaScript/Guide/Text_formatting\">Text formatting</a> guide</li>\n  <li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/String\"><code>String</code></a></li>\n  <li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/raw\"><code>String.raw()</code></a></li>\n  <li><a href=\"/en-US/docs/Web/JavaScript/Reference/Lexical_grammar\">Lexical grammar</a></li>\n  <li><a href=\"https://hacks.mozilla.org/2015/05/es6-in-depth-template-strings-2/\" class=\"external\" target=\"_blank\">ES6 in Depth: Template strings</a> on hacks.mozilla.org (2015)</li>\n</ul>"}}],"toc":[{"text":"Syntax","id":"syntax"},{"text":"Description","id":"description"},{"text":"Specifications","id":"specifications"},{"text":"Browser compatibility","id":"browser_compatibility"},{"text":"See also","id":"see_also"}],"summary":"Template literals are literals delimited with backtick (`) characters, allowing for multi-line strings, string interpolation with embedded expressions, and special constructs called tagged templates.","popularity":0.3059,"modified":"2023-12-03T00:03:52.000Z","other_translations":[{"locale":"es","title":"Plantillas literales (plantillas de cadenas)","native":"Español"},{"locale":"fr","title":"Littéraux de gabarits","native":"Français"},{"locale":"ja","title":"テンプレートリテラル (テンプレート文字列)","native":"日本語"},{"locale":"ko","title":"Template literals","native":"한국어"},{"locale":"pt-BR","title":"Template strings","native":"Português (do Brasil)"},{"locale":"ru","title":"Шаблонные строки","native":"Русский"},{"locale":"zh-CN","title":"模板字符串","native":"中文 (简体)"},{"locale":"zh-TW","title":"樣板字面值","native":"正體中文 (繁體)"}],"source":{"folder":"en-us/web/javascript/reference/template_literals","github_url":"https://github.com/mdn/content/blob/main/files/en-us/web/javascript/reference/template_literals/index.md","last_commit_url":"https://github.com/mdn/content/commit/0922a6ed8c2c4d365d1952a0cd3ddc5f79b61a3a","filename":"index.md"},"short_title":"Template literals (Template strings)","parents":[{"uri":"/en-US/docs/Web","title":"References"},{"uri":"/en-US/docs/Web/JavaScript","title":"JavaScript"},{"uri":"/en-US/docs/Web/JavaScript/Reference","title":"Reference"},{"uri":"/en-US/docs/Web/JavaScript/Reference/Template_literals","title":"Template literals (Template strings)"}],"pageTitle":"Template literals (Template strings) - JavaScript | MDN","noIndexing":false}}</script></body></html>