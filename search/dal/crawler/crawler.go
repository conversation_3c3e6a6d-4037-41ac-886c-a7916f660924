package crawler

import (
	"context"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"time"

	bytedtrace "code.byted.org/bytedtrace/interface-go"
	"code.byted.org/gopkg/logs/v2/log"
	md "github.com/<PERSON><PERSON>/html-to-markdown"
	"github.com/<PERSON>/html-to-markdown/plugin"
	"github.com/PuerkitoBio/goquery"
	"github.com/pkg/errors"
	"github.com/tmc/langchaingo/textsplitter"
	"golang.org/x/sync/semaphore"

	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/tcc"
	"code.byted.org/devgpt/kiwis/lib/util"
	"code.byted.org/devgpt/kiwis/port/rocketmq"
	"code.byted.org/devgpt/kiwis/search/dal/cache"
	"code.byted.org/devgpt/kiwis/search/entity"
)

// Crawler Parse the original content of a web page and convert it into markdown.
// The parsed md is split into different chunks for subsequent analysis and processing
type Crawler interface {
	// Crawl and parse web page content and return it in Markdown format
	Crawl(ctx context.Context, uri string) (content string, err error)
	// CrawlAndSplit By default, 10 chunks are returned
	CrawlAndSplit(ctx context.Context, uri string, chunkCount ...int) (chunks []string, err error)
	// getCache: Try to get the cache first, and then fetch the page
	getPage(ctx context.Context, uri string) (raw string, err error)
	fetchPage(ctx context.Context, uri string) (raw string, err error)
	convertToMarkdown(ctx context.Context, raw string, uri string) (content string, err error)
	split(ctx context.Context, content string, chunkCount int) (chunks []string, err error)
}

type crawler struct {
	sem       *semaphore.Weighted
	pageCache cache.PageCache
	mq        rocketmq.Client
	client    *http.Client
	splitter  textsplitter.RecursiveCharacter
}

func NewCrawler(config *tcc.GenericConfig[config.CrawlerConfig], pageCache cache.PageCache, mq rocketmq.Client) Crawler {
	return &crawler{
		sem:       semaphore.NewWeighted(int64(config.GetValue().MaxParallelTask)),
		pageCache: pageCache,
		mq:        mq,
		client: &http.Client{
			// Quick CrawlAndSplit, 5s is enough for well-known sites main html
			// fallback to offline crawler if failed to fetch within short amont of time
			Timeout: time.Second * 5,
			Transport: &http.Transport{
				// if we cannot establish a TCP connection within 1s, drop this reference
				// real time crawl should be as fast as possible
				// http does not provide DialTimeout
				Dial: func(network, addr string) (net.Conn, error) {
					return net.DialTimeout(network, addr, time.Second)
				},
				// don't waste time if server could not respond any http header in 3s
				ResponseHeaderTimeout: time.Second * 3,
			},
		},

		// chunk size是按照字符计算的，估算token大致按照 * 3
		// 这里设定的单个chunk是大致200token
		// 尝试过 MarkdownTextSplitter，效果不佳
		splitter: textsplitter.NewRecursiveCharacter(
			textsplitter.WithChunkSize(600),
			textsplitter.WithChunkOverlap(50),
			textsplitter.WithCodeBlocks(true),
		),
	}
}

func isHTML(raw string) bool {
	return strings.Contains(strings.ToValidUTF8(raw[:min(len(raw), 512)], ""), "<html")
}

func (s crawler) Crawl(ctx context.Context, uri string) (content string, err error) {
	raw, err := s.getPage(ctx, uri)
	if err != nil {
		return
	}

	// for content > 4MiB, only take the first 4MiB to process
	// larger content won't be processed
	if len(raw) > 4*1024*1024 {
		log.V1.CtxWarn(ctx, "content too long, only take the first 4MiB. uri: %s", uri)
		raw = raw[:4*1024*1024]
	}
	if !isHTML(raw) {
		log.V1.CtxInfo(ctx, "content is not html, skip converting. uri: %s", uri)
		// content may be html or plaintext, return truncated content
		return raw, nil
	}

	content, err = s.convertToMarkdown(ctx, raw, uri)
	if err != nil {
		return
	}

	content = s.postprocess(ctx, content)
	return
}

func (s crawler) CrawlAndSplit(ctx context.Context, uri string, chunkCount ...int) (chunks []string, err error) {
	span, ct := bytedtrace.StartCustomSpan(ctx, "crawl", "Crawl")
	defer span.Finish()

	content, err := s.Crawl(ct, uri)
	if err != nil {
		return
	}

	chunks, err = s.split(ctx, content, util.OrDefault(util.First(chunkCount), 10))
	return
}

func (s crawler) getPage(ctx context.Context, uri string) (raw string, err error) {
	// 目前线上整体流量并不大，且网页爬取逻辑并不完善
	// 所以将全部网页抓取需求都发送到 mq 一份，再离线进行更准备的爬取后覆盖缓存
	go func() {
		msg := entity.CrawlMessage{
			URL:    uri,
			TosKey: s.pageCache.GetKey(uri),
		}
		_ = s.mq.SendMessage(ctx, msg.Marshal(), msg.Tag())
	}()

	raw, _ = s.pageCache.Get(ctx, uri)
	if raw != "" {
		return
	}

	raw, err = s.fetchPage(ctx, uri)
	if err != nil {
		return
	}
	// update cache in background, which might cost ~200ms for large webpages and affect performance
	go func() {
		innerErr := s.pageCache.Set(ctx, uri, raw)
		if innerErr != nil {
			log.V1.CtxError(ctx, "failed to cache page [%s]: %v", uri, err)
		}
	}()
	return
}

func (s crawler) fetchPage(ctx context.Context, uri string) (string, error) {
	crawlSpan, ct := bytedtrace.StartCustomSpan(ctx, "crawl", "Fetch")
	defer crawlSpan.Finish()
	log.V1.CtxInfo(ct, "crawling url=%s", uri)

	// https://blog.cloudflare.com/the-complete-guide-to-golang-net-http-timeouts
	req, err := http.NewRequestWithContext(ct, http.MethodGet, uri, nil)
	if err != nil {
		return "", err
	}
	resp, err := s.client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("status code error: %d %s", resp.StatusCode, resp.Status)
	}
	if !strings.HasPrefix(resp.Header.Get("Content-Type"), "text/html") {
		return "", fmt.Errorf("content type not html: %s", resp.Header.Get("Content-Type"))
	}

	body, err := io.ReadAll(resp.Body)
	return string(body), err
}

func (s crawler) convertToMarkdown(ctx context.Context, raw string, uri string) (content string, err error) {
	convertSpan, _ := bytedtrace.StartCustomSpan(ctx, "crawl", "Convert")
	defer convertSpan.Finish()

	u, err := url.Parse(uri)
	if err != nil {
		return
	}
	baseURL := u.Scheme + "://" + u.Host
	converter := md.NewConverter(baseURL, true, nil)
	converter.Use(plugin.GitHubFlavored())
	tagsToRemove := []string{
		"head", "script", "style", "noscript", "link", "meta",
		"header", "footer", "title", "nav", "aside",
		"svg", "img", "picture", "button", "select", "input",
		"form", "dialog",

		".banner", ".hidden",
		"[role=navigation]", "[role=banner]", "[role=search]", "[role=contentinfo]", "[role=form]",
		"[role=menu]", "[role=menubar]", "[role=menuitemradio]", "[role=menuitemcheckbox]", "[role=button]", "[role=toolbar]", "[role=dialog]",
	}

	// the lib seems to have a bug that tags removed by this function are actually ignored
	//converter.Remove("script", "style", "noscript", "svg", "img", "link", "meta", "title", "button", "select", "input", "nav", "aside")
	converter.Before(func(selec *goquery.Selection) {
		selec.Find(strings.Join(tagsToRemove, ",")).Remove()
		// a tag may contains some text so we just remove it's url
		selec.Find("a").Each(func(_ int, s *goquery.Selection) {
			href := s.AttrOr("href", "")
			// if the content of anchor is the same as its url, keep it
			if href == s.Text() {
				return
			}
			// only remove internal links, which are likely navigations, other terms; related pages should be recalled by search
			if strings.HasPrefix(href, "/") || strings.HasPrefix(href, "#") || strings.HasPrefix(href, baseURL) {
				s.RemoveAttr("href")
			}
		})
	})
	converter.AddRules(
		// replace default code rule to detect language
		md.Rule{
			Filter:      []string{"pre"},
			Replacement: convertCodeblock,
		},
	)
	content, err = converter.ConvertString(raw)
	if err != nil {
		err = errors.WithMessage(err, "failed to parse content to markdown")
	}
	return
}

func (s crawler) postprocess(ctx context.Context, content string) string {
	content = multiLineRegex.ReplaceAllString(content, "\n")

	// 必须是old 和 new 的序列，传入基数个参数会panic
	replacer := strings.NewReplacer("�", "")
	content = replacer.Replace(content)

	return content
}

var (
	multiLineRegex = regexp.MustCompile("\n+")
)
