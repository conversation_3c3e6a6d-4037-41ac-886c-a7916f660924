package defaultjournalservice

import (
	"context"
	"reflect"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gorm.io/gorm"

	"code.byted.org/devgpt/kiwis/bitscopilot/journal/dal"
	"code.byted.org/devgpt/kiwis/bitscopilot/journal/dal/po"
	"code.byted.org/devgpt/kiwis/copilotstack/journal"
)

var _ journal.JournalService = &JournalService{}

type JournalService struct {
	DAO dal.JournalDAO
}

func NewJournalService(DAO dal.JournalDAO) (*JournalService, error) {
	s := &JournalService{DAO: DAO}
	return s, nil
}

func (j *JournalService) CreateLog(ctx context.Context, opt journal.CreateLogOption) (*journal.PromptCompletion, error) {
	res, err := j.DAO.CreatePromptCompletionLog(ctx, dal.CreatePromptCompletionLogOption{
		AppID:           opt.AppId,
		Username:        opt.Username,
		ModelName:       opt.ModelName,
		Status:          opt.Status,
		SessionId:       opt.SessionId,
		Prompt:          opt.Prompt,
		Type:            opt.Type,
		ContentRaw:      opt.ContentRaw,
		RequestMetadata: po.PromptCompletionRequestMetadata(opt.RequestMetadata),
	})

	if err != nil {
		return nil, errors.WithMessage(err, "failed to create db record")
	}

	return res, nil
}

func (j *JournalService) UpdateLog(ctx context.Context, opt journal.UpdateLogOption) (*journal.PromptCompletion, error) {
	log, err := j.DAO.GetPromptCompletionLogById(ctx, opt.ID)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get db record")
	}

	// merge from two metadata
	requestMetadata := &journal.PromptCompletionRequestMetadata{
		Stream:            lo.Ternary(empty(opt.RequestMetadata.Stream), log.RequestMetadata.Stream, opt.RequestMetadata.Stream),
		MaxTokens:         lo.Ternary(empty(opt.RequestMetadata.MaxTokens), log.RequestMetadata.MaxTokens, opt.RequestMetadata.MaxTokens),
		Temperature:       lo.Ternary(empty(opt.RequestMetadata.Temperature), log.RequestMetadata.Temperature, opt.RequestMetadata.Temperature),
		TopP:              lo.Ternary(empty(opt.RequestMetadata.TopP), log.RequestMetadata.TopP, opt.RequestMetadata.TopP),
		TopK:              lo.Ternary(empty(opt.RequestMetadata.TopK), log.RequestMetadata.TopK, opt.RequestMetadata.TopK),
		N:                 lo.Ternary(empty(opt.RequestMetadata.N), log.RequestMetadata.N, opt.RequestMetadata.N),
		FrequencyPenalty:  lo.Ternary(empty(opt.RequestMetadata.FrequencyPenalty), log.RequestMetadata.FrequencyPenalty, opt.RequestMetadata.FrequencyPenalty),
		PresencePenalty:   lo.Ternary(empty(opt.RequestMetadata.PresencePenalty), log.RequestMetadata.PresencePenalty, opt.RequestMetadata.PresencePenalty),
		MinNewTokens:      lo.Ternary(empty(opt.RequestMetadata.MinNewTokens), log.RequestMetadata.MinNewTokens, opt.RequestMetadata.MinNewTokens),
		MaxPromptTokens:   lo.Ternary(empty(opt.RequestMetadata.MaxPromptTokens), log.RequestMetadata.MaxPromptTokens, opt.RequestMetadata.MaxPromptTokens),
		RepetitionPenalty: lo.Ternary(empty(opt.RequestMetadata.RepetitionPenalty), log.RequestMetadata.RepetitionPenalty, opt.RequestMetadata.RepetitionPenalty),
		ThinkingConfig:    lo.Ternary(empty(opt.RequestMetadata.ThinkingConfig), log.RequestMetadata.ThinkingConfig, opt.RequestMetadata.ThinkingConfig),
		ID:                lo.Ternary(empty(opt.RequestMetadata.ID), log.RequestMetadata.ID, opt.RequestMetadata.ID),
		Usage:             lo.Ternary(empty(opt.RequestMetadata.Usage), log.RequestMetadata.Usage, opt.RequestMetadata.Usage),
		Latency:           lo.Ternary(empty(opt.RequestMetadata.Latency), log.RequestMetadata.Latency, opt.RequestMetadata.Latency),
		FirstTokenLatency: lo.Ternary(empty(opt.RequestMetadata.FirstTokenLatency), log.RequestMetadata.FirstTokenLatency, opt.RequestMetadata.FirstTokenLatency),
		Error:             lo.Ternary(empty(opt.RequestMetadata.Error), log.RequestMetadata.Error, opt.RequestMetadata.Error),
		FinishReason:      lo.Ternary(empty(opt.RequestMetadata.FinishReason), log.RequestMetadata.FinishReason, opt.RequestMetadata.FinishReason),
		LogID:             lo.Ternary(empty(opt.RequestMetadata.LogID), log.RequestMetadata.LogID, opt.RequestMetadata.LogID),
	}

	res, err := j.DAO.UpdatePromptCompletionLog(ctx, dal.UpdatePromptCompletionLogOption{
		ID:              opt.ID,
		AppID:           opt.AppId,
		Username:        opt.Username,
		ModelName:       opt.ModelName,
		Status:          opt.Status,
		SessionId:       opt.SessionId,
		Prompt:          opt.Prompt,
		Type:            opt.Type,
		ContentRaw:      opt.ContentRaw,
		RequestMetadata: requestMetadata,
	})

	if err != nil {
		return nil, errors.WithMessage(err, "failed to update db record")
	}

	return res, nil
}

func (j *JournalService) UpsertFeedback(ctx context.Context, opt journal.CreateFeedbackOption) (*journal.Feedback, error) {
	feedback, err := j.DAO.GetFeedback(ctx, opt.Username, opt.SessionId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return j.DAO.CreateFeedback(ctx, dal.CreateFeedbackOption{
				Username:  opt.Username,
				SessionId: opt.SessionId,
				Type:      opt.Type,
				Content:   opt.Content,
			})
		}
		return nil, errors.WithMessage(err, "failed to get feedback")
	}

	// Update the existed feedback.
	return j.DAO.UpdateFeedback(ctx, dal.UpdateFeedbackOption{
		FeedbackID: feedback.ID,
		Type:       opt.Type,
		Content:    opt.Content,
	})
}

// empty returns true if the given value has the zero value for its type.
func empty(i interface{}) bool {
	g := reflect.ValueOf(i)
	if !g.IsValid() {
		return true
	}

	switch g.Kind() {
	default:
		return g.IsNil()
	case reflect.Array, reflect.Slice, reflect.Map, reflect.String:
		return g.Len() == 0
	case reflect.Bool:
		return !g.Bool()
	case reflect.Complex64, reflect.Complex128:
		return g.Complex() == 0
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return g.Int() == 0
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr:
		return g.Uint() == 0
	case reflect.Float32, reflect.Float64:
		return g.Float() == 0
	case reflect.Struct:
		return false
	}
}
