package dal

import (
	"context"
	"time"

	"code.byted.org/devgpt/kiwis/bitscopilot/agent/chat/dal/po"
	"code.byted.org/devgpt/kiwis/bitscopilot/agent/entity"
	"code.byted.org/devgpt/kiwis/port/db"
	"code.byted.org/lang/gg/gptr"
	"github.com/pkg/errors"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"gorm.io/plugin/dbresolver"
)

type ConversationDAO interface {
	CreateConversation(ctx context.Context, opt CreateConversationOption) (*entity.Conversation, error)
	GetConversation(ctx context.Context, opt GetConversationOption) (*entity.Conversation, error)
	UpdateConversation(ctx context.Context, opt UpdateConversationOption) (*entity.Conversation, error)
	GetSubConversations(ctx context.Context, opt GetSubConversationsOption) ([]*entity.ConversationRelation, error)
	GetSubConversationByAgentId(ctx context.Context, opt GetSubConversationByAgentIdOption) ([]*entity.ConversationRelation, error)
	CreateSubConversation(ctx context.Context, opt CreateSubConversationOption) (*entity.ConversationRelation, error)
	UpdateConversationInstance(ctx context.Context, conversationId, instanceId string) error
}

var _ ConversationDAO = &ConversationDAOImpl{}

type ConversationDAOImpl struct {
	cli db.Client
}

func (c *ConversationDAOImpl) UpdateConversationInstance(ctx context.Context, conversationId, instanceId string) error {
	return c.cli.NewRequest(ctx).Model(&po.ConversationsPO{}).Where("conversation_id=?", conversationId).Update("instance_id", instanceId).Error
}

type CreateSubConversationOption struct {
	ConversationID uint64
	AgentID        int
	BizID          string
}

func (c *ConversationDAOImpl) CreateSubConversation(ctx context.Context, opt CreateSubConversationOption) (*entity.ConversationRelation, error) {
	m := &entity.ConversationRelation{
		ConversationID: opt.ConversationID,
		AgentID:        opt.AgentID,
		BizID:          opt.BizID,
		CreateAt:       time.Now(),
	}
	err := c.cli.NewRequest(ctx).Create(m).Error
	if err != nil {
		return nil, err
	}
	return m, nil
}

type GetSubConversationByAgentIdOption struct {
	ConversationId uint64
	AgentId        uint64
}

func (c *ConversationDAOImpl) GetSubConversationByAgentId(ctx context.Context, opt GetSubConversationByAgentIdOption) ([]*entity.ConversationRelation, error) {
	sc := make([]*entity.ConversationRelation, 0)
	err := c.cli.NewRequest(ctx).Where("conversation_id = ? and agent_id = ?", opt.ConversationId, opt.AgentId).Order("create_at desc").Find(&sc).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return sc, nil
}

type GetSubConversationsOption struct {
	ConversationId uint64
}

func (c *ConversationDAOImpl) GetSubConversations(ctx context.Context, opt GetSubConversationsOption) ([]*entity.ConversationRelation, error) {
	sc := make([]*entity.ConversationRelation, 0)
	err := c.cli.NewRequest(ctx).Where("conversation_id = ?", opt.ConversationId).Order("create_at desc").Limit(10).Find(&sc).Error
	if err != nil {
		return nil, err
	}
	return sc, nil
}

func NewConversationDAOImpl(cli db.Client) *ConversationDAOImpl {
	return &ConversationDAOImpl{cli}
}

func (c *ConversationDAOImpl) CreateConversation(ctx context.Context, opt CreateConversationOption) (*entity.Conversation, error) {
	if opt.AppID == "" || opt.UserName == "" {
		return nil, errors.New("illegal argument")
	}
	newPO := &po.ConversationsPO{
		ConversationID: opt.ConversationID,
		AppID:          opt.AppID,
		Title:          opt.Title,
		UserName:       opt.UserName,
		Status:         string(entity.ConversationStatusInitialized),
		CreatedBy:      opt.CreatedBy,
		CreatedAt:      time.Now(),
	}
	if opt.OverrideModelConfigs != nil {
		newPO.OverrideConfigs = datatypes.NewJSONType[po.ConversationsOverrideConfigs](opt.OverrideModelConfigs)
	}
	if opt.Inputs != nil {
		newPO.Inputs = datatypes.NewJSONType[po.ConversationsInputs](*opt.Inputs)
	}

	if err := c.cli.NewRequest(ctx).Create(newPO).Error; err != nil {
		return nil, err
	}
	return getConversationFromPO(newPO, 0), nil
}

type CreateConversationOption struct {
	ConversationID       string
	AppID                string
	OverrideModelConfigs map[string]any
	Title                string
	Inputs               *[]entity.Input
	UserName             string
	CreatedBy            string
}

func (c *ConversationDAOImpl) GetConversation(ctx context.Context, opt GetConversationOption) (*entity.Conversation, error) {
	return c.getConversation(ctx, opt, true)
}

func (c *ConversationDAOImpl) getConversation(ctx context.Context, opt GetConversationOption, sync bool) (*entity.Conversation, error) {
	if opt.ConversationID == "" || opt.AppID == "" {
		return nil, errors.New("illegal argument")
	}
	var conversationsPO po.ConversationsPO
	request := c.cli.NewRequest(ctx)
	if sync {
		request = request.Clauses(dbresolver.Write)
	}
	if err := request.
		Where("conversation_id = ?", opt.ConversationID).
		Where("app_id = ?", opt.AppID).
		First(&conversationsPO).Error; err != nil {
		// just not found is ok, not throw error
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return getConversationFromPO(&conversationsPO, 0), nil
}

type UpdateConversationOption struct {
	ID         string
	AppID      string
	AgentRunID *int64
}

func (c *ConversationDAOImpl) UpdateConversation(ctx context.Context, opt UpdateConversationOption) (*entity.Conversation, error) {
	if opt.ID == "" || opt.AppID == "" {
		return nil, errors.New("illegal argument")
	}
	if opt.AgentRunID == nil {
		return nil, errors.New("no update field")
	}
	updates := make(map[string]any)
	if opt.AgentRunID != nil {
		updates["agent_run_id"] = *opt.AgentRunID
	}
	if err := c.cli.NewRequest(ctx).
		Model(&po.ConversationsPO{}).
		Where("conversation_id = ?", opt.ID).
		Where("app_id = ?", opt.AppID).
		Updates(updates).Error; err != nil {
		return nil, err
	}

	return c.getConversation(ctx, GetConversationOption{
		ConversationID: opt.ID,
		AppID:          opt.AppID,
	}, true)
}

func getConversationFromPO(conversation *po.ConversationsPO, _ int) *entity.Conversation {
	res := &entity.Conversation{
		ID:                   conversation.ConversationID,
		AppID:                conversation.AppID,
		AgentRunID:           conversation.AgentRunID,
		OverrideModelConfigs: conversation.OverrideConfigs.Data(),
		Title:                conversation.Title,
		Inputs:               conversation.Inputs.Data(),
		Status:               entity.ConversationStatus(conversation.Status),
		UserName:             conversation.UserName,
		CreatedBy:            conversation.CreatedBy,
		CreatedAt:            conversation.CreatedAt,
		UpdatedAt:            conversation.UpdatedAt,
		InstanceId:           gptr.Indirect(conversation.InstanceID),
	}
	if conversation.Summary != nil {
		res.Summary = *conversation.Summary
	}

	return res
}
