package dal

import (
	"context"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gorm.io/datatypes"
	"gorm.io/plugin/dbresolver"

	"code.byted.org/devgpt/kiwis/bitscopilot/agent/app/dal/po"
	"code.byted.org/devgpt/kiwis/bitscopilot/agent/app/entity"
	"code.byted.org/devgpt/kiwis/port/db"
)

type AppDAO interface {
	CreateApp(ctx context.Context, opt CreateAppOption) (*entity.App, error)
	GetApp(ctx context.Context, opt GetAppOption) (*entity.App, error)
	ListApps(ctx context.Context, opt ListAppsOption) ([]*entity.App, int32, error)
	UpdateApp(ctx context.Context, opt UpdateAppOption) (*entity.App, error)
	GetAgentByID(ctx context.Context, agentID uint64) (*entity.Agent, error)
	GetAgentByIDs(ctx context.Context, agentIDs []uint64) ([]*entity.Agent, error)
	GetAgentConfigById(ctx context.Context, configId uint64) (*entity.AgentConfig, error)
	ListAgents(ctx context.Context) ([]*entity.Agent, error)
	UpdateAgentConfig(ctx context.Context, config *entity.AgentConfig) error
	UpdateAgent(ctx context.Context, agent *entity.Agent) error
}

var _ AppDAO = &DAO{}

type DAO struct {
	cli db.Client
}

func (d *DAO) ListAgents(ctx context.Context) ([]*entity.Agent, error) {
	agents := make([]*entity.Agent, 0)
	err := d.cli.NewRequest(ctx).Find(&agents).Error
	if err != nil {
		return nil, err
	}
	return agents, nil
}

func (d *DAO) UpdateAgentConfig(ctx context.Context, config *entity.AgentConfig) error {
	return d.cli.NewRequest(ctx).Save(config).Error
}

func (d *DAO) UpdateAgent(ctx context.Context, agent *entity.Agent) error {
	return d.cli.NewRequest(ctx).Save(agent).Error
}

func NewDAO(cli db.Client) *DAO {
	return &DAO{cli}
}

type CreateAppOption struct {
	BizID       string
	Name        string
	Description string
	Mode        entity.AppMode
	AppConfig   *entity.AppConfig
	Status      entity.AppStatus
	IsPublic    bool
	Debug       bool
	Trace       bool
	Creator     string
	Maintainers []string
}

func (d *DAO) CreateApp(ctx context.Context, opt CreateAppOption) (*entity.App, error) {
	newPO := &po.AppsPO{
		BizID:       opt.BizID,
		Name:        opt.Name,
		Description: &opt.Description,
		Mode:        string(opt.Mode),
		Status:      string(opt.Status),
		IsPublic:    opt.IsPublic,
		Debug:       opt.Debug,
		Trace:       opt.Trace,
		Maintainers: lo.ToPtr(strings.Join(opt.Maintainers, ",")),
		CreatedBy:   opt.Creator,
		CreatedAt:   time.Now(),
	}
	if opt.AppConfig != nil {
		newPO.AppConfig = datatypes.NewJSONType[po.AppsAppConfig](*opt.AppConfig)
	}
	if err := d.cli.NewRequest(ctx).Create(newPO).Error; err != nil {
		return nil, err
	}

	return getAppFromPO(newPO, 0), nil
}

func getAppFromPO(app *po.AppsPO, _ int) *entity.App {
	res := &entity.App{
		ID:        app.BizID,
		Name:      app.Name,
		Mode:      entity.AppMode(app.Mode),
		AppConfig: app.AppConfig.Data(),
		Status:    entity.AppStatus(app.Status),
		IsPublic:  app.IsPublic,
		Debug:     app.Debug,
		Trace:     app.Trace,
		CreatedBy: app.CreatedBy,
		CreatedAt: app.CreatedAt,
		UpdatedAt: app.UpdatedAt,
	}
	if app.Description != nil {
		res.Description = *app.Description
	}
	if app.Maintainers != nil {
		res.Maintainers = strings.Split(*app.Maintainers, ",")
	}

	return res
}

type GetAppOption struct {
	BizID string
}

func (d *DAO) GetApp(ctx context.Context, opt GetAppOption) (*entity.App, error) {
	return d.getApp(ctx, opt, false)
}

func (d *DAO) getApp(ctx context.Context, opt GetAppOption, sync bool) (*entity.App, error) {
	var app po.AppsPO
	request := d.cli.NewRequest(ctx)
	if sync {
		request = request.Clauses(dbresolver.Write)
	}
	if err := request.Where("biz_id = ?", opt.BizID).First(&app).Error; err != nil {
		return nil, err
	}
	return getAppFromPO(&app, 0), nil
}

type ListAppsOption struct {
	Offset int
	Limit  int
}

func (d *DAO) ListApps(ctx context.Context, opt ListAppsOption) ([]*entity.App, int32, error) {
	var (
		res   = make([]*po.AppsPO, 0)
		count int64

		req      = d.cli.NewRequest(ctx).Model(&po.AppsPO{})
		countReq = d.cli.NewRequest(ctx).Model(&po.AppsPO{})
	)

	req = req.Order("created_at desc").Offset(opt.Offset).Limit(opt.Limit)
	if err := req.Find(&res).Error; err != nil {
		return nil, 0, err
	}
	if err := countReq.Count(&count).Error; err != nil {
		return nil, 0, errors.WithMessage(err, "count apps failed")
	}

	return lo.Map(res, getAppFromPO), int32(count), nil
}

type UpdateAppOption struct {
	BizID       string
	Name        *string
	Description *string
	Mode        *entity.AppMode
	AppConfig   *entity.AppConfig
	Debug       *bool
	Trace       *bool
}

var (
	NoFiledToUpdate = errors.New("no field to update, which means nothing change")
)

func (d *DAO) UpdateApp(ctx context.Context, opt UpdateAppOption) (*entity.App, error) {
	if opt.BizID == "" {
		return nil, errors.New("biz id is empty")
	}
	if opt.Name == nil && opt.Description == nil && opt.Mode == nil && opt.AppConfig == nil && opt.Debug == nil && opt.Trace == nil {
		return nil, NoFiledToUpdate
	}

	updates := make(map[string]any)
	if opt.Name != nil {
		updates["name"] = *opt.Name
	}
	if opt.Description != nil {
		updates["description"] = *opt.Description
	}
	if opt.Mode != nil {
		updates["mode"] = *opt.Mode
	}
	if opt.AppConfig != nil {
		updates["app_config"] = datatypes.NewJSONType[po.AppsAppConfig](*opt.AppConfig)
	}
	if opt.Debug != nil {
		updates["debug"] = *opt.Debug
	}
	if opt.Trace != nil {
		updates["trace"] = *opt.Trace
	}

	if err := d.cli.NewRequest(ctx).
		Model(&po.AppsPO{}).
		Where("biz_id = ?", opt.BizID).
		Updates(updates).Error; err != nil {
		return nil, err
	}

	return d.getApp(ctx, GetAppOption{
		BizID: opt.BizID,
	}, true)
}

func (d *DAO) GetAgentByID(ctx context.Context, agentID uint64) (*entity.Agent, error) {
	agent := &entity.Agent{}
	err := d.cli.NewRequest(ctx).Where("id = ?", agentID).First(agent).Error
	if err != nil {
		return nil, err
	}
	return agent, nil
}

func (d *DAO) GetAgentConfigById(ctx context.Context, configId uint64) (*entity.AgentConfig, error) {
	agentConfig := &entity.AgentConfig{}
	err := d.cli.NewRequest(ctx).Where("id = ?", configId).First(agentConfig).Error
	if err != nil {
		return nil, err
	}
	return agentConfig, nil
}

func (d *DAO) GetAgentByIDs(ctx context.Context, agentIDs []uint64) ([]*entity.Agent, error) {
	agents := make([]*entity.Agent, 0)
	err := d.cli.NewRequest(ctx).Where("id IN (?)", agentIDs).Find(&agents).Error
	if err != nil {
		return nil, err
	}
	return agents, nil
}
