package defaultagentictool

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNode(t *testing.T) {
	t.Run("NewNode", func(t *testing.T) {
		node := NewNode("root")
		assert.Equal(t, "root", node.Name)
		assert.NotNil(t, node.Children)
		assert.Equal(t, 0, len(node.Children))
	})

	t.Run("AddPath single level", func(t *testing.T) {
		root := NewNode("root")
		root.AddPath("file1.txt")

		assert.Contains(t, root.Children, "file1.txt")
		assert.Equal(t, "file1.txt", root.Children["file1.txt"].Name)
		assert.Equal(t, 0, len(root.Children["file1.txt"].Children))
	})

	t.Run("AddPath multi-level", func(t *testing.T) {
		root := NewNode("root")
		root.AddPath("dir1/file1.txt")

		assert.Contains(t, root.Children, "dir1")
		assert.Contains(t, root.Children["dir1"].Children, "file1.txt")
		assert.Equal(t, "dir1", root.Children["dir1"].Name)
		assert.Equal(t, "file1.txt", root.Children["dir1"].Children["file1.txt"].Name)
	})

	t.Run("BuildTreeString simple structure", func(t *testing.T) {
		root := NewNode("root")
		root.AddPath("folder1/file1.txt")
		root.AddPath("folder1/file2.txt")
		root.AddPath("folder2/subfolder1/file3.txt")
		root.AddPath("folder3")

		expected := `root
- folder1
  - file1.txt
  - file2.txt
- folder2
  - subfolder1
    - file3.txt
- folder3
`
		assert.Equal(t, expected, root.BuildTreeString("", true, false, -1))
	})

	t.Run("BuildTreeString single level", func(t *testing.T) {
		root := NewNode("root")
		root.AddPath("file1.txt")
		root.AddPath("file2.txt")

		expected := `root
- file1.txt
- file2.txt
`
		assert.Equal(t, expected, root.BuildTreeString("", true, false, -1))
	})

	t.Run("BuildTreeString complex structure", func(t *testing.T) {
		root := NewNode("root")
		root.AddPath("dir1/subdir1/file1.txt")
		root.AddPath("dir1/subdir1/file2.txt")
		root.AddPath("dir1/subdir2/file3.txt")
		root.AddPath("dir2/subdir3/file4.txt")
		root.AddPath("dir2/file5.txt")
		root.AddPath("dir3/subdir4/subsubdir1/file6.txt")
		root.AddPath("dir3/subdir4/subsubdir2/file7.txt")
		root.AddPath("dir3/file8.txt")

		expected := `root
- dir1
  - subdir1
    - file1.txt
    - file2.txt
  - subdir2
    - file3.txt
- dir2
  - file5.txt
  - subdir3
    - file4.txt
- dir3
  - file8.txt
  - subdir4
    - subsubdir1
      - file6.txt
    - subsubdir2
      - file7.txt
`
		assert.Equal(t, expected, root.BuildTreeString("", true, false, -1))
	})
}
