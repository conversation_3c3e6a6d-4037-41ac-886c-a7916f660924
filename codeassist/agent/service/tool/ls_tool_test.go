package tool

import (
	"context"
	"errors"
	"testing"

	"code.byted.org/gopkg/logs/v2"
	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	"go.uber.org/mock/gomock"

	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	mockService "code.byted.org/devgpt/kiwis/codeassist/agent/mock/service"
	"code.byted.org/devgpt/kiwis/codeassist/agent/service"
	sandboxService "code.byted.org/devgpt/kiwis/codeassist/sandbox/service"
	"code.byted.org/devgpt/kiwis/lib/metrics"
)

func TestLSTool_Name(t *testing.T) {
	mockey.PatchConvey("TestLSTool_Name", t, func() {
		lsTool := NewLSTool(nil)
		convey.So(lsTool.Name(), convey.ShouldEqual, "ls")
	})
}

func TestLSTool_Description(t *testing.T) {
	mockey.PatchConvey("TestLSTool_Description", t, func() {
		lsTool := NewLSTool(nil)
		convey.So(lsTool.Description(), convey.ShouldEqual, "A tool to list files.")
	})
}

func TestLSTool_Run(t *testing.T) {
	mockey.PatchConvey("TestLSTool_Run", t, func() {
		ctrl := gomock.NewController(t)
		mockAgentRunService := mockService.NewMockAgentRunService(ctrl)
		lsTool := NewLSTool(mockAgentRunService)

		mockey.PatchConvey("Input params is nil", func() {
			// 模拟 metrics.CodeAssistMetric.ReportAgentToolCallMetrics
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, toolCallStatus metrics.AgentToolCallStatus) {
				convey.So(toolCallStatus, convey.ShouldEqual, metrics.AgentToolCallErr)
			}).Build()

			result, err := lsTool.Run(context.Background(), service.ToolContext{}, nil)
			convey.So(result, convey.ShouldNotBeNil)
			convey.So(err, convey.ShouldBeNil)
			lsResult, ok := result.(*service.LSResult)
			convey.So(ok, convey.ShouldBeTrue)
			convey.So(lsResult.Error, convey.ShouldEqual, "ls tool input params is null")
			convey.So(lsResult.Display, convey.ShouldEqual, "ls tool input params is null")
		})

		mockey.PatchConvey("Path is empty", func() {
			// 模拟 metrics.CodeAssistMetric.ReportAgentToolCallMetrics
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, toolCallStatus metrics.AgentToolCallStatus) {
				convey.So(toolCallStatus, convey.ShouldEqual, metrics.AgentToolCallErr)
			}).Build()

			result, err := lsTool.Run(context.Background(), service.ToolContext{}, map[string]string{
				"path":        "",
				"show_hidden": "false",
			})
			convey.So(result, convey.ShouldNotBeNil)
			convey.So(err, convey.ShouldBeNil)
			lsResult, ok := result.(*service.LSResult)
			convey.So(ok, convey.ShouldBeTrue)
			convey.So(lsResult.Error, convey.ShouldEqual, "ls tool input params field `path` is empty")
			convey.So(lsResult.Display, convey.ShouldEqual, "ls tool input params field `path` is empty")
		})

		mockey.PatchConvey("GetAgentRun error", func() {
			// 模拟 metrics.CodeAssistMetric.ReportAgentToolCallMetrics
			var status metrics.AgentToolCallStatus
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, toolCallStatus metrics.AgentToolCallStatus) {
				status = toolCallStatus
			}).Build()

			mockAgentRunService.EXPECT().GetAgentRun(gomock.Any(), gomock.Any()).Return(nil, errors.New("get agent run error"))

			result, err := lsTool.Run(context.Background(), service.ToolContext{AgentRunID: "test-id"}, map[string]string{
				"path":        "/test/path",
				"show_hidden": "false",
			})
			convey.So(result, convey.ShouldBeNil)
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldEqual, "get agent run error")
			convey.So(status, convey.ShouldEqual, metrics.AgentToolCallErr)
		})

		mockey.PatchConvey("AgentRun not ready", func() {
			// 模拟 metrics.CodeAssistMetric.ReportAgentToolCallMetrics
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, toolCallStatus metrics.AgentToolCallStatus) {
				convey.So(toolCallStatus, convey.ShouldEqual, metrics.AgentToolCallErr)
			}).Build()

			mockAgentRunService.EXPECT().GetAgentRun(gomock.Any(), gomock.Any()).Return(&entity.AgentRun{Status: entity.AgentRunStatusCreated}, nil)
			result, err := lsTool.Run(context.Background(), service.ToolContext{AgentRunID: "test-id"}, map[string]string{
				"path":        "/test/path",
				"show_hidden": "false",
			})
			convey.So(result, convey.ShouldBeNil)
			convey.So(err, convey.ShouldEqual, service.ToolCallNotReadyError)

		})

		mockey.PatchConvey("ExecuteCode returns general error", func() {
			// 模拟 metrics.CodeAssistMetric.ReportAgentToolCallMetrics
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, toolCallStatus metrics.AgentToolCallStatus) {
				convey.So(toolCallStatus, convey.ShouldEqual, metrics.AgentToolCallErr)
			}).Build()

			mockAgentRunService.EXPECT().GetAgentRun(gomock.Any(), gomock.Any()).Return(&entity.AgentRun{Status: entity.AgentRunStatusRunning, UserID: 123}, nil)
			mockAgentRunService.EXPECT().ExecuteCode(gomock.Any(), gomock.Any()).Return(nil, errors.New("execute code error"))

			// Mock logs.V1.CtxWarn to avoid actual logging during test
			mockey.Mock(logs.V1.CtxWarn).Return().Build()

			result, err := lsTool.Run(context.Background(), service.ToolContext{AgentRunID: "test-id", ConversationID: "conv-id"}, map[string]string{
				"path":        "/test/path",
				"show_hidden": "false",
			})
			convey.So(result, convey.ShouldBeNil)
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldEqual, "execute code error")

		})

		mockey.PatchConvey("ExecuteCode returns sandbox error", func() {
			// 模拟 metrics.CodeAssistMetric.ReportAgentToolCallMetrics
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, toolCallStatus metrics.AgentToolCallStatus) {
				convey.So(toolCallStatus, convey.ShouldEqual, metrics.AgentToolCallErr)
			}).Build()

			// 创建一个 SandboxError
			sandboxErr := &sandboxService.SandboxError{Message: "sandbox execution error"}

			mockAgentRunService.EXPECT().GetAgentRun(gomock.Any(), gomock.Any()).Return(&entity.AgentRun{Status: entity.AgentRunStatusRunning, UserID: 123}, nil)
			mockAgentRunService.EXPECT().ExecuteCode(gomock.Any(), gomock.Any()).Return(nil, sandboxErr)

			// Mock logs.V1.CtxWarn to avoid actual logging during test
			mockey.Mock(logs.V1.CtxWarn).Return().Build()

			result, err := lsTool.Run(context.Background(), service.ToolContext{AgentRunID: "test-id", ConversationID: "conv-id"}, map[string]string{
				"path":        "/test/path",
				"show_hidden": "false",
			})
			convey.So(result, convey.ShouldBeNil)
			convey.So(err, convey.ShouldNotBeNil)
			_, isSandboxErr := err.(*sandboxService.SandboxError)
			convey.So(isSandboxErr, convey.ShouldBeTrue)

		})

		mockey.PatchConvey("Empty RunResult", func() {
			// 模拟 metrics.CodeAssistMetric.ReportAgentToolCallMetrics
			var status metrics.AgentToolCallStatus
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, toolCallStatus metrics.AgentToolCallStatus) {
				status = toolCallStatus
			}).Build()

			mockAgentRunService.EXPECT().GetAgentRun(gomock.Any(), gomock.Any()).Return(&entity.AgentRun{Status: entity.AgentRunStatusRunning, UserID: 123}, nil)
			mockAgentRunService.EXPECT().ExecuteCode(gomock.Any(), gomock.Any()).Return(&sandboxService.ExecuteResponse{RunResult: []*sandboxService.ExecuteCommandResult{}}, nil)

			result, err := lsTool.Run(context.Background(), service.ToolContext{AgentRunID: "test-id", ConversationID: "conv-id"}, map[string]string{
				"path":        "/test/path",
				"show_hidden": "false",
			})
			convey.So(result, convey.ShouldNotBeNil)
			convey.So(err, convey.ShouldBeNil)
			lsResult, ok := result.(*service.LSResult)
			convey.So(ok, convey.ShouldBeTrue)
			convey.So(lsResult.Output, convey.ShouldEqual, "")
			convey.So(lsResult.Display, convey.ShouldEqual, "")
			convey.So(status, convey.ShouldEqual, metrics.AgentToolCallSuccess)
		})

		mockey.PatchConvey("Success with normal output", func() {
			// 模拟 metrics.CodeAssistMetric.ReportAgentToolCallMetrics
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, toolCallStatus metrics.AgentToolCallStatus) {
				convey.So(toolCallStatus, convey.ShouldEqual, metrics.AgentToolCallSuccess)
			}).Build()

			mockAgentRunService.EXPECT().GetAgentRun(gomock.Any(), gomock.Any()).Return(&entity.AgentRun{Status: entity.AgentRunStatusRunning, UserID: 123}, nil)
			mockAgentRunService.EXPECT().ExecuteCode(gomock.Any(), gomock.Any()).Return(&sandboxService.ExecuteResponse{RunResult: []*sandboxService.ExecuteCommandResult{
				{Content: "file1.txt\nfile2.txt\ndir1/\n", Type: sandboxService.CodeOutputTypeStdout},
			}}, nil)

			result, err := lsTool.Run(context.Background(), service.ToolContext{AgentRunID: "test-id", ConversationID: "conv-id"}, map[string]string{
				"path":        "/test/path",
				"show_hidden": "false",
			})
			convey.So(result, convey.ShouldNotBeNil)
			convey.So(err, convey.ShouldBeNil)
			lsResult, ok := result.(*service.LSResult)
			convey.So(ok, convey.ShouldBeTrue)
			convey.So(lsResult.Output, convey.ShouldEqual, "file1.txt\nfile2.txt\ndir1/\n")
			convey.So(lsResult.Display, convey.ShouldEqual, "file1.txt\nfile2.txt\ndir1/\n")

		})

		mockey.PatchConvey("Success with too many files", func() {
			// 模拟 metrics.CodeAssistMetric.ReportAgentToolCallMetrics
			var status metrics.AgentToolCallStatus
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, toolCallStatus metrics.AgentToolCallStatus) {
				status = toolCallStatus
			}).Build()

			mockAgentRunService.EXPECT().GetAgentRun(gomock.Any(), gomock.Any()).Return(&entity.AgentRun{Status: entity.AgentRunStatusRunning, UserID: 123}, nil)

			// Generate a large file list that exceeds MAX_LS_FILE_NUM
			fileList := ""
			for i := 0; i < MAX_LS_FILE_NUM+100; i++ {
				fileList += "file" + string(rune('0'+i%10)) + ".txt\n"
			}

			mockAgentRunService.EXPECT().ExecuteCode(gomock.Any(), gomock.Any()).Return(&sandboxService.ExecuteResponse{RunResult: []*sandboxService.ExecuteCommandResult{
				{Content: fileList, Type: sandboxService.CodeOutputTypeStdout},
			}}, nil)

			result, err := lsTool.Run(context.Background(), service.ToolContext{AgentRunID: "test-id", ConversationID: "conv-id"}, map[string]string{
				"path":        "/test/path",
				"show_hidden": "false",
			})
			convey.So(result, convey.ShouldNotBeNil)
			convey.So(err, convey.ShouldBeNil)
			lsResult, ok := result.(*service.LSResult)
			convey.So(ok, convey.ShouldBeTrue)

			// Check that the output is cropped
			expectedPrefix := "该目录下有超过 500 个文件，请给出更具体的路径来探索嵌套目录，前 500 个文件和目录如下:"
			convey.So(lsResult.Output, convey.ShouldContainSubstring, expectedPrefix)
			convey.So(lsResult.Display, convey.ShouldContainSubstring, expectedPrefix)
			convey.So(status, convey.ShouldEqual, metrics.AgentToolCallSuccess)
		})

		mockey.PatchConvey("Success with show_hidden=true", func() {
			// 模拟 metrics.CodeAssistMetric.ReportAgentToolCallMetrics
			var status metrics.AgentToolCallStatus
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, toolCallStatus metrics.AgentToolCallStatus) {
				status = toolCallStatus
			}).Build()

			mockAgentRunService.EXPECT().GetAgentRun(gomock.Any(), gomock.Any()).Return(&entity.AgentRun{Status: entity.AgentRunStatusRunning, UserID: 123}, nil)
			mockAgentRunService.EXPECT().ExecuteCode(gomock.Any(), gomock.Any()).Do(func(_ context.Context, opt *service.ExecuteCodeOpt) {
				// Verify that the command includes -a flag
				convey.So(opt.Code, convey.ShouldEqual, "tree -a /test/path")
			}).Return(&sandboxService.ExecuteResponse{RunResult: []*sandboxService.ExecuteCommandResult{
				{Content: ".hidden\nfile1.txt\nfile2.txt\n", Type: sandboxService.CodeOutputTypeStdout},
			}}, nil)

			result, err := lsTool.Run(context.Background(), service.ToolContext{AgentRunID: "test-id", ConversationID: "conv-id"}, map[string]string{
				"path":        "/test/path",
				"show_hidden": "true",
			})
			convey.So(result, convey.ShouldNotBeNil)
			convey.So(err, convey.ShouldBeNil)
			lsResult, ok := result.(*service.LSResult)
			convey.So(ok, convey.ShouldBeTrue)
			convey.So(lsResult.Output, convey.ShouldEqual, ".hidden\nfile1.txt\nfile2.txt\n")
			convey.So(lsResult.Display, convey.ShouldEqual, ".hidden\nfile1.txt\nfile2.txt\n")
			convey.So(status, convey.ShouldEqual, metrics.AgentToolCallSuccess)
		})

		mockey.PatchConvey("Success with stderr output", func() {
			// 模拟 metrics.CodeAssistMetric.ReportAgentToolCallMetrics
			var status metrics.AgentToolCallStatus
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, toolCallStatus metrics.AgentToolCallStatus) {
				status = toolCallStatus
			}).Build()

			mockAgentRunService.EXPECT().GetAgentRun(gomock.Any(), gomock.Any()).Return(&entity.AgentRun{Status: entity.AgentRunStatusRunning, UserID: 123}, nil)
			mockAgentRunService.EXPECT().ExecuteCode(gomock.Any(), gomock.Any()).Return(&sandboxService.ExecuteResponse{RunResult: []*sandboxService.ExecuteCommandResult{
				{Content: "tree: error: No such file or directory", Type: sandboxService.CodeOutputTypeStderr},
			}}, nil)

			result, err := lsTool.Run(context.Background(), service.ToolContext{AgentRunID: "test-id", ConversationID: "conv-id"}, map[string]string{
				"path":        "/nonexistent/path",
				"show_hidden": "false",
			})
			convey.So(result, convey.ShouldNotBeNil)
			convey.So(err, convey.ShouldBeNil)
			lsResult, ok := result.(*service.LSResult)
			convey.So(ok, convey.ShouldBeTrue)
			convey.So(lsResult.Error, convey.ShouldEqual, "tree: error: No such file or directory")
			convey.So(lsResult.Display, convey.ShouldEqual, "tree: error: No such file or directory")
			convey.So(status, convey.ShouldEqual, metrics.AgentToolRunErr)
		})
	})
}

func TestLSTool_FormatResult(t *testing.T) {
	mockey.PatchConvey("TestLSTool_FormatResult", t, func() {
		lsTool := NewLSTool(nil)

		mockey.PatchConvey("Format LSResult", func() {
			lsResult := &service.LSResult{
				BaseResult: service.BaseResult{
					Output:  "file1.txt\nfile2.txt",
					Error:   "",
					Display: "file1.txt\nfile2.txt",
				},
			}

			formatted := lsTool.FormatResult(lsResult)
			convey.So(formatted, convey.ShouldContainSubstring, "file1.txt\nfile2.txt")
		})

		mockey.PatchConvey("Format non-LSResult", func() {
			formatted := lsTool.FormatResult("not an LSResult")
			convey.So(formatted, convey.ShouldEqual, "")
		})
	})
}

func TestLSTool_InputSpec(t *testing.T) {
	mockey.PatchConvey("TestLSTool_InputSpec", t, func() {
		lsTool := NewLSTool(nil)
		spec := lsTool.InputSpec()

		convey.So(spec.Name, convey.ShouldEqual, "ls tool input schema")
		convey.So(spec.Type, convey.ShouldEqual, "object")
		convey.So(spec.Description, convey.ShouldEqual, "The path of the file to ls.")
		convey.So(spec.Properties, convey.ShouldNotBeNil)
		convey.So(len(spec.Properties), convey.ShouldEqual, 2)

		// Check path property
		pathProp, ok := spec.Properties["path"]
		convey.So(ok, convey.ShouldBeTrue)
		convey.So(pathProp.Name, convey.ShouldEqual, "path")
		convey.So(pathProp.Type, convey.ShouldEqual, "string")
		convey.So(pathProp.Required, convey.ShouldBeTrue)

		// Check show_hidden property
		showHiddenProp, ok := spec.Properties["show_hidden"]
		convey.So(ok, convey.ShouldBeTrue)
		convey.So(showHiddenProp.Name, convey.ShouldEqual, "show_hidden")
		convey.So(showHiddenProp.Type, convey.ShouldEqual, "bool")
		convey.So(showHiddenProp.Required, convey.ShouldBeFalse)
		convey.So(showHiddenProp.Default, convey.ShouldEqual, false)
	})
}
