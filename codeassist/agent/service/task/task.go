package task

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	samanthaevent "code.byted.org/flow/samantha_nova/pkg/client/pb_gen/event"
	fornaxtrace "code.byted.org/flowdevops/fornax_sdk/infra/ob"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/overpass/flowpc_chat_eventstream/kitex_gen/flowpc_chat_eventstream"
	"code.byted.org/overpass/flowpc_chat_eventstream/kitex_gen/flowpc_chat_eventstream/eventstreamservice"
	"code.byted.org/samanthapkg/eventstream"
	"code.byted.org/samanthapkg/jobschedule"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	poolsdk "github.com/sourcegraph/conc/pool"

	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/codeassist"
	"code.byted.org/devgpt/kiwis/codeassist/agent/agents"
	"code.byted.org/devgpt/kiwis/codeassist/agent/dal"
	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	"code.byted.org/devgpt/kiwis/codeassist/agent/pack"
	"code.byted.org/devgpt/kiwis/codeassist/agent/service"
	chatentity "code.byted.org/devgpt/kiwis/codeassist/chat/entity"
	contextentity "code.byted.org/devgpt/kiwis/codeassist/context/entity"
	journalentity "code.byted.org/devgpt/kiwis/codeassist/journal/entity"
	copilotentity "code.byted.org/devgpt/kiwis/copilotstack/entity"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/devgpt/kiwis/lib/stream"
	"code.byted.org/devgpt/kiwis/lib/tcc"
	"code.byted.org/devgpt/kiwis/lib/util"
	"code.byted.org/devgpt/kiwis/port/fornax"
)

var _ service.TaskService = &Service{}

type Service struct {
	FileConcurrencyConfig *tcc.GenericConfig[config.CodeAssistFileConcurrencyConfig]

	TaskDAO     dal.TaskDAO
	AgentRunDAO dal.AgentRunDAO

	FornaxClient fornax.Client

	AgentFactoryService service.AgentFactoryService
	AgentRunService     service.AgentRunService
	MemoryService       service.MemoryService
	DoubaoProtocol      service.DoubaoProtocol
	ArtifactsService    service.ArtifactsService
	EventStreamService  eventstreamservice.Client
}

func (s *Service) NewTask(ctx context.Context, chatContext *chatentity.ChatContext) (string, error) {
	task := &entity.Task{
		UserID:         strconv.FormatInt(chatContext.UserID, 10),
		ConversationID: chatContext.UserMessage.ConversationID,
		MessageID:      chatContext.UserMessage.MessageID,
		Upstream:       entity.TaskUpstreamDoubao,
		Status:         entity.TaskStatusCreated,
		Stage:          entity.TaskStageBegin,
	}

	taskID, err := s.TaskDAO.Create(ctx, task)
	if err != nil {
		logs.V1.CtxError(ctx, "[Run] create task failed. err: %v", err)
		return "", err
	}
	return taskID, nil
}

func (s *Service) RunWithScheduler(ctx context.Context, opt service.RunTaskOption) (err error) {
	ctx = context.WithoutCancel(ctx)
	if err = jobschedule.AsyncRunTask(ctx, "codeassist_conf", opt.TaskID, s.Run, jobschedule.WithWrapCreateBizParam(opt)); err != nil {
		logs.V1.CtxError(ctx, "[RunWithScheduler] run task failed. err: %v", err)
		return err
	}
	return nil
}

func (s *Service) RecoverFromSchedulerCallBack(ctx context.Context, req *codeassist.ExecuteJobCallbackReq) (err error) {
	// 反序列化入参
	opt := service.RunTaskOption{}
	err = json.Unmarshal(req.JobParamBytes, &opt)
	if err != nil {
		return err
	}

	stage, err := s.TaskDAO.GetStageByID(ctx, opt.TaskID)
	if err != nil {
		logs.V1.CtxError(ctx, "[RecoverFromSchedulerCallBack] get task stage failed. err: %v", err)
		return err
	}
	if stage != entity.TaskStageAsync {
		logs.V1.CtxWarn(ctx, "[RecoverFromSchedulerCallBack] task in sync stage, not need to recover.")
		return nil
	}

	opt.NeedRecover = true
	paramBytes, err := json.Marshal(opt)
	if err != nil {
		logs.V1.CtxError(ctx, "[RecoverFromSchedulerCallBack] marshal param failed. err: %v", err)
		return err
	}
	req.JobParamBytes = paramBytes
	if err = jobschedule.AsyncRunCallBack(ctx, req, s.Run); err != nil {
		logs.V1.CtxError(ctx, "[RecoverFromSchedulerCallBack] recover task failed. err: %v", err)
		return err
	}
	return nil
}

func (s *Service) InterruptTask(ctx context.Context, messageID string, userID string) error {
	err := s.TaskDAO.UpdateStageByMessageIDAndUser(ctx, messageID, userID, entity.TaskStageInterrupted)
	if err != nil {
		if errors.Is(err, dal.UpdateNothingError) {
			logs.V1.CtxError(ctx, "[InterruptTask] update nothing failed. err: %v", err)
			return service.ErrTaskNotFound
		}
		return err
	}
	logs.V1.CtxInfo(ctx, "[InterruptTask] update success")
	return nil
}

func (s *Service) Run(ctx context.Context, opt service.RunTaskOption) (err error) {
	startTime := time.Now()
	_ = metrics.CodeAssistMetric.TaskBeginThroughput.WithTags(&metrics.CodeAssistTaskBeginTag{
		AgentName: opt.AgentName,
	}).Add(1)

	span, ctx, err := s.FornaxClient.StartQuerySpan(ctx, journalentity.SpanTask, fornaxtrace.AsyncChildSpan())
	if err != nil {
		logs.V1.CtxWarn(ctx, "[Task Run] start query span error: %s", err)
	}

	logs.V1.CtxInfo(ctx, "[Run] start task: %s", util.MarshalStruct(opt))
	// 设置task状态
	if opt.NeedRecover {
		s.updateTaskStatus(ctx, opt, entity.TaskStatusRunning, entity.TaskStatusRecovering)
	} else {
		s.updateTaskStatus(ctx, opt, entity.TaskStatusCreated, entity.TaskStatusInitializing)
	}

	// 1. 创建AgentRun
	reportTaskStepMetricsFunc := metrics.CodeAssistMetric.ReportTaskStepMetrics()
	logs.V1.CtxInfo(ctx, "[Run] begin to create or get agent run")
	createOrGetOption := &service.CreateOrGetOption{
		TaskID:         opt.TaskID,
		UserID:         opt.UserID,
		ConversationID: opt.UserMessage.ConversationID,
		AgentID:        opt.AgentName,
		AgentVersion:   opt.AgentVersion,
	}
	agentRun, isNew, err := s.AgentRunService.CreateOrGet(ctx, createOrGetOption)
	if err != nil {
		logs.V1.CtxError(ctx, "[Run] create or get agent run failed. err: %v", err)
		reportTaskStepMetricsFunc(opt.AgentName, metrics.CodeAssistTaskStepCreateSandbox, true)
		return err
	}
	reportTaskStepMetricsFunc(opt.AgentName, metrics.CodeAssistTaskStepCreateSandbox, false)

	// 2. upload用户输入 && 历史产物
	reportTaskStepMetricsFunc = metrics.CodeAssistMetric.ReportTaskStepMetrics()
	// 2.1 过滤非Agent历史
	filteredHistoryMessage, err := s.filterHistoryMessages(ctx, opt)
	if err != nil {
		logs.V1.CtxError(ctx, "[Run] filter history messages failed. err: %v", err)
		return err
	}
	opt.HistoryMessages = filteredHistoryMessage

	// 2.2 解析用户输入和历史产物
	logs.V1.CtxInfo(ctx, "[Run] begin to parse input and artifact")
	parseResult := s.parseUserInputAndHistory(ctx, opt)
	parseResult.WorkspaceInfo.WorkspaceDirectory = agentRun.WorkspaceDir
	reportTaskStepMetricsFunc(opt.AgentName, metrics.CodeAssistTaskStepParseInputAndHistory, false)

	// 2.3 如果sandbox是新建的，需要上传历史文件; 否则需要上传用户引用的文件。上传完成后更新AgentRun状态
	go func() {
		reportTaskStepMetricsFunc = metrics.CodeAssistMetric.ReportTaskStepMetrics()
		logs.V1.CtxInfo(ctx, "[Run] begin to upload user input, history %s to upload", lo.Ternary(isNew, "need", "need not"))
		// 默认会重复上传历史文件，防止沙盒中的文件丢失
		err = s.batchUploadFilesToAgentRun(ctx, opt, parseResult, true)
		if err != nil {
			logs.V1.CtxError(ctx, "[Run] batch upload files failed. err: %v", err)
		}

		err = s.AgentRunService.UpdateAgentRunStatus(ctx, agentRun.UUID, entity.AgentRunStatusRunning)
		if err != nil {
			logs.V1.CtxError(ctx, "[Run] update agent run status failed. err: %v", err)
		}
		logs.V1.CtxInfo(ctx, "[Run] upload user input and history done")
		reportTaskStepMetricsFunc(opt.AgentName, metrics.CodeAssistTaskStepFileUpload, false)
	}()

	// 3. 构建Agent
	reportTaskStepMetricsFunc = metrics.CodeAssistMetric.ReportTaskStepMetrics()
	logs.V1.CtxInfo(ctx, "[Run] begin to build agent")
	agent, agentRunContext, err := s.buildAgent(ctx, opt, agentRun, parseResult.WorkspaceInfo)
	if err != nil {
		logs.V1.CtxError(ctx, "[Run] build agent failed. err: %v", err)
		reportTaskStepMetricsFunc(opt.AgentName, metrics.CodeAssistTaskStepBuildAgent, true)
		return err
	}

	// 3.1 创建控制信号和事件通道
	controlChan := make(chan agents.ControlSignal, 1)
	eventSend, eventRcv := stream.NewChannel[*entity.AgentEvent](200)

	agentRunContext.ControlChan = controlChan
	agentRunContext.EventChan = eventSend
	logs.V1.CtxInfo(ctx, "[Run] build agent finished")
	reportTaskStepMetricsFunc(opt.AgentName, metrics.CodeAssistTaskStepBuildAgent, false)

	// 4. 创建异步stream
	logs.V1.CtxInfo(ctx, "[Run] create event stream")
	writeStream, err := eventstream.NewWriteStream(ctx, eventstream.WriteStreamContext{
		TaskID:  opt.TaskID,
		UserID:  opt.UserID,
		BizType: flowpc_chat_eventstream.BizType_CodeAssist,
	})

	// 5. 构建doubao event ctx
	reportTaskStepMetricsFunc = metrics.CodeAssistMetric.ReportTaskStepMetrics()
	doubaoEventContext, err := s.buildDoubaoEventCtx(ctx, opt, agentRun, writeStream)
	if err != nil {
		logs.V1.CtxError(ctx, "[Run] build doubao event ctx failed. err: %v", err)
		reportTaskStepMetricsFunc(opt.AgentName, metrics.CodeAssistTaskStepRecoverTask, true)
		return err
	}
	logs.V1.CtxInfo(ctx, "[Run] build doubao event ctx done")
	reportTaskStepMetricsFunc(opt.AgentName, metrics.CodeAssistTaskStepRecoverTask, false)

	// 6. 启动Agent
	reportTaskStepMetricsFunc = metrics.CodeAssistMetric.ReportTaskStepMetrics()
	logs.V1.CtxInfo(ctx, "[Run] run agent")
	go agent.Run(ctx, agentRunContext)

	// 7. 启动timer，定期检测task是否被中断
	timer := time.NewTicker(2 * time.Second)
	defer timer.Stop()

	taskStatus := metrics.CodeAssistTaskStatusFailed
	defer func() {
		// 判断任务状态
		if taskStatus == metrics.CodeAssistTaskStatusFailed {
			if err == nil || errors.Is(err, service.ErrAgentFinished) {
				taskStatus = metrics.CodeAssistTaskStatusSuccess
			}
		}

		// 发送终止消息
		err = writeStream.WriteEvent(ctx, s.buildEndEvent(doubaoEventContext, opt, taskStatus))
		if err != nil {
			logs.V1.CtxError(ctx, "[Run] write finish event failed. err: %v", err)
		}

		// 关闭异步通道
		_ = writeStream.Close(ctx)

		// 通知离线存储
		err = s.offlineStoreMessage(ctx, opt, taskStatus)
		if err != nil {
			logs.V1.CtxError(ctx, "[Run] offline message failed. err: %v", err)
		}

		// 修改agent run状态为完成
		err = s.AgentRunService.UpdateAgentRunStatus(ctx, agentRun.UUID, entity.AgentRunStatusCompleted)
		if err != nil {
			logs.V1.CtxError(ctx, "[Run] update agent run status failed. err: %v", err)
		}

		// 通知agent停止
		controlChan <- agents.ControlSignalStop

		// 关闭通道
		if opt.Send != nil && !opt.Send.Stopped() {
			opt.Send.Close()
		}
		if !eventRcv.Stopped() {
			eventRcv.Close()
		}

		// 上报trace
		taskRunInfo := map[string]interface{}{}
		taskRunInfo["task_status"] = taskStatus
		span.SetTag(ctx, map[string]interface{}{
			"task_id":       opt.TaskID,
			"task_run_info": taskRunInfo,
		})
		span.Finish(ctx)

		// 上报指标
		lastTokenLatency := time.Since(startTime).Milliseconds()
		_ = metrics.CodeAssistMetric.TaskLastTokenLatency.WithTags(&metrics.CodeAssistTaskLastTokenTag{
			AgentName: opt.AgentName,
			Status:    taskStatus,
		}).Observe(float64(lastTokenLatency))

		errMsg := ""
		if err != nil {
			errMsg = err.Error()
		}
		_ = metrics.CodeAssistMetric.TaskCompleteThroughput.WithTags(&metrics.CodeAssistTaskCompleteTag{
			AgentName:   opt.AgentName,
			Status:      taskStatus,
			ErrorReason: errMsg,
		}).Add(1)
		reportTaskStepMetricsFunc(opt.AgentName, metrics.CodeAssistTaskStepAgentRun, taskStatus == metrics.CodeAssistTaskStatusFailed)
	}()

	// 8. 开始接收Agent数据
	var doubaoEvent *entity.DoubaoEvent
	firstToken := true
	for {
		select {
		case event := <-eventRcv.DataChannel:
			doubaoEvent, err = s.DoubaoProtocol.Transfer(ctx, event, doubaoEventContext)
			if err != nil {
				if errors.Is(err, service.ErrAgentFinished) {
					logs.V1.CtxInfo(ctx, "[Run] agent run finished.")
					return nil
				}

				logs.V1.CtxError(ctx, "[Run] transfer event failed. err: %v", err)
				if errors.Is(err, service.ErrReviewNotPass) {
					resetEvent := s.DoubaoProtocol.PackReviewUnpassedEvent(ctx, doubaoEventContext)
					s.sendDoubaoEvent(ctx, opt, resetEvent, doubaoEventContext, writeStream)
				}
				return err
			}

			// 如果有消息，优先发消息
			if !doubaoEvent.HasEvent() {
				continue
			}

			if firstToken {
				firstTokenLatency := time.Since(startTime).Milliseconds()
				_ = metrics.CodeAssistMetric.TaskFirstTokenLatency.WithTags(&metrics.CodeAssistTaskFirstTokenTag{
					AgentName: opt.AgentName,
				}).Observe(float64(firstTokenLatency))
				firstToken = false
			}

			s.sendDoubaoEvent(ctx, opt, doubaoEvent, doubaoEventContext, writeStream)
		case <-timer.C:
			_ = metrics.CodeAssistMetric.TaskQueryThroughput.WithTags(&metrics.CodeAssistTaskQueryTag{
				AgentName: opt.AgentName,
				TableName: "task",
			}).Add(1)
			stage, daoErr := s.TaskDAO.GetStageByID(ctx, opt.TaskID)
			if daoErr != nil {
				logs.V1.CtxError(ctx, "[Run] get status failed. err: %v", daoErr)
			}
			if stage != entity.TaskStageInterrupted {
				continue
			}

			// 发送取消消息
			cancelEvent := s.DoubaoProtocol.PackCancelEvent(ctx, doubaoEventContext)
			s.sendDoubaoEvent(ctx, opt, cancelEvent, doubaoEventContext, writeStream)
			taskStatus = metrics.CodeAssistTaskStatusCanceled
			logs.V1.CtxInfo(ctx, "[Run] task %s is interrupted.", opt.TaskID)
			return nil
		}
	}
}

func (s *Service) updateTaskStatus(ctx context.Context, opt service.RunTaskOption, oldStatus, newStatus entity.TaskStatus) {
	if err := s.TaskDAO.UpdateStatusByID(ctx, opt.TaskID, oldStatus, newStatus); err != nil {
		logs.V1.CtxError(ctx, "[Run] update task status failed. err: %v", err)
	}
}

// filterHistoryMessages 过滤掉历史消息中非Agent的历史数据
func (s *Service) filterHistoryMessages(ctx context.Context, opt service.RunTaskOption) ([]*chatentity.Message, error) {
	historyMessageIDs := lo.Map(opt.HistoryMessages, func(item *chatentity.Message, _ int) string {
		return item.MessageID
	})
	messageIDMap, err := s.getUserMessageIDs(ctx, historyMessageIDs)
	if err != nil {
		logs.V1.CtxError(ctx, "[filterHistoryMessages] get user message ids failed. err: %v", err)
		return nil, err
	}

	newMessages := make([]*chatentity.Message, 0)
	for i := 0; i < len(opt.HistoryMessages); i += 2 {
		if opt.HistoryMessages[i].Role != copilotentity.RoleUser {
			continue
		}
		// 非Agent的历史数据，过滤
		if _, ok := messageIDMap[opt.HistoryMessages[i].MessageID]; !ok {
			continue
		}
		// 保留当前消息和下一条消息
		newMessages = append(newMessages, opt.HistoryMessages[i])
		if i+1 < len(opt.HistoryMessages) {
			newMessages = append(newMessages, opt.HistoryMessages[i+1])
		}
	}
	return newMessages, nil
}

type ParseResult struct {
	WorkspaceInfo    *entity.WorkspaceInfo
	UserInputFiles   []*contextentity.ContextIdentifier
	HistoryFiles     []*contextentity.ContextIdentifier
	HistoryArtifacts []*entity.Artifacts
}

func (s *Service) parseUserInputAndHistory(ctx context.Context, opt service.RunTaskOption) *ParseResult {
	parseResult := &ParseResult{}
	parseResult.WorkspaceInfo = &entity.WorkspaceInfo{}

	userInputFileIdentifiers := chatentity.ContextBlocks(opt.UserMessage.ContextBlocks).GetFileIdentifiers()
	for _, identifier := range userInputFileIdentifiers {
		logs.V1.CtxInfo(ctx, "[ParseUserInputAndHistory] user input file name: %s id: %s", identifier.Name, identifier.ResourceKey)
		if identifier.Name == "" {
			logs.V1.CtxWarn(ctx, "[ParseUserInputAndHistory] identifier name is empty")
			continue
		}
		parseResult.WorkspaceInfo.UserFiles = append(parseResult.WorkspaceInfo.UserFiles, identifier.Name)
		parseResult.UserInputFiles = append(parseResult.UserInputFiles, identifier)
	}

	for _, historyMessage := range opt.HistoryMessages {
		if historyMessage.Role != copilotentity.RoleAssistant {
			continue
		}

		historyFileIdentifiers := chatentity.ContextBlocks(historyMessage.ContextBlocks).GetFileIdentifiers()
		for _, identifier := range historyFileIdentifiers {
			logs.V1.CtxInfo(ctx, "[ParseUserInputAndHistory] history file name: %s id: %s", identifier.Name, identifier.ResourceKey)
			if identifier.Name == "" {
				logs.V1.CtxWarn(ctx, "[ParseUserInputAndHistory] history identifier name is empty")
				continue
			}
			parseResult.WorkspaceInfo.WorkspaceFiles = append(parseResult.WorkspaceInfo.WorkspaceFiles, identifier.Name)
			parseResult.HistoryFiles = append(parseResult.HistoryFiles, identifier)
		}

		messageArtifacts, remainingContent := s.parseArtifacts(ctx, historyMessage.Content)
		// 更新历史消息内容
		historyMessage.Content = remainingContent
		messageArtifactsName := lo.Map(messageArtifacts, func(item *entity.Artifacts, _ int) string {
			return item.Path
		})
		parseResult.WorkspaceInfo.WorkspaceFiles = append(parseResult.WorkspaceInfo.WorkspaceFiles, messageArtifactsName...)
		parseResult.HistoryArtifacts = append(parseResult.HistoryArtifacts, messageArtifacts...)
	}

	return parseResult
}

func (s *Service) parseArtifacts(ctx context.Context, content string) ([]*entity.Artifacts, string) {
	artifacts := make([]*entity.Artifacts, 0)
	remainingContent := content

	// 查找所有产物，产物是包裹在"<doubaocanvas><doubaocanvas-part path=\"xxx\"></doubaocanvas-part></doubaocanvas>"内的内容
	for {
		// 检索 <doubaocanvas> 标签
		tagContent, leftContent := getTagRange(remainingContent, "doubaocanvas")
		if tagContent == "" {
			break
		}
		remainingContent = leftContent

		// 检索 <doubaocanvas> 标签
		tagContent, _ = getTagRange(tagContent, "doubaocanvas-part")
		if tagContent == "" {
			continue
		}

		// 解析属性
		attr := `path="`
		attrStart := strings.Index(tagContent, attr)
		if attrStart == -1 {
			continue
		}

		attrEnd := strings.Index(tagContent[attrStart+len(attr):], `"`)
		if attrEnd == -1 {
			continue
		}
		attrEnd = attrStart + len(attr) + attrEnd
		pathValue := tagContent[attrStart+len(attr) : attrEnd]

		// 提取标签内容
		contentStart := strings.Index(tagContent, ">")
		if contentStart == -1 {
			continue
		}
		contentEnd := strings.LastIndex(tagContent, "<")
		if contentEnd == -1 {
			continue
		}
		contentValue := tagContent[contentStart+1 : contentEnd]

		logs.V1.CtxInfo(ctx, "[ParseUserInputAndHistory] history artifact name: %s", pathValue)
		// 创建 Artifacts 对象
		artifact := &entity.Artifacts{
			Path:    pathValue,
			Content: contentValue,
		}
		artifacts = append(artifacts, artifact)
	}

	return artifacts, remainingContent
}

func getTagRange(content string, tag string) (string, string) {
	startIdx := strings.Index(content, fmt.Sprintf("<%s", tag))
	if startIdx == -1 {
		return "", ""
	}

	// 找到标签的结束位置
	endTag := fmt.Sprintf("</%s>", tag)
	endIdx := strings.Index(content[startIdx:], endTag)
	if endIdx == -1 {
		return "", ""
	}
	endIdx += startIdx + len(endTag)

	// 提取标签内容
	return content[startIdx:endIdx], content[:startIdx] + "\n" + content[endIdx:]
}

func (s *Service) getUserMessageIDs(ctx context.Context, messageIDs []string) (map[string]struct{}, error) {
	messageList, err := s.TaskDAO.FilterGivenMessageList(ctx, messageIDs)
	if err != nil {
		logs.V1.CtxError(ctx, "[Run] get message list failed. err: %v", err)
		return nil, err
	}

	messageIDMap := make(map[string]struct{})
	for _, messageID := range messageList {
		messageIDMap[messageID] = struct{}{}
	}
	return messageIDMap, nil
}

func (s *Service) batchUploadFilesToAgentRun(ctx context.Context, opt service.RunTaskOption, parseResult *ParseResult, needHistory bool) error {
	concurrency := s.FileConcurrencyConfig.GetPointer().GetUploadFilesToAgentWorkspaceConcurrency()
	repoAnalysisPool := poolsdk.New().WithMaxGoroutines(concurrency).WithErrors()

	var mu sync.Mutex
	files := map[string][]byte{}
	for _, identifier := range parseResult.UserInputFiles {
		uri := identifier.ResourceKey
		name := identifier.Name

		repoAnalysisPool.Go(func() error {
			content, err := s.ArtifactsService.DownloadFile(ctx, uri, opt.UserID)
			if err != nil {
				logs.V1.CtxError(ctx, "[batchUploadFilesToAgentRun] download user input failed. err: %v", err)
				return err
			}

			mu.Lock()
			files[name] = content
			mu.Unlock()
			return nil
		})
	}
	if needHistory {
		for _, identifier := range parseResult.HistoryFiles {
			uri := identifier.ResourceKey
			name := identifier.Name

			repoAnalysisPool.Go(func() error {
				content, err := s.ArtifactsService.DownloadFile(ctx, uri, opt.UserID)
				if err != nil {
					logs.V1.CtxError(ctx, "[batchUploadFilesToAgentRun] download history file failed. err: %v", err)
					return err
				}

				mu.Lock()
				files[name] = content
				mu.Unlock()
				return nil
			})
		}
	}
	err := repoAnalysisPool.Wait()
	if err != nil {
		logs.V1.CtxError(ctx, "[batchUploadFilesToAgentRun] download files failed. err: %v", err)
		return err
	}

	if needHistory {
		for _, artifact := range parseResult.HistoryArtifacts {
			files[artifact.Path] = []byte(artifact.Content)
		}
	}

	err = s.AgentRunService.UploadFile(ctx, &service.UploadFileOption{
		UserID:         opt.UserID,
		ConversationID: opt.UserMessage.ConversationID,
		Files:          files,
	})
	if err != nil {
		logs.V1.CtxError(ctx, "[batchUploadFilesToAgentRun] upload file failed. err: %v", err)
	}
	return err
}

func (s *Service) buildAgent(ctx context.Context, opt service.RunTaskOption, agentRun *entity.AgentRun, workspaceInfo *entity.WorkspaceInfo) (agents.Agent, *agents.RunContext, error) {
	agent, agentRunContext, err := s.AgentFactoryService.Get(ctx, service.AgentGetOption{
		Name:           opt.AgentName,
		Version:        opt.AgentVersion,
		UserID:         opt.UserID,
		ConversationID: opt.UserMessage.ConversationID,
		AgentRunID:     agentRun.UUID,
		TaskID:         opt.TaskID,
	})
	if err != nil {
		return nil, nil, err
	}

	// 构建Agent上下文
	agentMessage := &entity.AgentRunHistoryMessage{
		ChatHistory:   s.buildChatHistory(opt),
		UserQuery:     s.buildUserQuery(opt),
		WorkspaceInfo: workspaceInfo,
	}
	round := 0
	actorIndex := 0

	if opt.NeedRecover {
		logs.V1.CtxInfo(ctx, "[buildAgent] recover agent step begin")
		currentAgentRunSteps, err := s.MemoryService.GetAgentRunSteps(ctx, &service.GetAgentRunStepOption{
			AgentRunID: agentRun.UUID,
		})
		if err != nil {
			logs.V1.CtxError(ctx, "[buildAgent] get agent run steps failed. err: %v", err)
		}
		if len(currentAgentRunSteps) != 0 {
			lastStep := currentAgentRunSteps[len(currentAgentRunSteps)-1]
			round = lastStep.Round
			actorIndex = lastStep.ActorIndex
			agentMessage.AgentTrajectory = s.buildAgentTrajectory(currentAgentRunSteps)
		} else {
			logs.V1.CtxWarn(ctx, "[buildAgent] get agent run steps failed. agent run steps not found")
		}
		logs.V1.CtxInfo(ctx, "[buildAgent] recover agent step done")
	}

	if actorIndex >= len(agentRunContext.Actors) {
		logs.V1.CtxError(ctx, "[buildAgent] actor index out of range. actor index: %d, actors length: %d", actorIndex, len(agentRunContext.Actors))
		return nil, nil, errors.New("actor index out of range")
	}

	agentRunContext.Round = round
	agentRunContext.StartTime = agentRun.CreatedAt
	agentRunContext.Messages = agentMessage
	agentRunContext.CurrentActor = &agents.CurrentActor{
		Actor: agentRunContext.Actors[actorIndex],
		Index: actorIndex,
	}
	return agent, agentRunContext, nil
}

func (s *Service) buildChatHistory(opt service.RunTaskOption) []*entity.MessageUnit {
	return lo.Map(opt.HistoryMessages, func(item *chatentity.Message, _ int) *entity.MessageUnit {
		return &entity.MessageUnit{
			Role:    entity.MessageUnitRole(item.Role),
			Content: item.Content,
		}
	})
}

func (s *Service) buildUserQuery(opt service.RunTaskOption) *entity.MessageUnit {
	return &entity.MessageUnit{
		Role:    entity.MessageUnitRole(opt.UserMessage.Role),
		Content: opt.UserMessage.Content,
	}
}

func (s *Service) buildAgentTrajectory(agentRunStep []*entity.AgentRunStep) []*entity.MessageUnit {
	var agentTrajectory []*entity.MessageUnit
	for _, step := range agentRunStep {
		assist := &entity.MessageUnit{
			Role:    entity.MessageUnitRoleAssistant,
			Content: step.RawMessage,
			Actor:   step.Actor,
		}
		agentTrajectory = append(agentTrajectory, assist)

		tools := lo.Map(step.AgentRunStepToolCallList, func(item *entity.AgentRunStepToolCall, _ int) *entity.MessageUnit {
			return &entity.MessageUnit{
				Role:    entity.MessageUnitRoleTool,
				Content: item.Observation,
				Actor:   step.Actor,
			}
		})
		agentTrajectory = append(agentTrajectory, tools...)
	}
	return agentTrajectory
}

func (s *Service) buildDoubaoEventCtx(ctx context.Context, opt service.RunTaskOption, agentRun *entity.AgentRun, writeStream eventstream.WriteStream) (*entity.DoubaoEventContext, error) {
	// 新建or恢复协议上下文，并置Task状态为Running
	if opt.NeedRecover {
		logs.V1.CtxInfo(ctx, "[buildDoubaoEventCtx] recover event stream")
		s.updateTaskStatus(ctx, opt, entity.TaskStatusRecovering, entity.TaskStatusRunning)

		doubaoEventContext, doubaoEvent, err := s.DoubaoProtocol.Recover(ctx, opt.TaskID, opt.UserID)
		if err != nil {
			logs.V1.CtxError(ctx, "[buildDoubaoEventCtx] recover task failed. err: %v", err)
			return nil, err
		}
		if eventData := doubaoEvent.BuildString(); eventData != "" {
			streamEvent := &flowpc_chat_eventstream.StreamEvent{
				EventId:   doubaoEventContext.CurEventID,
				EventType: int32(samanthaevent.EventType_BLOCKLIST),
				Data:      eventData,
			}
			err = writeStream.WriteEvent(ctx, streamEvent)
			if err != nil {
				logs.V1.CtxError(ctx, "[Run] write event failed. err: %v", err)
			}
		}
	}

	s.updateTaskStatus(ctx, opt, entity.TaskStatusInitializing, entity.TaskStatusRunning)

	doubaoEventContext := &entity.DoubaoEventContext{
		UserID:         opt.UserID,
		MessageID:      opt.UserMessage.MessageID,
		AnswerID:       opt.UserMessage.AnswerID,
		ConversationID: opt.UserMessage.ConversationID,
		TaskID:         opt.TaskID,
		TaskStage:      entity.TaskStageSync,
		AgentRunID:     agentRun.UUID,
		CurEventID:     0,
	}
	return doubaoEventContext, nil
}

func (s *Service) buildSyncExtMap(ctx context.Context, opt service.RunTaskOption) map[string]string {
	asyncTask := &flowpc_chat_eventstream.AsyncTask{
		Id:     opt.TaskID,
		Status: flowpc_chat_eventstream.AsyncTaskStatus_AsyncTaskStatusProcessing,
	}

	asyncTaskStr, err := json.Marshal(asyncTask)
	if err != nil {
		logs.V1.CtxError(ctx, "[Run] marshal async task failed. err: %v", err)
		return nil
	}

	return map[string]string{
		"async_task": string(asyncTaskStr),
	}
}

func (s *Service) sendDoubaoEvent(ctx context.Context, opt service.RunTaskOption, doubaoEvent *entity.DoubaoEvent, doubaoEventContext *entity.DoubaoEventContext, writeStream eventstream.WriteStream) {
	if doubaoEvent == nil || doubaoEventContext == nil || writeStream == nil {
		logs.V1.CtxError(ctx, "[sendDoubaoEvent] invalid params")
		return
	}
	switch doubaoEvent.TaskStage {
	case entity.TaskStageSync:
		if opt.Send == nil {
			logs.V1.CtxError(ctx, "[Run] send channel is nil")
			return
		}
		for _, wrapper := range doubaoEvent.BlockWrappers {
			rc := chatentity.ChatEvent{
				Output: &chatentity.ChatEventContent{
					ContentType: pack.BlockTypeToDTO(wrapper.Block.ContentType),
					Content:     wrapper.BlockContent.BuildString(),
					Ext:         s.buildSyncExtMap(ctx, opt),
					ID:          wrapper.Block.Id,
				},
			}
			//logs.V1.CtxInfo(ctx, "[Run] send sync block event: %s", util.MarshalStruct(rc))

			opt.Send.Publish(rc)
		}

	case entity.TaskStageAsync:
		// 阶段切换，终止同步阶段
		if opt.Send != nil && !opt.Send.Stopped() {
			rc := chatentity.ChatEvent{
				Output: &chatentity.ChatEventContent{
					ContentType: chatentity.ChatContentTypeBlockText,
					IsFinish:    true,
					Content:     "",
					Ext:         s.buildSyncExtMap(ctx, opt),
				},
			}

			opt.Send.Publish(rc)
			opt.Send.Close()
		}

		if doubaoEvent.NeedSplitSend {
			eventDataList := doubaoEvent.BuildStringList()
			if len(eventDataList) == 0 {
				logs.V1.CtxError(ctx, "[Run] no async block event list")
				return
			}

			for _, eventData := range eventDataList {
				streamEvent := &flowpc_chat_eventstream.StreamEvent{
					EventId:   doubaoEventContext.CurEventID,
					EventType: int32(samanthaevent.EventType_BLOCKLIST),
					Data:      eventData,
				}
				//logs.V1.CtxInfo(ctx, "[Run] send async block event: %s", util.MarshalStruct(streamEvent))
				err := writeStream.WriteEvent(ctx, streamEvent)
				if err != nil {
					logs.V1.CtxError(ctx, "[Run] write event failed. err: %v", err)
				}
				doubaoEventContext.CurEventID += 1
			}
		} else {
			eventData := doubaoEvent.BuildString()
			if eventData == "" {
				logs.V1.CtxError(ctx, "[Run] no async block event")
				return
			}

			streamEvent := &flowpc_chat_eventstream.StreamEvent{
				EventId:   doubaoEventContext.CurEventID,
				EventType: int32(samanthaevent.EventType_BLOCKLIST),
				Data:      eventData,
			}
			//logs.V1.CtxInfo(ctx, "[Run] send async block event: %s", util.MarshalStruct(streamEvent))
			err := writeStream.WriteEvent(ctx, streamEvent)
			if err != nil {
				logs.V1.CtxError(ctx, "[Run] write event failed. err: %v", err)
			}
		}
	default:
		logs.V1.CtxError(ctx, "[Run] invalid task stage. task stage: %v", doubaoEvent.TaskStage)
	}
}

func (s *Service) buildEndEvent(doubaoEventContext *entity.DoubaoEventContext, opt service.RunTaskOption, taskStatus metrics.CodeAssistTaskStatus) *flowpc_chat_eventstream.StreamEvent {
	status := samanthaevent.AsyncTaskStatus_AsyncTaskStatusUnknown
	switch taskStatus {
	case metrics.CodeAssistTaskStatusSuccess:
		status = samanthaevent.AsyncTaskStatus_AsyncTaskStatusSucceed
	case metrics.CodeAssistTaskStatusFailed:
		status = samanthaevent.AsyncTaskStatus_AsyncTaskStatusFailed
	case metrics.CodeAssistTaskStatusCanceled:
		status = samanthaevent.AsyncTaskStatus_AsyncTaskStatusCanceled
	}

	fin := &samanthaevent.Fin{
		AsyncTask: &samanthaevent.AsyncTask{
			Status: status,
			Id:     opt.TaskID,
		},
	}

	return &flowpc_chat_eventstream.StreamEvent{
		EventId:   doubaoEventContext.CurEventID + 1,
		EventType: int32(samanthaevent.EventType_FIN),
		IsEnd:     true,
		Data:      util.MarshalStruct(fin),
	}
}

func (s *Service) offlineStoreMessage(ctx context.Context, opt service.RunTaskOption, taskStatus metrics.CodeAssistTaskStatus) error {
	answerMsgID, err := strconv.ParseInt(opt.UserMessage.AnswerID, 10, 64)
	if err != nil {
		logs.V1.CtxError(ctx, "[offlineStoreMessage] answer id invalid: %s", opt.UserMessage.AnswerID)
		return err
	}

	status := flowpc_chat_eventstream.AsyncTaskStatus_AsyncTaskStatusUnknown
	switch taskStatus {
	case metrics.CodeAssistTaskStatusSuccess:
		status = flowpc_chat_eventstream.AsyncTaskStatus_AsyncTaskStatusSucceed
	case metrics.CodeAssistTaskStatusFailed:
		status = flowpc_chat_eventstream.AsyncTaskStatus_AsyncTaskStatusFailed
	case metrics.CodeAssistTaskStatusCanceled:
		status = flowpc_chat_eventstream.AsyncTaskStatus_AsyncTaskStatusCanceled
	}

	_, err = s.EventStreamService.Store2Message(ctx, &flowpc_chat_eventstream.Store2MessageReq{
		TaskId:    opt.TaskID,
		UserId:    opt.UserID,
		BizType:   flowpc_chat_eventstream.BizType_CodeAssist,
		MessageId: answerMsgID,
		AsyncTask: &flowpc_chat_eventstream.AsyncTask{
			Status: status,
			Id:     opt.TaskID,
		},
	})

	if err != nil {
		logs.V1.CtxError(ctx, "[Run] store response message failed. err: %v", err)
		return err
	}
	return nil
}
