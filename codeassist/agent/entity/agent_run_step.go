package entity

type AgentRunStepStatus int8

const (
	AgentRunStepStatusRunning   AgentRunStepStatus = 1
	AgentRunStepStatusCompleted AgentRunStepStatus = 2
	AgentRunStepStatusCancelled AgentRunStepStatus = 3
)

func (s AgentRunStepStatus) String() string {
	switch s {
	case AgentRunStepStatusRunning:
		return "running"
	case AgentRunStepStatusCompleted:
		return "completed"
	case AgentRunStepStatusCancelled:
		return "cancelled"
	default:
		return "unknown"
	}
}

type AgentRunStep struct {
	UUID                     string                  `json:"uuid"`
	AgentRunID               string                  `json:"agent_run_id"`
	Actor                    string                  `json:"actor"`
	Round                    int                     `json:"round"`
	MetaData                 *AgentRunStepMetaData   `json:"meta_data,omitempty"`
	RawMessage               string                  `json:"raw_message"`
	Status                   AgentRunStepStatus      `json:"status"`
	AgentRunStepToolCallList []*AgentRunStepToolCall `json:"agent_run_step_tool_call_list"`
	ActorIndex               int                     `json:"actor_index"`
}

type AgentRunStepMetaData struct {
}

type AgentRunStepToolCall struct {
	UUID           string `json:"uuid"`
	AgentRunID     string `json:"agent_run_id"`
	AgentRunStepID string `json:"agent_run_step_id"`
	Index          int    `json:"index"`
	Observation    string `json:"observation"`
	ToolName       string `json:"tool_name"`
	Status         string `json:"status"`
}
