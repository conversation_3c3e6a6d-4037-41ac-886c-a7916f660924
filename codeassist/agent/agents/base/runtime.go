package base

import (
	"context"
	"errors"
	"fmt"
	"time"

	fornaxtrace "code.byted.org/flowdevops/fornax_sdk/infra/ob"
	"code.byted.org/gopkg/logs/v2"
	"github.com/jinzhu/copier"

	"code.byted.org/devgpt/kiwis/codeassist/agent/agents"
	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	journalentity "code.byted.org/devgpt/kiwis/codeassist/journal/entity"
	"code.byted.org/devgpt/kiwis/lib/metrics"
)

const (
	WaitToolCallReadyInterval = 3 * time.Second // 工具调用未就绪时，等待时间
	ErrMsgPrefix              = "任务执行失败，Error: "
)

func (a *AgentBaseService) Run(ctx context.Context, runContext *agents.RunContext) error {
	span, ctx, _ := a.FornaxClient.StartQuerySpan(ctx, journalentity.SpanAgent, fornaxtrace.AsyncChildSpan())

	if runContext.StartTime.IsZero() {
		runContext.StartTime = time.Now()
	}
	// 发送启动事件
	runContext.EventChan.Publish(&entity.AgentEvent{
		RunID:     runContext.AgentRunID,
		EventType: entity.AgentEventTypeAgentProgress,
		Timestamp: time.Now(),
		AgentProgressEvent: &entity.AgentProgressEvent{
			Status: entity.AgentStatusCreated,
		},
	})

	var err error
	defer func() {
		agentRunInfo := map[string]interface{}{}

		progressEvent := &entity.AgentProgressEvent{
			Status:       entity.AgentStatusCompleted,
			ErrorMessage: "",
		}
		if err != nil {
			logs.V1.CtxError(ctx, "Run error: %s", err)
			progressEvent.Status = entity.AgentStatusFailed
			progressEvent.ErrorMessage = err.Error()
			agentRunInfo["status"] = "fail"
		} else {
			agentRunInfo["status"] = "success"
		}

		agentRunInfo["total_round"] = runContext.Round
		agentRunInfo["run_time_duration"] = time.Since(runContext.StartTime).Seconds()
		if runContext.RunInfo != nil {
			agentRunInfo["total_completion_tokens"] = runContext.RunInfo.TotalCompletionTokens
			agentRunInfo["total_prompt_tokens"] = runContext.RunInfo.TotalPromptTokens
			agentRunInfo["total_tokens"] = runContext.RunInfo.TotalTokens
		}

		_ = metrics.CodeAssistMetric.AgentActorThroughput.WithTags(&metrics.CodeAssistAgentActorTag{
			AgentName: runContext.AgentName,
			Actor:     runContext.CurrentActor.Actor.GetName(),
			Status:    metrics.CodeAssistActorStatus(progressEvent.Status),
		}).Add(1)

		// 上报agent任务总token消耗和总轮数
		agentTaskTag := &metrics.CodeAssistAgentTaskTag{
			AgentName: runContext.AgentName,
		}
		if runContext.RunInfo != nil {
			_ = metrics.CodeAssistMetric.AgentTaskTotalTokens.WithTags(agentTaskTag).Observe(float64(runContext.RunInfo.TotalTokens))
		}
		_ = metrics.CodeAssistMetric.AgentTaskTotalRounds.WithTags(agentTaskTag).Observe(float64(runContext.Round))
		span.SetTag(ctx, map[string]interface{}{
			"task_id":        runContext.TaskID,
			"agent_run_id":   runContext.AgentRunID,
			"agent_name":     runContext.AgentName,
			"agent_run_info": agentRunInfo,
		})
		span.Finish(ctx)

		runContext.EventChan.Publish(&entity.AgentEvent{
			RunID:              runContext.AgentRunID,
			EventType:          entity.AgentEventTypeAgentProgress,
			Timestamp:          time.Now(),
			AgentProgressEvent: progressEvent,
		})
	}()

	// 初始化当前Actor
	if len(runContext.Actors) == 0 {
		return errors.New("no actors configured")
	}
	actorStartTime := time.Now()
	if runContext.CurrentActor == nil {
		runContext.CurrentActor = &agents.CurrentActor{
			Actor: runContext.Actors[0],
			Index: 0,
		}
	}
	if runContext.CurrentActor.ActorMessage == nil {
		runContext.CurrentActor.ActorMessage = &entity.AgentRunHistoryMessage{}
		copyErr := copier.Copy(runContext.CurrentActor.ActorMessage, runContext.Messages)
		if copyErr != nil {
			logs.V1.CtxError(ctx, "Copy runContext.Messages to runContext.CurrentActor.ActorMessage error: %s", copyErr)
		}
	}

	paused := false
	actorRounds := 0 // Actor执行轮数计数器
	agentCtx := ctx

	// 主循环
	for {
		// Agent Step指标打点
		reportStepFunc := metrics.CodeAssistMetric.ReportAgentStepMetrics()

		select {
		case <-ctx.Done():
			// 上下文取消，停止Agent
			return ctx.Err()
		case signal := <-runContext.ControlChan:
			// 处理控制信号
			switch signal {
			case agents.ControlSignalStop:
				logs.V1.CtxInfo(ctx, "Control signal stop")
				return nil
			case agents.ControlSignalPause:
				logs.V1.CtxInfo(ctx, "Control signal pause")
				paused = true
				continue
			case agents.ControlSignalResume:
				logs.V1.CtxInfo(ctx, "Control signal resume")
				paused = false
				continue
			default:
				logs.V1.CtxError(ctx, "Unknown control signal: %d", signal)
				return fmt.Errorf("unknown control signal: %d", signal)
			}
		default:
			// 检查当前状态
			if paused {
				// 暂停状态，等待恢复
				time.Sleep(100 * time.Millisecond)
				continue
			}
			// 检查当前Actor是否完成
			if runContext.CurrentActor.Actor.IsFinished(ctx, runContext) {
				// Agent Actor指标打点
				reportActorFunc := metrics.CodeAssistMetric.ReportAgentActorMetrics()
				reportActorFunc(runContext.AgentName, runContext.CurrentActor.Actor.GetName(), runContext.RunError != nil, actorRounds, actorStartTime)

				logs.V1.CtxInfo(ctx, "Actor %s finished", runContext.CurrentActor.Actor.GetName())
				// 切换到下一个Actor
				if runContext.CurrentActor.Index < len(runContext.Actors)-1 {
					runContext.CurrentActor.Index++
					runContext.CurrentActor.Actor = runContext.Actors[runContext.CurrentActor.Index]
					runContext.CurrentActor.ActorMessage = &entity.AgentRunHistoryMessage{} // 为新Actor初始化 ActorMessage
					actorRounds = 0                                                         // 重置新Actor的轮数计数器
					actorStartTime = time.Now()
					copyErr := copier.Copy(runContext.CurrentActor.ActorMessage, runContext.Messages)
					if copyErr != nil {
						logs.V1.CtxError(ctx, "Copy runContext.Messages to runContext.CurrentActor.ActorMessage error: %s", copyErr)
					}
					logs.V1.CtxInfo(ctx, "Switching to actor %s", runContext.CurrentActor.Actor.GetName())
				} else {
					logs.V1.CtxInfo(ctx, "All actors finished")
					return nil
				}
			}
			runContext.Round++
			actorRounds++ // 增加轮数计数

			var roundSpan fornaxtrace.QuerySpan
			roundSpan, ctx, _ = a.FornaxClient.StartQuerySpan(agentCtx, fmt.Sprintf("%s_%d", journalentity.SpanAgentRound, runContext.Round))

			runContext.EventChan.Publish(&entity.AgentEvent{
				EventType: entity.AgentEventTypeAgentStep,
				Timestamp: time.Now(),
				RunID:     runContext.AgentRunID,
				AgentStepEvent: &entity.AgentStepEvent{
					Status:     entity.AgentStepStatusStarted,
					Actor:      runContext.CurrentActor.Actor.GetName(),
					ActorIndex: runContext.CurrentActor.Index,
					Round:      runContext.Round,
				},
			})

			if err = runContext.CurrentActor.Actor.BeforeExecute(ctx, runContext); err != nil {
				logs.V1.CtxError(ctx, "BeforeExecute error: %s", err)
				runContext.RunError = err
				return err
			}

			var errMsg string
			err = a.ProcessMessages(ctx, runContext)
			if err != nil {
				logs.V1.CtxError(ctx, "ProcessMessages error: %s", err)
				runContext.RunError = err
				errMsg = err.Error()
				runContext.Messages.AgentTrajectory = append(runContext.Messages.AgentTrajectory, &entity.MessageUnit{
					Role:    entity.MessageUnitRoleAssistant,
					Content: ErrMsgPrefix + errMsg,
					Actor:   runContext.CurrentActor.Actor.GetName(),
				})
				runContext.CurrentActor.ActorMessage.AgentTrajectory = append(runContext.CurrentActor.ActorMessage.AgentTrajectory, &entity.MessageUnit{
					Role:    entity.MessageUnitRoleAssistant,
					Content: ErrMsgPrefix + errMsg,
					Actor:   runContext.CurrentActor.Actor.GetName(),
				})
			}

			// 执行Actor的后置处理
			if err = runContext.CurrentActor.Actor.AfterExecute(ctx, runContext); err != nil {
				logs.V1.CtxError(ctx, "AfterExecute error: %s", err)
				runContext.RunError = err
				return err
			}

			runContext.EventChan.Publish(&entity.AgentEvent{
				EventType: entity.AgentEventTypeAgentStep,
				Timestamp: time.Now(),
				RunID:     runContext.AgentRunID,
				AgentStepEvent: &entity.AgentStepEvent{
					Status:       entity.AgentStepStatusCompleted,
					Actor:        runContext.CurrentActor.Actor.GetName(),
					ActorIndex:   runContext.CurrentActor.Index,
					Round:        runContext.Round,
					ErrorMessage: errMsg,
				},
			})

			// 上报Step指标
			reportStepFunc(runContext.AgentName, runContext.CurrentActor.Actor.GetName(), err != nil)
			roundSpan.SetTag(ctx, map[string]interface{}{
				"task_id":      runContext.TaskID,
				"agent_run_id": runContext.AgentRunID,
				"agent_name":   runContext.AgentName,
				"actor":        runContext.CurrentActor.Actor.GetName(),
			})
			roundSpan.Finish(ctx)
		}
	}
}

func (a *AgentBaseService) GenDeltaEvent(ctx context.Context, content string, buffer agents.DeltaEventBuffer) (
	[]*entity.AgentEvent, agents.DeltaEventBuffer, error) {

	var err error
	events := make([]*entity.AgentEvent, 0)
	event := &entity.AgentEvent{}

	// 合并缓冲区和新内容
	buffer.Content = buffer.Content + content
	if buffer.ContentType == "" {
		buffer.ContentType = entity.AgentDeltaTypeInfo
	}

	// 遍历 buffer 每个字符，尝试匹配各种标签
	// 如果匹配到标签，根据匹配到的标签做对应的处理
	for {
		length := len(buffer.Content)
		// 遍历 buffer.Content 每个字符，直到遇到可能的后续标签
		idx := 0
		for idx < length {
			if buffer.Content[idx] == '<' && a.CouldBePartOfTag(buffer.Content[idx:]) {
				break
			}
			idx++
		}
		// 根据 buffer 生成第一个事件，并移动指针
		if idx > 0 {
			// 如果不在 artifacts 标签内，处理内容
			if !buffer.InArtifacts {
				if buffer.InParameter && !buffer.ParamSchema.StreamType {
					// 如果在非流式参数标签内，缓存内容而不是生成事件
					buffer.ParamContent = buffer.ParamContent + buffer.Content[:idx]
				} else {
					// 生成事件
					metadata, err := a.BufferToMetaData(ctx, buffer)
					if err != nil {
						return nil, buffer, err
					}
					event = &entity.AgentEvent{
						EventType: entity.AgentEventTypeAgentMessageDelta,
						Timestamp: time.Now(),
						RunID:     buffer.RunID,
						AgentMessageDeltaEvent: &entity.AgentMessageDeltaEvent{
							Type:     buffer.ContentType,
							Content:  buffer.Content[:idx],
							MetaData: metadata,
							Actor:    buffer.Actor,
						},
					}
					events = append(events, event)
				}
			}
			// 无论哪种情况都需要移动指针
			buffer.Content = buffer.Content[idx:]
		}
		// 根据 buffer 生成第一个事件，并移动指针
		ok, tagHandler := a.GetTagHandler(buffer.Content)
		if !ok {
			if len(buffer.Content) > 0 && !a.CouldBePartOfTag(buffer.Content) {
				// 处理剩余内容
				if !buffer.InArtifacts {
					if buffer.InParameter && !buffer.ParamSchema.StreamType {
						// 如果在非流式参数标签内，缓存内容而不是生成事件
						buffer.ParamContent = buffer.ParamContent + buffer.Content
					} else {
						// 生成事件
						metadata, err := a.BufferToMetaData(ctx, buffer)
						if err != nil {
							return nil, buffer, err
						}
						event := &entity.AgentEvent{
							EventType: entity.AgentEventTypeAgentMessageDelta,
							Timestamp: time.Now(),
							RunID:     buffer.RunID,
							AgentMessageDeltaEvent: &entity.AgentMessageDeltaEvent{
								Type:     buffer.ContentType,
								Content:  buffer.Content,
								MetaData: metadata,
								Actor:    buffer.Actor,
							},
						}
						events = append(events, event)
					}
				}
				buffer.Content = ""
			}
			break
		}
		event, buffer, err = tagHandler(ctx, buffer)
		if err != nil {
			return nil, buffer, err
		}
		if event != nil {
			events = append(events, event)
		}
		if len(buffer.Content) == 0 || len(buffer.Content) == length {
			break
		}
	}
	return events, buffer, nil
}

func (a *AgentBaseService) BufferToMetaData(ctx context.Context, buffer agents.DeltaEventBuffer) (entity.AgentMessageDeltaMetaData, error) {
	var invokeID string
	if len(buffer.InvokeIDs) > 0 {
		invokeID = buffer.InvokeIDs[len(buffer.InvokeIDs)-1]
	}

	metadata := entity.AgentMessageDeltaMetaData{
		ParamName:  buffer.ParamName,
		InvokeName: entity.InvokeName(buffer.InvokeName),
		InvokeID:   invokeID,
	}

	if buffer.InThink && buffer.InFunction {
		logs.V1.CtxError(ctx, "think and function cannot be in the same tag")
		// return metadata, errors.New("think and function cannot be in the same tag")
	}
	if buffer.InParameter && !buffer.InInvoke {
		logs.V1.CtxError(ctx, "parameter must be in invoke tag")
		return metadata, errors.New("parameter must be in invoke tag")
	}
	return metadata, nil
}
