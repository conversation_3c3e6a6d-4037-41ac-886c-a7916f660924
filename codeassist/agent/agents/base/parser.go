package base

import (
	"context"
	"errors"
	"fmt"
	"io"
	"regexp"
	"strings"
	"time"

	fornaxobtype "code.byted.org/flow/flow-telemetry-common/go/obtype"
	fornaxtrace "code.byted.org/flowdevops/fornax_sdk/infra/ob"
	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/gopkg/logs/v2"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/codeassist/agent/agents"
	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	"code.byted.org/devgpt/kiwis/codeassist/agent/service"
	journalentity "code.byted.org/devgpt/kiwis/codeassist/journal/entity"
	copilotstackentity "code.byted.org/devgpt/kiwis/copilotstack/entity"
	"code.byted.org/devgpt/kiwis/copilotstack/llm"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/metrics"
)

const (
	ToolCallNotFoundMsg       = "```STDERR\n invoke not found, please check the invoke name```"
	SandboxNotReadyMaxRetries = 20
)

// ProcessMessages 处理历史消息，调用模型生成输出
func (a *AgentBaseService) ProcessMessages(ctx context.Context, runContext *agents.RunContext) error {
	// 裁剪消息以适应token限制
	err := a.TrimMessages(ctx, runContext)
	if err != nil {
		return fmt.Errorf("TrimMessages error: %w", err)
	}

	messages := agentMessageToLLMMessage(ctx, runContext)
	span, modelCtx, err := a.startChatCompletionModelSpan(ctx, journalentity.SpanAgentThink, messages)
	if err != nil {
		logs.V1.CtxWarn(modelCtx, "[Agent Run] start round query span error: %s", err)
	}
	// todo delete at last
	for index, message := range messages {
		logs.V1.CtxInfo(modelCtx, "model message: (%d) %s, %s", index, message.Role, message.Content)
	}

	modelConf := runContext.CurrentActor.Actor.GetModelConfig()
	completionResult, err := a.ChatCompletion(modelCtx, ChatContext{
		Messages:       messages,
		ModelConf:      modelConf,
		ConversationID: runContext.ConversationID,
		UserID:         runContext.UserID,
		AgentID:        runContext.AgentRunID,
	})
	if err != nil {
		// LLM调用失败打点
		agentName := runContext.AgentName
		modelMetricName := modelConf.ModelArch + "_" + modelConf.ModelDescName
		metrics.CodeAssistMetric.ReportAgentLLMThroughputMetrics(agentName, modelMetricName, true, err)
		return fmt.Errorf("ChatCompletion error: %w", err)
	}

	defer func() {
		if err := completionResult.Close(modelCtx); err != nil {
			logs.V1.CtxWarn(modelCtx, "failed to close llm answer stream: %+v", err)
		}
	}()

	// 处理模型返回
	parsedContent, err := a.processModelResult(modelCtx, completionResult, runContext)
	if parsedContent == nil || err != nil {
		return fmt.Errorf("processModelResult error: %w", err)
	}
	stats := completionResult.Statistics(modelCtx)
	a.finishChatCompletionModelSpan(modelCtx, span, runContext, stats, parsedContent.RawContent)

	runContext.ParsedContent = parsedContent
	// todo delete 模型空回复兜底，后续删除
	if parsedContent.RawContent == "" {
		modelConf := runContext.CurrentActor.Actor.GetModelConfig()
		tokenTags := &metrics.ChatTokensTag{
			ModelName: modelConf.Name,
			ModelArch: modelConf.ModelArch,
			Handler:   runContext.AgentName,
		}
		_ = metrics.CodeAssistMetric.ChatEmptyResponseThroughput.WithTags(tokenTags).Add(1)

		logs.V1.CtxError(ctx, "Empty response")
	}

	if runContext.RunInfo == nil {
		runContext.RunInfo = &agents.RunInfo{}
	}
	runContext.RunInfo.TotalPromptTokens += stats.PromptTokens
	runContext.RunInfo.TotalCompletionTokens += stats.CompletionTokens
	runContext.RunInfo.TotalTokens += stats.TotalTokens

	runContext.Messages.AgentTrajectory = append(runContext.Messages.AgentTrajectory, &entity.MessageUnit{
		Role:    entity.MessageUnitRoleAssistant,
		Content: parsedContent.RawContent,
		Actor:   runContext.CurrentActor.Actor.GetName(),
	})
	runContext.CurrentActor.ActorMessage.AgentTrajectory = append(runContext.CurrentActor.ActorMessage.AgentTrajectory, &entity.MessageUnit{
		Role:    entity.MessageUnitRoleAssistant,
		Content: parsedContent.RawContent,
		Actor:   runContext.CurrentActor.Actor.GetName(),
	})

	attachments := make([]*entity.AgentMessageAttachment, 0)

	if parsedContent.Artifacts.HasArtifacts {
		files, err := a.AgentRunService.DownloadFile(ctx, &service.DownloadFileOption{
			UserID:         runContext.UserID,
			ConversationID: runContext.ConversationID,
			Files: lo.Map(parsedContent.Artifacts.Artifacts, func(artifact agents.Artifact, _ int) string {
				return artifact.Content
			}),
		})
		if err != nil {
			return fmt.Errorf("DownloadFile error: %w", err)
		}
		for name, content := range files {
			attachments = append(attachments, &entity.AgentMessageAttachment{
				RawContent: content,
				Name:       name,
			})
		}
	}

	if parsedContent.ToolCalls.HasToolCalls {
		toolResult := ""
		for _, invoke := range parsedContent.ToolCalls.Invokes {
			var toolSpan fornaxtrace.QuerySpan
			toolSpan, _, err = a.FornaxClient.StartQuerySpan(ctx, journalentity.SpanAgentToolCall)
			if err != nil {
				logs.V1.CtxWarn(ctx, "[Agent Run] start round query span error: %s", err)
			}
			toolExecInfo := map[string]interface{}{}
			toolExecInfo["tool_name"] = invoke.FunctionName
			toolExecInfo["input"] = invoke.Parameters

			tool := a.ToolService.GetTool(invoke.FunctionName)
			if tool == nil {
				logs.V1.CtxWarn(ctx, "Tool not found: %s", invoke.FunctionName)
				toolResult = ToolCallNotFoundMsg
			} else {
				var output any
				var err error
				var reportSandboxRetryFunc func(agentName string, toolName string)
				var sandboxRetryCount int
				for retry := 0; retry < SandboxNotReadyMaxRetries; retry++ {
					output, err = tool.Run(ctx, service.ToolContext{
						UserID:         runContext.UserID,
						ConversationID: runContext.ConversationID,
						AgentRunID:     runContext.AgentRunID,
						AgentName:      runContext.AgentName,
					}, invoke.Parameters)

					if err != nil {
						// 处理工具调用错误
						if errors.Is(err, service.ToolCallNotReadyError) {
							// 首次遇到沙盒未就绪错误，开始打点计时
							if sandboxRetryCount == 0 {
								reportSandboxRetryFunc = metrics.CodeAssistMetric.ReportAgentSandboxReadyRetryMetrics()
							}
							sandboxRetryCount++
							logs.V1.CtxInfo(ctx, "Sandbox not ready, waiting... (retry %d/%d)", retry+1, SandboxNotReadyMaxRetries)
							time.Sleep(WaitToolCallReadyInterval) // 等待工具调用准备就绪
							continue                              // 重试当前工具调用
						}
						// 其他错误直接跳出重试循环
						break
					}
					// 成功则跳出重试循环
					break
				}

				// 如果发生了沙盒重试，记录重试指标
				if sandboxRetryCount > 0 && reportSandboxRetryFunc != nil {
					reportSandboxRetryFunc(runContext.AgentName, invoke.FunctionName)
				}
				toolExecInfo["output"] = output

				if err != nil {
					runContext.RunError = err
					logs.V1.CtxError(ctx, "Tool call error: %s", err)
					toolExecInfo["observation"] = toolResult
					toolExecInfo["error_msg"] = err.Error()
					toolExecInfo["status"] = "fail"
					toolSpan.SetTag(ctx, map[string]interface{}{
						"task_id":        runContext.TaskID,
						"agent_run_id":   runContext.AgentRunID,
						"agent_name":     runContext.AgentName,
						"actor":          runContext.CurrentActor.Actor.GetName(),
						"tool_exec_info": toolExecInfo,
					})
					toolSpan.Finish(ctx)
					continue
				}

				toolResult = trimToolCallResult(runContext, tool.FormatResult(output))
				// 发送 tool event
				runContext.EventChan.Publish(&entity.AgentEvent{
					EventType: entity.AgentEventTypeAgentToolCall,
					RunID:     runContext.AgentRunID,
					Timestamp: time.Now(),
					AgentToolCallEvent: &entity.AgentToolCallEvent{
						InvokeID:    invoke.InvokeID,
						InvokeName:  entity.InvokeName(invoke.FunctionName),
						Description: tool.Description(),
						Inputs:      invoke.Parameters,
						Status:      entity.ToolCallStatusCompleted,
						Outputs:     output,
						OutputsStr:  toolResult,
					},
				})
			}
			runContext.Messages.AgentTrajectory = append(runContext.Messages.AgentTrajectory, &entity.MessageUnit{
				Role:    entity.MessageUnitRoleTool,
				Content: toolResult,
				Actor:   runContext.CurrentActor.Actor.GetName(),
			})
			runContext.CurrentActor.ActorMessage.AgentTrajectory = append(runContext.CurrentActor.ActorMessage.AgentTrajectory, &entity.MessageUnit{
				Role:    entity.MessageUnitRoleTool,
				Content: toolResult,
				Actor:   runContext.CurrentActor.Actor.GetName(),
			})

			toolExecInfo["observation"] = toolResult
			toolExecInfo["error_msg"] = ""
			toolExecInfo["status"] = "success"
			toolSpan.SetTag(ctx, map[string]interface{}{
				"task_id":        runContext.TaskID,
				"agent_run_id":   runContext.AgentRunID,
				"agent_name":     runContext.AgentName,
				"actor":          runContext.CurrentActor.Actor.GetName(),
				"tool_exec_info": toolExecInfo,
			})
			toolSpan.Finish(ctx)
		}
	}

	runContext.EventChan.Publish(&entity.AgentEvent{
		EventType: entity.AgentEventTypeAgentMessage,
		RunID:     runContext.AgentRunID,
		Timestamp: time.Now(),
		AgentMessageEvent: &entity.AgentMessageEvent{
			Actor:       runContext.CurrentActor.Actor.GetName(),
			Content:     parsedContent.RawContent,
			Attachments: attachments,
		},
	})

	return nil
}

func (a *AgentBaseService) startChatCompletionModelSpan(ctx context.Context, spanName string, messages []llm.ChatCompletionMessage) (fornaxtrace.ModelSpan, context.Context, error) {
	span, newCtx, err := a.FornaxClient.StartModelSpan(ctx, spanName)
	if err != nil {
		logs.V1.CtxWarn(ctx, "failed to start model span: %v", err)
		return nil, ctx, err
	}
	span.SetInput(ctx, &fornaxobtype.ModelInput{
		Messages: lo.Map(messages, func(item llm.ChatCompletionMessage, index int) *fornaxobtype.ModelMessage {
			return &fornaxobtype.ModelMessage{
				Role:    item.Role,
				Content: item.Content,
			}
		}),
	})
	return span, newCtx, nil
}

func (a *AgentBaseService) finishChatCompletionModelSpan(ctx context.Context, span fornaxtrace.ModelSpan, runContext *agents.RunContext, stats llm.Stats, answerContent string) {
	span.SetOutput(ctx, &fornaxobtype.ModelOutput{
		Choices: []*fornaxobtype.ModelChoice{
			{
				FinishReason: "",
				Index:        0,
				Message: &fornaxobtype.ModelMessage{
					Role:    copilotstackentity.RoleAssistant,
					Content: answerContent,
				},
			},
		},
	})

	modelConf := *runContext.CurrentActor.Actor.GetModelConfig()

	// LLM调用成功打点
	agentName := a.Name()
	modelMetricName := modelConf.ModelArch + "_" + modelConf.ModelDescName
	// LLM调用吞吐量打点
	metrics.CodeAssistMetric.ReportAgentLLMThroughputMetrics(agentName, modelMetricName, false, nil)

	// LLM Token统计打点
	if stats.PromptTokens > 0 || stats.CompletionTokens > 0 {
		metrics.CodeAssistMetric.ReportAgentLLMTokensMetrics(agentName, modelMetricName, stats.PromptTokens, stats.CompletionTokens)
	}

	// 上报 request_metadata 埋点
	metadata := packRequestMetadata(ctx, modelConf, stats)
	span.SetTag(ctx, map[string]interface{}{
		"task_id":          runContext.TaskID,
		"agent_run_id":     runContext.AgentRunID,
		"agent_name":       runContext.AgentName,
		"actor":            runContext.CurrentActor.Actor.GetName(),
		"request_metadata": metadata,
	})
	span.Finish(ctx)
}

func packRequestMetadata(ctx context.Context, modelConf config.CodeAssistLLMConfig, stats llm.Stats) *journalentity.RequestMetadata {
	metadata := &journalentity.RequestMetadata{}
	metadata.Name = modelConf.Name
	metadata.Stream = true
	metadata.MaxTokens = modelConf.MaxTokens
	metadata.Temperature = modelConf.Temperature
	metadata.TopP = modelConf.TopP
	metadata.TopK = modelConf.TopK
	metadata.MaxPromptTokens = modelConf.PromptMaxTokens

	if metadata.Usage == nil {
		metadata.Usage = &journalentity.Usage{}
	}
	metadata.Usage.PromptTokens = stats.PromptTokens
	metadata.Usage.CompletionTokens = stats.CompletionTokens
	metadata.Usage.TotalTokens = stats.TotalTokens
	metadata.Latency = stats.TotalTimeCost
	metadata.FirstTokenLatency = stats.FirstCountLatency
	metadata.LogID = ctxvalues.LogIDDefault(ctx)
	return metadata
}

func (a *AgentBaseService) processModelResult(
	ctx context.Context,
	result llm.ChatCompletionStreamResult,
	runContext *agents.RunContext) (*agents.ParsedContent, error) {
	bufferedContent := agents.DeltaEventBuffer{
		ContentType: entity.AgentDeltaTypeInfo,
		RunID:       runContext.AgentRunID,
		Actor:       runContext.CurrentActor.Actor.GetName(),
	}
	var events []*entity.AgentEvent
	var allChatContent strings.Builder

	// LLM指标相关变量
	agentName := runContext.AgentName
	modelMetricName := runContext.CurrentActor.Actor.GetModelConfig().ModelArch + "_" + runContext.CurrentActor.Actor.GetModelConfig().ModelDescName

	firstTokenReceived := false
	chunkCount := 0
	reportAvgTokenFunc := metrics.CodeAssistMetric.ReportAgentLLMAvgTokenLatencyMetrics()
	reportFirstTokenFunc := metrics.CodeAssistMetric.ReportAgentLLMFirstTokenMetrics()
	reportTotalTokenFunc := metrics.CodeAssistMetric.ReportAgentLLMTotalTokenMetrics()

	// 攒包相关变量
	chunkBatchSize := runContext.ChunkBatchSize
	if chunkBatchSize <= 0 {
		chunkBatchSize = 1 // 默认为1，表示不攒包
	}
	batchChunkCount := 0
	var batchContent strings.Builder

	lastChunkTime := time.Now()
	for {
		chunk, err := result.NextChunk(ctx)
		if err != nil {
			if err != io.EOF {
				logs.V1.CtxError(ctx, "failed to get next chunk: %v", err)
				return nil, err
			}
			// EOF 时发送剩余的events
			if batchContent.Len() > 0 {
				events, bufferedContent, err = a.GenDeltaEvent(ctx, batchContent.String(), bufferedContent)
				if err != nil {
					logs.V1.CtxWarn(ctx, "error model response: %s", allChatContent.String())
					return nil, err
				}

				for _, event := range events {
					//eventJSON, _ := json.Marshal(event)
					//logs.V1.CtxInfo(ctx, "Sending delta event: %s", string(eventJSON))
					runContext.EventChan.Publish(event)
				}
			}
			break
		}
		if chunk != nil && len(chunk.Choices) > 0 {
			content := chunk.Choices[0].Delta.Content

			// 首Token打点
			if !firstTokenReceived && len(content) > 0 {
				firstTokenReceived = true
				reportFirstTokenFunc(agentName, modelMetricName)
			}

			if len(content) > 0 {
				chunkCount++
				chunkInterval := float64(time.Since(lastChunkTime).Milliseconds())
				if chunkInterval >= 100 {
					logs.V1.CtxWarn(ctx, "chunk interval warn: %d ms, task id: %s", chunkInterval, runContext.TaskID)
				}
				metrics.CodeAssistMetric.ReportAgentLLMChunkIntervalMetrics(agentName, modelMetricName, chunkInterval)
				lastChunkTime = time.Now()
			}

			// 累计所有 content
			allChatContent.WriteString(content)

			// 攒包
			batchContent.WriteString(content)
			batchChunkCount++
			if batchChunkCount < chunkBatchSize {
				continue
			}

			reportDeltaEventFunc := metrics.CodeAssistMetric.ReportAgentLLMGenDeltaEventLatencyMetrics()
			events, bufferedContent, err = a.GenDeltaEvent(ctx, batchContent.String(), bufferedContent)
			if err != nil {
				logs.V1.CtxWarn(ctx, "error model response: %s", allChatContent.String())
				return nil, err
			}

			for _, event := range events {
				//eventJSON, _ := json.Marshal(event)
				//logs.V1.CtxInfo(ctx, "Sending delta event: %s", string(eventJSON))
				runContext.EventChan.Publish(event)
			}
			reportDeltaEventFunc(agentName, modelMetricName)

			batchChunkCount = 0
			batchContent.Reset()
		}
	}

	// 总Token时间打点
	if firstTokenReceived {
		reportTotalTokenFunc(agentName, modelMetricName)
	}
	// Chunk间隔时间打点
	if chunkCount > 0 {
		reportAvgTokenFunc(agentName, modelMetricName, chunkCount)
	}

	logs.V1.CtxWarn(ctx, "model response: %s", allChatContent.String())

	// 解析结果中的工具调用和artifacts
	parsedContent, err := a.ParseContent(ctx, allChatContent.String(), bufferedContent)
	if err != nil {
		metrics.CodeAssistMetric.ReportAgentLLMParseErrorMetrics(agentName, err.Error())
		return nil, err
	}

	return parsedContent, nil
}

func agentMessageToLLMMessage(ctx context.Context, runContext *agents.RunContext) (messages []llm.ChatCompletionMessage) {
	systemPromptList := runContext.CurrentActor.Actor.GetSystemPrompt(ctx, runContext)
	actorMessages := runContext.CurrentActor.ActorMessage
	if actorMessages == nil {
		logs.V1.CtxWarn(ctx, "actorMessages is nil, use runContext.Messages")
		actorMessages = runContext.Messages
	}
	for _, systemPrompt := range systemPromptList {
		messages = append(messages, llm.ChatCompletionMessage{
			Role:    string(entity.MessageUnitRoleSystem),
			Content: systemPrompt,
		})
	}

	for _, msg := range actorMessages.ChatHistory {
		messages = append(messages, llm.ChatCompletionMessage{
			Role:    string(msg.Role),
			Content: msg.Content,
		})
	}

	contextPromptList := runContext.CurrentActor.Actor.GetContextPrompt(ctx, runContext)
	for _, contextPrompt := range contextPromptList {
		messages = append(messages, llm.ChatCompletionMessage{
			Role:    string(entity.MessageUnitRoleUser),
			Content: contextPrompt,
		})
	}

	messages = append(messages, llm.ChatCompletionMessage{
		Role:    string(actorMessages.UserQuery.Role),
		Content: actorMessages.UserQuery.Content,
	})

	for _, msg := range actorMessages.AgentTrajectory {
		messages = append(messages, llm.ChatCompletionMessage{
			Role:    string(msg.Role),
			Content: msg.Content,
		})
	}

	return messages
}

func (a *AgentBaseService) ParseContent(ctx context.Context, content string, buffer agents.DeltaEventBuffer) (*agents.ParsedContent, error) {
	// 解析工具调用
	toolCalls, err := a.parseToolCall(ctx, content, buffer)
	if err != nil {
		return nil, err
	}

	// 解析 artifacts
	artifacts, err := a.parseArtifacts(ctx, content)
	if err != nil {
		return nil, err
	}

	result := &agents.ParsedContent{
		ToolCalls:  toolCalls,
		Artifacts:  artifacts,
		RawContent: content,
	}

	return result, nil
}

func (a *AgentBaseService) parseToolCall(ctx context.Context, content string, buffer agents.DeltaEventBuffer) (*agents.ParsedToolCalls, error) {
	result := &agents.ParsedToolCalls{
		HasToolCalls: false,
		Invokes:      []agents.Invoke{},
	}

	// 查找所有的function_calls块
	functionCallsPattern := regexp.MustCompile(`(?s)<doubao:function_calls>(.*?)</doubao:function_calls>`)
	functionCallsMatches := functionCallsPattern.FindAllStringSubmatch(content, -1)

	if len(functionCallsMatches) == 0 {
		return result, nil
	}

	result.HasToolCalls = true

	for _, match := range functionCallsMatches {
		if len(match) < 2 {
			continue
		}
		functionCallsBlock := match[1]

		// 查找所有的invoke块
		invokePattern := regexp.MustCompile(`(?s)<doubao:invoke\s+name="([^"]+)">(.*?)</doubao:invoke>`)
		invokeMatches := invokePattern.FindAllStringSubmatch(functionCallsBlock, -1)

		for idx, invokeMatch := range invokeMatches {
			if len(invokeMatch) < 3 {
				continue
			}

			functionName := invokeMatch[1]
			invokeContent := invokeMatch[2]

			// 解析参数
			parameters := make(map[string]string)
			parameterPattern := regexp.MustCompile(`(?s)<doubao:parameter\s+name="([^"]+)">(.*?)</doubao:parameter>`)
			parameterMatches := parameterPattern.FindAllStringSubmatch(invokeContent, -1)

			for _, paramMatch := range parameterMatches {
				if len(paramMatch) < 3 {
					continue
				}
				paramName := paramMatch[1]
				paramValue := strings.TrimSpace(paramMatch[2])
				parameters[paramName] = paramValue
			}
			var invokeID string
			if idx < len(buffer.InvokeIDs) {
				invokeID = buffer.InvokeIDs[idx]
			} else {
				return nil, fmt.Errorf("invokeID not found for function %s at index %d", functionName, idx)
			}
			toolCall := agents.Invoke{
				FunctionName: functionName,
				Parameters:   parameters,
				InvokeID:     invokeID,
			}
			result.Invokes = append(result.Invokes, toolCall)
		}
	}

	return result, nil
}

// parseArtifacts 解析内容中的 artifacts 标签
func (a *AgentBaseService) parseArtifacts(ctx context.Context, content string) (*agents.ParsedArtifacts, error) {
	result := &agents.ParsedArtifacts{
		HasArtifacts: false,
		Artifacts:    []agents.Artifact{},
	}

	// 查找所有的 artifacts 标签
	artifactsPattern := regexp.MustCompile(`(?s)<artifacts>(.*?)</artifacts>`)
	artifactsMatches := artifactsPattern.FindAllStringSubmatch(content, -1)

	if len(artifactsMatches) == 0 {
		return result, nil
	}

	result.HasArtifacts = true

	for _, match := range artifactsMatches {
		if len(match) < 2 {
			continue
		}

		artifactContent := strings.TrimSpace(match[1])
		if artifactContent != "" {
			artifact := agents.Artifact{
				Content: artifactContent,
			}
			result.Artifacts = append(result.Artifacts, artifact)
		}
	}

	return result, nil
}
