package pack

import (
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/codeassist"
	"code.byted.org/devgpt/kiwis/codeassist/common/constant"
)

func ResourceTypeToDTO(resourceType constant.ContextResourceType) codeassist.ResourceType {
	switch resourceType {
	case constant.ContextResourceTypeRepository:
		return codeassist.ResourceType_Repository
	case constant.ContextResourceTypeFile:
		return codeassist.ResourceType_File
	case constant.ContextResourceTypeDirectory:
		return codeassist.ResourceType_Directory
	default:
		return 0
	}
}
