// Code generated by SQL PO generator, DO NOT EDIT.
// This src file contains PO(persistent object) struct definition for database table `context`.
package po

import (
	datatypes "gorm.io/datatypes"
	"time"
)

// ContextPO is context.
type ContextPO struct {
	// ID is ID.
	ID int64 `gorm:"column:id;primaryKey"`
	// UniqueID is unique string ID, usually UUID.
	//
	// index: uniq_unique_id, priority: 1.
	//
	UniqueID string `gorm:"column:unique_id;size:64"`
	// DatasetID is dataset id in knowledge base.
	DatasetID int64 `gorm:"column:dataset_id"`
	// DatasourceID is datasource id in knowledge base.
	DatasourceID int64 `gorm:"column:datasource_id"`
	// Name is context name.
	Name string `gorm:"column:name;size:256"`
	// Type is context type.
	//
	// index: idx_resource_key_type, priority: 2.
	//
	Type string `gorm:"column:type;size:64"`
	// EntityID is entity unique id.
	//
	// index: uniq_entity_id, priority: 1.
	//
	EntityID string `gorm:"column:entity_id;size:64"`
	// ResourceKey is resource key.
	//
	// index: idx_resource_key_type, priority: 1.
	//
	ResourceKey string `gorm:"column:resource_key;size:512"`
	// Status is context index status.
	Status int `gorm:"column:status"`
	// Progress is context index progress.
	Progress int `gorm:"column:progress"`
	// Extra is extra info.
	Extra *datatypes.JSONType[ContextExtra] `gorm:"column:extra"`
	// CreatedAt is create timestamp.
	CreatedAt time.Time `gorm:"column:created_at"`
	// UpdatedAt is last update timestamp.
	UpdatedAt time.Time `gorm:"column:updated_at"`
}

// TableName returns PO's corresponding DB table name: `context`.
func (*ContextPO) TableName() string {
	return "context"
}
