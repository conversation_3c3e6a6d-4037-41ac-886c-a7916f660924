package service

import (
	"context"

	"code.byted.org/devgpt/kiwis/codeassist/context/entity"
	misartifact "code.byted.org/overpass/flow_samantha_misc/kitex_gen/flow/samantha/misc/artifact"
)

type DoubaoService interface {
	DownloadFileToMemory(ctx context.Context, url string) ([]byte, error)
	DownloadFile(ctx context.Context, filepath string, url string) error
	DownloadFileByDoubaoURI(ctx context.Context, uri string, userID int64) ([]byte, error)
	DownloadFileToDiskByDoubaoURI(ctx context.Context, filepath string, uri string, userID int64) error
	DownloadFileToDiskByDoubaoURL(ctx context.Context, filepath string, url *DoubaoFileURL) error
	MGetDoubaoDirectory(ctx context.Context, userID int64, directoryID int64, paths []string) ([]*entity.DirectoryNode, error)
	GetDoubaoDirectory(ctx context.Context, userID int64, directoryID int64, path string) ([]*entity.DirectoryNode, error)
	StreamUploadFile(ctx context.Context, filepath string, zipName string, userID int64) (string, error)
	GetDoubaoURLByURI(ctx context.Context, uri string, userID int64) (string, string, error)
	MGetDoubaoURLByURI(ctx context.Context, uris []string, userID int64) ([]*DoubaoFileURL, error)
	StreamUploadSlice(ctx context.Context, fileContent []byte, fileName string, userID int64) (string, error)
	GetCodeArtifact(ctx context.Context, resourceID, resourceVersion string, userID int64) (string, string, int64, error)
	GetArtifactCodeNodes(ctx context.Context, resourceID, resourceVersion string, userID int64) ([]*entity.ArtifactsCodeNodeContent, error)
	GetArtifact(ctx context.Context, artifactsID, userID int64) (*misartifact.GetArtifactResponse, error)
}

type DoubaoFileURL struct {
	// tos中的相对地址，也是文件的唯一标识
	Uri string
	// 分发url，外网可访问
	MainUrl string
	// 备用分发url，外网可访问
	BackUrl string
}
