package defaultcontext

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"strconv"
	"testing"
	"time"

	"github.com/alicebob/miniredis"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"

	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/knowledgebase"
	mockknowledgebase "code.byted.org/devgpt/kiwis/api/idl/kitex_gen/knowledgebase/mock"
	"code.byted.org/devgpt/kiwis/codeassist/common/constant"
	"code.byted.org/devgpt/kiwis/codeassist/context/dal"
	dalimpl "code.byted.org/devgpt/kiwis/codeassist/context/dal/impl"
	"code.byted.org/devgpt/kiwis/codeassist/context/dal/po"
	"code.byted.org/devgpt/kiwis/codeassist/context/entity"
	"code.byted.org/devgpt/kiwis/codeassist/context/service"
	"code.byted.org/devgpt/kiwis/codeassist/context/service/disabledirectory"
	"code.byted.org/devgpt/kiwis/codeassist/context/service/repositorycache"
	"code.byted.org/devgpt/kiwis/codeassist/context/test"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/devgpt/kiwis/lib/tcc"
	"code.byted.org/devgpt/kiwis/lib/tokenizer"
	"code.byted.org/devgpt/kiwis/port/db"
	"code.byted.org/devgpt/kiwis/port/dgit"
	mockdgit "code.byted.org/devgpt/kiwis/port/dgit/mock"
	mockeventbus "code.byted.org/devgpt/kiwis/port/eventbus/mock"
	"code.byted.org/devgpt/kiwis/port/github"
	mockgithub "code.byted.org/devgpt/kiwis/port/github/mock"
	"code.byted.org/devgpt/kiwis/port/redis"
	"code.byted.org/gopkg/lang/v2/conv"
)

func newTestContextService(t *testing.T) (*ContextServiceImpl, *mockOption) {
	_ = metrics.InitCodeAssistMetric()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	dbCli, _ := db.NewMockSQLiteClient(&po.ContextPO{}, &po.ContextRelationPO{}, &po.FilePO{}, &po.RepositoryPO{}, &po.DirectoryPO{})

	mockDoubaoService := test.NewMockDoubaoService(ctrl)
	mockDirectoryCompressService := test.NewMockDirectoryCompressService(ctrl)
	mockDesensitizationService := test.NewMockDesensitizationService(ctrl)
	mockKnowledgebaseClient := mockknowledgebase.NewMockClient(ctrl)
	mockGithubClient := mockgithub.NewMockClient(ctrl)
	mockDGitClient := mockdgit.NewMockClient(ctrl)
	mockOrgInstallationCollection := &config.OrgInstallationCollection{
		OrgNames: []string{"org1", "org2"},
		OrgInstallationIDs: map[string]int64{
			"org1": 1,
			"org2": 2,
		},
	}

	r, err := miniredis.Run()
	require.NoError(t, err)
	mockRedisCli, err := redis.NewBytedRedisClient(config.RedisConfig{
		PSM:  "",
		Addr: r.Addr(),
	})

	mockRepositoryCacheService := &repositorycache.Service{
		RedisClient: mockRedisCli,
	}

	mockContextDAO := &dalimpl.ContextDAOImpl{DB: dbCli}
	mockContextExtraDAO := &dalimpl.ContextExtraDAOImpl{DB: dbCli}
	mockContextRelationDAO := &dalimpl.ContextRelationDAOImpl{DB: dbCli}
	mockFileDAO := &dalimpl.FileDaoImpl{DB: dbCli}
	mockRepositoryDAO := &dalimpl.RepositoryDaoImpl{DB: dbCli}
	mockDirectoryDAO := &dalimpl.DirectoryDAOImpl{DB: dbCli}
	mockFileAnalysisConfig, _ := tcc.NewConfig[config.FileAnalysisConfig](
		config.FileAnalysisConfig{SupportLanguageByExtension: nil}, "", "", "", tcc.ConfigFormatYAML,
	)
	mockKnowledgeBaseDatasetConfig, _ := tcc.NewConfig[config.KnowledgeBaseDatasetConfig](
		config.KnowledgeBaseDatasetConfig{FileDatasetID: 1, DirectoryDatasetID: 2, RepositoryDatasetID: 3}, "", "", "", tcc.ConfigFormatYAML,
	)
	mockContextRetryConfig, _ := tcc.NewConfig[config.ContextRetryConfig](
		config.ContextRetryConfig{
			FileUpdateFilesRetryConfig:       config.CodeAssistRetryConfig{},
			DirectoryUpdateFilesRetryConfig:  config.CodeAssistRetryConfig{},
			RepositoryUpdateFilesRetryConfig: config.CodeAssistRetryConfig{},
			UpdateFoldersRetryConfig:         config.CodeAssistRetryConfig{},
			DeleteFilesRetryConfig:           config.CodeAssistRetryConfig{},
			GetFileContentRetryConfig:        config.CodeAssistRetryConfig{},
			ImageXDownloadRetryConfig:        config.CodeAssistRetryConfig{},
		}, "", "", "", tcc.ConfigFormatYAML,
	)
	mockRepositoryRestrictionConfig, _ := tcc.NewConfig[config.RepositoryRestrictionConfig](
		config.RepositoryRestrictionConfig{SizeLimitKB: 40960}, "", "", "", tcc.ConfigFormatYAML)

	mockDisabledDirectoryConfig, _ := tcc.NewConfig[config.DisabledDirectoryConfig](
		config.DisabledDirectoryConfig{
			FileSuffixWhiteList: []string{".md", ".txt", ".go"},
			CKGFileMaxSize:      4096,
			CKGFileMaxLineSize:  512,
			CKGFileMaxLineNum:   5000,
		}, "", "", "", tcc.ConfigFormatYAML)
	mockEventbusConfig, _ := tcc.NewConfig[config.EventBusConfig](
		config.EventBusConfig{Timeout: 10}, "", "", "", tcc.ConfigFormatYAML)

	mockFileConcurrencyConfig, _ := tcc.NewConfig[config.CodeAssistFileConcurrencyConfig](
		config.CodeAssistFileConcurrencyConfig{
			FirstLevelUpdateFilesConcurrency: 2,
			UpdateFilesConcurrency:           2,
			UpdateTreeConcurrency:            2,
			DeleteFilesConcurrency:           2,
			DGitDownloadFilesConcurrency:     2,
			DoubaoDownloadTreeConcurrency:    2,
			DoubaoDownloadFilesConcurrency:   2,
			DesensitizeConcurrency:           2,
		},
		"", "", "", tcc.ConfigFormatYAML,
	)

	resourceKeyBlackListConfig, _ := tcc.NewConfig[config.ResourceKeyBlackListConfig](
		config.ResourceKeyBlackListConfig{}, "", "", "", tcc.ConfigFormatYAML)

	mockTccConfig := &config.CodeAssistTCCConfig{
		KnowledgeBaseDatasetConfig:  mockKnowledgeBaseDatasetConfig,
		ContextRetryConfig:          mockContextRetryConfig,
		FileAnalysisConfig:          mockFileAnalysisConfig,
		RepositoryRestrictionConfig: mockRepositoryRestrictionConfig,
		DisabledDirectoryConfig:     mockDisabledDirectoryConfig,
		FileConcurrencyConfig:       mockFileConcurrencyConfig,
		EventBusConfig:              mockEventbusConfig,
		ResourceKeyBlackListConfig:  resourceKeyBlackListConfig,
	}
	mockDisabledDirectoryService := &disabledirectory.DisabledDirectoryService{
		DisabledDirectoryConfig: mockDisabledDirectoryConfig,
	}
	mockEventbusClient := mockeventbus.NewMockClient(ctrl)
	mockAssistantConfig := &config.CodeAssistAssistantConfig{
		ContextEventBusConfig: config.ContextEventBusConfig{
			EventName:     "fake_event",
			ConsumerGroup: "fake_group",
			ConsumerPSM:   "fake_psm",
		},
		ContextConfig: config.ContextConfig{
			FileFetchSizeLimit: 10000,
		},
	}

	s := &ContextServiceImpl{
		Config:                mockAssistantConfig,
		TccConfig:             mockTccConfig,
		FileAnalysisConfig:    mockTccConfig.FileAnalysisConfig,
		FileConcurrencyConfig: mockTccConfig.FileConcurrencyConfig,

		EventbusClient:      mockEventbusClient,
		DGitClient:          mockDGitClient,
		KnowledgeBaseClient: mockKnowledgebaseClient,
		RedisClient:         mockRedisCli,

		GitHubClient:     mockGithubClient,
		OrgInstallations: mockOrgInstallationCollection,

		DoubaoService:              mockDoubaoService,
		DisabledDirectoryService:   mockDisabledDirectoryService,
		RepositoryInfoCacheService: mockRepositoryCacheService,
		DirectoryCompressService:   mockDirectoryCompressService,
		LocalCacheService:          NewLocalCacheService(),
		DesensitizationService:     mockDesensitizationService,

		ContextMetaDAO:      mockContextDAO,
		ContextExtraDAO:     mockContextExtraDAO,
		FileEntityDAO:       mockFileDAO,
		DirectoryEntityDAO:  mockDirectoryDAO,
		ContextRelationDAO:  mockContextRelationDAO,
		RepositoryEntityDAO: mockRepositoryDAO,
	}

	mockOpt := &mockOption{
		MockDBClient:                 dbCli,
		MockDoubaoService:            mockDoubaoService,
		MockKnowledgebaseClient:      mockKnowledgebaseClient,
		MockDisabledDirectoryService: mockDisabledDirectoryService,
		MockContextDAO:               mockContextDAO,
		MockContextExtraDAO:          mockContextExtraDAO,
		MockContextRelationDAO:       mockContextRelationDAO,
		MockFiledAO:                  mockFileDAO,
		MockRepositoryEntityDAO:      mockRepositoryDAO,
		MockDirectoryEntityDAO:       mockDirectoryDAO,
		MockEventbusClient:           mockEventbusClient,
		MockGitHubClient:             mockGithubClient,
		MockDGitClient:               mockDGitClient,
		MockTCCConfig:                mockTccConfig,
		MockDirectoryCompressService: mockDirectoryCompressService,
		MockDesensitizationService:   mockDesensitizationService,
	}
	return s, mockOpt
}

type mockOption struct {
	MockDBClient                 db.Client
	MockDoubaoService            *test.MockDoubaoService
	MockDisabledDirectoryService service.DisabledDirectoryService
	MockKnowledgebaseClient      *mockknowledgebase.MockClient
	MockContextDAO               dal.ContextDAO
	MockContextExtraDAO          dal.ContextExtraDAO
	MockContextRelationDAO       dal.ContextRelationDAO
	MockFiledAO                  dal.FileDAO
	MockRepositoryEntityDAO      dal.RepositoryDAO
	MockDirectoryEntityDAO       dal.DirectoryDAO
	MockEventbusClient           *mockeventbus.MockClient
	MockGitHubClient             *mockgithub.MockClient
	MockDGitClient               *mockdgit.MockClient
	MockTCCConfig                *config.CodeAssistTCCConfig
	MockDirectoryCompressService *test.MockDirectoryCompressService
	MockDesensitizationService   *test.MockDesensitizationService
}

func TestContextServiceImpl_GetFileLanguage(t *testing.T) {
	ser := &ContextServiceImpl{}

	supportedLanguageByExtension := map[string]string{
		".c":     "C",
		".cpp":   "C++",
		".h":     "C",
		".hpp":   "C++",
		".java":  "Java",
		".py":    "Python",
		".js":    "JavaScript",
		".ts":    "TypeScript",
		".html":  "HTML",
		".css":   "CSS",
		".php":   "PHP",
		".rb":    "Ruby",
		".pl":    "Perl",
		".sh":    "Shell",
		".bash":  "Shell",
		".swift": "Swift",
		".kt":    "Kotlin",
		".go":    "Go",
		".dart":  "Dart",
		".scala": "Scala",
		".cs":    "C#",
		".xaml":  "XAML",
		".vue":   "Vue",
		".md":    "MarkDown",
		".txt":   "TXT",
		".json":  "JSON",
		".proto": "Protocol Buffers",
	}

	tests := []struct {
		name      string
		checkFunc func(t *testing.T, ser *ContextServiceImpl)
	}{
		{
			name: "go language",
			checkFunc: func(t *testing.T, service *ContextServiceImpl) {
				language := service.GetFileLanguage("aa.go", []byte("package defaultcontext\n\nimport (\n\t\"testing\"\n)"), supportedLanguageByExtension)
				require.Equal(t, language, "Go")
			},
		},
		{
			name: "md language, has file content",
			checkFunc: func(t *testing.T, service *ContextServiceImpl) {
				language := service.GetFileLanguage("readme.md", []byte("dasdada"), nil)
				require.Equal(t, language, "Markdown")
			},
		},
		{
			name: "md language, no file content",
			checkFunc: func(t *testing.T, service *ContextServiceImpl) {
				language := service.GetFileLanguage("readme.md", nil, supportedLanguageByExtension)
				require.Equal(t, language, "MarkDown")
			},
		},
		{
			name: "php language, has content, no supported language by extension",
			checkFunc: func(t *testing.T, service *ContextServiceImpl) {
				language := service.GetFileLanguage("a.php", []byte("<!DOCTYPE html>\n<html>\n<body>\n\n<h1>My first PHP page</h1>\n\n<?php\necho \"Hello World!\";\n?>\n\n</body>\n</html>"), nil)
				require.Equal(t, language, "PHP")
			},
		},
		{
			name: "html language",
			checkFunc: func(t *testing.T, service *ContextServiceImpl) {
				language := service.GetFileLanguage("a.html", nil, nil)
				require.Equal(t, language, "HTML")
			},
		},
		{
			name: ".bash_profile language",
			checkFunc: func(t *testing.T, service *ContextServiceImpl) {
				language := service.GetFileLanguage(".bash_profile", nil, nil)
				require.Equal(t, language, "Shell")
			},
		},
		{
			name: "yaml language",
			checkFunc: func(t *testing.T, service *ContextServiceImpl) {
				language := service.GetFileLanguage("a.yml", nil, nil)
				require.Equal(t, language, "YAML")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.checkFunc(t, ser)
		})
	}
}

func TestGetMaxLanguage(t *testing.T) {
	ser := &ContextServiceImpl{}

	tests := []struct {
		name      string
		checkFunc func(t *testing.T, service *ContextServiceImpl)
	}{
		{
			name: "go language",
			checkFunc: func(t *testing.T, service *ContextServiceImpl) {
				languageMap := map[string]int{
					"Go":     5,
					"Java":   2,
					"Python": 1,
					"PHP":    1,
				}
				language, count := ser.GetMaxLanguage(languageMap)
				require.Equal(t, language, "Go")
				require.Equal(t, count, 5)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.checkFunc(t, ser)
		})
	}
}

func TestMQTime(t *testing.T) {
	ts := time.Now()
	task := entity.AnalysisTask{
		EntryTime: ts,
	}
	value, err := json.Marshal(task)
	require.NoError(t, err)

	parsedTask := entity.AnalysisTask{}
	err = json.Unmarshal(value, &parsedTask)
	require.NoError(t, err)
	require.True(t, ts.Equal(parsedTask.EntryTime))
}

func TestCreateFileContext(t *testing.T) {
	ctx := context.Background()

	fakeFileContent := "fake file content, hello world!"
	fakeFileContentTokenCount := tokenizer.CountTokensByTokenizer(fakeFileContent)
	require.NotZero(t, fakeFileContentTokenCount)

	tests := []struct {
		name      string
		mockFunc  func(ctx context.Context, mockOption *mockOption)
		checkFunc func(t *testing.T, service *ContextServiceImpl)
	}{
		{
			name: "normal create file",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				mockOption.MockDoubaoService.EXPECT().DownloadFileByDoubaoURI(ctx, gomock.Any(), gomock.Any()).Return(
					[]byte(fakeFileContent), nil)
				mockOption.MockKnowledgebaseClient.EXPECT().CreateDatasource(ctx, gomock.Any()).Return(
					&knowledgebase.CreateDatasourceResponse{
						Datasource: &knowledgebase.Datasource{
							ID: 1234,
						},
					}, nil,
				)
				mockOption.MockKnowledgebaseClient.EXPECT().UpdateFiles(ctx, gomock.Any(), gomock.Any()).Return(&knowledgebase.UpdateFilesResponse{
					Files: []*knowledgebase.File{},
				}, nil)

			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl) {
				opt := service.CreateContextOption{
					CreatorID:   1234,
					Name:        "fake.go",
					ResourceKey: "fake.go",
				}
				contextEntity, err := ser.CreateFileContext(ctx, opt)
				require.NoError(t, err)
				require.NotEmpty(t, contextEntity)

				requireFunc := func(contextDetail *service.ContextDetail, err error) {
					require.NoError(t, err)
					require.NotEmpty(t, contextDetail)
					require.Equal(t, constant.ContextResourceTypeFile, contextDetail.Context.Type)
					require.Equal(t, opt.Name, contextDetail.Context.Name)
					require.Equal(t, opt.ResourceKey, contextDetail.Context.ResourceKey)
					require.Equal(t, entity.ContextStatusSuccess, contextDetail.Context.Status)
					require.Equal(t, entity.ContextIndexProcessSuccess, contextDetail.Context.Progress)

					require.NotEmpty(t, contextDetail.FileEntity)
					require.Equal(t, opt.Name, contextDetail.FileEntity.Name)
					require.Equal(t, opt.ResourceKey, contextDetail.FileEntity.URI)
					require.NotZero(t, contextDetail.FileEntity.TokenCount)
					require.Equal(t, int64(fakeFileContentTokenCount), contextDetail.FileEntity.TokenCount)
				}

				contextDetail, err := ser.GetContextDetail(ctx, entity.ContextIdentifier{
					Type:        constant.ContextResourceTypeFile,
					ResourceKey: opt.ResourceKey,
				})
				requireFunc(contextDetail, err)

				contextDetails, err := ser.BatchGetContextDetail(ctx, service.BatchContextOption{
					Type:            constant.ContextResourceTypeFile,
					ResourceKeyList: []string{opt.ResourceKey},
				})
				require.NoError(t, err)
				require.Len(t, contextDetails, 1)
				requireFunc(contextDetails[0], err)

				contextRelation, err := ser.ContextRelationDAO.GetContextRelation(ctx, contextDetail.Context.UniqueID, entity.ContextRelationTypeReference)
				require.NoError(t, err)
				require.NotEmpty(t, contextRelation)
				require.Equal(t, contextDetail.Context.UniqueID, contextRelation.ContextID)
				require.Equal(t, entity.ContextRelationTypeReference, contextRelation.RelationType)
				require.Equal(t, strconv.FormatInt(opt.CreatorID, 10), contextRelation.RelatedID)

				// check MGetContext status
				fileContexts, err := ser.MGetContext(ctx, service.MGetContextStatusOption{
					Identifiers: []*entity.ContextIdentifier{
						{
							Type:        constant.ContextResourceTypeFile,
							ResourceKey: opt.ResourceKey,
						},
					},
				})
				require.NoError(t, err)
				require.NotEmpty(t, fileContexts)
				require.Len(t, fileContexts, 1)
				require.Equal(t, opt.ResourceKey, fileContexts[0].ResourceKey)
				require.Equal(t, entity.ContextStatusSuccess, fileContexts[0].Status)
				require.Equal(t, entity.ContextIndexProcessSuccess, fileContexts[0].Progress)
			},
		},
		{
			name: "file disabled",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl) {
				opt := service.CreateContextOption{
					CreatorID:   1234,
					Name:        "fake.exe",
					ResourceKey: "fake.exe",
				}
				_, err := ser.CreateFileContext(ctx, opt)
				require.Error(t, err)
				require.ErrorIs(t, err, service.ErrFileDisabled)
			},
		},
		{
			name: "file ckg disabled, oversize test",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				mockOption.MockTCCConfig.DisabledDirectoryConfig.GetPointer().CKGFileMaxSize = 2

				mockOption.MockDoubaoService.EXPECT().DownloadFileByDoubaoURI(ctx, gomock.Any(), gomock.Any()).Return(
					[]byte("1231 fake"), nil)
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl) {
				opt := service.CreateContextOption{
					CreatorID:   1234,
					Name:        "fake.go",
					ResourceKey: "fake.go",
				}
				_, err := ser.CreateFileContext(ctx, opt)
				require.Error(t, err)
				require.ErrorIs(t, err, service.ErrFileDisabled)
			},
		},
		{
			name: "file ckg disabled, line oversize test",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				mockOption.MockTCCConfig.DisabledDirectoryConfig.GetPointer().CKGFileMaxLineSize = 2

				mockOption.MockDoubaoService.EXPECT().DownloadFileByDoubaoURI(ctx, gomock.Any(), gomock.Any()).Return(
					[]byte("1231 fake"), nil)
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl) {
				opt := service.CreateContextOption{
					CreatorID:   1234,
					Name:        "fake.go",
					ResourceKey: "fake.go",
				}
				_, err := ser.CreateFileContext(ctx, opt)
				require.Error(t, err)
				require.ErrorIs(t, err, service.ErrFileDisabled)
			},
		},
		{
			name: "file ckg disabled, extra line count test",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				mockOption.MockTCCConfig.DisabledDirectoryConfig.GetPointer().CKGFileMaxLineNum = 1

				mockOption.MockDoubaoService.EXPECT().DownloadFileByDoubaoURI(ctx, gomock.Any(), gomock.Any()).Return(
					[]byte("1231 fake\n4567 fake"), nil)
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl) {
				opt := service.CreateContextOption{
					CreatorID:   1234,
					Name:        "fake.go",
					ResourceKey: "fake.go",
				}
				_, err := ser.CreateFileContext(ctx, opt)
				require.Error(t, err)
				require.ErrorIs(t, err, service.ErrFileDisabled)
			},
		},
		{
			name: "non code file update",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				mockOption.MockTCCConfig.DisabledDirectoryConfig.GetPointer().CKGFileMaxLineNum = 1
				mockOption.MockTCCConfig.DisabledDirectoryConfig.GetPointer().CKGFileMaxLineSize = 1

				mockOption.MockDoubaoService.EXPECT().DownloadFileByDoubaoURI(ctx, gomock.Any(), gomock.Any()).Return(
					[]byte("1231 fake\n4567 fake"), nil)

				mockOption.MockKnowledgebaseClient.EXPECT().CreateDatasource(ctx, gomock.Any()).Return(
					&knowledgebase.CreateDatasourceResponse{
						Datasource: &knowledgebase.Datasource{
							ID: 1234,
						},
					}, nil,
				)
				mockOption.MockKnowledgebaseClient.EXPECT().UpdateFiles(ctx, gomock.Any(), gomock.Any()).Return(&knowledgebase.UpdateFilesResponse{
					Files: []*knowledgebase.File{},
				}, nil)
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl) {
				opt := service.CreateContextOption{
					CreatorID:   1234,
					Name:        "fake.md",
					ResourceKey: "fake.md",
				}
				contextEntity, err := ser.CreateFileContext(ctx, opt)
				require.NoError(t, err)
				require.NotEmpty(t, contextEntity)

				requireFunc := func(contextDetail *service.ContextDetail, err error) {
					require.NoError(t, err)
					require.NotEmpty(t, contextDetail)
					require.Equal(t, constant.ContextResourceTypeFile, contextDetail.Context.Type)
					require.Equal(t, opt.Name, contextDetail.Context.Name)
					require.Equal(t, opt.ResourceKey, contextDetail.Context.ResourceKey)
					require.Equal(t, entity.ContextStatusSuccess, contextDetail.Context.Status)
					require.Equal(t, entity.ContextIndexProcessSuccess, contextDetail.Context.Progress)

					require.NotEmpty(t, contextDetail.FileEntity)
					require.Equal(t, opt.Name, contextDetail.FileEntity.Name)
					require.Equal(t, opt.ResourceKey, contextDetail.FileEntity.URI)
					require.NotZero(t, contextDetail.FileEntity.TokenCount)
					require.Equal(t, int64(fakeFileContentTokenCount), contextDetail.FileEntity.TokenCount)
				}

				contextDetail, err := ser.GetContextDetail(ctx, entity.ContextIdentifier{
					Type:        constant.ContextResourceTypeFile,
					ResourceKey: opt.ResourceKey,
				})
				requireFunc(contextDetail, err)

				contextDetails, err := ser.BatchGetContextDetail(ctx, service.BatchContextOption{
					Type:            constant.ContextResourceTypeFile,
					ResourceKeyList: []string{opt.ResourceKey},
				})
				require.NoError(t, err)
				require.Len(t, contextDetails, 1)
				requireFunc(contextDetails[0], err)

				contextRelation, err := ser.ContextRelationDAO.GetContextRelation(ctx, contextDetail.Context.UniqueID, entity.ContextRelationTypeReference)
				require.NoError(t, err)
				require.NotEmpty(t, contextRelation)
				require.Equal(t, contextDetail.Context.UniqueID, contextRelation.ContextID)
				require.Equal(t, entity.ContextRelationTypeReference, contextRelation.RelationType)
				require.Equal(t, strconv.FormatInt(opt.CreatorID, 10), contextRelation.RelatedID)

				// check MGetContext status
				fileContexts, err := ser.MGetContext(ctx, service.MGetContextStatusOption{
					Identifiers: []*entity.ContextIdentifier{
						{
							Type:        constant.ContextResourceTypeFile,
							ResourceKey: opt.ResourceKey,
						},
					},
				})
				require.NoError(t, err)
				require.NotEmpty(t, fileContexts)
				require.Len(t, fileContexts, 1)
				require.Equal(t, opt.ResourceKey, fileContexts[0].ResourceKey)
				require.Equal(t, entity.ContextStatusSuccess, fileContexts[0].Status)
				require.Equal(t, entity.ContextIndexProcessSuccess, fileContexts[0].Progress)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, mockOptions := newTestContextService(t)
			tt.mockFunc(ctx, mockOptions)
			tt.checkFunc(t, s)
		})
	}
}

func TestCreateDirectoryContext(t *testing.T) {
	ctx := context.Background()

	tests := []struct {
		name      string
		mockFunc  func(ctx context.Context, mockOption *mockOption)
		checkFunc func(t *testing.T, service *ContextServiceImpl)
	}{
		{
			name: "normal create directory context",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				mockOption.MockKnowledgebaseClient.EXPECT().CreateDatasource(ctx, gomock.Any()).Return(
					&knowledgebase.CreateDatasourceResponse{
						Datasource: &knowledgebase.Datasource{
							ID: 1234,
						},
					}, nil,
				)
				mockOption.MockEventbusClient.EXPECT().Publish(ctx, gomock.Any()).Return(nil)
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl) {
				opt := service.CreateContextOption{
					CreatorID:   123,
					Name:        "fake_dir",
					ResourceKey: "123123123131",
				}
				contextEntity, err := ser.CreateDirectoryContext(ctx, opt)
				require.NoError(t, err)
				require.NotEmpty(t, contextEntity)

				requireFunc := func(contextDetail *service.ContextDetail, err error) {
					require.NoError(t, err)
					require.NotEmpty(t, contextDetail)
					require.Equal(t, constant.ContextResourceTypeDirectory, contextDetail.Context.Type)
					require.Equal(t, opt.Name, contextDetail.Context.Name)
					require.Equal(t, opt.ResourceKey, contextDetail.Context.ResourceKey)
					require.Equal(t, entity.ContextStatusJustCreated, contextDetail.Context.Status)
					require.Equal(t, entity.ContextIndexProcessCreated, contextDetail.Context.Progress)

					require.NotEmpty(t, contextDetail.DirectoryEntity)
					require.Equal(t, opt.Name, contextDetail.DirectoryEntity.Name)
					require.Equal(t, opt.ResourceKey, strconv.FormatInt(contextDetail.DirectoryEntity.DirectoryID, 10))
					require.Equal(t, contextDetail.Context.EntityID, contextDetail.DirectoryEntity.UniqueID)
				}

				contextDetail, err := ser.GetContextDetail(ctx, entity.ContextIdentifier{
					Type:        constant.ContextResourceTypeDirectory,
					ResourceKey: opt.ResourceKey,
				})
				requireFunc(contextDetail, err)

				contextDetails, err := ser.BatchGetContextDetail(ctx, service.BatchContextOption{
					Type:            constant.ContextResourceTypeDirectory,
					ResourceKeyList: []string{opt.ResourceKey},
				})
				require.NoError(t, err)
				require.Len(t, contextDetails, 1)
				requireFunc(contextDetails[0], err)

				contextRelation, err := ser.ContextRelationDAO.GetContextRelation(ctx, contextDetail.Context.UniqueID, entity.ContextRelationTypeReference)
				require.NoError(t, err)
				require.NotEmpty(t, contextRelation)
				require.Equal(t, contextDetail.Context.UniqueID, contextRelation.ContextID)
				require.Equal(t, entity.ContextRelationTypeReference, contextRelation.RelationType)
				require.Equal(t, strconv.FormatInt(opt.CreatorID, 10), contextRelation.RelatedID)

				// check MGetContext status
				directoryContexts, err := ser.MGetContext(ctx, service.MGetContextStatusOption{
					Identifiers: []*entity.ContextIdentifier{
						{
							Type:        constant.ContextResourceTypeDirectory,
							ResourceKey: opt.ResourceKey,
						},
					},
				})
				require.NoError(t, err)
				require.NotEmpty(t, directoryContexts)
				require.Len(t, directoryContexts, 1)
				require.Equal(t, constant.ContextResourceTypeDirectory, directoryContexts[0].Type)
				require.Equal(t, opt.ResourceKey, directoryContexts[0].ResourceKey)
				require.Equal(t, entity.ContextStatusJustCreated, directoryContexts[0].Status)
				require.Equal(t, entity.ContextIndexProcessCreated, directoryContexts[0].Progress)
			},
		},
		{
			name: "directory id invalid",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				mockOption.MockKnowledgebaseClient.EXPECT().CreateDatasource(ctx, gomock.Any()).Return(
					&knowledgebase.CreateDatasourceResponse{
						Datasource: &knowledgebase.Datasource{
							ID: 1234,
						},
					}, nil,
				)
				mockOption.MockEventbusClient.EXPECT().Publish(ctx, gomock.Any()).Return(nil)
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl) {
				opt := service.CreateContextOption{
					CreatorID:   123,
					Name:        "fake_dir",
					ResourceKey: "23131----SDA",
				}
				_, err := ser.CreateDirectoryContext(ctx, opt)
				require.Error(t, err)
				require.ErrorIs(t, err, service.ParamInvalidError)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, mockOptions := newTestContextService(t)
			tt.mockFunc(ctx, mockOptions)
			tt.checkFunc(t, s)
		})
	}
}

func TestSniffRepositoryLink(t *testing.T) {
	ctx := context.Background()

	repoLink := "https://github.com/abice/go-enum"
	repoName := "abice/go-enum"
	repoNameSpilt := "go-enum"
	datasourceID := int64(1234)
	repoBranch := "master"
	language := "Go"
	updateTimes := int64(2)

	checkTestCommonFunc := func(contextDetail *service.ContextDetail, checkStatus entity.ContextStatus, checkProgress int) {
		require.NotEmpty(t, contextDetail)
		require.Equal(t, constant.ContextResourceTypeRepository, contextDetail.Context.Type)
		require.Equal(t, repoName, contextDetail.Context.Name)
		require.Equal(t, checkStatus, contextDetail.Context.Status)
		require.Equal(t, checkProgress, contextDetail.Context.Progress)
		require.Equal(t, datasourceID, contextDetail.Context.DatasourceID)

		require.NotEmpty(t, contextDetail.RepositoryEntity)
		require.Equal(t, repoName, contextDetail.RepositoryEntity.Name)
		require.Equal(t, repoBranch, contextDetail.RepositoryEntity.Ref)
		require.Equal(t, language, contextDetail.RepositoryEntity.Language)
		require.Equal(t, contextDetail.Context.EntityID, contextDetail.RepositoryEntity.UniqueID)
	}

	tests := []struct {
		name      string
		mockFunc  func(ctx context.Context, mockOption *mockOption)
		checkFunc func(t *testing.T, service *ContextServiceImpl, mockOption *mockOption)
	}{
		{
			name: "sniff github repo, link is not github repo",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				mockOption.MockGitHubClient.EXPECT().ParseGetGithubUrl(gomock.Any()).Return("", "", errors.New("not github repo"))
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl, mockOption *mockOption) {
				sniffResult, err := ser.SniffRepositoryLink(ctx, service.SniffRepositoryLinkOption{
					Link: "123131",
				})
				require.NoError(t, err)
				require.Empty(t, sniffResult.RepositoryEntity)
				require.Equal(t, sniffResult.NotSupportReason, entity.SniffRepositoryNotSupportReasonNotGitHubLink)
			},
		},
		{
			name: "sniff github repo, dgit not exit, github exit. new create repo",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				mockOption.MockGitHubClient.EXPECT().ParseGetGithubUrl(gomock.Any()).Return(repoName, repoLink, nil)
				mockOption.MockGitHubClient.EXPECT().GetAccessTokenByInstallationID(gomock.Any(), gomock.Any()).Return(
					"fake_token", time.Now().Add(time.Hour), nil)
				mockOption.MockGitHubClient.EXPECT().GetRepository(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&github.RespRepo{
						Name:          conv.Ptr(repoNameSpilt),
						DefaultBranch: conv.Ptr(repoBranch),
						Size:          conv.Ptr(10),
						Language:      conv.Ptr(language),
					}, nil)
				mockOption.MockKnowledgebaseClient.EXPECT().CreateDatasource(ctx, gomock.Any()).Return(
					&knowledgebase.CreateDatasourceResponse{
						Datasource: &knowledgebase.Datasource{
							ID: datasourceID,
						},
					}, nil,
				)
				mockOption.MockEventbusClient.EXPECT().Publish(ctx, gomock.Any()).Return(nil)
				mockOption.MockDGitClient.EXPECT().FindRepositoryDefaultBranchName(ctx, gomock.Any()).Return(
					"", errors.New("repo not found"),
				)
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl, mockOption *mockOption) {
				sniffResult, err := ser.SniffRepositoryLink(ctx, service.SniffRepositoryLinkOption{
					Link:       repoLink,
					UpdateMode: entity.RepositoryUpdateModeNormal,
				})
				require.NoError(t, err)
				require.True(t, sniffResult.SupportIndex)
				require.NotEmpty(t, sniffResult.RepositoryEntity)

				repoContext, err := ser.GetContextDetail(ctx, entity.ContextIdentifier{
					Type:        constant.ContextResourceTypeRepository,
					ResourceKey: sniffResult.RepositoryEntity.UniqueID,
				})
				require.NoError(t, err)
				checkTestCommonFunc(repoContext, entity.ContextStatusJustCreated, entity.ContextIndexProcessCreated)

				// check MGetContext status
				repoContexts, err := ser.MGetContext(ctx, service.MGetContextStatusOption{
					Identifiers: []*entity.ContextIdentifier{
						{
							Type:        constant.ContextResourceTypeRepository,
							ResourceKey: sniffResult.RepositoryEntity.UniqueID,
						},
					},
				})
				require.NoError(t, err)
				require.NotEmpty(t, repoContexts)
				require.Len(t, repoContexts, 1)

				var repoCtx *entity.Context
				for _, context := range repoContexts {
					if context.DatasetID == ser.TccConfig.KnowledgeBaseDatasetConfig.GetValue().RepositoryDatasetID {
						repoCtx = context
					}
				}
				require.NotNil(t, repoCtx)

				require.Equal(t, constant.ContextResourceTypeRepository, repoCtx.Type)
				require.Equal(t, sniffResult.RepositoryEntity.UniqueID, repoCtx.ResourceKey)
				require.Equal(t, entity.ContextStatusJustCreated, repoCtx.Status)
				require.Equal(t, entity.ContextIndexProcessCreated, repoCtx.Progress)
			},
		},
		{
			name: "sniff github repo, dgit not exit, github not exit",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				mockOption.MockGitHubClient.EXPECT().ParseGetGithubUrl(gomock.Any()).Return(repoName, repoLink, nil)
				mockOption.MockGitHubClient.EXPECT().GetAccessTokenByInstallationID(gomock.Any(), gomock.Any()).Return(
					"fake_token", time.Now().Add(time.Hour), nil)
				mockOption.MockGitHubClient.EXPECT().GetRepository(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					nil, errors.New(github.ErrCodeNotFound.MessageFormat),
				)
				mockOption.MockDGitClient.EXPECT().FindRepositoryDefaultBranchName(ctx, gomock.Any()).Return(
					"", errors.New("repo not found"),
				)
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl, mockOption *mockOption) {
				sniffResult, err := ser.SniffRepositoryLink(ctx, service.SniffRepositoryLinkOption{
					Link:       repoLink,
					UpdateMode: entity.RepositoryUpdateModeNormal,
				})
				require.NoError(t, err)
				require.Empty(t, sniffResult.RepositoryEntity)
				require.Equal(t, sniffResult.NotSupportReason, entity.SniffRepositoryNotSupportReasonNotPublicRepository)
			},
		},
		{
			name: "sniff github repo, dgit exit, db not exist, github exit. new create repo",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				mockOption.MockGitHubClient.EXPECT().ParseGetGithubUrl(gomock.Any()).Return(repoName, repoLink, nil)
				mockOption.MockGitHubClient.EXPECT().GetAccessTokenByInstallationID(gomock.Any(), gomock.Any()).Return(
					"fake_token", time.Now().Add(time.Hour), nil)
				mockOption.MockGitHubClient.EXPECT().GetRepository(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&github.RespRepo{
						Name:          conv.Ptr(repoNameSpilt),
						DefaultBranch: conv.Ptr(repoBranch),
						Size:          conv.Ptr(10),
						Language:      conv.Ptr(language),
					}, nil)
				mockOption.MockKnowledgebaseClient.EXPECT().CreateDatasource(ctx, gomock.Any()).Return(
					&knowledgebase.CreateDatasourceResponse{
						Datasource: &knowledgebase.Datasource{
							ID: datasourceID,
						},
					}, nil,
				)
				mockOption.MockEventbusClient.EXPECT().Publish(ctx, gomock.Any()).Return(nil)
				mockOption.MockDGitClient.EXPECT().FindRepositoryDefaultBranchName(ctx, gomock.Any()).Return(
					repoBranch, nil,
				)
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl, mockOption *mockOption) {
				sniffResult, err := ser.SniffRepositoryLink(ctx, service.SniffRepositoryLinkOption{
					Link:       repoLink,
					UpdateMode: entity.RepositoryUpdateModeNormal,
				})
				require.NoError(t, err)
				require.NotEmpty(t, sniffResult.RepositoryEntity)

				repoContext, err := ser.GetContextDetail(ctx, entity.ContextIdentifier{
					Type:        constant.ContextResourceTypeRepository,
					ResourceKey: sniffResult.RepositoryEntity.UniqueID,
				})
				require.NoError(t, err)
				checkTestCommonFunc(repoContext, entity.ContextStatusJustCreated, entity.ContextIndexProcessCreated)
			},
		},
		{
			name: "sniff github repo, dgit exit, db exist, context status is success",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				mockOption.MockGitHubClient.EXPECT().ParseGetGithubUrl(gomock.Any()).Return(repoName, repoLink, nil)
				mockOption.MockGitHubClient.EXPECT().GetAccessTokenByInstallationID(gomock.Any(), gomock.Any()).Return(
					"fake_token", time.Now().Add(time.Hour), nil)
				mockOption.MockGitHubClient.EXPECT().GetRepository(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&github.RespRepo{
						Name:          conv.Ptr(repoNameSpilt),
						DefaultBranch: conv.Ptr(repoBranch),
						Size:          conv.Ptr(10),
						Language:      conv.Ptr(language),
					}, nil)
				mockOption.MockKnowledgebaseClient.EXPECT().CreateDatasource(ctx, gomock.Any()).Return(
					&knowledgebase.CreateDatasourceResponse{
						Datasource: &knowledgebase.Datasource{
							ID: datasourceID,
						},
					}, nil,
				)
				mockOption.MockEventbusClient.EXPECT().Publish(ctx, gomock.Any()).Return(nil)
				mockOption.MockDGitClient.EXPECT().FindRepositoryDefaultBranchName(ctx, gomock.Any()).Return(
					repoBranch, nil,
				)
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl, mockOption *mockOption) {
				// create repo success
				repoID, _ := ser.RepositoryEntityDAO.Create(ctx, &entity.RepositoryEntity{
					Name:        repoName,
					Link:        repoLink,
					Ref:         repoBranch,
					Source:      entity.ContextSourceGitHub,
					Language:    language,
					UpdateTimes: updateTimes,
				})
				_, _ = ser.ContextMetaDAO.Create(ctx, &entity.Context{
					Name:         repoName,
					Type:         constant.ContextResourceTypeRepository,
					EntityID:     repoID,
					ResourceKey:  repoID,
					Status:       entity.ContextStatusSuccess,
					Progress:     entity.ContextIndexProcessSuccess,
					DatasourceID: datasourceID,
					DatasetID:    1,
				})

				// sniff repo with success
				sniffResult, err := ser.SniffRepositoryLink(ctx, service.SniffRepositoryLinkOption{
					Link:       repoLink,
					UpdateMode: entity.RepositoryUpdateModeNormal,
				})
				require.NoError(t, err)
				require.NotEmpty(t, sniffResult.RepositoryEntity)
				require.Equal(t, repoID, sniffResult.RepositoryEntity.UniqueID)
				require.Equal(t, language, sniffResult.RepositoryEntity.Language)

				repoContext, err := ser.GetContextDetail(ctx, entity.ContextIdentifier{
					Type:        constant.ContextResourceTypeRepository,
					ResourceKey: sniffResult.RepositoryEntity.UniqueID,
				})
				require.NoError(t, err)
				checkTestCommonFunc(repoContext, entity.ContextStatusSuccess, entity.ContextIndexProcessSuccess)
			},
		},
		{
			name: "sniff github repo, dgit exit, db exist, context status is success, last index time is expired",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				mockOption.MockGitHubClient.EXPECT().ParseGetGithubUrl(gomock.Any()).Return(repoName, repoLink, nil)
				mockOption.MockGitHubClient.EXPECT().GetAccessTokenByInstallationID(gomock.Any(), gomock.Any()).Return(
					"fake_token", time.Now().Add(time.Hour), nil)
				mockOption.MockGitHubClient.EXPECT().GetRepository(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&github.RespRepo{
						Name:          conv.Ptr(repoNameSpilt),
						DefaultBranch: conv.Ptr(repoBranch),
						Size:          conv.Ptr(10),
						Language:      conv.Ptr(language),
					}, nil)
				mockOption.MockKnowledgebaseClient.EXPECT().CreateDatasource(ctx, gomock.Any()).Return(
					&knowledgebase.CreateDatasourceResponse{
						Datasource: &knowledgebase.Datasource{
							ID: datasourceID,
						},
					}, nil,
				)
				mockOption.MockEventbusClient.EXPECT().Publish(ctx, gomock.Any()).Return(nil)
				mockOption.MockDGitClient.EXPECT().FindRepositoryDefaultBranchName(ctx, gomock.Any()).Return(
					repoBranch, nil,
				)
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl, mockOption *mockOption) {
				// create repo success
				repoID, _ := ser.RepositoryEntityDAO.Create(ctx, &entity.RepositoryEntity{
					Name:        repoName,
					Link:        repoLink,
					Ref:         repoBranch,
					Source:      entity.ContextSourceGitHub,
					Language:    language,
					UpdateTimes: updateTimes,
				})
				_, _ = ser.ContextMetaDAO.Create(ctx, &entity.Context{
					Name:         repoName,
					Type:         constant.ContextResourceTypeRepository,
					EntityID:     repoID,
					ResourceKey:  repoID,
					Status:       entity.ContextStatusSuccess,
					Progress:     entity.ContextIndexProcessSuccess,
					DatasourceID: datasourceID,
					DatasetID:    1,
				})
				_ = mockOption.MockDBClient.NewRequest(ctx).Model(&po.RepositoryPO{}).Where("unique_id = ?", repoID).Updates(map[string]any{
					"updated_at": time.Now().Add(-entity.RepositoryUpdateIntervalDuration - time.Minute),
				})

				ser.TccConfig.KnowledgeBaseDatasetConfig.GetPointer().RepositoryDatasetID = 1

				// sniff repo with success
				sniffResult, err := ser.SniffRepositoryLink(ctx, service.SniffRepositoryLinkOption{
					Link:       repoLink,
					UpdateMode: entity.RepositoryUpdateModeNormal,
				})
				require.NoError(t, err)
				require.NotEmpty(t, sniffResult.RepositoryEntity)
				require.Equal(t, repoID, sniffResult.RepositoryEntity.UniqueID)
				require.Equal(t, language, sniffResult.RepositoryEntity.Language)

				repoContext, err := ser.GetRepoContextDetail(ctx, entity.ContextIdentifier{
					Type:        constant.ContextResourceTypeRepository,
					ResourceKey: sniffResult.RepositoryEntity.UniqueID,
				})
				require.NoError(t, err)
				require.NotNil(t, repoContext)

				checkTestCommonFunc(repoContext, entity.ContextStatusSuccess, entity.ContextIndexProcessSuccess)
				require.Equal(t, updateTimes+1, repoContext.RepositoryEntity.UpdateTimes)
			},
		},
		{
			name: "sniff github repo, dgit exit, db exist, context status is ready, index time not exceed, return indexing repo",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				mockOption.MockGitHubClient.EXPECT().ParseGetGithubUrl(gomock.Any()).Return(repoName, repoLink, nil)
				mockOption.MockGitHubClient.EXPECT().GetAccessTokenByInstallationID(gomock.Any(), gomock.Any()).Return(
					"fake_token", time.Now().Add(time.Hour), nil)
				mockOption.MockGitHubClient.EXPECT().GetRepository(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&github.RespRepo{
						Name:          conv.Ptr(repoNameSpilt),
						DefaultBranch: conv.Ptr(repoBranch),
						Size:          conv.Ptr(10),
						Language:      conv.Ptr(language),
					}, nil)
				mockOption.MockKnowledgebaseClient.EXPECT().CreateDatasource(ctx, gomock.Any()).Return(
					&knowledgebase.CreateDatasourceResponse{
						Datasource: &knowledgebase.Datasource{
							ID: datasourceID,
						},
					}, nil,
				)
				mockOption.MockEventbusClient.EXPECT().Publish(ctx, gomock.Any()).Return(nil)
				mockOption.MockDGitClient.EXPECT().FindRepositoryDefaultBranchName(ctx, gomock.Any()).Return(
					repoBranch, nil,
				)
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl, mockOption *mockOption) {
				// create repo success
				repoID, _ := ser.RepositoryEntityDAO.Create(ctx, &entity.RepositoryEntity{
					Name:        repoName,
					Link:        repoLink,
					Ref:         repoBranch,
					Source:      entity.ContextSourceGitHub,
					Language:    language,
					UpdateTimes: updateTimes,
				})
				_, _ = ser.ContextMetaDAO.Create(ctx, &entity.Context{
					Name:         repoName,
					Type:         constant.ContextResourceTypeRepository,
					EntityID:     repoID,
					ResourceKey:  repoID,
					Status:       entity.ContextStatusReady,
					Progress:     entity.ContextIndexProcessSuccess,
					DatasourceID: datasourceID,
					DatasetID:    1,
				})

				// sniff repo with success
				sniffResult, err := ser.SniffRepositoryLink(ctx, service.SniffRepositoryLinkOption{
					Link:       repoLink,
					UpdateMode: entity.RepositoryUpdateModeNormal,
				})
				require.NoError(t, err)
				require.NotEmpty(t, sniffResult.RepositoryEntity)
				require.Equal(t, repoID, sniffResult.RepositoryEntity.UniqueID)
				require.Equal(t, language, sniffResult.RepositoryEntity.Language)

				repoContext, err := ser.GetContextDetail(ctx, entity.ContextIdentifier{
					Type:        constant.ContextResourceTypeRepository,
					ResourceKey: sniffResult.RepositoryEntity.UniqueID,
				})
				require.NoError(t, err)
				checkTestCommonFunc(repoContext, entity.ContextStatusReady, entity.ContextIndexProcessSuccess)
			},
		},
		{
			name: "sniff github repo, dgit exit, db exist, context index status is processing, but index time exceed, reindex",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				mockOption.MockGitHubClient.EXPECT().ParseGetGithubUrl(gomock.Any()).Return(repoName, repoLink, nil)
				mockOption.MockGitHubClient.EXPECT().GetAccessTokenByInstallationID(gomock.Any(), gomock.Any()).Return(
					"fake_token", time.Now().Add(time.Hour), nil)
				mockOption.MockGitHubClient.EXPECT().GetRepository(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&github.RespRepo{
						Name:          conv.Ptr(repoNameSpilt),
						DefaultBranch: conv.Ptr(repoBranch),
						Size:          conv.Ptr(10),
						Language:      conv.Ptr(language),
					}, nil)
				mockOption.MockKnowledgebaseClient.EXPECT().CreateDatasource(ctx, gomock.Any()).Return(
					&knowledgebase.CreateDatasourceResponse{
						Datasource: &knowledgebase.Datasource{
							ID: datasourceID,
						},
					}, nil,
				)
				mockOption.MockEventbusClient.EXPECT().Publish(ctx, gomock.Any()).Return(nil)
				mockOption.MockDGitClient.EXPECT().FindRepositoryDefaultBranchName(ctx, gomock.Any()).Return(
					repoBranch, nil,
				)
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl, mockOption *mockOption) {
				// create repo success
				repoID, _ := ser.RepositoryEntityDAO.Create(ctx, &entity.RepositoryEntity{
					Name:        repoName,
					Link:        repoLink,
					Ref:         repoBranch,
					Source:      entity.ContextSourceGitHub,
					Language:    language,
					UpdateTimes: updateTimes,
				})
				contextID, _ := ser.ContextMetaDAO.Create(ctx, &entity.Context{
					Name:         repoName,
					Type:         constant.ContextResourceTypeRepository,
					EntityID:     repoID,
					ResourceKey:  repoID,
					Status:       entity.ContextStatusProcessing,
					Progress:     entity.ContextIndexProcessSuccess,
					DatasourceID: datasourceID,
					DatasetID:    1,
				})
				_ = mockOption.MockDBClient.NewRequest(ctx).Model(&po.ContextPO{}).Where("unique_id = ?", contextID).Updates(map[string]any{
					"updated_at": time.Now().Add(-entity.ContextIndexMaxDuration - time.Minute),
				})

				ser.TccConfig.KnowledgeBaseDatasetConfig.GetPointer().RepositoryDatasetID = 1

				// sniff repo with success
				sniffResult, err := ser.SniffRepositoryLink(ctx, service.SniffRepositoryLinkOption{
					Link:       repoLink,
					UpdateMode: entity.RepositoryUpdateModeNormal,
				})
				require.NoError(t, err)
				require.NotEmpty(t, sniffResult.RepositoryEntity)
				require.Equal(t, language, sniffResult.RepositoryEntity.Language)

				repoContext, err := ser.GetRepoContextDetail(ctx, entity.ContextIdentifier{
					Type:        constant.ContextResourceTypeRepository,
					ResourceKey: sniffResult.RepositoryEntity.UniqueID,
				})
				require.NoError(t, err)
				require.NotNil(t, repoContext)

				// 索引中时间过长，回退到创建中，重新索引check
				checkTestCommonFunc(repoContext, entity.ContextStatusJustCreated, entity.ContextIndexProcessCreated)
			},
		},
		{
			name: "sniff github repo, dgit exit, db exist, context index status is processing, but index time exceed, repo is big",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				mockOption.MockGitHubClient.EXPECT().ParseGetGithubUrl(gomock.Any()).Return("test/bigsize", "https://github.com/test/bigsize.git", nil)
				mockOption.MockGitHubClient.EXPECT().GetAccessTokenByInstallationID(gomock.Any(), gomock.Any()).Return(
					"fake_token", time.Now().Add(time.Hour), nil)
				mockOption.MockGitHubClient.EXPECT().GetRepository(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&github.RespRepo{
						Name:          conv.Ptr("test/bigsize"),
						DefaultBranch: conv.Ptr(repoBranch),
						Size:          conv.Ptr(1000000000000),
						Language:      conv.Ptr(language),
					}, nil)
				mockOption.MockKnowledgebaseClient.EXPECT().CreateDatasource(ctx, gomock.Any()).Return(
					&knowledgebase.CreateDatasourceResponse{
						Datasource: &knowledgebase.Datasource{
							ID: datasourceID,
						},
					}, nil,
				)
				mockOption.MockEventbusClient.EXPECT().Publish(ctx, gomock.Any()).Return(nil)
				mockOption.MockDGitClient.EXPECT().FindRepositoryDefaultBranchName(ctx, gomock.Any()).Return(
					repoBranch, nil,
				)
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl, mockOption *mockOption) {
				// create repo success
				repoID, _ := ser.RepositoryEntityDAO.Create(ctx, &entity.RepositoryEntity{
					Name:        "test/bigsize",
					Link:        "https://github.com/test/bigsize.git",
					Ref:         repoBranch,
					Source:      entity.ContextSourceGitHub,
					Language:    language,
					UpdateTimes: updateTimes,
				})
				contextID, _ := ser.ContextMetaDAO.Create(ctx, &entity.Context{
					Name:         "test/bigsize",
					Type:         constant.ContextResourceTypeRepository,
					EntityID:     repoID,
					ResourceKey:  repoID,
					Status:       entity.ContextStatusProcessing,
					Progress:     entity.ContextIndexProcessSuccess,
					DatasourceID: datasourceID,
					DatasetID:    1,
				})
				_ = mockOption.MockDBClient.NewRequest(ctx).Model(&po.ContextPO{}).Where("unique_id = ?", contextID).Updates(map[string]any{
					"updated_at": time.Now().Add(-entity.ContextIndexMaxDuration - time.Minute),
				})

				// sniff repo with success
				sniffResult, err := ser.SniffRepositoryLink(ctx, service.SniffRepositoryLinkOption{
					Link:       "https://github.com/test/bigsize",
					UpdateMode: entity.RepositoryUpdateModeNormal,
				})
				require.NoError(t, err)
				require.True(t, sniffResult.SupportIndex)
				require.NotEmpty(t, sniffResult.RepositoryEntity)
			},
		},
		{
			name: "sniff github repo, repo is big, after change ",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				mockOption.MockGitHubClient.EXPECT().ParseGetGithubUrl(gomock.Any()).Return(
					"test/bigsize", "https://github.com/test/bigsize.git", nil).AnyTimes()
				mockOption.MockGitHubClient.EXPECT().GetAccessTokenByInstallationID(gomock.Any(), gomock.Any()).Return(
					"fake_token", time.Now().Add(time.Hour), nil).AnyTimes()
				mockOption.MockGitHubClient.EXPECT().GetRepository(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&github.RespRepo{
						Name:          conv.Ptr("test/bigsize"),
						DefaultBranch: conv.Ptr(repoBranch),
						Size:          conv.Ptr(40969),
						Language:      conv.Ptr(language),
					}, nil).AnyTimes()
				mockOption.MockKnowledgebaseClient.EXPECT().CreateDatasource(ctx, gomock.Any()).Return(
					&knowledgebase.CreateDatasourceResponse{
						Datasource: &knowledgebase.Datasource{
							ID: datasourceID,
						},
					}, nil,
				).Times(2)
				mockOption.MockEventbusClient.EXPECT().Publish(ctx, gomock.Any()).Return(nil).Times(2)
				mockOption.MockDGitClient.EXPECT().FindRepositoryDefaultBranchName(ctx, gomock.Any()).Return(
					repoBranch, nil,
				).AnyTimes()
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl, mockOption *mockOption) {
				// sniff repo with not support
				sniffResult, err := ser.SniffRepositoryLink(ctx, service.SniffRepositoryLinkOption{
					Link:       "https://github.com/test/bigsize",
					UpdateMode: entity.RepositoryUpdateModeNormal,
				})
				require.NoError(t, err)
				require.NotEmpty(t, sniffResult.RepositoryEntity)
				require.False(t, sniffResult.SupportIndex)
				require.Equal(t, sniffResult.NotSupportReason, entity.SniffRepositoryNotSupportReasonSizeOverLimit)
				require.NotZero(t, sniffResult.SizeLimitKB)

				mockOption.MockTCCConfig.RepositoryRestrictionConfig.GetPointer().SizeLimitKB = 409600

				// sniff repo with success
				sniffResult, err = ser.SniffRepositoryLink(ctx, service.SniffRepositoryLinkOption{
					Link:       "https://github.com/test/bigsize",
					UpdateMode: entity.RepositoryUpdateModeNormal,
				})
				require.NoError(t, err)
				require.NotEmpty(t, sniffResult.RepositoryEntity)
				require.True(t, sniffResult.SupportIndex)
				require.Equal(t, sniffResult.NotSupportReason, entity.SniffRepositoryNotSupportReasonNoReason)
			},
		},
		{
			name: "sniff github repo, dgit exit, db exist, context index status is failed, reindex",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				mockOption.MockGitHubClient.EXPECT().ParseGetGithubUrl(gomock.Any()).Return(repoName, repoLink, nil)
				mockOption.MockGitHubClient.EXPECT().GetAccessTokenByInstallationID(gomock.Any(), gomock.Any()).Return(
					"fake_token", time.Now().Add(time.Hour), nil)
				mockOption.MockGitHubClient.EXPECT().GetRepository(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&github.RespRepo{
						Name:          conv.Ptr(repoNameSpilt),
						DefaultBranch: conv.Ptr(repoBranch),
						Size:          conv.Ptr(10),
						Language:      conv.Ptr(language),
					}, nil)
				mockOption.MockKnowledgebaseClient.EXPECT().CreateDatasource(ctx, gomock.Any()).Return(
					&knowledgebase.CreateDatasourceResponse{
						Datasource: &knowledgebase.Datasource{
							ID: datasourceID,
						},
					}, nil,
				)
				mockOption.MockEventbusClient.EXPECT().Publish(ctx, gomock.Any()).Return(nil)
				mockOption.MockDGitClient.EXPECT().FindRepositoryDefaultBranchName(ctx, gomock.Any()).Return(
					repoBranch, nil,
				)
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl, mockOption *mockOption) {
				// create repo success
				repoID, _ := ser.RepositoryEntityDAO.Create(ctx, &entity.RepositoryEntity{
					Name:        repoName,
					Link:        repoLink,
					Ref:         repoBranch,
					Source:      entity.ContextSourceGitHub,
					Language:    language,
					UpdateTimes: updateTimes,
				})
				_, _ = ser.ContextMetaDAO.Create(ctx, &entity.Context{
					Name:         repoName,
					Type:         constant.ContextResourceTypeRepository,
					EntityID:     repoID,
					ResourceKey:  repoID,
					Status:       entity.ContextStatusFailed,
					Progress:     entity.ContextIndexProcessIndexingFailed,
					DatasourceID: datasourceID,
					DatasetID:    1,
				})

				ser.TccConfig.KnowledgeBaseDatasetConfig.GetPointer().RepositoryDatasetID = 1

				// sniff repo with success
				sniffResult, err := ser.SniffRepositoryLink(ctx, service.SniffRepositoryLinkOption{
					Link:       repoLink,
					UpdateMode: entity.RepositoryUpdateModeNormal,
				})
				require.NoError(t, err)
				require.NotEmpty(t, sniffResult.RepositoryEntity)
				require.Equal(t, language, sniffResult.RepositoryEntity.Language)

				repoContext, err := ser.GetRepoContextDetail(ctx, entity.ContextIdentifier{
					Type:        constant.ContextResourceTypeRepository,
					ResourceKey: sniffResult.RepositoryEntity.UniqueID,
				})
				require.NoError(t, err)
				require.NotNil(t, repoContext)

				// 索引失败，回退到创建中，重新索引check
				checkTestCommonFunc(repoContext, entity.ContextStatusJustCreated, entity.ContextIndexProcessCreated)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, mockOptions := newTestContextService(t)
			tt.mockFunc(ctx, mockOptions)
			tt.checkFunc(t, s, mockOptions)
		})
	}
}

func TestGetFileContent(t *testing.T) {
	ctx := context.Background()
	fakeFileContent := "fake file content"

	tests := []struct {
		name      string
		mockFunc  func(ctx context.Context, mockOption *mockOption)
		checkFunc func(t *testing.T, service *ContextServiceImpl)
	}{
		{
			name: "get file content",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				mockOption.MockDoubaoService.EXPECT().DownloadFileByDoubaoURI(ctx, gomock.Any(), gomock.Any()).Return(
					[]byte(fakeFileContent), nil)
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl) {
				opt := service.GetFileIdentifierOption{
					Identifier: entity.ContextIdentifier{
						Type:        constant.ContextResourceTypeFile,
						ResourceKey: "fake.go",
					},
					Path:   conv.Ptr("fake.go"),
					UserID: 1234,
				}
				content, err := ser.GetFileContent(ctx, opt)
				require.NoError(t, err)
				require.Equal(t, fakeFileContent, content.Content)
				require.Equal(t, int64(len([]byte(fakeFileContent))), content.Size)
			},
		},
		{
			name: "get directory file content",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				mockOption.MockDoubaoService.EXPECT().GetDoubaoDirectory(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return(
					[]*entity.DirectoryNode{
						{
							Name:       "fake.go",
							Type:       entity.DirectoryNodeTypeFile,
							Path:       "fake.go",
							ParentPath: "",
							URI:        "fake.go",
						},
					}, nil)
				mockOption.MockDoubaoService.EXPECT().DownloadFileByDoubaoURI(ctx, gomock.Any(), gomock.Any()).Return(
					[]byte(fakeFileContent), nil)
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl) {
				opt := service.GetFileIdentifierOption{
					Identifier: entity.ContextIdentifier{
						Type:        constant.ContextResourceTypeDirectory,
						ResourceKey: "123123131",
					},
					Path:   conv.Ptr("fake.go"),
					UserID: 1234,
				}
				content, err := ser.GetFileContent(ctx, opt)
				require.NoError(t, err)
				require.Equal(t, fakeFileContent, content.Content)
				require.Equal(t, int64(len([]byte(fakeFileContent))), content.Size)
			},
		},
		{
			name: "get repo file content",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				mockOption.MockDGitClient.EXPECT().GetFileContentOfRepository(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&dgit.FileEntity{
						Filepath: "fake.go",
						Content:  []byte(fakeFileContent),
						Size:     int64(len([]byte(fakeFileContent))),
					}, nil)
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl) {
				repoID, _ := ser.RepositoryEntityDAO.Create(ctx, &entity.RepositoryEntity{
					Name: "fake_repo",
					Link: "https://github.com/test/bigsize",
					Ref:  "master",
				})
				opt := service.GetFileIdentifierOption{
					Identifier: entity.ContextIdentifier{
						Type:        constant.ContextResourceTypeRepository,
						ResourceKey: repoID,
					},
					Path:   conv.Ptr("fake.go"),
					UserID: 1234,
				}
				content, err := ser.GetFileContent(ctx, opt)
				require.NoError(t, err)
				require.Equal(t, fakeFileContent, content.Content)
				require.Equal(t, int64(len([]byte(fakeFileContent))), content.Size)
			},
		},
		{
			name: "get file content with encode base64",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				mockOption.MockDoubaoService.EXPECT().DownloadFileByDoubaoURI(ctx, gomock.Any(), gomock.Any()).Return(
					[]byte(fakeFileContent), nil)
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl) {
				opt := service.GetFileIdentifierOption{
					Identifier: entity.ContextIdentifier{
						Type:        constant.ContextResourceTypeFile,
						ResourceKey: "fake.go",
					},
					UserID:   1234,
					Encoding: entity.EncodingTypeBase64,
				}
				content, err := ser.GetFileContent(ctx, opt)
				require.NoError(t, err)
				decodeContent, err := base64.StdEncoding.DecodeString(content.Content)
				require.NoError(t, err)
				fakeFileContentBytes := []byte(fakeFileContent)
				require.Equal(t, fakeFileContentBytes, decodeContent)
				require.Equal(t, int64(len(fakeFileContentBytes)), content.Size)
			},
		},
		{
			name: "get directory file content with encode base64",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				mockOption.MockDoubaoService.EXPECT().GetDoubaoDirectory(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return(
					[]*entity.DirectoryNode{
						{
							Name:       "fake.go",
							Type:       entity.DirectoryNodeTypeFile,
							Path:       "fake.go",
							ParentPath: "",
							URI:        "fake.go",
						},
					}, nil)
				mockOption.MockDoubaoService.EXPECT().DownloadFileByDoubaoURI(ctx, gomock.Any(), gomock.Any()).Return(
					[]byte(fakeFileContent), nil)
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl) {
				opt := service.GetFileIdentifierOption{
					Identifier: entity.ContextIdentifier{
						Type:        constant.ContextResourceTypeDirectory,
						ResourceKey: "123123131",
					},
					Path:     conv.Ptr("fake.go"),
					UserID:   1234,
					Encoding: entity.EncodingTypeBase64,
				}
				content, err := ser.GetFileContent(ctx, opt)
				require.NoError(t, err)
				decodeContent, err := base64.StdEncoding.DecodeString(content.Content)
				require.NoError(t, err)
				fakeFileContentBytes := []byte(fakeFileContent)
				require.Equal(t, fakeFileContentBytes, decodeContent)
				require.Equal(t, int64(len(fakeFileContentBytes)), content.Size)
			},
		},
		{
			name: "get repo file content with encode base64",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				mockOption.MockDGitClient.EXPECT().GetFileContentOfRepository(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&dgit.FileEntity{
						Filepath: "fake.go",
						Content:  []byte(fakeFileContent),
						Size:     int64(len(fakeFileContent)),
					}, nil)
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl) {
				repoID, _ := ser.RepositoryEntityDAO.Create(ctx, &entity.RepositoryEntity{
					Name: "fake_repo",
					Link: "https://github.com/test/bigsize",
					Ref:  "master",
				})
				opt := service.GetFileIdentifierOption{
					Identifier: entity.ContextIdentifier{
						Type:        constant.ContextResourceTypeRepository,
						ResourceKey: repoID,
					},
					Path:     conv.Ptr("fake.go"),
					UserID:   1234,
					Encoding: entity.EncodingTypeBase64,
				}
				content, err := ser.GetFileContent(ctx, opt)
				require.NoError(t, err)
				decodeContent, err := base64.StdEncoding.DecodeString(content.Content)
				require.NoError(t, err)
				fakeFileContentBytes := []byte(fakeFileContent)
				require.Equal(t, fakeFileContentBytes, decodeContent)
				require.Equal(t, int64(len(fakeFileContentBytes)), content.Size)
			},
		},
		{
			name: "get file content with encode none",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				mockOption.MockDoubaoService.EXPECT().DownloadFileByDoubaoURI(ctx, gomock.Any(), gomock.Any()).Return(
					[]byte(fakeFileContent), nil)
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl) {
				opt := service.GetFileIdentifierOption{
					Identifier: entity.ContextIdentifier{
						Type:        constant.ContextResourceTypeFile,
						ResourceKey: "fake.go",
					},
					UserID:   1234,
					Encoding: entity.EncodingTypeNone,
				}
				content, err := ser.GetFileContent(ctx, opt)
				require.NoError(t, err)
				require.Equal(t, fakeFileContent, content.Content)
				require.Equal(t, int64(len(fakeFileContent)), content.Size)
			},
		},

		{
			name: "get directory file content with encode none",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				mockOption.MockDoubaoService.EXPECT().GetDoubaoDirectory(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return(
					[]*entity.DirectoryNode{
						{
							Name:       "fake.go",
							Type:       entity.DirectoryNodeTypeFile,
							Path:       "fake.go",
							ParentPath: "",
							URI:        "fake.go",
						},
					}, nil)
				mockOption.MockDoubaoService.EXPECT().DownloadFileByDoubaoURI(ctx, gomock.Any(), gomock.Any()).Return(
					[]byte(fakeFileContent), nil)
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl) {
				opt := service.GetFileIdentifierOption{
					Identifier: entity.ContextIdentifier{
						Type:        constant.ContextResourceTypeDirectory,
						ResourceKey: "123123131",
					},
					Path:     conv.Ptr("fake.go"),
					UserID:   1234,
					Encoding: entity.EncodingTypeNone,
				}
				content, err := ser.GetFileContent(ctx, opt)
				require.NoError(t, err)
				require.Equal(t, fakeFileContent, content.Content)
				require.Equal(t, int64(len(fakeFileContent)), content.Size)
			},
		},
		{
			name: "get repo file content with encode none",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				mockOption.MockDGitClient.EXPECT().GetFileContentOfRepository(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&dgit.FileEntity{
						Filepath: "fake.go",
						Content:  []byte(fakeFileContent),
						Size:     int64(len(fakeFileContent)),
					}, nil)
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl) {
				repoID, _ := ser.RepositoryEntityDAO.Create(ctx, &entity.RepositoryEntity{
					Name: "fake_repo",
					Link: "https://github.com/test/bigsize",
					Ref:  "master",
				})
				opt := service.GetFileIdentifierOption{
					Identifier: entity.ContextIdentifier{
						Type:        constant.ContextResourceTypeRepository,
						ResourceKey: repoID,
					},
					Path:     conv.Ptr("fake.go"),
					UserID:   1234,
					Encoding: entity.EncodingTypeNone,
				}
				content, err := ser.GetFileContent(ctx, opt)
				require.NoError(t, err)
				require.Equal(t, fakeFileContent, content.Content)
				require.Equal(t, int64(len(fakeFileContent)), content.Size)
			},
		},
		{
			name: "get repo file content with unknown encode type",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				mockOption.MockDGitClient.EXPECT().GetFileContentOfRepository(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&dgit.FileEntity{
						Filepath: "fake.go",
						Content:  []byte(fakeFileContent),
						Size:     int64(len(fakeFileContent)),
					}, nil)
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl) {
				repoID, _ := ser.RepositoryEntityDAO.Create(ctx, &entity.RepositoryEntity{
					Name: "fake_repo",
					Link: "https://github.com/test/bigsize",
					Ref:  "master",
				})
				opt := service.GetFileIdentifierOption{
					Identifier: entity.ContextIdentifier{
						Type:        constant.ContextResourceTypeRepository,
						ResourceKey: repoID,
					},
					Path:     conv.Ptr("fake.go"),
					UserID:   1234,
					Encoding: -1,
				}
				_, err := ser.GetFileContent(ctx, opt)
				require.Error(t, err, service.ParamInvalidError)
			},
		},
		{
			name: "get repo file content with base64 encode type and truncated",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				mockOption.MockDGitClient.EXPECT().GetFileContentOfRepository(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&dgit.FileEntity{
						Filepath: "fake.go",
						Content:  []byte(fakeFileContent),
						Size:     int64(len(fakeFileContent)),
					}, nil)
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl) {
				repoID, _ := ser.RepositoryEntityDAO.Create(ctx, &entity.RepositoryEntity{
					Name: "fake_repo",
					Link: "https://github.com/test/bigsize",
					Ref:  "master",
				})
				limit := int64(4)
				opt := service.GetFileIdentifierOption{
					Identifier: entity.ContextIdentifier{
						Type:        constant.ContextResourceTypeRepository,
						ResourceKey: repoID,
					},
					Path:      conv.Ptr("fake.go"),
					UserID:    1234,
					Encoding:  entity.EncodingTypeNone,
					SizeLimit: &limit,
				}
				content, err := ser.GetFileContent(ctx, opt)
				require.NoError(t, err)
				require.Equal(t, fakeFileContent[:limit], content.Content)
				require.Equal(t, int64(len(fakeFileContent)), content.Size)
				require.Equal(t, true, content.Truncated)
			},
		},
		{
			name: "get file content with encode base64 and truncated",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				mockOption.MockDoubaoService.EXPECT().DownloadFileByDoubaoURI(ctx, gomock.Any(), gomock.Any()).Return(
					[]byte(fakeFileContent), nil)
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl) {
				limit := int64(4)
				opt := service.GetFileIdentifierOption{
					Identifier: entity.ContextIdentifier{
						Type:        constant.ContextResourceTypeFile,
						ResourceKey: "fake.go",
					},
					UserID:    1234,
					Encoding:  entity.EncodingTypeBase64,
					SizeLimit: &limit,
				}
				content, err := ser.GetFileContent(ctx, opt)
				require.NoError(t, err)
				decodeContent, err := base64.StdEncoding.DecodeString(content.Content)
				require.NoError(t, err)
				fakeFileContentBytes := []byte(fakeFileContent)
				require.Equal(t, fakeFileContentBytes[:limit], decodeContent)
				require.Equal(t, int64(len(fakeFileContentBytes)), content.Size)
				require.Equal(t, true, content.Truncated)
			},
		},
		{
			name: "get file content with encode base64 and no truncated",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				mockOption.MockDoubaoService.EXPECT().DownloadFileByDoubaoURI(ctx, gomock.Any(), gomock.Any()).Return(
					[]byte(fakeFileContent), nil)
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl) {
				limit := int64(9999)
				opt := service.GetFileIdentifierOption{
					Identifier: entity.ContextIdentifier{
						Type:        constant.ContextResourceTypeFile,
						ResourceKey: "fake.go",
					},
					UserID:    1234,
					Encoding:  entity.EncodingTypeBase64,
					SizeLimit: &limit,
				}
				content, err := ser.GetFileContent(ctx, opt)
				require.NoError(t, err)
				decodeContent, err := base64.StdEncoding.DecodeString(content.Content)
				require.NoError(t, err)
				fakeFileContentBytes := []byte(fakeFileContent)
				require.Equal(t, fakeFileContentBytes, decodeContent)
				require.Equal(t, int64(len(fakeFileContentBytes)), content.Size)
				require.Equal(t, false, content.Truncated)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, mockOptions := newTestContextService(t)
			tt.mockFunc(ctx, mockOptions)
			tt.checkFunc(t, s)
		})
	}
}

func TestGetContextDownloadURLInfo(t *testing.T) {
	ctx := context.Background()

	tests := []struct {
		name      string
		mockFunc  func(ctx context.Context, mockOption *mockOption)
		checkFunc func(t *testing.T, service *ContextServiceImpl)
	}{
		{
			name: "get repository download url",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl) {
				repoLink := "https://github.com/test/bigsize"
				repoName := "fake_repo"

				repoID, _ := ser.RepositoryEntityDAO.Create(ctx, &entity.RepositoryEntity{
					Name: repoName,
					Link: repoLink,
					Ref:  "master",
				})

				opt := service.ContextDownloadOption{
					Identifier: entity.ContextIdentifier{
						Type:        constant.ContextResourceTypeRepository,
						ResourceKey: repoID,
					},
					UserID: 1234,
				}
				downloadURLInfo, err := ser.GetContextDownloadURLInfo(ctx, opt)
				require.NoError(t, err)
				require.NotEmpty(t, downloadURLInfo)
				require.Equal(t, repoLink, downloadURLInfo.URL)
				require.Equal(t, repoName, downloadURLInfo.Name)
			},
		},
		{
			name: "get repository download url, but repo not exist",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl) {
				opt := service.ContextDownloadOption{
					Identifier: entity.ContextIdentifier{
						Type:        constant.ContextResourceTypeRepository,
						ResourceKey: "1231313131",
					},
					UserID: 1234,
				}
				_, err := ser.GetContextDownloadURLInfo(ctx, opt)
				require.Error(t, err)
				require.ErrorIs(t, err, service.ResourceNotFoundError)
			},
		},
		{
			name: "get directory download url, url has been generated",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				mockOption.MockDoubaoService.EXPECT().GetDoubaoDirectory(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return(
					[]*entity.DirectoryNode{
						{
							Name:       "fake.go",
							Type:       entity.DirectoryNodeTypeFile,
							Path:       "fake.go",
							ParentPath: "",
							URI:        "fake.go",
						},
					}, nil)
				mockOption.MockDoubaoService.EXPECT().GetDoubaoURLByURI(ctx, gomock.Any(), gomock.Any()).Return("https://test.com/tos-sda/123123131.zip", "", nil)
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl) {
				directoryName := "fake_directory"
				directoryID := int64(123123131)
				directoryURL := "https://test.com/tos-sda/123123131.zip"

				_, _ = ser.DirectoryEntityDAO.Create(ctx, &entity.DirectoryEntity{
					DirectoryID: directoryID,
					Name:        directoryName,
					ZipURI:      "tos-sda/123123131.zip",
				})

				opt := service.ContextDownloadOption{
					Identifier: entity.ContextIdentifier{
						Type:        constant.ContextResourceTypeDirectory,
						ResourceKey: strconv.FormatInt(directoryID, 10),
					},
					UserID: 1234,
				}
				downloadURLInfo, err := ser.GetContextDownloadURLInfo(ctx, opt)
				require.NoError(t, err)
				require.NotEmpty(t, downloadURLInfo)
				require.Equal(t, directoryURL, downloadURLInfo.URL)
				require.Equal(t, directoryName, downloadURLInfo.Name)
			},
		},
		{
			name: "get directory download url, url is empty",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				mockOption.MockDoubaoService.EXPECT().GetDoubaoDirectory(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return(
					[]*entity.DirectoryNode{
						{
							Name:       "fake.go",
							Type:       entity.DirectoryNodeTypeFile,
							Path:       "fake.go",
							ParentPath: "",
							URI:        "fake.go",
						},
					}, nil)
				mockOption.MockDirectoryCompressService.EXPECT().CompressAndUploadDoubaoDirectory(ctx, gomock.Any()).Return("os-sda/123123131.zip", 0, nil)
				mockOption.MockDoubaoService.EXPECT().GetDoubaoURLByURI(ctx, gomock.Any(), gomock.Any()).Return("https://test.com/tos-sda/123123131.zip", "", nil)
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl) {
				directoryName := "fake_directory"
				directoryID := int64(123123131)
				directoryURL := "https://test.com/tos-sda/123123131.zip"

				_, _ = ser.DirectoryEntityDAO.Create(ctx, &entity.DirectoryEntity{
					DirectoryID: directoryID,
					Name:        directoryName,
				})

				opt := service.ContextDownloadOption{
					Identifier: entity.ContextIdentifier{
						Type:        constant.ContextResourceTypeDirectory,
						ResourceKey: strconv.FormatInt(directoryID, 10),
					},
					UserID: 1234,
				}
				downloadURLInfo, err := ser.GetContextDownloadURLInfo(ctx, opt)
				require.NoError(t, err)
				require.NotEmpty(t, downloadURLInfo)
				require.Equal(t, directoryURL, downloadURLInfo.URL)
				require.Equal(t, directoryName, downloadURLInfo.Name)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, mockOptions := newTestContextService(t)
			tt.mockFunc(ctx, mockOptions)
			tt.checkFunc(t, s)
		})
	}
}

func TestAnalysisRepositoryContext(t *testing.T) {
	ctx := context.Background()

	var repositoryContextID string
	var repositoryID string

	datasetID := int64(3)
	datasourceID := int64(1234)

	repoLink := "https://github.com/abice/go-enum"
	repoName := "abice/go-enum"
	repoBranch := "master"
	commitSHA := "abc123"
	language := "Go"

	tests := []struct {
		name      string
		mockFunc  func(ctx context.Context, mockOption *mockOption)
		checkFunc func(t *testing.T, service *ContextServiceImpl)
	}{
		{
			name: "normal analysis repository context",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				repositoryID, _ = mockOption.MockRepositoryEntityDAO.Create(ctx, &entity.RepositoryEntity{
					Name:     repoName,
					Link:     repoLink,
					Ref:      repoBranch,
					Language: language,
				})

				repositoryContextID, _ = mockOption.MockContextDAO.Create(ctx, &entity.Context{
					DatasetID:    datasetID,
					DatasourceID: datasourceID,
					Name:         repoName,
					Type:         constant.ContextResourceTypeRepository,
					EntityID:     repositoryID,
					ResourceKey:  repositoryID,
					Status:       entity.ContextStatusJustCreated,
					Progress:     entity.ContextIndexProcessCreated,
				})

				mockOption.MockKnowledgebaseClient.EXPECT().UpdateFiles(ctx, gomock.Any(), gomock.Any()).Return(&knowledgebase.UpdateFilesResponse{
					Files: []*knowledgebase.File{},
				}, nil).AnyTimes()

				mockOption.MockDGitClient.EXPECT().GetTreeEntriesOfRepositoryRecursively(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return(
					[]*dgit.DirectoryEntity{
						{
							Name: "main.go",
							Path: "main.go",
							Type: dgit.DirectoryTypeFile,
						},
						{
							Name: "aaa.go",
							Path: "aaa.go",
							Type: dgit.DirectoryTypeFile,
						},
						{
							Name: "sub",
							Path: "sub",
							Type: dgit.DirectoryTypeFolder,
						},
						{
							Name: "bbb.go",
							Path: "sub/bbb.go",
							Type: dgit.DirectoryTypeFile,
						},
						{
							Name: "ccc.go",
							Path: "sub/ccc.go",
							Type: dgit.DirectoryTypeFile,
						},
					}, nil)
				mockOption.MockDGitClient.EXPECT().RepositoryExists(ctx, gomock.Any()).Return(true, nil)
				mockOption.MockDGitClient.EXPECT().FetchRepositoryFromRemote(ctx, gomock.Any(), gomock.Any()).Return(nil)
				mockOption.MockDGitClient.EXPECT().CreateRepositoryFromURL(ctx, gomock.Any(), gomock.Any()).Return(nil)
				mockOption.MockDGitClient.EXPECT().GetCommitIdOfRepository(ctx, gomock.Any(), gomock.Any()).Return(commitSHA, nil)
				mockOption.MockDGitClient.EXPECT().GetFileContentOfRepository(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&dgit.FileEntity{
						Content: []byte("hei hei hei"),
					}, nil).AnyTimes()
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl) {
				err := ser.AnalysisRepositoryContext(ctx, service.AnalysisRepoOption{
					Context: &entity.Context{
						RecordMeta: entity.RecordMeta{
							UniqueID: repositoryContextID,
						},
						DatasetID:    datasetID,
						DatasourceID: datasourceID,
						Name:         repoName,
						Type:         constant.ContextResourceTypeRepository,
						EntityID:     repositoryID,
						ResourceKey:  repositoryID,
					},

					RepoName: repoName,
					RepoLink: repoLink,
				})
				require.NoError(t, err)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, mockOptions := newTestContextService(t)
			tt.mockFunc(ctx, mockOptions)
			tt.checkFunc(t, s)
		})
	}
}

func TestAnalysisDirectoryContext(t *testing.T) {
	ctx := context.Background()

	var directoryContextID string
	var directoryID string

	datasetID := int64(2)
	datasourceID := int64(1234)

	dirName := "test"
	dirID := int64(9527)
	userID := int64(9527)

	fakeDirectoryZIPUri := "fake_directory_uri"

	tests := []struct {
		name      string
		mockFunc  func(ctx context.Context, mockOption *mockOption)
		checkFunc func(t *testing.T, service *ContextServiceImpl)
	}{
		{
			name: "normal analysis repository context",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				directoryID, _ = mockOption.MockDirectoryEntityDAO.Create(ctx, &entity.DirectoryEntity{
					Name:        dirName,
					DirectoryID: dirID,
				})

				directoryContextID, _ = mockOption.MockContextDAO.Create(ctx, &entity.Context{
					DatasetID:    datasetID,
					DatasourceID: datasourceID,
					Name:         dirName,
					Type:         constant.ContextResourceTypeDirectory,
					EntityID:     directoryID,
					ResourceKey:  strconv.FormatInt(dirID, 10),
					Status:       entity.ContextStatusJustCreated,
					Progress:     entity.ContextIndexProcessCreated,
				})

				mockOption.MockKnowledgebaseClient.EXPECT().UpdateFiles(ctx, gomock.Any(), gomock.Any()).Return(&knowledgebase.UpdateFilesResponse{
					Files: []*knowledgebase.File{},
				}, nil).AnyTimes()

				mockOption.MockDoubaoService.EXPECT().MGetDoubaoDirectory(ctx, gomock.Any(), gomock.Any(), []string{RootPath}).Return(
					[]*entity.DirectoryNode{
						{
							Name: "main.go",
							Path: "test/main.go",
							Type: entity.DirectoryNodeTypeFile,
						},
						{
							Name: "aaa.go",
							Path: "test/aaa.go",
							Type: entity.DirectoryNodeTypeFile,
						},
						{
							Name: "sub",
							Path: "test/sub",
							Type: entity.DirectoryNodeTypeDirectory,
						},
					}, nil)
				mockOption.MockDoubaoService.EXPECT().MGetDoubaoDirectory(ctx, gomock.Any(), gomock.Any(), []string{"test/sub"}).Return(
					[]*entity.DirectoryNode{
						{
							Name: "bbb.go",
							Path: "test/sub/bbb.go",
							Type: entity.DirectoryNodeTypeFile,
						},
						{
							Name: "ccc.go",
							Path: "test/sub/ccc.go",
							Type: entity.DirectoryNodeTypeFile,
						},
					}, nil)
				mockOption.MockDoubaoService.EXPECT().DownloadFileByDoubaoURI(ctx, gomock.Any(), gomock.Any()).Return([]byte("hei hei hei"), nil).AnyTimes()

				mockOption.MockDirectoryCompressService.EXPECT().CompressAndUploadDoubaoDirectory(ctx, gomock.Any()).Return(fakeDirectoryZIPUri, 0, nil)
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl) {
				err := ser.AnalysisDirectoryContext(ctx, service.AnalysisDirectoryOption{
					Context: &entity.Context{
						RecordMeta: entity.RecordMeta{
							UniqueID: directoryContextID,
						},
						DatasetID:    datasetID,
						DatasourceID: datasourceID,
						Name:         dirName,
						Type:         constant.ContextResourceTypeDirectory,
						EntityID:     directoryID,
						ResourceKey:  strconv.FormatInt(dirID, 10),
					},

					UserID:      userID,
					DirectoryID: dirID,
				})
				require.NoError(t, err)
			},
		},
		{
			name: "analysis repository context with err dir",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				directoryID, _ = mockOption.MockDirectoryEntityDAO.Create(ctx, &entity.DirectoryEntity{
					Name:        dirName,
					DirectoryID: dirID,
				})

				directoryContextID, _ = mockOption.MockContextDAO.Create(ctx, &entity.Context{
					DatasetID:    datasetID,
					DatasourceID: datasourceID,
					Name:         dirName,
					Type:         constant.ContextResourceTypeDirectory,
					EntityID:     directoryID,
					ResourceKey:  strconv.FormatInt(dirID, 10),
					Status:       entity.ContextStatusJustCreated,
					Progress:     entity.ContextIndexProcessCreated,
				})

				mockOption.MockKnowledgebaseClient.EXPECT().UpdateFiles(ctx, gomock.Any(), gomock.Any()).Return(&knowledgebase.UpdateFilesResponse{
					Files: []*knowledgebase.File{},
				}, nil).AnyTimes()

				mockOption.MockDoubaoService.EXPECT().MGetDoubaoDirectory(ctx, gomock.Any(), gomock.Any(), []string{RootPath}).Return(
					[]*entity.DirectoryNode{
						{
							Name: "main.go",
							Path: "test/main.go",
							Type: entity.DirectoryNodeTypeFile,
						},
						{
							Name: "aaa.go",
							Path: "test/aaa.go",
							Type: entity.DirectoryNodeTypeFile,
						},
						{
							Name: "sub",
							Path: "test/sub",
							Type: entity.DirectoryNodeTypeDirectory,
						},
						{
							Name: "",
							Path: "test",
							Type: entity.DirectoryNodeTypeDirectory,
						},
					}, nil)
				mockOption.MockDoubaoService.EXPECT().MGetDoubaoDirectory(ctx, gomock.Any(), gomock.Any(), []string{"test"}).Return(
					[]*entity.DirectoryNode{
						{
							Name: "main.go",
							Path: "test/main.go",
							Type: entity.DirectoryNodeTypeFile,
						},
						{
							Name: "aaa.go",
							Path: "test/aaa.go",
							Type: entity.DirectoryNodeTypeFile,
						},
						{
							Name: "sub",
							Path: "test/sub",
							Type: entity.DirectoryNodeTypeDirectory,
						},
						{
							Name: "",
							Path: "test",
							Type: entity.DirectoryNodeTypeDirectory,
						},
					}, nil)
				mockOption.MockDoubaoService.EXPECT().MGetDoubaoDirectory(ctx, gomock.Any(), gomock.Any(), []string{"test/sub"}).Return(
					[]*entity.DirectoryNode{
						{
							Name: "bbb.go",
							Path: "test/sub/bbb.go",
							Type: entity.DirectoryNodeTypeFile,
						},
						{
							Name: "ccc.go",
							Path: "test/sub/ccc.go",
							Type: entity.DirectoryNodeTypeFile,
						},
						{
							Name: "",
							Path: "test", // 返回上层path
							Type: entity.DirectoryNodeTypeDirectory,
						},
					}, nil)
				mockOption.MockDoubaoService.EXPECT().DownloadFileByDoubaoURI(ctx, gomock.Any(), gomock.Any()).Return([]byte("hei hei hei"), nil).AnyTimes()

				mockOption.MockDirectoryCompressService.EXPECT().CompressAndUploadDoubaoDirectory(ctx, gomock.Any()).Return(fakeDirectoryZIPUri, 0, nil)
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl) {
				err := ser.AnalysisDirectoryContext(ctx, service.AnalysisDirectoryOption{
					Context: &entity.Context{
						RecordMeta: entity.RecordMeta{
							UniqueID: directoryContextID,
						},
						DatasetID:    datasetID,
						DatasourceID: datasourceID,
						Name:         dirName,
						Type:         constant.ContextResourceTypeDirectory,
						EntityID:     directoryID,
						ResourceKey:  strconv.FormatInt(dirID, 10),
					},

					UserID:      userID,
					DirectoryID: dirID,
				})
				require.NoError(t, err)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, mockOptions := newTestContextService(t)
			tt.mockFunc(ctx, mockOptions)
			tt.checkFunc(t, s)
		})
	}
}

func TestUpdateRepositoryContext(t *testing.T) {
	ctx := context.Background()

	var repositoryID string

	datasetID := int64(3)
	datasourceID := int64(1234)

	repoLink := "https://github.com/abice/go-enum"
	repoName := "abice/go-enum"
	repoBranch := "master"
	commitSHA := "abc123"
	language := "Go"

	tests := []struct {
		name      string
		mockFunc  func(ctx context.Context, mockOption *mockOption)
		checkFunc func(t *testing.T, service *ContextServiceImpl)
	}{
		{
			name: "normal update repository context",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				repositoryID, _ = mockOption.MockRepositoryEntityDAO.Create(ctx, &entity.RepositoryEntity{
					Name:      repoName,
					Link:      repoLink,
					Ref:       repoBranch,
					Language:  language,
					CommitSHA: commitSHA,
				})

				mockOption.MockKnowledgebaseClient.EXPECT().UpdateFiles(ctx, gomock.Any(), gomock.Any()).Return(&knowledgebase.UpdateFilesResponse{
					Files: []*knowledgebase.File{},
				}, nil).AnyTimes()
				mockOption.MockKnowledgebaseClient.EXPECT().DeleteFiles(ctx, gomock.Any(), gomock.Any()).Return(&knowledgebase.DeleteFilesResponse{}, nil).AnyTimes()
				mockOption.MockKnowledgebaseClient.EXPECT().GetFiles(ctx, gomock.Any(), gomock.Any()).Return(&knowledgebase.GetFilesResponse{
					Files: []*knowledgebase.File{
						{
							ID:          "go-enum/main.go",
							ContentHash: "1",
						},
						{
							ID:          "go-enum/aaa.go",
							ContentHash: "2",
						},
						{
							ID:          "go-enum/sub/bbb.go",
							ContentHash: "3",
						},
						{
							ID:          "folders_0",
							ContentHash: "",
						},
						{
							ID:          "folders_1",
							ContentHash: "",
						},
					},
				}, nil)

				mockOption.MockDGitClient.EXPECT().GetTreeEntriesOfRepositoryRecursively(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return(
					[]*dgit.DirectoryEntity{
						{
							Name:     "main.go",
							Path:     "main.go",
							Type:     dgit.DirectoryTypeFile,
							ObjectID: "1.1",
						},
						{
							Name:     "aaa.go",
							Path:     "aaa.go",
							Type:     dgit.DirectoryTypeFile,
							ObjectID: "2",
						},
						{
							Name: "sub",
							Path: "sub",
							Type: dgit.DirectoryTypeFolder,
						},
						{
							Name:     "bbb.go",
							Path:     "sub/bbb.go",
							Type:     dgit.DirectoryTypeFile,
							ObjectID: "3",
						},
						{
							Name:     "ccc.go",
							Path:     "sub/ccc.go",
							Type:     dgit.DirectoryTypeFile,
							ObjectID: "4",
						},
					}, nil)
				mockOption.MockDGitClient.EXPECT().FetchRepositoryFromRemote(ctx, gomock.Any(), gomock.Any()).Return(nil)
				mockOption.MockDGitClient.EXPECT().GetCommitIdOfRepository(ctx, gomock.Any(), gomock.Any()).Return(commitSHA, nil)
				mockOption.MockDGitClient.EXPECT().GetFileContentOfRepository(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&dgit.FileEntity{
						Content: []byte("hei hei hei"),
					}, nil).AnyTimes()
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl) {
				err := ser.UpdateRepositoryContext(ctx, service.UpdateRepositoryContextOption{
					Context: &entity.Context{
						DatasetID:    datasetID,
						DatasourceID: datasourceID,
						Name:         repoName,
						Type:         constant.ContextResourceTypeRepository,
						EntityID:     repositoryID,
						ResourceKey:  repositoryID,
					},

					RepoName: repoName,
					RepoLink: repoLink,
				})
				require.NoError(t, err)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, mockOptions := newTestContextService(t)
			tt.mockFunc(ctx, mockOptions)
			tt.checkFunc(t, s)
		})
	}
}

func TestDesensitizeContext(t *testing.T) {
	ctx := context.Background()

	userID := int64(1234)
	fileResourceKeys := []string{
		"111.py",
		"222.go",
		"333.md",
	}
	DesensitizedContextID := "aaa.tar.gz"
	DesensitizedContextDownloadLink := "https://xxx.com/bucket/aaa.tar.gz"

	tests := []struct {
		name      string
		mockFunc  func(ctx context.Context, mockOption *mockOption)
		checkFunc func(t *testing.T, service *ContextServiceImpl)
	}{
		{
			name: "normal update repository context",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				for _, fileResourceKey := range fileResourceKeys {
					fileEntityId, _ := mockOption.MockFiledAO.Create(ctx, &entity.FileEntity{
						Name: fileResourceKey,
						URI:  fileResourceKey,
					})

					fileContextID, _ := mockOption.MockContextDAO.Create(ctx, &entity.Context{
						Name:        fileResourceKey,
						Type:        constant.ContextResourceTypeFile,
						EntityID:    fileEntityId,
						ResourceKey: fileResourceKey,
					})

					_, _ = mockOption.MockContextRelationDAO.Create(ctx, &entity.ContextRelation{
						ContextID:    fileContextID,
						RelatedID:    strconv.FormatInt(userID, 10),
						RelationType: entity.ContextRelationTypeReference,
					})
				}

				mockOption.MockDesensitizationService.EXPECT().Desensitize(ctx, gomock.Any()).Return(DesensitizedContextID, DesensitizedContextDownloadLink, nil)
			},
			checkFunc: func(t *testing.T, ser *ContextServiceImpl) {
				desensitizedContextInfo, err := ser.DesensitizeContext(ctx, service.DesensitizeContextOption{
					Type:         constant.ContextResourceTypeFile,
					ResourceKeys: fileResourceKeys,
				})
				require.NoError(t, err)
				require.Equal(t, DesensitizedContextID, desensitizedContextInfo.DesensitizedContextID)
				require.Equal(t, DesensitizedContextDownloadLink, desensitizedContextInfo.DesensitizedContextDownloadLink)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, mockOptions := newTestContextService(t)
			tt.mockFunc(ctx, mockOptions)
			tt.checkFunc(t, s)
		})
	}
}
