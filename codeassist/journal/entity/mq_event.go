package entity

// MetricsEvent 埋点上报到 MQ -> Hive 的事件.
type MetricsEvent struct {
	UserID    int64  `json:"user_id"`
	MessageID int64  `json:"message_id"`
	EventName string `json:"event_name"`
	EventData any    `json:"event_data"`
	EventTime int64  `json:"event_time"`
}

const (
	SandboxEventNameExecutableCheck = "executable_check_result"
	SandboxEventNameRunCode         = "run_code_result"
	SandboxEventNameCompileCode     = "compile_code_result"
)

type ExecutableCheckResultEvent struct {
	// 输入
	Identifier string `json:"identifier"`
	Version    string `json:"version"`
	CodeType   string `json:"code_type"`
	Language   string `json:"language"`
	Time       int64  `json:"time"`
	// 输出
	IsExecutable          bool         `json:"is_executable"`
	NotExecutableCategory string       `json:"not_executable_category"`
	NotExecutableReason   []*FileIssue `json:"not_executable_reason"`
	// 其他
	LogID string `json:"log_id"`
}

type FileIssue struct {
	FilePath string `json:"file_path"`
	Issue    string `json:"issue"`
	Category string `json:"category"`
}

type ExecuteCodeResultEvent struct {
	// 输入
	UserID       int64  `json:"user_id"`
	MessageID    int64  `json:"message_id"`
	Identifier   string `json:"identifier"`
	Version      string `json:"version"`
	CodeID       int64  `json:"code_id"`
	CodeVersion  int32  `json:"code_version"`
	Language     string `json:"language"`
	CodeType     string `json:"code_type"`
	FunctionType int32  `json:"function_type"`
	SandboxType  int32  `json:"sandbox_type"`
	// 输出
	Time       int64  `json:"time"`
	IsSuccess  bool   `json:"is_success"`
	FailReason string `json:"fail_reason"`
	FailDetail string `json:"fail_detail"`
	// 其他信息
	LogID                  string `json:"log_id"`
	ArtifactGenerateStatus int64  `json:"artifact_generate_status"` // 1 生成中 2 生成成功 3 用户编辑过
}
