{"name": "react-ts-vite-shadcn", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "3.10.0", "@radix-ui/react-accordion": "1.2.1", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@tailwindcss/vite": "4.0.15", "autoprefixer": "10.4.21", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "cmdk": "1.0.0", "date-fns": "3.6.0", "embla-carousel-react": "8.5.2", "input-otp": "1.4.2", "lucide-react": "0.471.1", "next-themes": "0.4.4", "postcss": "^8", "react": "^18.3.1", "react-day-picker": "8.10.1", "react-dom": "^18.3.1", "react-hook-form": "7.54.2", "react-resizable-panels": "2.1.7", "react-router-dom": "^6.30.0", "recharts": "2.15.0", "sonner": "1.7.1", "tailwind-merge": "2.6.0", "tailwindcss": "^3.4.1", "tailwindcss-animate": "1.0.7", "vaul": "1.1.2", "zod": "3.24.1"}, "devDependencies": {"@eslint/js": "^9.13.0", "@types/node": "22.13.13", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.3", "eslint": "^9.13.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.11.0", "typescript": "~5.6.2", "typescript-eslint": "^8.11.0", "vite": "^5.4.10", "vite-plugin-singlefile": "2.2.0"}}