package entity

import "time"

const (
	// 代码运行/编译 错误归因
	FailReasonPass         string = ""
	FailReasonUnknown      string = "unknown"
	FailReasonPreCheck     string = "pre_check"
	FailReasonSandboxError string = "sandbox_internal_error"
	FailReasonVeFaaSError  string = "ve_faas_error"

	FailReasonRunTimeoutError string = "run_timeout_error"
	FailReasonRunFailed       string = "run_failed"
	FailReasonCompileFailed   string = "compile_failed"

	FailReasonCompileTimeoutError     string = "compile_timeout_error"
	FailReasonCompileSyntaxError      string = "compile_syntax_error"
	FailReasonCompileDependencyError  string = "compile_dependency_error"
	FailReasonCompileTypeError        string = "compile_type_error"
	FailReasonCompilePackageError     string = "compile_package_error"
	FailReasonCompileBuildConfigError string = "compile_build_config_error"
	FailReasonCompileRuntimeError     string = "compile_runtime_error"

	// MaxSession
	SingleInstanceDefaultMaxSession       = 10
	SingleUserDefaultMaxShareInstance     = 10
	SingleUserDefaultMaxExclusiveInstance = 10

	SandboxEffectiveMaxPercentage = 100

	// TTL
	AllocateInstanceLockTTL  = 10 * time.Second
	PreemptInstanceLockTTL   = 10 * time.Second
	SandboxActionDefaultTime = 300 // 沙盒默认存活时长,单位s

	// Sandbox Temporary Unused Timespan
	SandboxTemporaryUnusedTimespan    = 15 * time.Minute
	SandboxTemporaryUnusedMaxTimespan = 1 * time.Hour

	// CommandRunStatus
	CommandExecuteTimeLimitExceeded = "TimeLimitExceeded"
	CommandExecuteReturnNonZeroCode = "ReturnNonZeroCode"

	// RunStatus
	ExecuteSuccess      = "Success"
	ExecuteFailed       = "Failed"
	ExecuteSandboxError = "SandboxError"

	// ExecuteStage
	ExecuteStageCompile = "compile"
	ExecuteStageExecute = "run"

	// MigrateInstance 迁移实例错误码
	MigrateInstanceInternalServiceError          = "InternalServiceError"
	MigrateInstanceAccountFlowLimitExceededError = "AccountFlowLimitExceeded"

	// RunCode返回类型
	RunCodeRetTypeStdout = "stdout"
	RunCodeRetTypeStderr = "stderr"
	RunCodeRetTypeImg    = "image"

	// 获取运行产物
	FetchFileDistZip  = "dist.zip"
	FetchFileCodeFile = "index.html"

	// Sandbox event timespan
	SandboxResendErrEventTimespan = 1 * time.Minute
	SandboxDeleteRetryInterval    = 300 * time.Millisecond
	SandboxDeleteRetryMaxTimes    = 3

	// Instance status
	InstanceStatusReady    = "Ready"
	InstanceStatusStarting = "Starting"
)
