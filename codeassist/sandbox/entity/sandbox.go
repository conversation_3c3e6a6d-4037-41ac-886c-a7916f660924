package entity

import (
	"fmt"
	"time"

	journalentity "code.byted.org/devgpt/kiwis/codeassist/journal/entity"
)

type ResultOperation struct {
	Ops []string `json:"ops"`
}

// ExecuteCodeOption 运行代码request
type ExecuteCodeOption struct {
	Identifier             string // 这里Identifier是artifact的唯一标识，和context的Identifier不一致
	UID                    int64
	Version                string
	ConversionID           string
	MessageID              int64
	LanguageType           string
	CodeFiles              []*CodeFile
	FetchFiles             []string
	LinkPrefix             string
	CodeID                 int64
	CodeVersion            int32
	CodeType               string // 打点用
	ArtifactGenerateStatus int64  // 打点用
}

type CodeFile struct {
	Path     string `json:"path"`
	Content  string `json:"content"`
	Language string `json:"language"`
}

// ExecuteCodeResult 运行代码response
type ExecuteCodeResult struct {
	Message string
	Code    int32
	Data    ExecuteCodeData
}

type ExecuteCodeData struct {
	Result    ExecuteCodeSlice
	IsPartial bool // 是否为部分信息，当代码执行超时时，沙箱返回部分执行结果，此时该字段为 true。完整执行完毕或中途执行报错，该值为 false
}

type ExecuteCodeSlice struct {
	CodeOutputResult []ExecuteCodeOutputResult `json:"code_output_result"`
}

type ExecuteCodeOutputResult struct {
	Stage      string
	Type       string
	ReturnCode int
	// 输出的内容 对于 stdout 类型，输出 text;对于 image 类型，输出图片下载链接;对于 stderr 类型，输出 failure-trace 信息; 对于 zip 类型，输出base64编码
	Content string
	Reason  string
}

// 代码语言
const (
	LanguageTypePython     = "python"
	LanguageTypeJava       = "java"
	LanguageTypeCpp        = "cpp"
	LanguageTypeC          = "c"
	LanguageTypeJavascript = "javascript"
	LanguageTypeJs         = "js"
	LanguageTypeNodeJs     = "nodejs"
	LanguageTypeNode       = "node"
	LanguageTypeGo         = "go"
	LanguageTypeReact      = "react"
	LanguageTypeTsx        = "tsx"
)

type CodeType string

const (
	CodeTypeArtifacts CodeType = "artifacts"
	CodeTypeRaw       CodeType = "raw"
	CodeTypeUnknown   CodeType = "unknown"
)

type ArtifactGenerateStatus int64

const (
	ArtifactGenerateStatusGenerating ArtifactGenerateStatus = 1
	ArtifactGenerateStatusGenerated                         = 2
	ArtifactGenerateStatusEdited                            = 3
)

type SandboxType int

const (
	SandboxTypeExclusive SandboxType = 0 // 独占沙盒
	SandboxTypeShare     SandboxType = 1 // 共享沙盒
)

type FunctionType int // 沙盒函数类型

const (
	FunctionTypeRun         FunctionType = 1 // 运行
	FunctionTypeCompile     FunctionType = 2 // 编译
	FunctionTypeAgent       FunctionType = 3 // agent
	FunctionTypeProcessData FunctionType = 4 //
)

type SandboxRuntimeStatus int

const (
	SandboxRuntimeStatusRunning SandboxRuntimeStatus = 1
	SandboxRuntimeStatusStopped SandboxRuntimeStatus = 2
)

// SandboxIdentifier 沙盒标识
type SandboxIdentifier struct {
	AllocationID string // 沙盒分配策略ID, 传输一样时sandbox尽可能复用到一个实例里, 例如user id
	SessionID    string
	FunctionType FunctionType
	SandboxType  SandboxType
}

func (s *SandboxIdentifier) String() string {
	return fmt.Sprintf("%s-%s-%d-%d", s.AllocationID, s.SessionID, s.FunctionType, s.SandboxType)
}

func (s *SandboxIdentifier) GetSandboxWorkDir() string {
	return "/mnt/" + s.SessionID
}

type Sandbox struct {
	ID string
	SandboxIdentifier
	FunctionID   string
	InstanceName string
	Status       SandboxRuntimeStatus
	AliveTime    int64
	CreatedAt    time.Time
	UpdatedAt    time.Time
	DeletedAt    *time.Time
}

func (s *Sandbox) AliveTimeToDuration() time.Duration {
	return time.Duration(s.AliveTime) * time.Second
}

// SandboxRecycleEvent 回收事件
type SandboxRecycleEvent struct {
	UID          int64  `json:"uid"`
	FunctionID   string `json:"function_id"`
	InstanceName string `json:"instance_name"`
	FunctionType int    `json:"function_type"`
	Language     string `json:"language"`
	AllocationID string `json:"allocation_id"`
	SessionID    string `json:"session_id"`
	SandboxType  int    `json:"sandbox_type"`
}

type BizParam struct {
	Text           string        `json:"text,omitempty"`
	Texts          []string      `json:"texts,omitempty"`
	Prompt         string        `json:"prompt,omitempty"`
	Response       string        `json:"response,omitempty"`
	BotID          string        `json:"bot_id,omitempty"`
	ChatID         string        `json:"chat_id,omitempty"`
	Round          string        `json:"round,omitempty"`
	BotType        string        `json:"bot_type,omitempty"`
	ModelID        string        `json:"model_id,omitempty"`
	Input          *InputParam   `json:"input_byte_list,omitempty"`
	URL            string        `json:"url,omitempty"`
	URLType        string        `json:"url_type,omitempty"`
	URLDecoded     string        `json:"url_decoded,omitempty"`
	LocalMessageID string        `json:"local_message_id,omitempty"`
	UserID         int64         `json:"user_id,omitempty"`
	UserIDStr      string        `json:"user_id_str,omitempty"`
	CallbackMeta   *CallbackMeta `json:"callback_meta,omitempty"`
	Source         string        `json:"source,omitempty"`
	LibraVersions  string        `json:"libra_versions,omitempty"`

	// 仅埋点：表示ClearContext使用新版审核规则 https://bytedance.larkoffice.com/docx/QcOwd6ihVoZzWRxevL8cTeN4nPe
	ReviewRebuildSwitch bool `json:"review_rebuild_switch,omitempty"`
	// 是否命中libra，安全提供了个串行做实验逻辑，字段定义的比较僵，目前应用于风险召回模型的切换 https://bytedance.larkoffice.com/wiki/ESORwOQIEiP74MkaqjicYkwcnte
	HitLibraName string `json:"hit_libra_name,omitempty"`
	// 命中个人信息
	HitNormalName bool `json:"hit_normal_name,omitempty"`
	// prompt uuid，用于串联上下行审核
	PromptUUID string `json:"prompt_uuid,omitempty"`
	// 模型意图
	LLMIntention string `json:"llm_intention,omitempty"`
}

type InputParam struct {
	Prompt   [][]byte `json:"prompt,omitempty"`
	Response [][]byte `json:"response,omitempty"`
}

type CallbackMeta struct {
	UserID              int64  `json:"user_id"`
	ConversationID      int64  `json:"conversation_id"`
	ImageMessageID      int64  `json:"image_message_id"`
	ImageLocalMessageID string `json:"image_local_message_id"`
	BizID               int64  `json:"biz_id"`
	TenantID            int64  `json:"tenant_id"`
	AppID               int32  `json:"app_id"`
}

type CheckCodeEventOption struct {
	EventName              string
	UserID                 int64
	MessageID              int64
	Language               string
	Identifier             string
	Version                string
	CodeID                 int64
	CodeVersion            int32
	CodeType               CodeType
	IsExecutable           bool
	NotExecutableCategory  string
	NotExecutableReason    []*journalentity.FileIssue
	Time                   int64
	ArtifactGenerateStatus int64  // 1 生成中 2 生成成功 3 用户编辑过
	LogID                  string // logID 用于追踪日志
}

type ExecuteCodeEventOption struct {
	EventName              string
	UserID                 int64
	MessageID              int64
	Language               string
	Identifier             string
	Version                string
	CodeID                 int64
	CodeVersion            int32
	CodeType               CodeType
	FunctionType           FunctionType
	SandboxType            SandboxType
	IsExecutable           bool
	NotExecutableReason    []*journalentity.FileIssue
	IsSuccess              bool
	FailReason             string
	FailDetail             string
	Time                   int64
	ArtifactGenerateStatus int64  // 1 生成中 2 生成成功 3 用户编辑过
	LogID                  string // logID 用于追踪日志
	// runCode 实际结果
	RunCodeResultCode    int32
	RunCodeResultMessage string
}
