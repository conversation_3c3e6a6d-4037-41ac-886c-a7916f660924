package sandboxmanager

import (
	"context"
	"encoding/json"
	"fmt"
	"path/filepath"
	"regexp"
	"runtime/debug"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/samber/lo/mutable"

	journalentity "code.byted.org/devgpt/kiwis/codeassist/journal/entity"
	journalservice "code.byted.org/devgpt/kiwis/codeassist/journal/service"
	"code.byted.org/devgpt/kiwis/codeassist/sandbox/dal"
	"code.byted.org/devgpt/kiwis/codeassist/sandbox/entity"
	"code.byted.org/devgpt/kiwis/codeassist/sandbox/service"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/devgpt/kiwis/lib/tcc"
	"code.byted.org/devgpt/kiwis/port/eventbus"
	redisCli "code.byted.org/devgpt/kiwis/port/redis"
	"code.byted.org/devgpt/kiwis/port/sandbox"
	eb "code.byted.org/eventbus/client-go"
	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/lang/gg/choose"
)

const (
	allocateInstanceLockKey = "allocate:instance:lock:%s:%d"
	preemptInstanceLockKey  = "preempt:instance:lock:%s"
)

var _ service.SandboxManagerService = &ServiceImpl{}

type ServiceImpl struct {
	SandboxClient                sandbox.Client
	JournalService               *journalservice.Service
	SandboxRecycleEventBusConfig config.SandboxRecycleEventBusConfig
	RedisCli                     redisCli.Client
	EventbusClient               eventbus.Client
	SandboxDAO                   dal.SandboxDAO
	SandboxAuthConfig            *tcc.GenericConfig[config.SandboxAuthConfig]
	SandboxFaaSFunctionsConfig   *tcc.GenericConfig[config.FaaSFunctionsConfigNew]
	SandboxAPIConfig             *tcc.GenericConfig[config.SandboxAPIConfig]
	SandboxAllocationLimitConfig *tcc.GenericConfig[config.SandboxAllocationNumLimitConfig]
}

func (s *ServiceImpl) CreateSandbox(ctx context.Context, opt *service.CreateSandboxOpt) (*entity.Sandbox, error) {
	if opt == nil {
		return nil, errors.New("invalid params")
	}
	ctx = logs.CtxAddKVs(ctx, "_allocation_id", opt.AllocationID, "_session_id", opt.SessionID)

	var (
		percentage         = s.SandboxAPIConfig.GetValue().CommonEffectivePercentage
		isSandboxDowngrade bool
		err                error
	)
	// 判断是否降级
	if percentage > entity.SandboxEffectiveMaxPercentage {
		percentage = entity.SandboxEffectiveMaxPercentage
	} else if percentage < 0 {
		percentage = 0
	}
	if percentage == 0 || (percentage < entity.SandboxEffectiveMaxPercentage && func() bool {
		uniqueID, err := strconv.ParseInt(opt.AllocationID, 10, 64)
		if err != nil {
			return false
		}
		return uniqueID%entity.SandboxEffectiveMaxPercentage >= int64(percentage)
	}()) {
		isSandboxDowngrade = true
	}

	// 先拿已分配过的沙盒, 如果已分配过直接返回
	exitRunningSandbox, err := s.SandboxDAO.GetSandboxByIdentifier(ctx, entity.SandboxIdentifier{
		AllocationID: opt.AllocationID,
		FunctionType: opt.FunctionType,
		SessionID:    opt.SessionID,
		SandboxType:  opt.SandboxType,
	}, &dal.GetSandboxOption{
		Status: lo.ToPtr(entity.SandboxRuntimeStatusRunning),
	})
	if err != nil {
		return nil, errors.WithMessage(err, "[CreateSandbox] get allocated sandbox failed")
	}
	if exitRunningSandbox != nil {
		// 更新容器使用时间进行续签, 如果更新失败，则待后续清理，无需返回错误
		updateErr := s.UpdateSandboxUsedTime(ctx, exitRunningSandbox.SandboxIdentifier)
		if updateErr != nil {
			logs.V1.CtxError(ctx, "Update instance failed, error: %v", err)
		}
		return exitRunningSandbox, nil
	}

	// 没有已分配的实例，如果降级就返回报错
	if isSandboxDowngrade {
		return nil, &service.StatusCodeError{
			StatusCode: service.ErrCodeSandboxDowngrade,
			Message:    "Sandbox instance is downgrade",
		}
	}

	// 分配新容器 拿到分布式锁就去分配 拿不到就sleep一会再重试
	newSandbox, err := s.allocateNewSandbox(ctx, opt)
	if err != nil {
		return nil, &service.StatusCodeError{
			StatusCode: service.ErrCodeSandboxInternalError,
			Message:    errors.WithMessage(err, "[CreateSandbox] allocate new sandbox failed").Error(),
		}
	}
	logs.V1.CtxInfo(ctx, "[CreateSandbox] SUCCESS allocationID: %v, allocate new instance: %v, functionID: %v", opt.AllocationID, newSandbox.InstanceName, newSandbox.FunctionID)

	err = s.sendMQEvent(ctx, &entity.SandboxRecycleEvent{
		AllocationID: newSandbox.AllocationID,
		FunctionID:   newSandbox.FunctionID,
		InstanceName: newSandbox.InstanceName,
		FunctionType: int(newSandbox.FunctionType),
		SessionID:    newSandbox.SessionID,
		SandboxType:  int(newSandbox.SandboxType),
	}, s.calculateAliveTime(opt))
	if err != nil {
		logs.V1.CtxError(ctx, "Send event failed, error: %v", err)
	}
	return newSandbox, nil
}

// calculateAliveTime 首次发送沙盒存活时间MQ事件，时间为 alive time + 超卖时间（15min）
func (s *ServiceImpl) calculateAliveTime(opt *service.CreateSandboxOpt) time.Duration {
	return time.Duration(opt.GetAliveTime())*time.Second + entity.SandboxTemporaryUnusedTimespan
}

func (s *ServiceImpl) allocateNewSandbox(ctx context.Context, opt *service.CreateSandboxOpt) (*entity.Sandbox, error) {
	// 分配实例时延
	defer metrics.TraceTimer(metrics.CodeAssistMetric.SandboxInstanceAllocationLatency.WithTags(&metrics.CodeAssistSandboxTag{
		FunctionType: int(opt.FunctionType),
		SandboxType:  int(opt.SandboxType),
		Language:     lo.FromPtr(opt.Language),
	}))()
	getLock, err := s.tryGetLockToAllocateNewInstance(ctx, opt.AllocationID, opt.FunctionType)
	if err != nil {
		return nil, &service.StatusCodeError{
			StatusCode: service.ErrCodeSandboxInternalError,
			Message:    err.Error(),
		}
	}

	// 没拿到锁 说明前一个请求已经在分配新实例了 睡一会再去db里捞
	if !getLock {
		ticker := time.NewTicker(1 * time.Second)
		defer ticker.Stop()

		attempts := 0
		maxAttempts := 2

		for attempts < maxAttempts {
			select {
			case <-ticker.C:
				attempts++
				allocatedSandbox, err := s.SandboxDAO.GetSandboxByIdentifier(ctx, entity.SandboxIdentifier{
					AllocationID: opt.AllocationID,
					FunctionType: opt.FunctionType,
					SessionID:    opt.SessionID,
					SandboxType:  opt.SandboxType,
				}, &dal.GetSandboxOption{
					Status: lo.ToPtr(entity.SandboxRuntimeStatusRunning),
				})
				if err != nil {
					return nil, &service.StatusCodeError{
						StatusCode: service.ErrCodeSandboxInternalError,
						Message:    err.Error(),
					}
				}
				if allocatedSandbox != nil {
					if s.checkSandboxInstanceExists(ctx, allocatedSandbox.FunctionID, allocatedSandbox.InstanceName) {
						return allocatedSandbox, nil
					}
				}
			case <-ctx.Done():
				return nil, &service.StatusCodeError{
					StatusCode: service.ErrCodeSandboxStillRunning,
					Message:    "context canceled while allocating instance",
				}
			}
		}
		return nil, &service.StatusCodeError{
			StatusCode: service.ErrCodeRequestReachRateLimit,
			Message:    "allocate new instance failed after retry 2 times",
		}
	}

	// 拿到锁直接分配新容器 分配完释放锁
	newSandbox, err := s.createSandbox(ctx, opt)
	if err != nil {
		logs.V1.CtxError(ctx, "Get available instance failed, error: %v", err)
		return nil, &service.StatusCodeError{StatusCode: service.ErrCodeSandboxInternalError, Message: err.Error()}
	}
	err = s.releaseLockToAllocateNewInstance(ctx, opt.AllocationID, opt.FunctionType)
	if err != nil {
		logs.V1.CtxError(ctx, "Release lock failed, error: %v", err)
	}
	return newSandbox, nil
}

func (s *ServiceImpl) createSandbox(ctx context.Context, opt *service.CreateSandboxOpt) (*entity.Sandbox, error) {
	allocatedInstanceNum, err := s.SandboxDAO.GetAllocatedInstanceNum(ctx, opt.AllocationID, opt.FunctionType, opt.SandboxType)
	if err != nil {
		logs.V1.CtxError(ctx, "[createSandbox] GetAllocatedInstanceNum failed, error: %v, allocationInstanceNum: %v", err, allocatedInstanceNum)
	}
	var (
		allocationLimitItem               = s.SandboxAllocationLimitConfig.GetValue().SandboxAllocationNumLimit[int(opt.FunctionType)]
		singleInstanceMaxSessionNum       = choose.If(allocationLimitItem.SingleInstanceMaxSessionNum < 1, entity.SingleInstanceDefaultMaxSession, allocationLimitItem.SingleInstanceMaxSessionNum)
		singleUserMaxShareInstanceNum     = choose.If(allocationLimitItem.SingleUserMaxShareInstanceNum < 1, entity.SingleUserDefaultMaxShareInstance, allocationLimitItem.SingleUserMaxShareInstanceNum)
		singleUserMaxExclusiveInstanceNum = choose.If(allocationLimitItem.SingleUserMaxExclusiveInstanceNum < 1, entity.SingleUserDefaultMaxExclusiveInstance, allocationLimitItem.SingleUserMaxExclusiveInstanceNum)
		language                          = choose.IfLazyL(opt.Language != nil, func() string { return *opt.Language }, "unknown")
	)
	if opt.SandboxType == entity.SandboxTypeShare {
		functionID, instanceName, _ := s.SandboxDAO.GetShareInstanceBySessionNum(ctx, opt.AllocationID, singleInstanceMaxSessionNum, int(opt.FunctionType))
		if functionID != "" {
			newSandbox, err := s.SandboxDAO.CreateSandbox(ctx, &entity.Sandbox{
				SandboxIdentifier: entity.SandboxIdentifier{
					AllocationID: opt.AllocationID,
					SessionID:    opt.SessionID,
					FunctionType: opt.FunctionType,
					SandboxType:  opt.SandboxType,
				},
				FunctionID:   functionID,
				InstanceName: instanceName,
				Status:       entity.SandboxRuntimeStatusRunning,
				AliveTime:    opt.GetAliveTime(),
			})
			if err != nil {
				logs.V1.CtxError(ctx, "[createSandbox] CreateSandbox failed, error: %v", err)
				return nil, errors.New(fmt.Sprintf("allocate share instance failed for session: %s", opt.SessionID))
			}
			return newSandbox, nil
		}
		if !opt.Extension && allocatedInstanceNum != 0 {
			return nil, errors.New("no available sandbox and not allow to extend")
		}
		if allocatedInstanceNum >= singleUserMaxShareInstanceNum {
			s.sendAllocationOverLimitMetrics(int(opt.FunctionType), int(opt.SandboxType), language, allocatedInstanceNum)
			logs.V1.CtxError(ctx, "[createSandbox] Allocate share instance failed for session: %s, allocatedInstanceNum: %v", opt.SessionID, allocatedInstanceNum)
			return nil, errors.New("achieve max instance number")
		}
	} else {
		if allocatedInstanceNum >= singleUserMaxExclusiveInstanceNum {
			logs.V1.CtxError(ctx, "[createSandbox] Allocate exclusive single instance failed for session: %s, allocationInstanceNum: %v", opt.SessionID, allocatedInstanceNum)
			s.sendAllocationOverLimitMetrics(int(opt.FunctionType), int(opt.SandboxType), language, allocatedInstanceNum)
			return nil, errors.New("achieve max instance number")
		}
	}

	newSandbox, err := s.createIdleSandbox(ctx, opt)
	if err != nil {
		return nil, errors.WithMessage(err, "create idle sandbox failed")
	}
	return newSandbox, nil
}

// getIdleSandbox 分配一个新实例沙盒
func (s *ServiceImpl) createIdleSandbox(ctx context.Context, opt *service.CreateSandboxOpt) (*entity.Sandbox, error) {
	for _, faasFunction := range s.SandboxFaaSFunctionsConfig.GetValue().FaaSFunctions {
		if faasFunction.FunctionType != int(opt.FunctionType) {
			continue
		}

		// 打乱 FunctionIDs 列表，避免固定顺序导致偏向
		functionIDs := faasFunction.FunctionIDs[:]
		mutable.Shuffle(functionIDs)
		logs.V1.CtxInfo(ctx, "[createIdleSandbox] functionIDs: %v", functionIDs)
		for _, functionID := range functionIDs {
			allInstances := s.getFunctionAvailableInstances(ctx, functionID)
			logs.V1.CtxInfo(ctx, "[createIdleSandbox] functionID: %v, allInstances: %v", functionID, allInstances)

			allocatedInstances, err := s.SandboxDAO.GetFunctionAllocatedInstances(ctx, functionID)
			logs.V1.CtxInfo(ctx, "[createIdleSandbox] functionID: %v, allocatedInstances: %v, err: %v", functionID, allocatedInstances, err)
			if err != nil {
				continue
			}

			// 构造set，做差集：未分配容器，提高分配效率
			allSet := lo.Associate(allInstances, func(instanceName string) (string, struct{}) {
				return instanceName, struct{}{}
			})
			logs.V1.CtxInfo(ctx, "[createIdleSandbox] functionID: %v, allSet-pre: %v", functionID, allSet)
			lo.ForEach(allocatedInstances, func(name string, _ int) {
				delete(allSet, name)
			})
			logs.V1.CtxInfo(ctx, "[createIdleSandbox] functionID: %v, allSet-after: %v", functionID, allSet)
			if len(allSet) > 0 {
				names := lo.Keys(allSet)
				mutable.Shuffle(names)
				for _, randomName := range names {
					flag, err := s.tryGetLockToPreemptNewInstance(ctx, randomName)
					if err != nil {
						logs.V1.CtxError(ctx, "[createIdleSandbox] GetLock exception, error: %v", err)
						continue
					}
					if flag {
						newSandbox, err := s.SandboxDAO.CreateSandbox(ctx, &entity.Sandbox{
							SandboxIdentifier: entity.SandboxIdentifier{
								AllocationID: opt.AllocationID,
								SessionID:    opt.SessionID,
								FunctionType: opt.FunctionType,
								SandboxType:  opt.SandboxType,
							},
							FunctionID:   functionID,
							InstanceName: randomName,
							Status:       entity.SandboxRuntimeStatusRunning,
							AliveTime:    opt.GetAliveTime(),
						})
						if err != nil {
							logs.V1.CtxError(ctx, "[createIdleSandbox] CreateSandbox ***insert*** failed, error: %v", err)
							continue
						}
						logs.V1.CtxInfo(ctx, "[createIdleSandbox] functionID: %v, randomName: %v", functionID, randomName)
						s.maintainAllocatedInstancesNum(ctx, functionID, randomName, int(opt.FunctionType), len(allocatedInstances)+1)
						_ = s.releaseLockToPreemptNewInstance(ctx, randomName)
						return newSandbox, nil
					}
				}
			}
		}
	}
	return nil, errors.New("[createIdleSandbox] no available instance")
}

func (s *ServiceImpl) checkSandboxInstanceExists(ctx context.Context, functionID string, instanceName string) bool {
	result := s.getFunctionAvailableInstances(ctx, functionID)
	return lo.Contains(result, instanceName)
}

func (s *ServiceImpl) getFunctionAvailableInstances(ctx context.Context, functionID string) (resp []string) {
	result, err := s.SandboxClient.GetFunctionInstanceList(ctx, functionID)
	logs.V1.CtxInfo(ctx, "[getFunctionAvailableInstances] functionID: %v, list:%v", functionID, result)
	if err != nil {
		log.V1.CtxError(ctx, "[getFunctionAvailableInstances] get instance list error, functionID: %v, err: %v", functionID, err)
		return
	}
	if result == nil {
		logs.V1.CtxError(ctx, "[getFunctionAvailableInstances] functionID: %v, result:%v", functionID, result)
		return
	}
	readyInstances := lo.Filter(result.InstanceItems, func(item sandbox.FunctionInstanceItem, _ int) bool {
		return item.InstanceStatus == entity.InstanceStatusReady
	})

	return lo.Map(readyInstances, func(item sandbox.FunctionInstanceItem, _ int) string {
		return item.InstanceName
	})
}

func (s *ServiceImpl) GetSandboxByIdentifier(ctx context.Context, identifier entity.SandboxIdentifier) (*entity.Sandbox, error) {
	sandboxEntity, err := s.SandboxDAO.GetSandboxByIdentifier(ctx, identifier, nil)
	if err != nil {
		return nil, err
	}
	return sandboxEntity, nil
}

func (s *ServiceImpl) GetAllSandboxDetailByAllocationID(ctx context.Context, opt *service.GetAllSandboxesOpt) ([]*entity.Sandbox, error) {
	res, err := s.SandboxDAO.GetUserFunctionAllSandboxes(ctx, opt.AllocationID, int(opt.FunctionType), int(opt.SandboxType), int(opt.Status))
	if err != nil {
		return nil, err
	}
	return res, nil
}

func (s *ServiceImpl) ExecuteCode(ctx context.Context, identifier entity.SandboxIdentifier, opt *service.ExecuteOpt) (*service.ExecuteResponse, error) {
	var (
		startTime      = time.Now()
		res            *sandbox.RunCodeResponse
		jupyterRes     *sandbox.RunJupyterResponse
		existedSandbox *entity.Sandbox
		err            error
		functionID     string
		instanceName   string
		execDetail     = &sandbox.RunCodeExecuteDetail{
			Code:               opt.ExecuteDetail.Code,
			CopyRuntimeTimeout: opt.ExecuteDetail.CopyRuntimeTimeout,
			CompileTimeout:     opt.ExecuteDetail.CompileTimeout,
			RunTimeout:         opt.ExecuteDetail.RunTimeout,
			Stdin:              opt.ExecuteDetail.Stdin,
			Language:           s.regulateCodeLanguage(opt.ExecuteDetail.Language),
			Files:              opt.ExecuteDetail.Files,
			FetchFiles:         opt.ExecuteDetail.FetchFiles,
			CWD:                choose.IfLazyL(opt.ExecuteDetail.CWD != nil, func() string { return *opt.ExecuteDetail.CWD }, identifier.GetSandboxWorkDir()),
			LinkPrefix:         choose.IfLazyL(opt.ExecuteDetail.LinkPrefix != nil, func() string { return *opt.ExecuteDetail.LinkPrefix }, ""),
			SessionID:          choose.IfLazyL(opt.ExecuteDetail.SessionID != nil, func() string { return *opt.ExecuteDetail.SessionID }, ""),
		}
		compileResults   []*service.ExecuteCommandResult
		runResults       []*service.ExecuteCommandResult
		status           string
		message          string
		veFaaSErr        bool
		veFaaSErrMessage string
	)

	// 埋点上报
	defer func() {
		go func() {
			defer func() {
				if r := recover(); r != nil {
					log.V1.CtxError(ctx, "[ExecuteCode]"+
						" panic in walkthrough goroutine: %+v, stacktrace: %s", r, string(debug.Stack()))
				}
			}()
			var (
				failReason        string
				failDetail        string
				errMsg            string
				logID, _          = ctxvalues.LogID(ctx)
				language          = opt.ExecuteDetail.Language
				runCommandRes     []*sandbox.RunCodeCommandResult
				compileCommandRes *sandbox.RunCodeCommandResult
			)
			// 获取 StdErr 信息
			if len(compileResults) > 0 {
				lo.ForEach(compileResults, func(item *service.ExecuteCommandResult, index int) {
					if item != nil {
						if item.Type == service.CodeOutputTypeStderr {
							errMsg += item.Content + "\n\n"
						}
					}
				})
			}
			if len(runResults) > 0 {
				lo.ForEach(runResults, func(item *service.ExecuteCommandResult, index int) {
					if item != nil {
						if item.Type == service.CodeOutputTypeStderr {
							errMsg += item.Content + "\n\n"
						}
					}
				})
			}
			// 解析 FailReason
			if veFaaSErr {
				failReason, failDetail = entity.FailReasonVeFaaSError, veFaaSErrMessage
			} else if status == entity.ExecuteSandboxError {
				failReason, failDetail = entity.FailReasonSandboxError, message
			} else {
				if res != nil {
					runCommandRes, compileCommandRes = []*sandbox.RunCodeCommandResult{res.RunResult}, res.CompileResult
				} else if jupyterRes != nil {
					runCommandRes, compileCommandRes = jupyterRes.RunResult, nil
				}
				failReason, failDetail = analyseFailReason(identifier.FunctionType, runCommandRes, compileCommandRes, errMsg), errMsg
			}
			s.sendCodeResultEvent(ctx, entity.ExecuteCodeEventOption{
				EventName:              choose.If(identifier.FunctionType == entity.FunctionTypeCompile, journalentity.SandboxEventNameCompileCode, journalentity.SandboxEventNameRunCode),
				UserID:                 opt.ExecuteMetricsDetail.UserID,
				MessageID:              opt.ExecuteMetricsDetail.MessageID,
				Language:               strings.ToLower(language),
				Identifier:             opt.ExecuteMetricsDetail.Identifier,
				Version:                opt.ExecuteMetricsDetail.Version,
				CodeID:                 opt.ExecuteMetricsDetail.CodeID,
				CodeVersion:            opt.ExecuteMetricsDetail.CodeVersion,
				FunctionType:           identifier.FunctionType,
				SandboxType:            identifier.SandboxType,
				CodeType:               opt.ExecuteMetricsDetail.CodeType,
				Time:                   time.Since(startTime).Milliseconds(),
				ArtifactGenerateStatus: opt.ExecuteMetricsDetail.ArtifactGenerateStatus,
				LogID:                  logID,
				IsSuccess:              status == entity.ExecuteSuccess,
				FailReason:             failReason,
				FailDetail:             failDetail,
			})
		}()
	}()

	// 更新sandbox使用时间
	defer func() {
		_ = s.UpdateSandboxUsedTime(ctx, identifier)
	}()

	if opt.InstanceName == "" || opt.FunctionID == "" {
		existedSandbox, err = s.SandboxDAO.GetSandboxByIdentifier(ctx, identifier, nil)
		if err != nil {
			return nil, err
		}
		if existedSandbox == nil {
			logs.V1.CtxWarn(ctx, "[ExecuteCode] Sandbox not found, %v", identifier)
			return nil, errors.New("[ExecuteCode] Sandbox not found")
		}
		functionID, instanceName = existedSandbox.FunctionID, existedSandbox.InstanceName
	} else {
		functionID, instanceName = opt.FunctionID, opt.InstanceName
	}

	if !s.checkSandboxInstanceExists(ctx, functionID, instanceName) {
		if identifier.FunctionType == entity.FunctionTypeAgent || identifier.FunctionType == entity.FunctionTypeProcessData {
			logs.V1.CtxWarn(ctx, "[ExecuteCode] Sandbox has migrated due to the update, %v", identifier)
			return nil, errors.New("[ExecuteCode] Sandbox has migrated probably due to the service upgrade")
		}

		if err := s.SandboxDAO.DeleteInstance(ctx, instanceName); err != nil {
			return nil, err
		}
		allocateSandbox, err := s.CreateSandbox(ctx, &service.CreateSandboxOpt{
			AllocationID: identifier.AllocationID,
			SessionID:    identifier.SessionID,
			FunctionType: identifier.FunctionType,
			SandboxType:  identifier.SandboxType,
			AliveTime: lo.ToPtr(int64(s.calculateAliveTime(&service.CreateSandboxOpt{AliveTime: choose.IfLazyL(existedSandbox != nil, func() *int64 {
				return &existedSandbox.AliveTime
			}, lo.ToPtr(int64(entity.SandboxActionDefaultTime)))}))),
			Language: &opt.ExecuteDetail.Language,
		})
		if err != nil {
			return nil, err
		}
		functionID, instanceName = allocateSandbox.FunctionID, allocateSandbox.InstanceName
	}

	// 沙盒执行代码耗时
	defer metrics.TraceTimer(metrics.CodeAssistMetric.SandboxCodeExecuteLatency.WithTags(&metrics.CodeAssistSandboxTag{
		FunctionID:   functionID,
		FunctionType: int(identifier.FunctionType),
		SandboxType:  int(identifier.SandboxType),
		Language:     strings.ToLower(opt.ExecuteDetail.Language),
	}))()

	if opt.ExecuteDetail.ShareContext && opt.ExecuteDetail.Language == entity.LanguageTypePython {
		jupyterRes, err = s.SandboxClient.RunJupyter(ctx, &sandbox.RunCodeOption{
			FunctionID:    functionID,
			InstanceName:  instanceName,
			SessionID:     identifier.SessionID,
			ExecuteDetail: execDetail,
		})
	} else {
		res, err = s.SandboxClient.RunCode(ctx, &sandbox.RunCodeOption{
			FunctionID:    functionID,
			InstanceName:  instanceName,
			SessionID:     identifier.SessionID,
			ExecuteDetail: execDetail,
		})
	}
	if err != nil {
		logs.V1.CtxError(ctx, "[ExecuteCode] RunCode/RunJupyter error: %+v", err)
		veFaaSErr = true
		veFaaSErrMessage = err.Error()
		return nil, err
	}
	compileResults, runResults, err = s.processExecuteResults(res, jupyterRes)
	if err != nil {
		logs.V1.CtxError(ctx, "[ExecuteCode] processExecuteResults error: %+v", err)
		veFaaSErr = true
		veFaaSErrMessage = err.Error()
		return nil, err
	}
	status = choose.IfLazy(res != nil, func() string { return res.Status }, func() string { return jupyterRes.Status })
	message = choose.IfLazy(res != nil, func() string { return res.Message }, func() string { return jupyterRes.Message })
	return &service.ExecuteResponse{
		Status:          status,
		Message:         message,
		CompileResult:   compileResults,
		RunResult:       runResults,
		ExecutorPodName: choose.IfLazy(res != nil, func() string { return res.ExecutorPodName }, func() string { return jupyterRes.ExecutorPodName }),
		Files:           choose.IfLazyL(res != nil, func() map[string]string { return res.Files }, map[string]string{}),
		ErrorCode:       choose.IfLazy(res != nil, func() string { return res.ErrorCode }, func() string { return jupyterRes.ErrorCode }),
		ErrorMessage:    choose.IfLazy(res != nil, func() string { return res.ErrorMessage }, func() string { return jupyterRes.ErrorMessage }),
	}, nil
}

func (s *ServiceImpl) regulateCodeLanguage(language string) string {
	normalized := strings.ToLower(strings.TrimSpace(language))
	switch normalized {
	case "python", "py":
		return entity.LanguageTypePython
	case "js", "javascript", "node", "nodejs":
		return entity.LanguageTypeNodeJs
	case "react", "tsx":
		return entity.LanguageTypeReact
	case "cpp":
		return entity.LanguageTypeCpp
	case "c":
		return entity.LanguageTypeC
	case "go":
		return entity.LanguageTypeGo
	default:
		return normalized
	}
}

func (s *ServiceImpl) processExecuteResults(res *sandbox.RunCodeResponse, jupyterRes *sandbox.RunJupyterResponse) ([]*service.ExecuteCommandResult, []*service.ExecuteCommandResult, error) {
	var (
		compileResults []*service.ExecuteCommandResult
		runResults     []*service.ExecuteCommandResult
		removeColorRE  = regexp.MustCompile(`\x1b[^m]*m`)
	)
	if jupyterRes != nil {
		if jupyterRes.ErrorCode != "" {
			return nil, nil, errors.New(jupyterRes.ErrorMessage)
		}
		for _, rr := range jupyterRes.RunResult {
			switch rr.Type {
			case "stdout":
				runResults = append(runResults, &service.ExecuteCommandResult{
					Type:          service.CodeOutputType(rr.Type),
					Status:        rr.Status,
					ExecutionTime: rr.ExecutionTime,
					ReturnCode:    rr.ReturnCode,
					Content:       rr.Stdout,
				})
			case "stderr":
				runResults = append(runResults, &service.ExecuteCommandResult{
					Type:          service.CodeOutputType(rr.Type),
					Status:        rr.Status,
					ExecutionTime: rr.ExecutionTime,
					ReturnCode:    rr.ReturnCode,
					Content:       removeColorRE.ReplaceAllString(rr.Stderr, ""),
				})
			case "image":
				for _, disp := range rr.Display {
					if imgData, ok := disp["image/png"].(string); ok {
						runResults = append(runResults, &service.ExecuteCommandResult{
							Type:          service.CodeOutputType(rr.Type),
							Status:        rr.Status,
							ExecutionTime: rr.ExecutionTime,
							ReturnCode:    rr.ReturnCode,
							Content:       imgData,
						})
					}
				}
			}
		}
	}
	if res != nil {
		if res.ErrorCode != "" {
			return nil, nil, errors.New(res.ErrorMessage)
		}
		if res.CompileResult != nil {
			cr := res.CompileResult
			if cr.Stderr != "" {
				compileResults = append(compileResults, &service.ExecuteCommandResult{
					Type:          service.CodeOutputTypeStderr,
					Status:        cr.Status,
					ExecutionTime: cr.ExecutionTime,
					ReturnCode:    cr.ReturnCode,
					Content:       removeColorRE.ReplaceAllString(cr.Stderr, ""),
				})
			}
			if cr.Stdout != "" {
				compileResults = append(compileResults, &service.ExecuteCommandResult{
					Type:          service.CodeOutputTypeStdout,
					Status:        cr.Status,
					ExecutionTime: cr.ExecutionTime,
					ReturnCode:    cr.ReturnCode,
					Content:       cr.Stdout,
				})
			}
			for _, disp := range cr.Display {
				if img, ok := disp["image/png"].(string); ok {
					compileResults = append(compileResults, &service.ExecuteCommandResult{
						Type:          service.CodeOutputTypeImage,
						Status:        cr.Status,
						ExecutionTime: cr.ExecutionTime,
						ReturnCode:    cr.ReturnCode,
						Content:       img,
					})
				}
			}
		}
		if res.RunResult != nil {
			rr := res.RunResult
			if rr.Stderr != "" {
				runResults = append(runResults, &service.ExecuteCommandResult{
					Type:          service.CodeOutputTypeStderr,
					Status:        rr.Status,
					ExecutionTime: rr.ExecutionTime,
					ReturnCode:    rr.ReturnCode,
					Content:       removeColorRE.ReplaceAllString(rr.Stderr, ""),
				})
			}
			if rr.Stdout != "" {
				runResults = append(runResults, &service.ExecuteCommandResult{
					Type:          service.CodeOutputTypeStdout,
					Status:        rr.Status,
					ExecutionTime: rr.ExecutionTime,
					ReturnCode:    rr.ReturnCode,
					Content:       rr.Stdout,
				})
			}
			for _, disp := range rr.Display {
				if img, ok := disp["image/png"].(string); ok {
					runResults = append(runResults, &service.ExecuteCommandResult{
						Type:          service.CodeOutputTypeImage,
						Status:        rr.Status,
						ExecutionTime: rr.ExecutionTime,
						ReturnCode:    rr.ReturnCode,
						Content:       img,
					})
				}
			}
		}
	}
	return compileResults, runResults, nil
}

func (s *ServiceImpl) sendCodeResultEvent(ctx context.Context, opt entity.ExecuteCodeEventOption) {
	log.V1.CtxInfo(ctx, "[sendCodeResultEvent] send run/compile result event, opt is %+v", opt)

	err := s.JournalService.SendEvent(ctx, &journalentity.MetricsEvent{
		UserID:    opt.UserID,
		EventName: opt.EventName,
		EventData: &journalentity.ExecuteCodeResultEvent{
			UserID:                 opt.UserID,
			MessageID:              opt.MessageID,
			Language:               opt.Language,
			Identifier:             opt.Identifier,
			Version:                opt.Version,
			CodeID:                 opt.CodeID,
			CodeVersion:            opt.CodeVersion,
			CodeType:               string(opt.CodeType),
			FunctionType:           int32(opt.FunctionType),
			SandboxType:            int32(opt.SandboxType),
			IsSuccess:              opt.IsSuccess,
			FailReason:             opt.FailReason,
			FailDetail:             opt.FailDetail,
			Time:                   opt.Time,
			ArtifactGenerateStatus: opt.ArtifactGenerateStatus,
			LogID:                  opt.LogID,
		},
	})
	if err != nil {
		logs.CtxError(ctx, "[sendCodeResultEvent] send run code result event failed, err: %v", err)
	}
	// 运行成功率/失败原因/吞吐
	_ = metrics.CodeAssistMetric.SandboxCodeRunThroughput.WithTags(&metrics.CodeAssistSandboxCodeRunTag{
		FunctionType: int(opt.FunctionType),
		SandboxType:  int(opt.SandboxType),
		Language:     opt.Language,
		IsSuccess:    opt.IsSuccess,
		CodeType:     string(opt.CodeType),
		FailReason:   opt.FailReason,
	}).Add(1)
}

func analyseFailReason(functionType entity.FunctionType, runCommandRes []*sandbox.RunCodeCommandResult, compileCommandRes *sandbox.RunCodeCommandResult, errMsg string) string {
	failReason := entity.FailReasonUnknown
	// 分析编译失败 failReason
	if compileCommandRes != nil && compileCommandRes.ReturnCode != 0 {
		if compileCommandRes.Status == entity.CommandExecuteTimeLimitExceeded {
			return entity.FailReasonCompileTimeoutError
		}
		// 分析 react
		if functionType == entity.FunctionTypeCompile {
			lowerLog := strings.ToLower(errMsg)

			lo.ForEach(entity.CompileErrorCategories, func(item entity.BuildErrorCategory, _ int) {
				for _, keyword := range item.Keywords {
					if strings.Contains(lowerLog, strings.ToLower(keyword)) {
						failReason = item.Name
						return
					}
				}
			})
			if failReason != entity.FailReasonUnknown {
				return failReason
			}
		}
		// 未能分析出编译错误详细归因
		return entity.FailReasonCompileFailed
	}
	// 编译通过 分析运行类 failReason
	for _, res := range runCommandRes {
		if res != nil {
			if res.Status == entity.CommandExecuteTimeLimitExceeded {
				return entity.FailReasonRunTimeoutError
			}
			if res.ReturnCode != 0 {
				return entity.FailReasonRunFailed
			}
		}
	}
	return failReason
}

func (s *ServiceImpl) CreateFile(ctx context.Context, identifier entity.SandboxIdentifier, opt *service.CreateFileOpt) (*service.CreateFileResponse, error) {
	if opt.Path == "" {
		return nil, errors.New("file path is empty")
	}

	exitedSandbox, err := s.SandboxDAO.GetSandboxByIdentifier(ctx, identifier, nil)
	if err != nil {
		return nil, err
	}
	if exitedSandbox == nil {
		return nil, errors.New("sandbox not exist")
	}
	defer func() {
		_ = s.UpdateSandboxUsedTime(ctx, identifier)
	}()

	// 构造带 session_id 前缀的新文件，实现session文件隔离
	realPath := opt.Path
	if !filepath.IsAbs(opt.Path) {
		baseDir := opt.WorkDir
		if baseDir == "" {
			baseDir = identifier.GetSandboxWorkDir()
		}
		realPath = filepath.Join(baseDir, opt.Path)
	}

	res, err := s.SandboxClient.WriteFile(ctx, &sandbox.WriteFileOption{
		FunctionID:   exitedSandbox.FunctionID,
		InstanceName: exitedSandbox.InstanceName,
		Path:         realPath,
		Content:      opt.Content,
	})
	if err != nil {
		logs.V1.CtxError(ctx, "[CreateFile] WriteFile err, err is %v", err)
		var se *sandbox.SandboxError
		if errors.As(err, &se) {
			return nil, &service.SandboxError{
				Code:    service.SandboxErrorCodePartialFilesUploadFailed,
				Message: se.Message,
			}
		}
		return nil, err
	}
	return &service.CreateFileResponse{
		Path:       realPath,
		FileSize:   res.FileSize,
		TotalLines: res.TotalLines,
	}, nil
}

func (s *ServiceImpl) DownloadFile(ctx context.Context, identifier entity.SandboxIdentifier, opt *service.DownloadFileOpt) (*service.DownloadFileResponse, error) {
	exitedSandbox, err := s.SandboxDAO.GetSandboxByIdentifier(ctx, identifier, nil)
	if err != nil {
		return nil, err
	}
	if exitedSandbox == nil {
		return nil, errors.New("sandbox not exist")
	}
	defer func() {
		_ = s.UpdateSandboxUsedTime(ctx, identifier)
	}()

	realPath := opt.Path
	if !filepath.IsAbs(opt.Path) {
		baseDir := opt.WorkDir
		if baseDir == "" {
			baseDir = identifier.GetSandboxWorkDir()
		}
		realPath = filepath.Join(baseDir, opt.Path)
	}
	res, err := s.SandboxClient.DownloadFile(ctx, &sandbox.DownloadFileOption{
		FunctionID:   exitedSandbox.FunctionID,
		InstanceName: exitedSandbox.InstanceName,
		Path:         realPath,
	})
	if err != nil {
		logs.V1.CtxError(ctx, "[DownloadFile] DownloadFile err, err is %v", err)
		var se *sandbox.SandboxError
		if errors.As(err, &se) {
			return nil, &service.SandboxError{
				Code:    service.SandboxErrorCodeInternalServerError,
				Message: err.Error(),
			}
		}
		return nil, err
	}
	return &service.DownloadFileResponse{
		Path:       realPath,
		Content:    res.Content,
		FileSize:   res.FileSize,
		TotalLines: res.TotalLines,
	}, nil
}

func (s *ServiceImpl) ReadFile(ctx context.Context, identifier entity.SandboxIdentifier, opt *service.ReadFileOpt) (*service.ReadFileResponse, error) {
	exitedSandbox, err := s.SandboxDAO.GetSandboxByIdentifier(ctx, identifier, nil)
	if err != nil {
		return nil, err
	}
	if exitedSandbox == nil {
		return nil, errors.New("sandbox not exist")
	}
	defer func() {
		_ = s.UpdateSandboxUsedTime(ctx, identifier)
	}()

	realPath := opt.Path
	if !filepath.IsAbs(opt.Path) {
		realPath = filepath.Join(identifier.GetSandboxWorkDir(), opt.Path)
	}
	res, err := s.SandboxClient.ReadFile(ctx, &sandbox.ReadFileOption{
		FunctionID:   exitedSandbox.FunctionID,
		InstanceName: exitedSandbox.InstanceName,
		Path:         realPath,
		Offset:       int64(opt.Offset),
		Limit:        int64(opt.LineLimit),
	})

	if err != nil {
		logs.V1.CtxError(ctx, "[ReadFile] ReadFile err, err is %v", err)
		var se *sandbox.SandboxError
		if errors.As(err, &se) {
			return nil, &service.SandboxError{
				Code:    service.SandboxErrorCodeInternalServerError,
				Message: err.Error(),
			}
		}
		return nil, err
	}
	return &service.ReadFileResponse{
		Content: res.Content,
	}, nil
}

func (s *ServiceImpl) EditFile(ctx context.Context, identifier entity.SandboxIdentifier, opt *service.EditFileOpt) (*service.EditFileResponse, error) {
	exitedSandbox, err := s.SandboxDAO.GetSandboxByIdentifier(ctx, identifier, nil)
	if err != nil {
		return nil, err
	}
	if exitedSandbox == nil {
		return nil, errors.New("sandbox not exist")
	}
	defer func() {
		_ = s.UpdateSandboxUsedTime(ctx, identifier)
	}()

	realPath := opt.Path
	if !filepath.IsAbs(opt.Path) {
		realPath = filepath.Join(identifier.GetSandboxWorkDir(), opt.Path)
	}

	res, err := s.SandboxClient.EditFile(ctx, &sandbox.EditFileOption{
		FunctionID:   exitedSandbox.FunctionID,
		InstanceName: exitedSandbox.InstanceName,
		Path:         realPath,
		OldStr:       opt.OldStr,
		NewStr:       opt.NewStr,
	})
	if err != nil {
		logs.V1.CtxError(ctx, "[EditFile] EditFile err, err is %v", err)
		var se *sandbox.SandboxError
		if errors.As(err, &se) {
			return nil, &service.SandboxError{
				Code:    service.SandboxErrorCodeInternalServerError,
				Message: err.Error(),
			}
		}
	}
	return &service.EditFileResponse{
		Path:       realPath,
		FileSize:   res.FileSize,
		TotalLines: res.TotalLines,
	}, nil
}

func (s *ServiceImpl) UpdateSandboxUsedTime(ctx context.Context, identifier entity.SandboxIdentifier) error {
	return s.SandboxDAO.UpdateSandboxUpdatedTime(ctx, identifier)
}

func (s *ServiceImpl) ClearSandboxOrInstance(ctx context.Context, identifier entity.SandboxIdentifier) error {
	// step1: 查询沙盒是否存在
	exitedSandbox, err := s.SandboxDAO.GetSandboxByIdentifier(ctx, identifier, nil)
	if err != nil {
		return err
	}
	if exitedSandbox == nil {
		return errors.New("sandbox not exist")
	}
	// step2: 删除实例上的session
	if exitedSandbox.Status != entity.SandboxRuntimeStatusStopped {
		_ = s.SandboxClient.DeleteInstanceSession(ctx, &sandbox.DeleteInstanceSessionOption{
			FunctionID:   exitedSandbox.FunctionID,
			InstanceName: exitedSandbox.InstanceName,
			SessionID:    exitedSandbox.SessionID,
		})
	}

	// step3: 如果是共享实例，查询是否是最后一个还在运行的 sandbox session. 如果是，需要回收实例
	migrateInstanceFlag := true
	if exitedSandbox.SandboxType == entity.SandboxTypeShare {
		sessionIDs, err := s.SandboxDAO.GetInstanceUnDeleteSandboxSessionIDs(ctx, exitedSandbox.InstanceName)
		if err != nil {
			logs.V1.CtxError(ctx, "[ClearSandboxOrInstance] GetInstanceUnDeleteSandboxSessionIDs failed: %v", err)
			return err
		}
		if !(len(sessionIDs) == 1 && sessionIDs[0] == identifier.SessionID) {
			migrateInstanceFlag = false
		}
	}

	// step4: 删除实例pod
	if migrateInstanceFlag {
		sandboxErr := s.migrateInstance(ctx, exitedSandbox.FunctionID, exitedSandbox.InstanceName)
		if sandboxErr != nil {
			logs.V1.CtxError(ctx, "[HandleSandboxMQEvent] failed to migrate function instances: %v", sandboxErr)
			if sandboxErr.Code == service.SandboxErrorCodeMigrateInstanceInternalServiceError ||
				sandboxErr.Code == service.SandboxErrorCodeFunctionConcurrencyLimitExceeded {
				return errors.New("[MigrateInstanceInternalServiceError] failed to migrate function instances")
			}
			return errors.New(sandboxErr.Error())
		}
	}

	// step5: db删除记录
	err = s.SandboxDAO.DeleteSandboxByID(ctx, exitedSandbox.ID)
	if err != nil {
		logs.V1.CtxError(ctx, "[ClearSandboxOrInstance] failed to delete sandbox[%s]: %v", exitedSandbox.ID, err)
	}
	return nil
}

func (s *ServiceImpl) migrateInstance(ctx context.Context, functionID string, instanceName string) *service.SandboxError {
	res, err := s.SandboxClient.MigrateInstance(ctx, functionID, instanceName)
	if err != nil {
		return &service.SandboxError{
			Code:    service.SandboxErrorCodeInternalServerError,
			Message: err.Error(),
		}
	}
	if res != nil && res.Error != nil {
		if res.Error.Code == entity.MigrateInstanceInternalServiceError {
			return &service.SandboxError{
				Code:    service.SandboxErrorCodeMigrateInstanceInternalServiceError,
				Message: res.Error.Message,
			}
		}
		if res.Error.Code == entity.MigrateInstanceAccountFlowLimitExceededError {
			return &service.SandboxError{
				Code:    service.SandboxErrorCodeMigrateInstanceAccountFlowLimitExceededError,
				Message: res.Error.Message,
			}
		}
	}
	return nil
}

func (s *ServiceImpl) GetNeedRecycleSandboxListByTime(ctx context.Context, fromTime, endTime time.Time) ([]*entity.Sandbox, error) {
	return s.SandboxDAO.GetNeedRecycleSandboxListByTime(ctx, fromTime, endTime)
}

func (s *ServiceImpl) maintainAllocatedInstancesNum(ctx context.Context, functionID string, instanceName string, functionType int, num int) {
	// 将容器分配情况上报
	if num >= s.SandboxAPIConfig.GetValue().CommonTotalInstancesNumber*s.SandboxAPIConfig.GetValue().CommonEffectivePercentage {
		logs.V1.CtxWarn(ctx, "[ExecuteCode] total instance number exceed percentage: %d", num)
	}
	logs.V1.CtxInfo(ctx, "[maintainAllocatedNum] functionID:%s, instanceName:%s, count: %v", functionID, instanceName, num)
	_ = metrics.CodeAssistMetric.SandboxInstanceAllocationThroughput.WithTags(&metrics.CodeAssistSandboxTag{
		FunctionID:   functionID,
		FunctionType: functionType,
	}).Store(float64(num))
}

func (s *ServiceImpl) tryGetLockToAllocateNewInstance(ctx context.Context, allocationID string, functionType entity.FunctionType) (bool, error) {
	lockName := fmt.Sprintf(allocateInstanceLockKey, allocationID, int(functionType))
	return s.RedisCli.SetNX(ctx, lockName, "1", entity.AllocateInstanceLockTTL)
}

func (s *ServiceImpl) releaseLockToAllocateNewInstance(ctx context.Context, allocationID string, functionType entity.FunctionType) error {
	lockName := fmt.Sprintf(allocateInstanceLockKey, allocationID, int(functionType))
	return s.RedisCli.Del(ctx, lockName)
}

func (s *ServiceImpl) tryGetLockToPreemptNewInstance(ctx context.Context, instanceName string) (bool, error) {
	lockName := fmt.Sprintf(preemptInstanceLockKey, instanceName)
	return s.RedisCli.SetNX(ctx, lockName, "1", entity.PreemptInstanceLockTTL)
}

func (s *ServiceImpl) releaseLockToPreemptNewInstance(ctx context.Context, instanceName string) error {
	lockName := fmt.Sprintf(preemptInstanceLockKey, instanceName)
	return s.RedisCli.Del(ctx, lockName)
}

func (s *ServiceImpl) sendMQEvent(ctx context.Context, event *entity.SandboxRecycleEvent, delay time.Duration) error {
	message, err := json.Marshal(event)
	if err != nil {
		return err
	}
	taskEvent := eb.NewProducerEventBuilder().
		WithEventName(s.SandboxRecycleEventBusConfig.EventName).
		WithValue(message).
		WithDelay(delay).
		Build()
	logs.V1.CtxInfo(ctx, "[sendMQEvent] send mq event:%s", taskEvent.String())
	if sendErr := s.EventbusClient.Publish(ctx, taskEvent); sendErr != nil {
		log.V1.CtxError(ctx, "[sendMQEvent] failed to publish event: %v", sendErr)
		return sendErr
	}
	return nil
}

func (s *ServiceImpl) sendAllocationOverLimitMetrics(functionType int, sandboxType int, language string, allocationNum int) {
	_ = metrics.CodeAssistMetric.SandboxInstanceAllocationOverLimitThroughput.WithTags(&metrics.CodeAssistSandboxInstanceAllocationOverLimitTag{
		FunctionType:  functionType,
		SandboxType:   sandboxType,
		Language:      language,
		AllocationNum: allocationNum,
	}).Add(1)
}
