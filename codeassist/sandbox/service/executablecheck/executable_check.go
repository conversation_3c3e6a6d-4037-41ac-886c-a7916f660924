package executablecheck

import (
	"context"
	"fmt"

	"github.com/pkg/errors"

	"code.byted.org/devgpt/kiwis/codeassist/sandbox/entity"
	"code.byted.org/devgpt/kiwis/codeassist/sandbox/service"
)

var _ service.ExecutableCheckService = &Service{}

type Service struct {
	PythonCodeAnalyzer *PythonAnalyzer
	CommonCodeAnalyzer *CommonAnalyzer
}

func NewExecutableCheckService(pyAnalyzer *PythonAnalyzer, commonAnalyzer *CommonAnalyzer) *Service {
	return &Service{
		PythonCodeAnalyzer: pyAnalyzer,
		CommonCodeAnalyzer: commonAnalyzer,
	}
}

func (s *Service) CheckCodeIsExecutable(ctx context.Context, opt *service.CodeIsExecutableOption) (bool, []*entity.ExecutableResult) {
	// 先写个简单的映射 之后有迭代需求再优化映射方式
	switch opt.CheckerType {
	case service.CheckerTypeLanguageType:
		checker, err := s.getCheckerByLanguageType(opt.LanguageType)
		if err != nil {
			return false, []*entity.ExecutableResult{
				{
					Issue:               err.Error(),
					ProhibitedOperation: "language not support",
					Category:            entity.ExecutableResultCategoryLanguageNotSupport,
				},
			}
		}
		return checker.CheckCodeIsExecutable(ctx, opt.Code)
	default:
		return false, []*entity.ExecutableResult{
			{
				Issue:               fmt.Sprintf("checkerType not support, checker type: %s, language: %s", opt.CheckerType, opt.LanguageType),
				ProhibitedOperation: "language not support",
				Category:            entity.ExecutableResultCategoryLanguageNotSupport,
			},
		}
	}
}

func (s *Service) getCheckerByLanguageType(language string) (service.Analyzer, error) {
	switch language {
	case entity.LanguageTypePython:
		return s.PythonCodeAnalyzer, nil
	case entity.LanguageTypeCpp, entity.LanguageTypeC, entity.LanguageTypeJava, entity.LanguageTypeGo,
		entity.LanguageTypeJavascript, entity.LanguageTypeJs, entity.LanguageTypeNodeJs, entity.LanguageTypeNode,
		entity.LanguageTypeReact, entity.LanguageTypeTsx:
		return s.CommonCodeAnalyzer, nil
	default:
		return nil, errors.New(fmt.Sprintf("not support language:%+v", language))
	}
}
