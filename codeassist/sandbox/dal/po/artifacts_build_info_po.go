// Code generated by SQL PO generator, DO NOT EDIT.
// This src file contains PO(persistent object) struct definition for database table `artifacts_build_info`.
package po

import (
	datatypes "gorm.io/datatypes"
	softdelete "gorm.io/plugin/soft_delete"
	"time"
)

// ArtifactsBuildInfoPO is 构建产物信息表.
type ArtifactsBuildInfoPO struct {
	// ID is 主键 ID.
	ID int64 `gorm:"column:id"`
	// CreatedAt is 创建时间.
	CreatedAt time.Time `gorm:"column:created_at"`
	// UpdatedAt is 更新时间.
	UpdatedAt time.Time `gorm:"column:updated_at"`
	// DeletedAt is 软删除时间.
	DeletedAt softdelete.DeletedAt `gorm:"column:deleted_at;softDelete:nano"`
	// CodeID is 资源 ID，如代码任务标识（int64）.
	//
	// index: idx_code_id_version, priority: 1.
	//
	CodeID int64 `gorm:"column:code_id"`
	// CodeVersion is 资源版本号（int32）.
	//
	// index: idx_code_id_version, priority: 2.
	//
	CodeVersion int `gorm:"column:code_version"`
	// ContentMd5 is 内容的 MD5 哈希值.
	ContentMd5 string `gorm:"column:content_md5;size:64"`
	// CompileResp is 编译响应 JSON，不含文件.
	CompileResp datatypes.JSONType[ArtifactsBuildInfoCompileResp] `gorm:"column:compile_resp"`
	// Status is 构建状态：1 编译中，2 上传中，3 完成，4 失败.
	Status int8 `gorm:"column:status"`
}

// TableName returns PO's corresponding DB table name: `artifacts_build_info`.
func (*ArtifactsBuildInfoPO) TableName() string {
	return "artifacts_build_info"
}
